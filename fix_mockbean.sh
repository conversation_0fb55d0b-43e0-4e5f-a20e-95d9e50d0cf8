#!/bin/bash

# 批量替换@MockBean为@MockitoBean
files=(
"src/test/java/com/sharecrm/egress/service/I18nLocationServiceTest.java"
"src/test/java/com/sharecrm/egress/service/MobileServiceTest.java"
"src/test/java/com/sharecrm/egress/sms/dingqiao/DingqiaoAliyunSmsSenderTest.java"
"src/test/java/com/sharecrm/egress/sms/mengniu/MengniuSmsServiceTest.java"
"src/test/java/com/sharecrm/egress/sms/share/MonternetChineseSenderTest.java"
"src/test/java/com/sharecrm/egress/sms/SmsReallyServiceTest.java"
"src/test/java/com/sharecrm/egress/sms/zsj/ZsjCmhkSmsServiceTest.java"
"src/test/java/com/sharecrm/egress/sms/zsj/ZsjSinoSmsServiceTest.java"
"src/test/java/com/sharecrm/egress/web/AsrControllerTest.java"
"src/test/java/com/sharecrm/egress/web/CoordinateConvertControllerTest.java"
"src/test/java/com/sharecrm/egress/web/DirtyWordControllerTest.java"
"src/test/java/com/sharecrm/egress/web/DistanceControllerTest.java"
"src/test/java/com/sharecrm/egress/web/DrivingRouteControllerTest.java"
"src/test/java/com/sharecrm/egress/web/EmailControllerTest.java"
"src/test/java/com/sharecrm/egress/web/GeoCodeControllerTest.java"
"src/test/java/com/sharecrm/egress/web/IpLocationControllerTest.java"
"src/test/java/com/sharecrm/egress/web/MobileControllerTest.java"
"src/test/java/com/sharecrm/egress/web/OcrControllerTest.java"
"src/test/java/com/sharecrm/egress/web/PoiSearchControllerTest.java"
"src/test/java/com/sharecrm/egress/web/ReverseGeoControllerTest.java"
"src/test/java/com/sharecrm/egress/web/ShortUrlControllerTest.java"
"src/test/java/com/sharecrm/egress/web/SmsControllerTest.java"
)

for file in "${files[@]}"; do
    echo "Processing $file"
    # 替换import语句
    sed -i '' 's/import org.springframework.boot.test.mock.mockito.MockBean;/import org.springframework.test.context.bean.override.mockito.MockitoBean;/g' "$file"
    sed -i '' 's/import org.springframework.boot.test.mock.mockito.MockBeans;/import org.springframework.test.context.bean.override.mockito.MockitoBean;/g' "$file"
    # 替换注解
    sed -i '' 's/@MockBean/@MockitoBean/g' "$file"
    sed -i '' 's/@MockBeans/@MockitoBean/g' "$file"
done

echo "All files processed!"
