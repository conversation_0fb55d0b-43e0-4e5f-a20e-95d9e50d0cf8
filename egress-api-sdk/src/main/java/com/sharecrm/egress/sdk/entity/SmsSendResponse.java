package com.sharecrm.egress.sdk.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * 发送短信响应体
 */
@Data
public class SmsSendResponse {

  /**
   * 发送结果，全部成功为true，否则为false
   */
  private boolean success = true;

  /**
   * 失败原因
   */
  private String message;

  /**
   * 批次ID，由我们自己生成，每次N条是同一个ID，可根据此ID按批次回查短信状态
   */
  private String batchMsgId;

  /**
   * 每个手机号的具体结果
   */
  private List<SmsResponseData> phones = new ArrayList<>();
}
