package com.sharecrm.egress.sdk.entity;

import lombok.Data;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;

@Data
public class EgressApiResponse<T> {

  /**
   * 状态码，正常为200，找不到为404，服务端错误为500
   */
  private int code;

  /**
   * 响应消息，正常为OK，其他是各业务自定义消息
   */
  private String message;

  /**
   * 响应数据，正常为查询结果，找不到为null，服务端错误为null
   */
  private T data;

  public EgressApiResponse() {
  }

  public EgressApiResponse(int code, String message) {
    this(code, message, null);
  }

  public EgressApiResponse(int code, String message, T data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public static <T> EgressApiResponse<T> ok(@Nullable T body) {
    return new EgressApiResponse<>(200, "ok", body);
  }

  public static <T> EgressApiResponse<T> notFound() {
    return notFound(null);
  }

  public static <T> EgressApiResponse<T> notFound(@Nullable T body) {
    return new EgressApiResponse<>(HttpStatus.NOT_FOUND.value(), HttpStatus.NOT_FOUND.getReasonPhrase(), body);
  }

  public static <T> EgressApiResponse<T> badRequest() {
    return badRequest(null);
  }

  public static <T> EgressApiResponse<T> badRequest(@Nullable T body) {
    return new EgressApiResponse<>(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase(), body);
  }

  public static <T> EgressApiResponse<T> internalServerError() {
    return internalServerError(null);
  }

  public static <T> EgressApiResponse<T> internalServerError(@Nullable T body) {
    return new EgressApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase(), body);
  }

}
