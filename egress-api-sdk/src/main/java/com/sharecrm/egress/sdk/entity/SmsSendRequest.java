package com.sharecrm.egress.sdk.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 发送短信请求体
 */
@Data
@NoArgsConstructor
public class SmsSendRequest {

  /**
   * 短信接收人手机号，一次最多允许200个手机号
   */
  private List<String> phones;

  /**
   * 短信内容，如果是语音验证码服务，内容是4或6位的验证码
   */
  private String content;

  /**
   * 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   */
  private String bizName;

  /**
   * 指定当前手机号的EI，如果有多个用逗号拼接（比如登录时还不确定租户，一个手机可能对应多个EI），根据EI选择定制渠道，不指定时尝试从Header中获取，获取不到使用纷享默认的渠道
   */
  private String enterpriseId;

  /**
   * 短信签名语言，目前仅支持 zh-CN 和 en 这两个。
   * <p>
   * 可以不指定，不指定时根据短信文本内容自动判断。
   */
  private String language;

  public SmsSendRequest(List<String> phones, String content, String bizName) {
    this.phones = phones;
    this.content = content;
    this.bizName = bizName;
  }

  public SmsSendRequest(List<String> phones, String content, String bizName, String enterpriseId) {
    this.phones = phones;
    this.content = content;
    this.bizName = bizName;
    this.enterpriseId = enterpriseId;
  }
}
