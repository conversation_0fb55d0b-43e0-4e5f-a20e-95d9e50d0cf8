package com.sharecrm.egress.sdk.entity;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 发送短信每个手机号的详细信息
 */
@Data
@NoArgsConstructor
public class SmsResponseData {

  private boolean success = true;

  private String phone;

  /**
   * 发送成功或失败的提示信息，不是短信内容
   */
  private String message;

  /**
   * 此次发送记录的消息ID，即发送流水号，可根据此ID查询此条短信的回执状态
   */
  private String msgId;

  /**
   * 各个短信供应商返回的消息ID，由供应商定义的流水号，部分供应商可能不支持，不要拿此ID查状态
   */
  private String serialId;

  public SmsResponseData(boolean success, String phone, String message, String msgId) {
    this.success = success;
    this.phone = phone;
    this.message = message;
    this.msgId = msgId;
  }

  public SmsResponseData(boolean success, String phone, String message, String msgId, String serialId) {
    this.success = success;
    this.phone = phone;
    this.message = message;
    this.msgId = msgId;
    this.serialId = serialId;
  }
}
