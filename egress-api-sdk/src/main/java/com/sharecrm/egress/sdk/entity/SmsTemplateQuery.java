package com.sharecrm.egress.sdk.entity;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 查询短信模板的请求参数
 */
@Data
@NoArgsConstructor
public class SmsTemplateQuery {

  /**
   * 模板ID，由本平台定义的ID，不是运营商提供的ID。可以不指定
   */
  private String templateId;

  /**
   * 模板类型：验证码、通知等，可以不指定
   */
  private String templateType;

  /**
   * 指定EI查询，根据EI确认单独定制的短信通道，如果不指定返回平台默认能用的模板
   */
  private Integer enterpriseId;

  /**
   * 按审核状态过滤
   */
  private String status;

  private String providerId;

  /**
   * 是否国际/港澳台短信,true为国际短信，false为国内短信
   */
  private Boolean international;

}
