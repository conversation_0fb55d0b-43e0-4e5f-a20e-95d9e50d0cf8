package com.sharecrm.egress.sdk.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 增加或修改短信模板的请求体
 */
@Data
@NoArgsConstructor
public class SmsTemplateRequest {

  /**
   * 模板名称，必填
   */
  private String name;

  /**
   * 内容，必填
   */
  private String content;

  /**
   * 说明，必填
   */
  private String remark;

  /**
   * 应用场景
   */
  private String applySceneContent;

  /**
   * 模板需要关联的签名名称，关联的短信签名必须为审核通过的签名。
   * 关联签名可以提升审核效率，此处关联的签名和短信发送时选择的签名无关。
   * 不填时，默认使用系统配置的中文签名。
   */
  private String relatedSignName;

  /**
   * 指定专属EI，根据EI选择定制渠道，必填
   */
  private Integer enterpriseId;

  /**
   * 可以指定允许哪些EI访问，使用Gray规则表达式，如果不指定默认只能当前EI使用
   */
  private String allowAccounts;

  /**
   * 指定短信服务商ID，不指定时使用纷享默认的渠道
   */
  private String providerId;

  /**
   * 短信类型：验证码,短信通知,推广短信。 必填
   */
  private String templateType;

  /**
   * 是否国际/港澳台短信, true为国际短信，false为国内短信
   */
  private boolean international;

  /**
   * 变量类型定义和描述，目前阿里云是必须指定，其他云不指定也能审核通过，指定后成功率更高
   */
  private List<SmsTemplateVariableAttr> variableAttributes;

}
