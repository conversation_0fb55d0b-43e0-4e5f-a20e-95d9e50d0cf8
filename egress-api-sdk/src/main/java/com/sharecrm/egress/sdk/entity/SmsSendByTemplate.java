package com.sharecrm.egress.sdk.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 按模板发送短信请求体
 */
@Data
public class SmsSendByTemplate {

  /**
   * 短信接收人手机号，一次最多允许200个手机号，要么全部是国内号码要么全部是国际号码
   */
  private List<String> phones;

  /**
   * 模板ID，根据此ID发送短信、回查状态
   */
  private String templateId;

  /**
   * 模板参数，根据此参数发送短信，key需要和模板一一对应
   */
  private Map<String, String> templateParam = new HashMap<>();

  /**
   * 指定当前手机号的EI，如果有多个用逗号拼接，根据EI选择定制渠道，不指定时尝试从Header中获取，获取不到使用纷享默认的渠道
   */
  private String enterpriseId;

  /**
   * 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   */
  private String bizName;

  /**
   * 目前仅用作短信签名语言，仅支持 zh-CN 和 en 这两个。
   * <p>
   * 可以不指定，不指定时根据短信文本内容自动判断。
   */
  private String language;

}
