package com.sharecrm.egress.sdk.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 文字语音请求体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TTSSendRequest {

  /**
   * 短信接收人手机号，一次最多允许200个手机号
   */
  private List<String> phones;

  /**
   * 模版ID，具体有哪些模版可用请参考以下链接，不够需向运营商提交申请：<a href="https://wiki.firstshare.cn/display/PlatformKnowledge/egress-api-service">...</a>
   */
  private Integer templateId;

  /**
   * 模板参数，带顺序的，参数个数需要和模版保持一致，否则发送失败
   */
  private String[] templateParams;

  /**
   * 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   */
  private String bizName;

  /**
   * 指定当前手机号的EI，如果有多个用逗号拼接（比如登录时还不确定租户，一个手机可能对应多个EI），根据EI选择定制渠道，不指定时尝试从Header中获取，获取不到使用纷享默认的渠道
   */
  private String enterpriseId;

}
