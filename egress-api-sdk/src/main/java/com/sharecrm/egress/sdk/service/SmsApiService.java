package com.sharecrm.egress.sdk.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.ConfigFactory;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;

import java.util.Arrays;

/**
 * 短信服务，发送国际、国内短信，语音验证码.
 * <p>
 * 注意事项:
 * 1. 服务有限流,一个手机号一分钟内只能发送一个短信.
 * 2. 批量发送每次最多允许200个.
 * 3. 此Api请求成功只代表发给服务成功，不代表发到手机端成功，不成功的原因有N多，运营商有诸多限制。
 */
@Slf4j
public class SmsApiService implements InitializingBean {

  private static final MediaType MEDIA_TYPE_JSON = MediaType.parse("application/json; charset=utf-8");
  private static final String VARIABLES_ENDPOINT = "variables_endpoint";
  private static final String EGRESS_API_SMS_URL_KEY = "egress_api_sms_url";

  private String egressSmsServiceUrl;
  private final OkHttpSupport okHttpSupport;

  public SmsApiService(OkHttpSupport okHttpSupport) {
    this.okHttpSupport = okHttpSupport;
  }

  /**
   * 发送国内短信，使用限制请参考请求体每个字段描述
   *
   * @param request 短信请求体
   * @return 发送结果，注意手机号有多个时，全部成功success为true，否则success为false，可查看结果中每个手机号的结果
   */
  public SmsSendResponse sendSms(SmsSendRequest request) {
    return sendToEgressApiService(request, egressSmsServiceUrl + "/api/v2/sms/internal");
  }

  /**
   * 发送国内短信
   *
   * @param phones  手机号，多个用英文逗号分割,一次最多只能200个手机号
   * @param content 短信内容
   * @param bizName 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   * @return 发送结果
   */
  public SmsSendResponse sendSms(String phones, String content, String bizName) {
    return sendSms(new SmsSendRequest(Arrays.asList(phones.split(",")), content, bizName));
  }

  /**
   * 发送国内短信
   *
   * @param phones       手机号，多个用英文逗号分割,一次最多只能200个手机号
   * @param content      短信内容
   * @param bizName      调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   * @param enterpriseId EI，如果有多个用逗号拼接（比如登录时还不确定租户，一个手机可能对应多个EI），根据EI选择定制渠道，不指定时尝试从Header中获取，获取不到使用纷享默认的渠道
   * @return 发送结果
   */
  public SmsSendResponse sendSms(String phones, String content, String bizName, String enterpriseId) {
    return sendSms(new SmsSendRequest(Arrays.asList(phones.split(",")), content, bizName, enterpriseId));
  }

  /**
   * 发送国际短信
   *
   * @param request 短信请求体
   * @return 发送结果
   */
  public SmsSendResponse sendIntlSMS(SmsSendRequest request) {
    return sendToEgressApiService(request, egressSmsServiceUrl + "/api/v2/sms/intl");
  }

  /**
   * 发送国际短信
   *
   * @param phones  国际手机号，多个用逗号分割,一次最多只能200个手机号,注意国际手机号必须以0开头
   * @param content 短信内容
   * @param bizName 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   * @return 发送结果
   */
  public SmsSendResponse sendIntlSMS(String phones, String content, String bizName) {
    return sendIntlSMS(new SmsSendRequest(Arrays.asList(phones.split(",")), content, bizName));
  }

  /**
   * 发送国际短信
   *
   * @param phones       国际手机号，多个用逗号分割,一次最多只能200个手机号,注意国际手机号必须以0开头
   * @param content      短信内容
   * @param bizName      调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   * @param enterpriseId EI，如果有多个用逗号拼接（比如登录时还不确定租户，一个手机可能对应多个EI），根据EI选择定制渠道，不指定时尝试从Header中获取，获取不到使用纷享默认的渠道
   * @return 发送结果
   */
  public SmsSendResponse sendIntlSMS(String phones, String content, String bizName, String enterpriseId) {
    return sendIntlSMS(new SmsSendRequest(Arrays.asList(phones.split(",")), content, bizName, enterpriseId));
  }

  /**
   * 发送语音验证码
   *
   * @param request 短信请求体
   * @return 发送结果
   */
  public SmsSendResponse sendCodeCall(SmsSendRequest request) {
    return sendToEgressApiService(request, egressSmsServiceUrl + "/api/v2/sms/code-call");
  }

  /**
   * 发送语音验证码
   *
   * @param phones  国内手机号，多个用逗号分割
   * @param code    6位的验证码
   * @param bizName 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   * @return 发送结果
   */
  public SmsSendResponse sendCodeCall(String phones, String code, String bizName) {
    return sendCodeCall(new SmsSendRequest(Arrays.asList(phones.split(",")), code, bizName));
  }

  /**
   * 发送国际短信
   *
   * @param phones       国内手机号，多个用逗号分割
   * @param code         6位的验证码
   * @param bizName      调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   * @param enterpriseId EI，如果有多个用逗号拼接（比如登录时还不确定租户，一个手机可能对应多个EI），根据EI选择定制渠道，不指定时尝试从Header中获取，获取不到使用纷享默认的渠道
   * @return 发送结果
   */
  public SmsSendResponse sendCodeCall(String phones, String code, String bizName, String enterpriseId) {
    return sendCodeCall(new SmsSendRequest(Arrays.asList(phones.split(",")), code, bizName, enterpriseId));
  }

  /**
   * 发送语音电话
   *
   * @param request 短信请求体
   * @return 发送结果
   */
  public SmsSendResponse sendTTS(TTSSendRequest request) {
    return sendToEgressApiService(request, egressSmsServiceUrl + "/api/v2/sms/tts");
  }

  private SmsSendResponse sendToEgressApiService(Object req, String url) {
    try {
      RequestBody body = RequestBody.create(JSON.toJSONString(req), MEDIA_TYPE_JSON);
      Request request = new Request.Builder().url(url).post(body).build();
      EgressApiResponse<SmsSendResponse> rs = okHttpSupport.parseObject(request,
        new TypeReference<EgressApiResponse<SmsSendResponse>>() {
        });
      return rs.getData();
    } catch (Exception e) {
      log.warn("send sms failed.", e);
      SmsSendResponse error = new SmsSendResponse();
      error.setSuccess(false);
      error.setMessage("server error.");
      return error;
    }
  }

  @Override
  public void afterPropertiesSet() {
    ConfigFactory.getConfig(VARIABLES_ENDPOINT, config -> egressSmsServiceUrl = config.get(EGRESS_API_SMS_URL_KEY));
    log.debug("egress sms service url:{}", egressSmsServiceUrl);
    Assert.hasText(egressSmsServiceUrl, "variables_endpoint " + EGRESS_API_SMS_URL_KEY + " not exists");
  }
}
