package com.sharecrm.egress.sdk;

/**
 * SDK对外开放常量定义，注意此处因为对外开放，不可随意变更
 */
public class SmsSdkConstants {

  private SmsSdkConstants() {
  }

  /**
   * 短信签名语言，中文
   */
  public static final String LANGUAGE_ZH = "zh-CN";

  /**
   * 短信签名语言，英文
   */
  public static final String LANGUAGE_EN = "en";

  /**
   * 短信模板类型：验证码
   */
  public static final String TEMPLATE_TYPE_VERIFY_CODE = "VERIFY_CODE";

  /**
   * 短信模板类型：短信通知
   */
  public static final String TEMPLATE_TYPE_NOTIFICATION = "NOTIFICATION";

  /**
   * 短信模板类型：推广短信
   */
  public static final String TEMPLATE_TYPE_PROMOTION = "PROMOTION";

  /**
   * 短信模板状态：刚初始化，还未获得供应商回复结果
   */
  public static final String TEMPLATE_STATUS_INIT = "NO_REPLY";

  /**
   * 短信模板状态：审核中
   */
  public static final String TEMPLATE_STATUS_APPROVING = "APPROVING";

  /**
   * 短信模板状态：审核通过
   */
  public static final String TEMPLATE_STATUS_APPROVED = "APPROVED";

  /**
   * 短信模板状态：审核未通过或审核失败
   */
  public static final String TEMPLATE_STATUS_REJECTED = "REJECTED";

  /**
   * 短信模板状态：取消审核
   */
  public static final String TEMPLATE_STATUS_CANCELED = "CANCELED";

  /**
   * 短信模板状态：已删除
   */
  public static final String TEMPLATE_STATUS_DELETED = "DELETED";

  /**
   * 接收短信和模板状态变更的 Rocket MQ Topic，注意和配置中心保持一致
   */
  public static final String MQ_TOPIC_SMS_TEXT = "fs-sms-text";
  /**
   * Rocket MQ Tag标记： 短信状态事件，第一次发送短信或者有短信状态变更时发出
   */
  public static final String MQ_TAG_SMS_STATUS = "fs-sms-status";

  /**
   * Rocket MQ Tag标记： 模板状态事件，第一次初始化模板和模板状态更新时发出
   */
  public static final String MQ_TAG_TEMPLATE_STATUS = "fs-template-status";

  /**
   * TTS模板ID：语音验证码
   */
  public static final int TTS_ID_VERIFY_CODE = 300;

  /**
   * TTS作为一种独立的短信类型，不与其他类型合并
   */
  public static final String SMS_TYPE_TTS = "tts";

  /**
   * 短信运营商类型标识：移动梦网
   */
  public static final String SMS_PROVIDER_TYPE_MONTERNET = "monternet";

  /**
   * 短信运营商类型标识：标准阿里云
   */
  public static final String SMS_PROVIDER_TYPE_ALIYUN = "aliyun";
  
  /**
   * 短信运营商类型标识：byteplus
   */
  public static final String SMS_PROVIDER_TYPE_BYTE_PLUS = "byteplus";

  /**
   * 短信运营商类型标识：标准华为云
   */
  public static final String SMS_PROVIDER_TYPE_HUAWEI = "huaweiyun";

  /**
   * 短信运营商类型标识：标准腾讯云
   */
  public static final String SMS_PROVIDER_TYPE_TENCENT = "tencent";

  /**
   * 短信运营商类型标识：蒙牛自有短信平台
   */
  public static final String SMS_PROVIDER_TYPE_MENGNIU = "mengniu";

}
