package com.sharecrm.egress.sdk.config;

import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.sdk.service.SmsApiService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;

/**
 * 不加注解，业务自行注入此Bean
 */
public class EgressApiSdkConfig {

  @Bean("egressApiHttpClient")
  public HttpSupportFactoryBean egressApiHttpClient() {
    return new HttpSupportFactoryBean();
  }

  @Bean("smsApiService")
  public SmsApiService smsApiService(@Qualifier("egressApiHttpClient") OkHttpSupport egressApiHttpClient) {
    return new SmsApiService(egressApiHttpClient);
  }

}
