package com.sharecrm.egress.sdk.entity;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 短信模板详情
 */
@Data
@NoArgsConstructor
public class SmsTemplateDetail {

  /**
   * 模板ID，根据此ID发送短信、回查状态
   */
  private String templateId;

  /**
   * 模板名称
   */
  private String name;

  /**
   * 内容
   */
  private String content;

  /**
   * 说明
   */
  private String remark;

  /**
   * 指定专属EI，根据EI选择定制渠道
   */
  private Integer enterpriseId;

  /**
   * 可以指定允许哪些EI访问，使用Gray规则表达式，如果不指定默认只能当前EI使用
   */
  private String allowAccounts;

  /**
   * 短信类型：验证码,短信通知,推广短信。
   */
  private String templateType;

  /**
   * 是否国际/港澳台短信, true为国际短信，false为国内短信
   */
  private boolean international;

  /**
   * 模板对应的供应商 ID
   */
  private String providerId;
  
  /**
   * 模板对应的供应商名字
   */
  private String providerName;

  /**
   * 审核状态
   */
  private String status;

  /**
   * 审核意见，如果失败包含失败原因
   */
  private String reply;

}
