# egress-api-service


## OCR 文字识别服务

```bash
# 本地测试， 命令行将图片转为base64
# base64 -i bs-card.png > bs-card-base64.txt

DATA=$(base64 -i "bs-card-0618.jpg")
echo "{ \"imageBase64\": \"$DATA\" }" | curl -X POST -H "Content-Type: application/json" -H "X-fs-User-Info: 90325.1001" -d @- http://************:13020/egress-api-service/api/v2/ocr/business-cards

# ASR 语音识别(firstshare)
curl --request POST -H "Content-Type: application/json" --data-binary "@16k16bit.wav" http://172.31.100.247:13020/egress-api-service/api/v1/asr/sentence-recognition

```

## 短链服务

短链服务提供长链接和短链接相互转换的功能。

注意： egress-api-service虽然各个云都部署了，但是在专属云不启用短链服务，因为短链只有一个域名(fs80.cn)只有一套数据库，业务调用时仍然需要跨云调用。


## 消息推送服务说明

实现苹果、安卓的消息推送，核心代码在 `com.sharecrm.egress.push`包下。

### 与终端的数据格式约定（Android, IOS8+平台的PushKit)

```mongodb-json-query
 {
   "isRing": 1,                 // num | 是否开启铃声 | 0：关闭，1：开启
   "soundName":"default",       // str | 铃声文件名
   "isVibrate": 1,              // num | 是否震动    | 0：关闭，1：开启
   "unreadNumber": 5,           // num | 未读数
   "enterpriseAccount": "fs",   // str | 接收人所属企业账号
   "employeeId": 1000,          // num | 接收人员工id
   "title":"纷享销客",            // str | 消息标题
   "summary": "文本内容",         // str | 最长20个汉字
   "separator":":",              // str | title与summary拼接分隔符
   "type": 1,                   // num | 消息类型 | 1：企信，2：音视频
   "data": {                    // obj-json |  自定义业务数据
     ...
   },
   "reportEnable": true,           // bool | 是否埋点上报
   "pushMessageId": "messageid",   // str | 推送消息关联的id
   
   "regionId": "regionId",        // str | 推送归属区域Id，用于聚合,如会话id
   "regionTitle": "北研",         // str | 推送归属区域名称,如会话名称
   "regionUnreadNumber": 3       // num | 推送归属区域未读数，如会话的未读数
 }   
```

### 修改PNSToken的几种情况

1. 新登录用户
   全新token添加，其他信息添加
2. 相同设备，用户变更
   token不变，activeSessionId变更，用户信息变更
3. 用户退出登录
   更具用户activeSessionId 和用户信息 删除
4. 同一用户，推送服务变化，导致token改变
   相同activeSessionId，变更token

```
   更新策略：
    根据token+platform进行更新，并同时删除同一activeSessionId下，具有不同token的项
```

### PNSToken字段定义

= == == = IOS的PNSToken的pushServiceSource字段取值 == =

```console
1，APPLE_PROD ：  生产环境apns的token
2，APPLE_ENT     ：  内测环境apns的token
3，APPLE_PUSHKIT_PROD   ：  生产环境pushkit的token
4，APPLE_PUSHKIT_ENT       ：   内测环境pushkit的token
```

= = = = == = IOS8+平台的pushkit推送的消息格式 = = = =

```console
{
"isRing": 1,                  // num | 是否开启铃声
soundName:"default",        // str | 铃声文件名
"unreadNumber": 5,           // num | 未读数
"enterpriseAccount": "fs",   // str | 接收人所属企业账号
"employeeId": 1000,          // num | 接收人员工id
"title": "纷享销客"           // str | 消息标题
"summary": "文本内容"         // str  | 最长20个汉字
"type": message-type,       // num | 消息类型 | 1：企信，2：音视频
"data": {                   // obj-json |  自定义业务数据
...
},
“reportEnable": true,                  //  bool | 是否埋点上报
"pushMessageId": "messageid"           // str | 推送消息关联的id
}
```

## 快速核对脚本

```bash

# 手机号查询
curl -X GET --location "http://localhost/egress-api-service/api/v2/mobile/batch-query?mobiles=***********%2C18511316416"

curl -X GET --location "http://localhost/egress-api-service/api/v2/ip/location?ip=*********"

# 短链
curl -X POST --location "http://localhost/egress-api-service/api/v2/private-short-urls" \
    -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{
          \"url\": \"https://www.bing.com/search?q=success\"
        }"
        
curl -X GET --location "http://localhost/egress-api-service/api/v2/public-short-urls/2lm0dv"
curl -X GET --location "http://localhost/egress-api-service/api/v2/private-short-urls/2lm0dv"

# 发送国内短信
curl -X POST --location "http://localhost/egress-api-service/api/v2/sms/internal" \
    -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{
          \"content\": \"Verification Code: 105008 (Do not tell others), valid for 15 mins , please complete the verification as soon as possible.\",
          \"bizName\": \"egress\",
          \"phones\": [
            \"***********\"
          ]
        }"

# 查询短信签名列表
curl 'http://localhost/egress-api-service/api/v2/sms/signs?id=fs-tester02-aliyun'

# 发送国际短信，手机号不可靠，去网上找手机号
curl -X POST --location "http://localhost/egress-api-service/api/v2/sms/intl" \
    -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{
          \"content\": \"Verification Code: 170028 (Do not tell others), valid for 15 mins , please complete the verification as soon as possible.\",
          \"bizName\": \"egress\",
          \"phones\": [
            \"004915510093716\"
          ]
        }"
 
curl -X POST --location "http://localhost/egress-api-service/api/v2/sms/code-call" \
    -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{
          \"content\": \"214567\",
          \"bizName\": \"egress\",
          \"enterpriseId\": \"90756\",
          \"phones\": [
            \"***********\"
          ]
        }"

curl -X POST --location "http://localhost/egress-api-service/api/v2/sms/tts" \
    -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{
          \"templateId\": 304,
          \"bizName\": \"egress\",
          \"enterpriseId\": \"90756\",
          \"templateParams\": [
            \"你的服务CPU利用率很高\"
          ],
          \"phones\": [
            \"***********\"
          ]
        }"
        
curl -X GET --location "http://************:13020/egress-api-service/api/v2/sms/status" 

curl -X POST --location "http://localhost/egress-api-service/api/v2/sms/histories" \
  -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{}"
        
# 发送邮件
curl -X POST --location "http://localhost/egress-api-service/api/v2/emails" \
    -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{
          \"to\": [
            \"<EMAIL>\"
          ],
          \"subject\": \"邮件中文Test\",
          \"content\": \"邮件详情，你写点啥吧，<h1>换行大标题</h1>\"
        }"
# geo
curl -X GET --location "http://localhost/egress-api-service/api/v2/geocode/batch-encode?addresses=%E5%B9%BF%E8%A5%BF%E6%A1%82%E6%9E%97%3B%E6%B1%9F%E8%8B%8F%E6%97%A0%E9%94%A1%E9%94%A1%E6%96%B0%E4%BA%8C%E8%B7%AF&cache=false"

curl -X POST --location "http://localhost/egress-api-service/api/v2/geocode/dec?address=%E5%B9%BF%E8%A5%BF%E6%A1%82%E6%9E%97"

curl -X GET --location "http://localhost/egress-api-service/api/v2/ip/find?ip=************"

# 根据纬经度查地址
curl -X GET --location "http://localhost/egress-api-service/api/v2/reverse-geocode/decode?latLng=21.869467%2C110.796600&cache=false"

# Google Translate

curl -X POST --location "http://localhost/egress-api-service/translates" \
    -H "content-type: application/json" \
    -H "Accept: application/json" \
    -d "{
          \"texts\": [\"<h1>这是我发的邮件,请查收，注意这个是HTML的格式</h1> <td>咖啡有点热，请注意烧嘴</td> 加油慢慢来。\",\"如果有问题给我打电话，这个电话要足够长否则无法校验最大值\"],
          \"target\": \"en\",
          \"format\": \"html\"
        }"

```
