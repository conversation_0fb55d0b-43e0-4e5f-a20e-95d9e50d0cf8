<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sharecrm.api</groupId>
    <artifactId>egress-api</artifactId>
    <version>3.0.0</version>
  </parent>

  <artifactId>egress-api-service</artifactId>
  <name>egress-api-service</name>
  <properties>
    <java.version>21</java.version>
    <maven.deploy.skip>true</maven.deploy.skip>
    <tencentcloud-sdk.version>3.1.977</tencentcloud-sdk.version>
    <grpc.version>1.62.2</grpc.version>
    <jjwt.version>0.12.6</jjwt.version>
    <!-- 纷享父pom为了兼容老服务版本比较低，这里主动提高版本，注意和Spring Boot版本对应  -->
    <elasticsearch.version>8.15.2</elasticsearch.version>
    <elasticsearch8.version>8.15.2</elasticsearch8.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>elasticsearch-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.fxiaoke.common</groupId>
          <artifactId>fs-rest-es-support</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!--使用gateway将在专属云内不支持的服务，代理到纷享  -->
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-gateway</artifactId>
    </dependency>
    <dependency>
      <!-- 为了 解决spring-cloud-starter-gateway的Bug  -->
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty</artifactId>
      <version>${grpc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-circuitbreaker-reactor-resilience4j</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>actuator-ext-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>redis-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>scheduler-lock-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis-spring</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.apache.tomcat.embed</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <!-- byte plus短信服务     -->
      <groupId>com.byteplus</groupId>
      <artifactId>byteplus-sdk-java</artifactId>
      <version>1.1.33</version>
    </dependency>

    <dependency>
      <groupId>com.tencentcloudapi</groupId>
      <artifactId>tencentcloud-sdk-java-sms</artifactId>
      <version>3.1.1043</version>
    </dependency>
    <dependency>
      <groupId>com.tencentcloudapi</groupId>
      <artifactId>tencentcloud-sdk-java-ocr</artifactId>
      <version>${tencentcloud-sdk.version}</version>
      <exclusions>
        <!-- 当前tencentcloud-sdk-java-sms的common版本高  -->
        <exclusion>
          <groupId>com.tencentcloudapi</groupId>
          <artifactId>tencentcloud-sdk-java-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.tencentcloudapi</groupId>
      <artifactId>tencentcloud-sdk-java-asr</artifactId>
      <version>${tencentcloud-sdk.version}</version>
      <exclusions>
        <!-- 当前tencentcloud-sdk-java-sms的common版本高  -->
        <exclusion>
          <groupId>com.tencentcloudapi</groupId>
          <artifactId>tencentcloud-sdk-java-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <!-- 注入OkHttpSupport   -->
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>http-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-dirty-words-support</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>metrics-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.ehcache</groupId>
      <artifactId>ehcache</artifactId>
      <version>3.10.8</version>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
      <version>3.33.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.spotbugs</groupId>
      <artifactId>spotbugs-annotations</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
      <version>2.7.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.yaml</groupId>
          <artifactId>snakeyaml</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!-- javadoc转成springdoc，详细参考springdoc说明   -->
      <groupId>com.github.therapi</groupId>
      <artifactId>therapi-runtime-javadoc</artifactId>
      <version>0.15.0</version>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.5.4</version>
    </dependency>
    <dependency>
      <groupId>com.aventrix.jnanoid</groupId>
      <artifactId>jnanoid</artifactId>
      <version>2.0.0</version>
    </dependency>
    
    <!-- begin for notify push service   -->
    <dependency>
      <groupId>com.facishare</groupId>
      <!-- 代码在https://git.firstshare.cn/Infrastructure/fs-online-manage -->
      <artifactId>fs-online-manage-common</artifactId>
      <version>1.1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-qixin-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.ow2.util.bundles</groupId>
          <artifactId>javassist-3.14.0-GA</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!-- 这个在推送服务，消息序列化的时候反射调用，去掉时谨慎，否则编译期不报错，运行时报错 -->
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-msg-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis-spring</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mongodb.morphia</groupId>
          <artifactId>morphia</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-qixin-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>mongo-spring-support</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.colin-lee</groupId>
          <artifactId>jedis-spring-support</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mongodb</groupId>
          <artifactId>*</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>*</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!-- 这个在推送服务，消息序列化的时候反射调用，去掉时谨慎，否则编译期不报错，运行时报错 -->
      <groupId>com.facishare</groupId>
      <artifactId>fs-qixin-api</artifactId>
      <version>0.1.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>org.mongodb</groupId>
          <artifactId>mongo-java-driver</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-dubbo-rest-plugin</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-oauth2-http</artifactId>
      <version>1.8.1</version>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-context</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- GeTui ThirdParty Notification Push -->
    <dependency>
      <groupId>com.gexin.platform</groupId>
      <artifactId>gexin-rp-fastjson</artifactId>
      <version>1.0.0.7</version>
    </dependency>
    <dependency>
      <groupId>com.gexin.platform</groupId>
      <artifactId>gexin-rp-sdk-base</artifactId>
      <version>4.0.0.37</version>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.gexin.platform</groupId>
      <artifactId>gexin-rp-sdk-http</artifactId>
      <version>4.1.1.4</version>
    </dependency>
    <dependency>
      <groupId>com.gexin.platform</groupId>
      <artifactId>gexin-rp-sdk-template</artifactId>
      <version>4.0.0.29</version>
    </dependency>

    <dependency>
      <groupId>com.eatthepath</groupId>
      <artifactId>pushy</artifactId>
      <version>0.15.4</version>
    </dependency>

    <!--xiaomi push sdk -->
    <dependency>
      <groupId>com.xiaomi.xmpush</groupId>
      <artifactId>server-sdk</artifactId>
      <version>2.2.18</version>
      <exclusions>
        <exclusion>
          <groupId>com.googlecode.json-simple</groupId>
          <artifactId>json-simple</artifactId>
        </exclusion>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mockito</groupId>
          <artifactId>mockito-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.googlecode.json-simple</groupId>
      <artifactId>json-simple</artifactId>
      <version>1.1.1</version>
      <exclusions>
        <exclusion>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 华为推送 -->
    <dependency>
      <groupId>com.huawei.developer</groupId>
      <artifactId>client-adapter-sdk-java-oauth2-json</artifactId>
      <version>0.3.12</version>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- jwt for harmony push  -->
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <version>${jjwt.version}</version>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <version>${jjwt.version}</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <version>${jjwt.version}</version>
      <scope>runtime</scope>
    </dependency>

    <!-- OPPO -->
    <dependency>
      <groupId>com.oppo</groupId>
      <!-- 这个是基于官方版本咱们自己编译的，支持http proxy -->
      <artifactId>opush-server-sdk-proxy</artifactId>
      <version>1.1.0-fs-RELEASE</version>
    </dependency>

    <!--httpclient, 版本太高会导致华为推送不可用,太低会导致oppo推送不可用 -->
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.netty</groupId>
      <!-- 这个必须和netty版本一一对应，不能高不能低 -->
      <!-- 引入这个是为了让ApnsClient使用OPENSSL，不使用JDK，JDK速度慢导致消息发送失败-->
      <!-- 是否生效请查看启动日志里是否有 ApnsClientBuilder Native SSL provider is available; will use native provider-->
      <artifactId>netty-tcnative-boringssl-static</artifactId>
      <scope>runtime</scope>
    </dependency>
    <!-- end for notify push service   -->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
      <exclusions>
        <exclusion>
          <!-- 与kafka版本冲突 -->
          <groupId>com.github.luben</groupId>
          <artifactId>zstd-jni</artifactId>
        </exclusion>
        <!-- grpc 与spring cloud gateway 冲突 -->
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.alibaba.csp</groupId>
      <artifactId>sentinel-spring-webflux-adapter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>cms-spring-cloud-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sharecrm.api</groupId>
      <artifactId>egress-api-sdk</artifactId>
      <version>${project.parent.version}</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.janino</groupId>
      <artifactId>janino</artifactId>
      <version>3.1.11</version>
    </dependency>

    <dependency>
      <!-- 为了兼容.net非常老的服务     -->
      <artifactId>fs-sms-api</artifactId>
      <groupId>com.facishare</groupId>
      <version>1.2.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.ow2.util.bundles</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 华为云OCR名片识别 -->
    <dependency>
      <groupId>com.huaweicloud.sdk</groupId>
      <artifactId>huaweicloud-sdk-ocr</artifactId>
      <version>3.1.146</version>
    </dependency>

    <dependency>
      <!-- 文本短信，请谨慎变更，注意和下面vmsApi兼容，否则都发不出去！-->
      <groupId>com.aliyun</groupId>
      <artifactId>dysmsapi20170525</artifactId>
      <version>3.0.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.jacoco</groupId>
          <artifactId>org.jacoco.agent</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!-- TTS语音     -->
      <groupId>com.aliyun</groupId>
      <artifactId>dyvmsapi20170525</artifactId>
      <version>3.2.1</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.16.3</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.12.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>1.23.0</version>
    </dependency>
    <dependency>
      <groupId>com.maxmind.geoip2</groupId>
      <artifactId>geoip2</artifactId>
      <version>4.0.1</version>
    </dependency>

    <!--   begin for sms service -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mongo-spring-support</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <!-- 用于国际电话号码判断和提取     -->
      <groupId>com.googlecode.libphonenumber</groupId>
      <artifactId>libphonenumber</artifactId>
      <version>9.0.8</version>
    </dependency>
    <!--   end for sms service -->

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-active-session-manage-api</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-eye-profiling-support</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.protostuff</groupId>
          <artifactId>*</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <!-- 互联用户身份认证 -->
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-enterpriserelation-rest-api2</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-indexer</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.vaadin.external.google</groupId>
          <artifactId>android-json</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <scope>runtime</scope>
      <optional>true</optional>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>JaCoCo Agent</id>
            <phase>test-compile</phase>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>JaCoCo Report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <artifactId>sonar-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <encoding>UTF-8</encoding>
          <!-- 增加-parameters让controller能够识别未明确名字的RequestParam -->
          <compilerArgument>-parameters</compilerArgument>
          <annotationProcessorPaths>
            <path>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-configuration-processor</artifactId>
              <version>${spring-boot.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <!-- javadoc转成springdoc，详细参考springdoc说明      -->
            <path>
              <groupId>com.github.therapi</groupId>
              <artifactId>therapi-runtime-javadoc-scribe</artifactId>
              <version>0.15.0</version>
            </path>
          </annotationProcessorPaths>
          <release>${java.version}</release>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
