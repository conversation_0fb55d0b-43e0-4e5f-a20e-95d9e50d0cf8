package com.sharecrm.egress.email;

import com.sharecrm.egress.entity.EmailInnerRequest;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.validator.routines.EmailValidator;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Stream;


@Slf4j
@Service
public class EmailService {

  private final ObjectProvider<EmailSender> senderObjectProvider;
  private final TaskExecutor executor;

  private static final EmailValidator validator = EmailValidator.getInstance();

  public EmailService(ObjectProvider<EmailSender> senderObjectProvider, @Qualifier("taskScheduler") TaskExecutor executor) {
    this.senderObjectProvider = senderObjectProvider;
    this.executor = executor;
  }

  public Mono<EmailSendResponse> sendEmail(EmailSendRequest request) {
    return Mono.just(request)
      .map(this::submitEmail);
  }

  public Mono<EmailSendResponse> sendInnerEmail(EmailInnerRequest request) {
    return Mono.just(request)
      .map(this::convertRequest)
      .map(this::submitEmail);
  }

  @NotNull
  private EmailSendRequest convertRequest(EmailInnerRequest e) {
    EmailSendRequest req = new EmailSendRequest();
    req.setTo(Arrays.stream(e.getTo().split(",")).toList());
    Optional.ofNullable(e.getCc())
      .filter(Predicate.not(String::isEmpty))
      .map(s -> s.split(","))
      .map(List::of)
      .ifPresent(req::setCc);
    req.setSubject(e.getSubject());
    req.setContent(e.getContent());
    return req;
  }

  private EmailSendResponse submitEmail(EmailSendRequest req) {
    if (hasBadEmailAddress(req)) {
      EmailSendResponse response = new EmailSendResponse();
      response.setSuccess(false);
      response.setMessage("request validate failed");
      return response;
    }
    return senderObjectProvider.stream()
      .findAny()
      .map(s -> asyncSend(s, req))
      .orElseGet(EmailService::noEmailProviderResponse);
  }

  private static EmailSendResponse noEmailProviderResponse() {
    EmailSendResponse response = new EmailSendResponse();
    response.setSuccess(false);
    response.setMessage("no email sender found");
    return response;
  }

  private EmailSendResponse asyncSend(EmailSender emailSender, EmailSendRequest req) {
    executor.execute(() -> emailSender.send(req));
    EmailSendResponse response = new EmailSendResponse();
    response.setSuccess(true);
    response.setMessage("accepted");
    return response;
  }

  private boolean hasBadEmailAddress(EmailSendRequest req) {
    return Stream.concat(req.getTo().stream(),
        Optional.ofNullable(req.getCc())
          .orElse(Collections.emptyList()).stream())
      .anyMatch(Predicate.not(validator::isValid));
  }
}
