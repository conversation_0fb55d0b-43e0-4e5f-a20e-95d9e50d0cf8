package com.sharecrm.egress.email;

import com.sharecrm.egress.config.ConditionalOnJavaMailEnabled;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import com.sharecrm.egress.utils.Constants;
import io.micrometer.core.annotation.Counted;
import io.micrometer.core.instrument.Metrics;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

@Slf4j
@Component
@RefreshScope
@ConditionalOnJavaMailEnabled
public class DefaultEmailSender implements EmailSender {

  private final JavaMailSender mailSender;
  private final MailProperties mailProperties;

  private static final EmailSendResponse SUCCESS = new EmailSendResponse();

  public DefaultEmailSender(JavaMailSender mailSender, MailProperties mailProperties) {
    this.mailSender = mailSender;
    this.mailProperties = mailProperties;
  }

  @Override
  @Counted(value = Constants.METRICS_EMAIL, extraTags = {"channel", "default"})
  public EmailSendResponse send(EmailSendRequest request) {
    try {
      MimeMessage message = mailSender.createMimeMessage();
      MimeMessageHelper helper = new MimeMessageHelper(message, true);
      helper.setFrom(mailProperties.getUsername());
      helper.setTo(request.getTo().toArray(new String[0]));
      Optional.ofNullable(request.getCc())
        .filter(Predicate.not(List::isEmpty))
        .ifPresent(ccs -> setCc(ccs, helper));
      helper.setSubject(request.getSubject());
      helper.setText(request.getContent(), true);
      mailSender.send(message);
      return SUCCESS;
    } catch (Exception e) {
      log.warn("send email to {} failed.", request.getTo(), e);
      Metrics.counter(Constants.METRICS_EMAIL, "channel", "default", "result", "failure").increment();
      EmailSendResponse response = new EmailSendResponse();
      response.setSuccess(false);
      response.setMessage("server error");
      return response;
    }
  }

  private void setCc(List<String> ccs, MimeMessageHelper helper) {
    try {
      helper.setCc(ccs.toArray(new String[0]));
    } catch (MessagingException e) {
      log.warn("email set cc failed.", e);
    }
  }
}
