package com.sharecrm.egress.geo;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.index.qual.NonNegative;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * GEO 数据缓存，Caffeine作为一级缓存，ES 作为二级缓存
 */
@Service
public class GeoCache {

  public static final LocationPoint EMPTY_LOCATION_POINT = new LocationPoint();
  public static final IpLocation EMPTY_IP_LOCATION = new IpLocation();
  public static final PointDistance EMPTY_POINT_DISTANCE = new PointDistance();
  public static final PoiResponse EMPTY_POI_RESPONSE = new PoiResponse();

  private Cache<String, LocationPoint> locationPointCache;
  private Cache<String, IpLocation> ipLocationCache;
  private Cache<String, List<PointDistance>> distanceCache;
  private Cache<String, GeoAddress> geoAddressCache;

  private Cache<String, GeoAddress> reverseGeoAddressCache;

  private Cache<DrivingRouteRequest, PointDistance> drivingCache;

  private Cache<PoiSearchRequest, PoiResponse> poiSearchCache;

  private final MapProperties properties;

  public GeoCache(MapProperties properties) {
    this.properties = properties;
    initCache();
  }

  private void initCache() {
    Duration empty = properties.getEmptyTimeout();
    int cacheMaximumSize = properties.getCacheMaximumSize();
    Duration timeout = properties.getCacheTimeout();

    this.locationPointCache = Caffeine.newBuilder()
      .maximumSize(cacheMaximumSize)
      .expireAfter(new EmptyExpiry<String, LocationPoint>(empty, properties.getLongCacheTimeout(), EMPTY_LOCATION_POINT::equals))
      .build();

    this.distanceCache = Caffeine.newBuilder().maximumSize(cacheMaximumSize)
      .expireAfter(new EmptyExpiry<String, List<PointDistance>>(empty, timeout, List::isEmpty))
      .build();

    this.drivingCache = Caffeine.newBuilder().maximumSize(cacheMaximumSize)
      .expireAfter(new EmptyExpiry<>(empty, timeout, EMPTY_POINT_DISTANCE::equals)).build();

    this.ipLocationCache = Caffeine.newBuilder()
      .maximumSize(cacheMaximumSize)
      .expireAfter(new EmptyExpiry<String, IpLocation>(empty, properties.getLongCacheTimeout(), EMPTY_IP_LOCATION::equals))
      .build();

    Function<GeoAddress, Boolean> emptyGeoAddress = e -> Objects.isNull(e) || StringUtils.isBlank(e.getGeocode());
    this.geoAddressCache = Caffeine.newBuilder()
      .maximumSize(cacheMaximumSize)
      .expireAfter(new EmptyExpiry<String, GeoAddress>(empty, properties.getLongCacheTimeout(), emptyGeoAddress))
      .build();

    this.poiSearchCache = Caffeine.newBuilder()
      .maximumSize(cacheMaximumSize)
      .expireAfter(new EmptyExpiry<PoiSearchRequest, PoiResponse>(empty, timeout, EMPTY_POI_RESPONSE::equals))
      .build();

    this.reverseGeoAddressCache = Caffeine.newBuilder()
      .maximumSize(cacheMaximumSize)
      .expireAfter(new EmptyExpiry<String, GeoAddress>(empty, properties.getLongCacheTimeout(), emptyGeoAddress))
      .build();

  }

  public List<PointDistance> getDistance(String key) {
    return distanceCache.getIfPresent(key);
  }

  public void putDistance(String key, List<PointDistance> distances) {
    distanceCache.put(key, distances);
  }

  /**
   * Get the value from cache.
   *
   * @param key the key
   * @return the value
   */
  public LocationPoint getLocationPoint(String key) {
    return locationPointCache.getIfPresent(key);
  }

  /**
   * Check if the value is empty.
   */
  public boolean isEmpty(LocationPoint loc) {
    return EMPTY_LOCATION_POINT.equals(loc);
  }

  /**
   * Put the value into cache.
   *
   * @param key   the key
   * @param value the value
   */
  public void putLocationPoint(String key, LocationPoint value) {
    locationPointCache.put(key, value);
  }

  public IpLocation getIpLocation(String ip, String language) {
    return ipLocationCache.getIfPresent(ipCacheKey(ip, language));
  }

  @NotNull
  private static String ipCacheKey(String ip, String language) {
    return Objects.toString(language, "") + ip;
  }

  public void putIpLocation(String ip, String language, IpLocation value) {
    ipLocationCache.put(ipCacheKey(ip, language), Objects.requireNonNullElse(value, EMPTY_IP_LOCATION));
  }

  public PointDistance getDriving(DrivingRouteRequest request) {
    return drivingCache.getIfPresent(request);
  }

  public void putDriving(DrivingRouteRequest key, PointDistance value) {
    drivingCache.put(key, value);
  }

  public GeoAddress getGeoAddress(String key) {
    return geoAddressCache.getIfPresent(key);
  }

  public void putGeoAddress(String key, GeoAddress value) {
    geoAddressCache.put(key, value);
  }

  public GeoAddress getReverseGeoAddress(String key) {
    return reverseGeoAddressCache.getIfPresent(key);
  }

  public void putReverseGeoAddress(String key, GeoAddress value) {
    reverseGeoAddressCache.put(key, value);
  }

  public PoiResponse getPoiResponse(PoiSearchRequest request) {
    return poiSearchCache.getIfPresent(request);
  }

  public void putPoiResponse(PoiSearchRequest key, PoiResponse value) {
    poiSearchCache.put(key, value);
  }

  private static class EmptyExpiry<K, V> implements Expiry<K, V> {

    private final Duration emptyTimeout;
    private final Duration cacheTimeout;
    private final Function<V, Boolean> isEmpty;

    private EmptyExpiry(Duration emptyTimeout, Duration cacheTimeout, Function<V, Boolean> isEmpty) {
      this.emptyTimeout = emptyTimeout;
      this.cacheTimeout = cacheTimeout;
      this.isEmpty = isEmpty;
    }

    @Override
    public long expireAfterCreate(K key, V value, long currentTime) {
      return isEmpty.apply(value) ? emptyTimeout.toNanos() : cacheTimeout.toNanos();
    }

    @Override
    public long expireAfterUpdate(K key, V value, long currentTime, @NonNegative long currentDuration) {
      return currentDuration;
    }

    @Override
    public long expireAfterRead(K key, V value, long currentTime, @NonNegative long currentDuration) {
      return currentDuration;
    }
  }

}
