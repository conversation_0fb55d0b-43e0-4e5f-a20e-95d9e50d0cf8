package com.sharecrm.egress.geo;

import com.sharecrm.egress.config.Weighted;
import com.sharecrm.egress.entity.CoordinateConvertRequest;
import com.sharecrm.egress.entity.CoordinateConvertResponse;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import reactor.core.publisher.Mono;

import java.util.List;

public interface GeoAdapter extends Weighted {

  /**
   * 距离计算服务
   */
  String SUPPORT_DISTANCE = "distance";

  /**
   * 驾车路线距离计算服务
   */
  String SUPPORT_DRIVING = "driving";
  String SUPPORT_LOCATION_POINT = "point";
  String SUPPORT_GEO_ADDRESS = "address";
  String SUPPORT_REVERSE_GEO = "reverse";
  /**
   * 支持海外逆编，目前只有高德支持，需要买高德海外服务
   */
  String SUPPORT_REVERSE_GEO_OVERSEAS = "reverse-overseas";
  String SUPPORT_POI_AROUND = "around";
  String SUPPORT_IP_LOCATION = "ip";
  /**
   * 坐标系转换
   */
  String SUPPORT_COORDINATE_CONVERT = "coordinate";

  /**
   * 支持Spring Cloud Gateway网关代理, rest api
   */
  String SUPPORT_GATEWAY_REST = "gateway-rest";

  /**
   * 支持Spring Cloud Gateway网关代理, web api 地图绘图组件专用
   */
  String SUPPORT_GATEWAY_WEB_API = "gateway-web";

  GeoProvider provider();

  /**
   * 支持哪些类型的服务，可能有些账户没有开通全部服务
   *
   * @return 支持能力集合
   */
  List<String> supports();

  /**
   * 经纬度转成地址
   */
  Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request);

  /**
   * 地址转经纬度
   */
  Mono<GeoAddress> queryGeoAddress(GeoEncodeRequest request);

  Mono<LocationPoint> queryLocationPoint(String address, String city);

  Mono<List<PointDistance>> queryDistance(List<LocationPoint> origins, List<LocationPoint> destinations);

  Mono<PointDistance> queryDriving(DrivingRouteRequest request);

  Mono<PoiResponse> queryPoiAround(PoiSearchRequest request);

  Mono<IpLocation> queryIpLocation(String ip, String language);

  default Mono<CoordinateConvertResponse> coordinateConvert(CoordinateConvertRequest request) {
    return Mono.empty();
  }
}
