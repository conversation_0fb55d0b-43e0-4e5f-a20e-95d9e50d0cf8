package com.sharecrm.egress.geo;

import com.sharecrm.egress.config.ConditionalMaxmindMapEnabled;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.exception.EgressAppException;
import com.sharecrm.egress.service.GeoLite2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

/**
 * 使用maxmind的免费ip地址库查询位置信息
 *
 * @see <a href="https://www.maxmind.com/en/accounts/399083/geoip/downloads">下载地址</a>
 */
@Slf4j
@Service
@ConditionalMaxmindMapEnabled
public class MaxmindGeoService implements GeoAdapter, GeoBeanIgnoreDestroy {

  private final GeoLite2Service geoLite2Service;

  public MaxmindGeoService(GeoLite2Service geoLite2Service) {
    this.geoLite2Service = geoLite2Service;
  }

  @Override
  public GeoProvider provider() {
    return geoLite2Service.provider();
  }

  @Override
  public List<String> supports() {
    return List.of(GeoAdapter.SUPPORT_IP_LOCATION);
  }

  @Override
  public Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request) {
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<GeoAddress> queryGeoAddress(GeoEncodeRequest request) {
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<LocationPoint> queryLocationPoint(String address, String city) {
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<List<PointDistance>> queryDistance(List<LocationPoint> origins, List<LocationPoint> destinations) {
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<PointDistance> queryDriving(DrivingRouteRequest request) {
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<PoiResponse> queryPoiAround(PoiSearchRequest request) {
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<IpLocation> queryIpLocation(String ip, String language) {
    return geoLite2Service.query(ip, Objects.toString(language, "zh-CN"))
      .map(e -> {
        e.setProvider(provider().getType());
        e.setLanguage(Objects.toString(language, "zh-CN"));
        return e;
      });
  }

  @Override
  public int getWeight() {
    return 1;
  }
}
