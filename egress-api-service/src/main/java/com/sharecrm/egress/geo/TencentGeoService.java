package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.TencentErrorCode;
import com.sharecrm.egress.api.TencentMapApi;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.entity.TencentAdInfo;
import com.sharecrm.egress.entity.TencentAddressDetail;
import com.sharecrm.egress.entity.TencentDistanceResultData;
import com.sharecrm.egress.entity.TencentGeoAddress;
import com.sharecrm.egress.entity.TencentGeoLocation;
import com.sharecrm.egress.entity.TencentGeoResult;
import com.sharecrm.egress.entity.TencentPoi;
import com.sharecrm.egress.entity.TencentRoute;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Tencent GEO服务接口
 */
@Slf4j
public class TencentGeoService implements GeoAdapter {

  private final TencentMapApi tencentApi;
  private final MapProperties.TencentConfig config;

  public TencentGeoService(TencentMapApi tencentApi, MapProperties.TencentConfig config) {
    this.tencentApi = tencentApi;
    this.config = config;
  }

  @Override
  public Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request) {
    LocationPoint point = new LocationPoint(request.getLongitude(), request.getLatitude());
    // 腾讯地图API的经纬度参数顺序为：纬度,经度
    String location = point.toLatitudeLongitude();
    String language = MapUtils.tencentLanguage(request.getLanguage());
    Integer radius = Objects.requireNonNullElse(request.getRadius(), config.getRadius());
    String options = "radius=%d".formatted(radius);
    return tencentApi.reverseGeoCode(location, language, options).mapNotNull(res -> {
      int status = res.getStatus();
      TencentGeoAddress result = res.getResult();
      if (status == 0 && result != null) {
        GeoAddress address = new GeoAddress();
        address.setGeocode(point.toLatitudeLongitude());
        address.setAddress(result.getAddress());
        Optional.ofNullable(result.getFormattedAddress())
          .ifPresent(format -> address.setAddress(StringUtils.defaultIfEmpty(format.getRecommend(), format.getRough())));
        TencentAddressDetail detail = result.getAddressComponent();
        if (detail != null) {
          address.setPois(Optional.ofNullable(result.getPois())
            .orElse(List.of())
            .stream()
            .map(this::tencentMapPoi)
            .toList());
          address.setCountry(detail.getCountry());
          address.setProvince(detail.getProvince());
          address.setCity(detail.getCity());
          address.setDistrict(detail.getDistrict());
          address.setStreet(detail.getStreet());
          address.setStreetNumber(detail.getStreetNumber());
          Optional.ofNullable(result.getAdInfo())
            .ifPresent(adInfo -> {
              address.setAdcode(adInfo.getAdcode());
              address.setCityCode(address.getCityCode());
            });
          Optional
            .ofNullable(result.getAddressReference())
            .flatMap(ref -> Optional.ofNullable(ref.getTown()))
            .ifPresent(town -> {
              address.setTown(town.getTitle());
              address.setTownCode(town.getId());
            });
          log.info("腾讯地图API查询地址成功，location={}, address={}", location, address.getAddress());
          return address;
        }
      } else {
        log.warn("腾讯地图API查询地址失败，status={}, message={}, location={}", status, TencentErrorCode.getMessage(status), location);
        tryLock(status, SUPPORT_REVERSE_GEO);
      }
      return null;
    });
  }

  private void tryLock(int status, String... api) {
    if (status == 121) {
      for (String s : api) {
        GeoProviderLock.lock(config.getId(), s);
      }
    }
  }

  @Override
  public Mono<GeoAddress> queryGeoAddress(GeoEncodeRequest request) {
    String address = request.getAddress();
    String city = request.getCity();
    String language = MapUtils.tencentLanguage(request.getLanguage());
    //实际上腾讯目前只支持中文语言
    return tencentApi.geoCode(address, city, language).mapNotNull(res -> {
      int status = res.getStatus();
      if (status == 0) {
        return tencentGeoLocationToGeoAddress(address, res);
      } else {
        String message = TencentErrorCode.getMessage(status);
        log.warn("腾讯GeoCode解析失败, city: {}, address: {}, status: {}, message: {}", city, address, status, message);
        tryLock(status, SUPPORT_GEO_ADDRESS, SUPPORT_LOCATION_POINT);
        return null;
      }
    });
  }

  @NotNull
  private GeoAddress tencentGeoLocationToGeoAddress(String address, TencentGeoResult res) {
    TencentGeoLocation rs = res.getResult();
    LocationPoint location = rs.getLocation();
    TencentAddressDetail geo = rs.getDetail();
    log.debug("腾讯GeoCode解析成功, detail: {}, address: {}, location: ({})", geo, address, location);
    GeoAddress result = new GeoAddress();
    result.setCountry(geo.getCountry());
    result.setAddress(address);
    result.setGeocode(location.toLatitudeLongitude());
    result.setProvince(geo.getProvince());
    result.setCity(geo.getCity());
    result.setDistrict(geo.getDistrict());
    result.setStreet(geo.getStreet());
    result.setStreetNumber(geo.getStreetNumber());
    result.setAdcode(geo.getAdcode());
    return result;
  }


  @Override
  public Mono<LocationPoint> queryLocationPoint(String address, String city) {
    return queryGeoAddress(new GeoEncodeRequest(address, city))
      .map(e -> LocationPoint.parseLatitudeLongitude(e.getGeocode()));
  }

  @Override
  public Mono<List<PointDistance>> queryDistance(List<LocationPoint> origins, List<LocationPoint> destinations) {
    String from = origins.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining(";"));
    String to = destinations.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining(";"));
    return tencentApi.distance(from, to).mapNotNull(res -> {
      int status = res.getStatus();
      String message = res.getMessage();
      TencentDistanceResultData result = res.getResult();
      if (status == 0 && result != null) {
        log.info("腾讯地图API查询成功, from={}, to={}, distance={}", from, to, result);
        List<PointDistance> values = new ArrayList<>();
        result.getRows().forEach(r -> values.addAll(r.getElements()));
        return values;
      }
      log.warn("腾讯地图API查询失败, from={}, to={}, status={}, message={}", from, to, status, message);
      tryLock(status, SUPPORT_DISTANCE);
      return null;
    });
  }

  @Override
  public Mono<PointDistance> queryDriving(DrivingRouteRequest request) {
    String from = request.getFrom().toLatitudeLongitude();
    String to = request.getTo().toLatitudeLongitude();
    String waypoints = null;
    List<LocationPoint> points = request.getWaypoints();
    if (points != null && !points.isEmpty()) {
      waypoints = points.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining(";"));
    }
    return tencentApi.driving(from, to, waypoints).mapNotNull(res -> {
      int status = res.getStatus();
      TencentRoute result = res.getResult();
      if (status == 0 && result != null) {
        List<PointDistance> results = result.getRoutes();
        log.info("腾讯地图API驾车规划成功，status={}, message={}, request={}, results={}", status, res.getMessage(), request, results);
        if (!results.isEmpty()) {
          PointDistance distance = results.getFirst();
          // 腾讯地图返回的是分钟数，而其他返回的是秒，这里保持统一
          distance.setDuration(distance.getDuration() * 60);
          return distance;
        }
      }
      String message = Optional.ofNullable(res.getMessage()).orElseGet(() -> TencentErrorCode.getMessage(status));
      log.warn("腾讯地图API驾车规划失败，status={}, message={}, request={}", status, message, request);
      tryLock(status, SUPPORT_DRIVING);
      return null;
    });
  }

  @Override
  public Mono<PoiResponse> queryPoiAround(PoiSearchRequest request) {
    LocationPoint loc = new LocationPoint(request.getLongitude(), request.getLatitude());
    int radius = request.getRadius();
    String keywords = MapUtils.join(" ", request.getKeywords());
    int pageSize = request.getPageSize();
    int pageNum = request.getPageNum();
    String boundary = String.format("nearby(%.6f,%.6f,%d)", loc.getLatitude(), loc.getLongitude(), radius);
    String types = MapUtils.join(",", request.getTypes());
    String filter = types != null && !types.isEmpty() ? "category=" + types : null;
    String language = MapUtils.tencentLanguage(request.getLanguage());
    return tencentApi.poiAround(keywords, boundary, filter, pageSize, pageNum, language).mapNotNull(res -> {
      int status = res.getStatus();
      int count = res.getCount();
      List<TencentPoi> data = res.getData();
      if (status == 0 && count > 0 && data != null) {
        List<MapPoi> results = data.stream().map(this::tencentMapPoi).toList();
        log.info("腾讯地图API查询POI成功，status={}, message={}, location={}, count={}", status, res.getMessage(), loc, count);
        return PoiResponse.builder().count(count).results(results).build();
      }
      String message = Optional.ofNullable(res.getMessage()).orElseGet(() -> TencentErrorCode.getMessage(status));
      log.warn("腾讯地图API查询POI失败，status={}, message={}, location={}, rs:{}", status, message, loc, res);
      tryLock(status, SUPPORT_POI_AROUND);
      return null;
    });
  }

  private MapPoi tencentMapPoi(TencentPoi poi) {
    MapPoi.MapPoiBuilder builder = MapPoi.builder();
    builder
      .provider(Constants.MAP_PROVIDER_TENCENT)
      .id(poi.getId())
      .name(poi.getName())
      .location(poi.getLocation())
      .address(poi.getAddress())
      .distance(poi.getDistance())
      .type(poi.getType())
      .telephone(poi.getTelephone());
    TencentAdInfo info = poi.getAdInfo();
    if (info != null) {
      builder.province(info.getProvince()).city(info.getCity()).district(info.getDistrict()).adcode(info.getAdcode());
    }
    return builder.build();
  }

  @Override
  public Mono<IpLocation> queryIpLocation(String ip, String language) {
    //这里的language运营商还不支持
    return tencentApi.ipLocation(ip, MapUtils.tencentLanguage(language)).mapNotNull(res -> {
      int status = res.getStatus();
      if (status == 0 && res.getContent() != null && res.getContent().getAddressDetail() != null) {
        TencentAddressDetail detail = res.getContent().getAddressDetail();
        String country = detail.getCountry();
        String province = detail.getProvince();
        String city = detail.getCity();
        log.debug("Tencent IP定位成功, IP: {}, country: {}, province: {}, city: {}", ip, country, province, city);
        return IpLocation.builder()
          .country(country)
          .ip(ip)
          .province(province)
          .city(city)
          .provider(provider().getType())
          .language(language)
          .build();
      } else {
        log.warn("Tencent IP定位失败, IP: {},  status: {}, message: {}", ip, status, TencentErrorCode.getMessage(status));
        tryLock(status, SUPPORT_IP_LOCATION);
        return null;
      }
    });
  }

  @Override
  public GeoProvider provider() {
    return config;
  }

  @Override
  public List<String> supports() {
    return config.getSupports();
  }

  @Override
  public int getWeight() {
    return config.getWeight();
  }
}
