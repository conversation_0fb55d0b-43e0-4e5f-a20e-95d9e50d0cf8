package com.sharecrm.egress.geo;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.DistanceUnit;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.GeoLocation;
import co.elastic.clients.elasticsearch._types.LatLonGeoLocation;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.GeoDistanceQuery;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.core.DeleteByQueryRequest;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.sharecrm.egress.config.FluxTrace;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoAddressEsCache;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.IpLocationEsCache;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.utils.AmapPoiUtils;
import com.sharecrm.egress.utils.MapUtils;
import com.sharecrm.egress.utils.SingleExecutorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_IP_LOCATION;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_REVERSE_GEO;
import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_GOOGLE;

/**
 * 使用ES作为数据缓存
 * 参考链接：<a href="https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-geo-distance-query.html#geo-distance-query-ex">...</a>
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Service
public class GeoEsCacheService {

  private static final long DAYS_MILLIS = TimeUnit.DAYS.toMillis(1);

  private final ElasticsearchClient client;
  private final MapProperties properties;

  public GeoEsCacheService(@Autowired(required = false) ElasticsearchClient client, MapProperties properties) {
    this.client = client;
    this.properties = properties;
  }

  @Nullable
  public IpLocation queryIpLocation(String ip, String language, String provider) {
    if (Objects.isNull(client) || !config().isIpCacheReadEnabled()) {
      log.debug("ip location es cache is disabled");
      return null;
    }
    log.debug("query es ip location cache, ip:{}, language:{}, provider:{}", ip, language, provider);
    try {
      // 组合多条件查询
      BoolQuery.Builder query = new BoolQuery.Builder();

      query.must(q -> q.match(m -> m.field("ip").query(ip)));

      if (StringUtils.isNotBlank(provider)) {
        query.must(q -> q.match(m -> m.field("provider").query(provider)));
      }

      if (StringUtils.isNotBlank(language)) {
        query.must(q -> q.match(m -> m.field("language").query(language)));
      }
      SearchRequest searchRequest = new SearchRequest.Builder()
        .index(config().getIpIndex())
        .query(q -> q.bool(query.build()))
        .build();

      SearchResponse<IpLocationEsCache> response = client.search(searchRequest, IpLocationEsCache.class);

      List<Hit<IpLocationEsCache>> hits = response.hits().hits();
      if (CollectionUtils.isEmpty(hits)) {
        log.debug("query es ip location cache, hits list is empty.");
        saveBizLog(null, ip, SUPPORT_IP_LOCATION, "failure");
        return null;
      }
      saveBizLog(null, ip, SUPPORT_IP_LOCATION, "success");
      IpLocationEsCache source = hits.stream()
        .max((o1, o2) -> Math.toIntExact(ipHitWeight(o1) - ipHitWeight(o2)))
        .map(Hit::source)
        .orElseThrow();
      //这两个字段不对外开放
      source.setCreateTime(null);
      source.setUpdateTime(null);
      return source;
    } catch (Exception e) {
      saveBizLog(null, ip, SUPPORT_IP_LOCATION, "failure");
      log.warn("query es ip location cache failed.", e);
      return null;
    }

  }

  @Nullable
  public void cacheIpLocation(IpLocation ipLocation) {
    if (Objects.isNull(client) || !config().isIpCacheWriteEnabled()) {
      log.debug("ip es cache is disabled");
      return;
    }
    //如果国家这个级别都没有获取到，认为获取的数据不全，不缓存
    String country = ipLocation.getCountry();
    if (StringUtils.isBlank(country)) {
      log.info("IpLocation country is empty, do not cache.");
      return;
    }

    //对于国内地址，如果没有city也不缓存（国外地址几乎都无法获取city）
    if (MapUtils.isChina(country) && StringUtils.isBlank(ipLocation.getCity())) {
      log.info("IpLocation city is empty, do not cache :{}.", ipLocation);
      return;
    }

    try {
      IpLocationEsCache ip = new IpLocationEsCache();
      long now = System.currentTimeMillis();
      ip.setCreateTime(now);
      ip.setUpdateTime(now);
      BeanUtils.copyProperties(ipLocation, ip);
      BulkRequest build = new BulkRequest.Builder()
        .index(config().getIpIndex())
        .operations(op -> op.index(idx -> idx.document(ip)))
        .build();
      BulkResponse response = client.bulk(build);
      log.debug("add ip location cache value:{}, response: {}", ipLocation, response);
    } catch (Exception e) {
      log.error("add es ip location failed.", e);
    }

  }

  @Nullable
  public GeoAddressEsCache queryReverseGeoAddress(ReverseGeoRequest request, FluxTrace trace, String provider) {
    if (Objects.isNull(client)) {
      log.debug("es cache is disabled");
      return null;
    }
    if (MAP_PROVIDER_GOOGLE.equals(provider)) {
      // 我们的Google缓存数据太少，不要用缓存
      log.debug("google es cache is disabled");
      return null;
    }
    try {
      // 组合多条件查询
      BoolQuery.Builder query = new BoolQuery.Builder();
      // 在 ES 里 distance 必须大于0，但是我们的api允许为0
      int distance = Math.max(1, Objects.requireNonNullElse(request.getRadius(), 1));
      GeoLocation location = new GeoLocation.Builder()
        .latlon(new LatLonGeoLocation.Builder()
          .lat(request.getLatitude())
          .lon(request.getLongitude())
          .build())
        .build();
      GeoDistanceQuery distanceQuery = GeoDistanceQuery.of(g -> g
        .field("geocode")
        //单位是米
        .distance(distance + "m")
        .location(location));

      query.filter(q -> q.geoDistance(distanceQuery));

      if (StringUtils.isNotBlank(provider)) {
        query.filter(q -> q.match(m -> m.field("provider").query(provider)));
      }

      if (StringUtils.isNotBlank(request.getLanguage())) {
        query.filter(q -> q.match(m -> m.field("language").query(request.getLanguage())));
      }
      List<FieldValue> pois = poiFilters(request);
      if (CollectionUtils.isNotEmpty(pois)) {
        //多个type之间使用terms实现或的关系
        query.filter(q -> q.terms(t -> t
          .field("poiCode")
          .terms(f -> f.value(pois))
        ));
      }

      //之前版本cache的时候没有name，过滤掉，以后等所有cache都统一了，可以去掉此条件
      query.filter(q -> q.exists(m -> m.field("sourceName")));

      //老数据没有缓存cityCode，后来才缓存的
      if (request.isRequireCityCode()) {
        query.filter(q -> q.exists(m -> m.field("cityCode")));
      }

      SearchRequest searchRequest = new SearchRequest.Builder()
        .index(indexName())
        .query(q -> q.bool(query.build()))
        .sort(st -> st.geoDistance(s -> s.field("geocode")
          .location(location)
          .order(SortOrder.Asc)
          //unit决定了hits中 sort value 值单位，注意保证一致
          .unit(DistanceUnit.Meters)))
        .size(config().getPageSize())
        .build();

      SearchResponse<GeoAddressEsCache> response = client.search(searchRequest, GeoAddressEsCache.class);

      List<Hit<GeoAddressEsCache>> hits = response.hits().hits();
      if (CollectionUtils.isEmpty(hits)) {
        log.info("query es geo cache, hits is empty. request:{}", request);
        saveBizLog(trace, request, SUPPORT_REVERSE_GEO, "failure");
        return null;
      }
      //按距离排序，距离最近的一条作为主数据返回
      GeoAddressEsCache first = hits.getFirst().source();
      if (Objects.isNull(first)) {
        log.info("query es geo cache first hit is null.");
        return null;
      }
      //多条数据组合成POI数据
      first.setPois(hits.stream()
        .map(this::convertMapPoi)
        .filter(Objects::nonNull)
        .filter(e -> Objects.nonNull(e.getLocation()))
        .filter(e -> StringUtils.isNotBlank(e.getName()))
        // 按多个属性去重
        .collect(distinctCollector()));

      //如果命中的数据量不够多，也不用
      int size = first.getPois().size();
      int min = config().getMinAddressHits() + distance / config().getMinAddressModulo();
      if (size <= min) {
        log.info("query es geo cache, hits size {} is shorter than min {}. es cache:{}, filter rs:{}", size, min, hits.size(), first);
        saveBizLog(trace, request, SUPPORT_REVERSE_GEO, "failure");
        return null;
      }
      saveBizLog(trace, request, SUPPORT_REVERSE_GEO, "success");
      log.info("matched es geo cache, request:{}, pois size:{},response: {}", request, size, first);
      return first;
    } catch (Exception e) {
      saveBizLog(trace, request, SUPPORT_REVERSE_GEO, "failure");
      log.warn("query es geo cache failed.", e);
      return null;
    }
  }

  @NotNull
  private List<FieldValue> poiFilters(ReverseGeoRequest request) {
    return Optional.ofNullable(request.getPoiTypes())
      .orElseGet(List::of)
      .stream()
      .filter(StringUtils::isNotBlank)
      .map(FieldValue::of)
      .toList();
  }

  @NotNull
  private static Collector<MapPoi, Object, ArrayList<MapPoi>> distinctCollector() {
    Function<MapPoi, String> compare = p -> p.getName() + ";" + p.getAddress() + ";" + p.getLocation().toLatitudeLongitude();
    return Collectors.collectingAndThen(Collectors.toCollection(() ->
      new TreeSet<>(Comparator.comparing(compare))), ArrayList::new);
  }

  private MapPoi convertMapPoi(Hit<GeoAddressEsCache> hit) {
    GeoAddressEsCache source = hit.source();
    if (Objects.isNull(source)) {
      log.warn("query es geo cache hit source is null.");
      return null;
    }
    try {
      MapPoi poi = new MapPoi();
      poi.setDistance((int) hit.sort().getFirst().doubleValue());
      poi.setId(source.getId());
      poi.setName(source.getSourceName());
      poi.setProvider(source.getProvider());
      poi.setTag(source.getTag());
      poi.setType(source.getTag());
      poi.setTelephone(source.getTelephone());
      poi.setLocation(LocationPoint.parseLatitudeLongitude(source.getGeocode()));
      poi.setAddress(source.getAddress());
      poi.setAdcode(source.getAdcode());
      poi.setProvince(source.getProvince());
      poi.setCity(source.getCity());
      poi.setDistrict(source.getDistrict());
      poi.setAdcode(source.getAdcode());
      poi.setType(source.getPoiType());
      return poi;
    } catch (Exception e) {
      log.warn("query es geo cache hit source convert failed.", e);
      return null;
    }
  }

  private long ipHitWeight(Hit<IpLocationEsCache> hit) {
    //计算权重排序，数字越大优先级越高
    IpLocationEsCache source = hit.source();
    if (Objects.isNull(source)) {
      return -1;
    }
    // 时间归整到天，时间新的优先级高
    long weight = source.getUpdateTime() / DAYS_MILLIS;

    if (StringUtils.isNotBlank(source.getCity())) {
      weight += 10000;
    }
    if (StringUtils.isNotBlank(source.getProvince())) {
      weight += 1000;
    }
    if (StringUtils.isNotBlank(source.getIsoCode())) {
      weight += 10;
    }
    return weight;
  }

  public void cacheReverseGeoAddress(GeoAddress address) {
    if (Objects.isNull(client) || Objects.isNull(address)) {
      return;
    }
    //如果连城市这个级别都没有获取到，认为获取的数据不全，不缓存
    if (StringUtils.isBlank(address.getCity())) {
      log.info("GeoAddress city is empty, do not cache. address:{}", address);
      return;
    }

    try {
      // 把poi list都转成单个的地址存储一份
      List<GeoAddressEsCache> cacheList = cacheAddressList(address);
      if (cacheList.isEmpty()) {
        log.info("GeoAddress no poi, do not cache, address:{}", address);
        return;
      }
      BulkRequest.Builder builder = new BulkRequest.Builder().index(indexName());
      for (GeoAddressEsCache document : cacheList) {
        builder.operations(op -> op.index(idx -> idx.document(document)));
      }
      BulkResponse response = client.bulk(builder.build());
      log.debug("add geo es cache address:{}, response: {}", address, response);
    } catch (Exception e) {
      log.error("add es geo cache failed.", e);
    }

  }

  List<GeoAddressEsCache> cacheAddressList(GeoAddress address) {
    long now = System.currentTimeMillis();
    return Optional.ofNullable(address.getPois())
      .orElseGet(List::of)
      .stream()
      .map(poi -> {
        if (Objects.isNull(poi.getLocation())) {
          return null;
        }
        GeoAddressEsCache document = new GeoAddressEsCache();
        document.setCreateTime(now);
        document.setUpdateTime(now);
        BeanUtils.copyProperties(address, document);

        document.setId(poi.getId());
        document.setSourceName(poi.getName());
        document.setTag(poi.getTag());
        document.setAlias(poi.getAlias());
        document.setPhotoUrl(poi.getPhotoUrl());
        document.setPoiType(poi.getType());
        AmapPoiUtils.nameToCode(poi.getType())
          .ifPresent(e -> document.setPoiCode(e.getCode()));
        document.setTelephone(poi.getTelephone());
        document.setGeocode(poi.getLocation().toLatitudeLongitude());
        document.setAddress(formatDocumentAddress(poi, address));
        document.setDistrict(StringUtils.defaultIfEmpty(poi.getDistrict(), address.getDistrict()));
        document.setAdcode(StringUtils.defaultIfEmpty(poi.getAdcode(), address.getAdcode()));
        document.setProvince(StringUtils.defaultIfEmpty(poi.getProvince(), address.getProvince()));
        document.setCity(StringUtils.defaultIfEmpty(poi.getCity(), address.getCity()));
        // clean 用不到了
        document.setPois(null);
        return document;
      })
      .filter(Objects::nonNull)
      //没有address的就不要cache了
      .filter(e -> StringUtils.isNotBlank(e.getAddress()))
      .distinct()
      .toList();
  }

  String formatDocumentAddress(MapPoi poi, GeoAddress address) {
    String rs = poi.getAddress();
    if (StringUtils.isEmpty(rs) && poi.getDistance() < 10) {
      //距离小于10米，没有地址，基本上都是AOI点值
      return address.getAddress();
    }
    //有时候已经包含省市了，但是没有区，避免把区拼接到省市的前面
    if (StringUtils.isEmpty(rs) || rs.contains("市") || rs.contains("省")) {
      return rs;
    }
    //没办法，只能这样格式化，POI里的address不带市区，而标准的要求里需要有，补一下数据
    String district = address.getDistrict();
    if (Objects.nonNull(district) && !rs.contains("区") && !rs.contains(district)) {
      rs = district + rs;
    }
    String city = address.getCity();
    if (Objects.nonNull(city) && !rs.contains("市") && !rs.contains(city)) {
      rs = city + rs;
    }
    String province = address.getProvince();
    if (Objects.nonNull(province) && !rs.contains("省") && !rs.contains(province)) {
      rs = province + rs;
    }
    return rs;
  }

  private String indexName() {
    //我们有删除动作，不要搞空了出大事故
    String index = config().getIndex();
    Assert.hasText(index, "index must not be empty");
    return index;
  }

  public void clearIpLocationExpireData() {
    try {
      log.info("start ip location es cache clean task, config: {}.", config());
      if (Objects.isNull(client)) {
        log.info("es client is null, ignore ip location es cache clean task");
        return;
      }
      long timeout = System.currentTimeMillis() - config().getIpCacheTimeout().toMillis();
      DeleteByQueryRequest request = new DeleteByQueryRequest.Builder()
        .index(config().getIpIndex())
        .query(q -> q.range(r -> r.number(n -> n.field("updateTime").lt((double) timeout))))
        .slices(s -> s.value(config().getSlices()))
        .scrollSize(config().getScrollSize())
        .requestsPerSecond(config().getRequestsPerSecond())
        .waitForCompletion(false)
        .build();
      client.deleteByQuery(request);
      log.info("finished ip location es cache clean task.");
    } catch (Exception e) {
      log.error("geo ip location cache clean failed.", e);
    }
  }

  public void clearGeoAddressExpireData() {
    try {
      log.info("start geo es cache clean task, config: {}.", config());
      if (Objects.isNull(client)) {
        log.info("es client is null, ignore geo es cache clean task");
        return;
      }
      long timeout = System.currentTimeMillis() - config().getCacheTimeout().toMillis();
      //清理过期数据，为防止系统过载，分多次任务删除
      DeleteByQueryRequest request = new DeleteByQueryRequest.Builder()
        .index(indexName())
        .query(q -> q.range(r -> r.number(n -> n.field("updateTime").lt((double) timeout))))
        .slices(s -> s.value(config().getSlices()))
        .scrollSize(config().getScrollSize())
        .requestsPerSecond(config().getRequestsPerSecond())
        .waitForCompletion(false)
        .build();
      client.deleteByQuery(request);
      log.info("finished geo es cache clean task.");
    } catch (Exception e) {
      log.error("geo es cache clean failed.", e);
    }

  }

  private void saveBizLog(FluxTrace trace, Object request, String api, String status) {
    SingleExecutorUtils.saveGeoBizLog(trace, request, config(), api, status);
  }

  private MapProperties.ElasticsearchConfig config() {
    return properties.getElasticsearch();
  }

}
