package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.Baidu<PERSON>pi;
import com.sharecrm.egress.api.BaiduErrorCode;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.BaiduAddressContent;
import com.sharecrm.egress.entity.BaiduAddressDetail;
import com.sharecrm.egress.entity.BaiduDistance;
import com.sharecrm.egress.entity.BaiduPoi;
import com.sharecrm.egress.entity.BaiduPoiDetail;
import com.sharecrm.egress.entity.BaiduPoiPoint;
import com.sharecrm.egress.entity.BaiduRoute;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.exception.EgressAppException;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sharecrm.egress.utils.MapUtils.countryChina;

/**
 * 百度GEO服务接口
 */
@Slf4j
public class BaiduGeoService implements GeoAdapter {

  private final BaiduApi baiduApi;
  private final MapProperties.BaiduConfig config;

  public BaiduGeoService(BaiduApi baiduApi, MapProperties.BaiduConfig config) {
    this.baiduApi = baiduApi;
    this.config = config;
  }

  @Override
  public Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request) {
    LocationPoint point = new LocationPoint(request.getLongitude(), request.getLatitude());
    // 百度地图API的经纬度参数顺序为：纬度,经度
    String location = point.toLatitudeLongitude();
    Integer radius = Objects.requireNonNullElse(request.getRadius(), config.getRadius());
    String language = MapUtils.baiduLanguage(request.getLanguage());
    return baiduApi.reverseGeoCode(location, radius, language).mapNotNull(res -> {
      int status = res.getStatus();
      BaiduAddressContent result = res.getResult();
      if (status == 0 && result != null) {
        GeoAddress address = new GeoAddress();
        address.setGeocode(point.toLatitudeLongitude());
        address.setAddress(result.getFormattedAddress());
        BaiduAddressDetail detail = result.getAddressComponent();
        if (detail != null) {
          address.setPois(Optional.ofNullable(result.getPois())
            .orElse(List.of())
            .stream()
            .map(this::baiduPoiToApiPoi)
            .toList());
          address.setCountry(detail.getCountry());
          address.setProvince(detail.getProvince());
          address.setCity(detail.getCity());
          address.setDistrict(detail.getDistrict());
          address.setTown(detail.getTown());
          address.setTownCode(detail.getTownCode());
          address.setStreet(detail.getStreet());
          address.setStreetNumber(detail.getStreetNumber());
          address.setAdcode(detail.getAdcode());
          log.info("百度地图API查询地址成功，location={}, address={}", location, address.getAddress());
          return address;
        }
      } else {
        String message = BaiduErrorCode.getMessage(status);
        log.warn("百度地图API查询地址失败，status={}, message={}, location={}", status, message, location);
        tryLock(status, SUPPORT_REVERSE_GEO);
      }
      return null;
    });
  }

  private void tryLock(int status, String... api) {
    // 天配额超限，限制访问
    if (status == 302) {
      for (String s : api) {
        GeoProviderLock.lock(config.getId(), s);
      }
    }
  }

  @Override
  public Mono<GeoAddress> queryGeoAddress(GeoEncodeRequest request) {
    //百度的返回结果里没有详细地址，只有坐标点，满足不了我们的要求
    log.warn("baidu not support queryGeoAddress yet.");
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<LocationPoint> queryLocationPoint(String address, String city) {
    //百度Api不支持返回详细的address信息，所以只支持查询经纬度
    return baiduApi.geoCode(address, city).mapNotNull(res -> {
        int status = res.getStatus();
        if (status == 0) {
          LocationPoint location = res.getResult().getLocation();
          log.info("百度GeoCode解析成功, city: {}, address: {}, location: ({})", city, address, location);
          return new GeoAddress(address, location.toLatitudeLongitude());
        } else {
          String message = res.getMessage();
          log.warn("百度GeoCode解析失败, city: {}, address: {}, status: {}, message: {}", city, address, status, message);
          tryLock(status, SUPPORT_GEO_ADDRESS, SUPPORT_LOCATION_POINT);
          return null;
        }
      })
      .map(e -> LocationPoint.parseLatitudeLongitude(e.getGeocode()));
  }

  @Override
  public Mono<List<PointDistance>> queryDistance(List<LocationPoint> origins, List<LocationPoint> destinations) {
    //此服务AK/SK算法失败，暂不要启用
    String from = origins.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining("|"));
    String to = destinations.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining("|"));
    return baiduApi.distance(from, to).mapNotNull(res -> {
      int status = res.getStatus();
      String message = res.getMessage();
      List<BaiduDistance> result = res.getResult();
      if (status == 0 && result != null) {
        log.info("百度地图API查询成功, from={}, to={}, distance={}", from, to, result);
        return result.stream().map(BaiduDistance::toPointDistance).toList();
      }
      log.warn("百度地图API查询失败, from={}, to={}, status={}, message={}", from, to, status, message);
      tryLock(status, SUPPORT_DISTANCE);
      return null;
    });
  }

  @Override
  public Mono<PointDistance> queryDriving(DrivingRouteRequest request) {
    String origin = request.getFrom().toLatitudeLongitude();
    String destination = request.getTo().toLatitudeLongitude();
    String waypoints = null;
    List<LocationPoint> points = request.getWaypoints();
    if (points != null && !points.isEmpty()) {
      waypoints = points.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining("|"));
    }
    return baiduApi.driving(origin, destination, waypoints).mapNotNull(res -> {
      int status = res.getStatus();
      BaiduRoute result = res.getResult();
      if (status == 0 && result != null) {
        List<PointDistance> results = result.getRoutes();
        log.info("百度地图API驾车规划成功，status={}, message={}, request={}, results={}", status, res.getMessage(), request, results);
        if (!results.isEmpty()) {
          return results.get(0);
        }
      }
      String message = Optional.ofNullable(res.getMessage()).orElseGet(() -> BaiduErrorCode.getMessage(status));
      log.warn("百度地图API驾车规划失败，status={}, message={}, request={}", status, message, request);
      tryLock(status, SUPPORT_DRIVING);
      return null;
    });
  }

  @Override
  public Mono<PoiResponse> queryPoiAround(PoiSearchRequest request) {
    //百度POI虽然代码支持，但是效果很差，配置上不启用
    String location = new LocationPoint(request.getLongitude(), request.getLatitude()).toLatitudeLongitude();
    int radius = request.getRadius();
    String query = MapUtils.join("$", request.getKeywords());
    String types = MapUtils.join("|", request.getTypes());
    int pageSize = request.getPageSize();
    int pageNum = request.getPageNum() - 1;
    String language = MapUtils.baiduLanguage(request.getLanguage());
    return baiduApi.poiAround(location, radius, query, types, pageSize, pageNum, language).mapNotNull(res -> {
      int status = res.getStatus();
      int total = res.getTotal();
      List<BaiduPoi> data = res.getResults();
      if (status == 0 && total >= 1 && data != null) {
        List<MapPoi> results = data.stream()
          .map(this::baiduPoiToApiPoi).toList();
        log.info("百度地图API查询POI成功，status={}, message={}, location={}, total={}", status, res.getMessage(), location, total);
        return PoiResponse.builder().count(total).results(results).build();
      }
      String message = Optional.ofNullable(res.getMessage()).orElseGet(() -> BaiduErrorCode.getMessage(status));
      log.warn("百度地图API查询POI失败，status={}, message={}, location={}, rs:{}", status, message, location, res);
      tryLock(status, SUPPORT_POI_AROUND);
      return null;
    });
  }

  private MapPoi baiduPoiToApiPoi(BaiduPoi poi) {
    MapPoi.MapPoiBuilder builder = MapPoi.builder();
    builder
      .provider(Constants.MAP_PROVIDER_BAIDU)
      .id(poi.getId())
      .name(poi.getName())
      .location(poi.getLocation())
      .province(poi.getProvince())
      .city(poi.getCity())
      .district(poi.getDistrict())
      .telephone(poi.getTelephone())
      .tag(poi.getTag())
      .distance((int) NumberUtils.toDouble(poi.getDistance()))
      .address(poi.getAddress())
      .location(Optional.ofNullable(poi.getPoint()).map(BaiduPoiPoint::toLocationPoint).orElse(null))
      .adcode(poi.getAdcode());
    BaiduPoiDetail detail = poi.getDetail();
    if (detail != null) {
      builder.distance(detail.getDistance())
        .alias(detail.getAlias())
        .tag(detail.getTag())
        .type(detail.getType())
        .photoUrl(CollectionUtils.isEmpty(detail.getPhotos()) ? null : detail.getPhotos().getFirst());
    }
    return builder.build();
  }

  @Override
  public Mono<IpLocation> queryIpLocation(String ip, String language) {
    return baiduApi.ipLocation(ip, MapUtils.baiduLanguage(language)).mapNotNull(res -> {
      int status = res.getStatus();
      if (status == 0 && res.getContent() != null && res.getContent().getAddressDetail() != null) {
        BaiduAddressDetail detail = res.getContent().getAddressDetail();
        String province = detail.getProvince();
        String city = detail.getCity();
        log.debug("Baidu IP定位成功, IP: {},  province: {}, city: {}", ip, province, city);
        return IpLocation.builder()
          .ip(ip)
          .country(countryChina(language))
          .province(province)
          .city(city)
          .language(language)
          .provider(provider().getType())
          .build();
      } else {
        log.warn("Baidu IP定位失败, IP: {},  status: {}, message: {}", ip, status, BaiduErrorCode.getMessage(status));
        tryLock(status, SUPPORT_IP_LOCATION);
        return null;
      }
    });
  }

  @Override
  public GeoProvider provider() {
    return config;
  }

  @Override
  public List<String> supports() {
    return config.getSupports();
  }

  @Override
  public int getWeight() {
    return config.getWeight();
  }
}
