package com.sharecrm.egress.geo;

import com.fxiaoke.common.IpUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.hash.Hashing;
import com.sharecrm.egress.config.FluxTrace;
import com.sharecrm.egress.entity.CoordinateConvertRequest;
import com.sharecrm.egress.entity.CoordinateConvertResponse;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoAddressEsCache;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.IpLocationRequest;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.EgressUtils;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.MapUtils;
import com.sharecrm.egress.utils.SchedulerUtils;
import com.sharecrm.egress.utils.SingleExecutorUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuples;

import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_COORDINATE_CONVERT;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_DISTANCE;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_DRIVING;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_GEO_ADDRESS;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_IP_LOCATION;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_LOCATION_POINT;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_POI_AROUND;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_REVERSE_GEO;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_REVERSE_GEO_OVERSEAS;
import static com.sharecrm.egress.geo.GeoCache.EMPTY_LOCATION_POINT;
import static com.sharecrm.egress.geo.GeoCache.EMPTY_POINT_DISTANCE;
import static com.sharecrm.egress.geo.GeoCache.EMPTY_POI_RESPONSE;
import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_EMPTY;
import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_GOOGLE;
import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_MAXMIND;
import static org.apache.commons.lang3.StringUtils.trimToNull;

/**
 * GEO解析和反解析服务，提供经纬度和地址互换, IP地址查询、距离计算等服务
 */
@Slf4j
@Service
public class GeoService {

  /**
   * 计数器，用于实现RoundRain算法计数
   */
  private static final AtomicInteger distanceCounter = new AtomicInteger(0);
  private static final AtomicInteger drivingCounter = new AtomicInteger(0);
  private static final AtomicInteger ipLocationCounter = new AtomicInteger(0);
  private static final AtomicInteger geoAddressCounter = new AtomicInteger(0);
  private static final AtomicInteger reverseGeoAddressCounter = new AtomicInteger(0);
  private static final AtomicInteger locationPointCounter = new AtomicInteger(0);
  private static final AtomicInteger coordinateConvertCounter = new AtomicInteger(0);
  private static final AtomicInteger poiCounter = new AtomicInteger(0);

  private final ApplicationContext applicationContext;
  private final GeoCache geoCache;
  private final GeoEsCacheService geoEsCacheService;

  private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("egress-geo");

  private static final ExecutorService executor = new ThreadPoolExecutor(4, 16, 30, TimeUnit.MINUTES,
    new ArrayBlockingQueue<>(100), new ThreadPoolExecutor.CallerRunsPolicy());

  public GeoService(ApplicationContext applicationContext, GeoCache geoCache, GeoEsCacheService geoEsCacheService) {
    this.applicationContext = applicationContext;
    this.geoCache = geoCache;
    this.geoEsCacheService = geoEsCacheService;
  }

  /**
   * GEO 地址反解析，经纬度转地址
   *
   * @param request request
   * @return 结构化地址信息
   */
  public Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request) {
    // 如果是指定Google，只能用Google
    String provider = isGoogle(request.getFirstProvider()) ? MAP_PROVIDER_GOOGLE : MAP_PROVIDER_EMPTY;
    return queryReverseGeoAddress(request, provider);
  }

  public Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request, String provider) {
    return WebUtils.fluxTrace()
      .zipWith(Mono.just(request))
      .flatMap(tpl -> {
        FluxTrace trace = tpl.getT1();
        ReverseGeoRequest req = tpl.getT2();
        req.setLanguage(StringUtils.defaultIfEmpty(req.getLanguage(), Constants.ZH_CN));
        req.setUserId(StringUtils.defaultIfEmpty(req.getUserId(), trace.getUid()));
        return queryReverseGeoAddress(req, trace, provider);
      })
      .map(e -> formatMapPoi(request, e));
  }

  @NotNull
  private GeoAddress formatMapPoi(ReverseGeoRequest request, GeoAddress e) {
    //当前服务还不稳定，先用info级别便于定位问题
    log.info("ReverseGeo, request: {}, response: {}", request, e.getAddress());
    List<MapPoi> poiList = Optional.ofNullable(e.getPois())
      .orElseGet(List::of)
      .stream()
      //这些和主体数据基本都重复，听从端上意见不返回置为null
      .map(this::resetMapPoiNullValues)
      //按距离排序
      .sorted(Comparator.comparing(o -> Objects.requireNonNullElse(o.getDistance(), 0)))
      .toList();

    if (!poiList.isEmpty()) {
      e.setFeatureName(poiList.getFirst().getName());
    }
    //端上有时候不希望返回详细的列表，减少流量传输，主动设置为空
    e.setPois(request.isReturnPois() ? poiList : List.of());
    return e;
  }

  @NotNull
  private MapPoi resetMapPoiNullValues(MapPoi poi) {
    poi.setCity(null);
    poi.setProvince(null);
    poi.setProvider(null);
    poi.setAdcode(null);
    poi.setId(null);
    poi.setTelephone(trimToNull(poi.getTelephone()));
    return poi;
  }

  private Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request, FluxTrace trace, String provider) {
    return queryReverseGeoAddressByCache(request, trace, provider)
      .switchIfEmpty(Mono.defer(() -> queryReverseGeoAddressAndCache(request, trace, provider)));
  }

  Mono<GeoAddress> queryReverseGeoAddressByCache(ReverseGeoRequest request, FluxTrace trace, String provider) {
    if (!request.isCache()) {
      return Mono.empty();
    }
    return Mono.just(reverseGeoCacheKey(request, provider))
      .flatMap(key -> Mono.justOrEmpty(geoCache.getReverseGeoAddress(key)))
      .switchIfEmpty(Mono.defer(() -> queryReverseGeoAddressByEsCache(request, trace, provider)));
  }

  private Mono<GeoAddressEsCache> queryReverseGeoAddressByEsCache(ReverseGeoRequest request, FluxTrace trace, String provider) {
    return Mono.just(isAllowEsCache(request))
      .flatMap(es -> es ?
        Mono.fromFuture(CompletableFuture.supplyAsync(() -> geoEsCacheService.queryReverseGeoAddress(request, trace, provider), executor))
          .map(e -> {
            //cache中的geo数据与原始请求有偏差，重置geocode，保持和api请求参数一致
            LocationPoint point = new LocationPoint(request.getLongitude(), request.getLatitude());
            e.setGeocode(point.toLatitudeLongitude());
            e.setCreateTime(null);
            e.setUpdateTime(null);
            e.setId(null);
            e.setSourceName(null);
            e.setAlias(null);
            e.setTag(null);
            e.setTelephone(null);
            e.setPhotoUrl(null);
            e.setPoiCode(null);
            e.setPoiType(null);
            log.info("ReverseGeo, es cache matched, request: {}, response: {}", request, e);
            return e;
          }) : Mono.empty());
  }

  private boolean isAllowEsCache(ReverseGeoRequest request) {
    // 按照用户租户灰度
    String grayRule = "reverse-es-cache";
    // 用EMPTY作为默认占位符，实现全局开关控制，比如ES故障时临时禁用
    String userId = StringUtils.defaultIfEmpty(request.getUserId(), "EMPTY");
    boolean rs = gray.isAllow(grayRule, userId);
    log.debug("gray：reverse-es-cache，rule:{},uid:{},result:{}", grayRule, userId, rs);
    return rs;
  }

  private Mono<GeoAddress> queryReverseGeoAddressAndCache(ReverseGeoRequest request, FluxTrace trace, String provider) {
    log.info("ReverseGeo cache missing, will do remote query: {}", request);
    List<GeoAdapter> adapters = filterGeoAdapterBeansByGray(provider, request);
    if (adapters.isEmpty()) {
      log.warn("ReverseGeo not support, no available provider.");
      return Mono.empty();
    }
    //优先provider
    String firstProvider = request.getFirstProvider();
    // 有指定优先级 provider，优先按照provider，查不到再用其他的； 如果是指定Google，只能用Google
    if (StringUtils.isNotBlank(firstProvider) && !isGoogle(firstProvider)) {
      GeoAdapter adapter = firstAdapter(adapters, firstProvider);
      return queryGeoAddressByAdapter(trace, request, adapter, provider)
        //回退其他provider查询
        .switchIfEmpty(Mono.defer(() -> queryGeoAddressByAdapter(trace, request, fallbackAdapter(adapters, firstProvider), provider)));
    }
    GeoAdapter adapter = EgressUtils.roundRobin(adapters, reverseGeoAddressCounter);
    return queryGeoAddressByAdapter(trace, request, adapter, provider)
      //这里主要是可能因为欠费、网络异常等，换个provider重试
      .switchIfEmpty(Mono.defer(() -> queryGeoAddressByAdapter(trace, request, fallbackAdapter(adapters, adapter.provider().getType()), provider)));

  }

  private Mono<GeoAddress> queryGeoAddressByAdapter(FluxTrace trace, ReverseGeoRequest request, GeoAdapter adapter, String limitProvider) {
    if (Objects.isNull(adapter)) {
      return Mono.empty();
    }
    return Mono.just(trace)
      .doOnNext(t -> saveBizLog(t, request, adapter, supportReverseGeo(request)))
      .flatMap(t -> adapter.queryReverseGeoAddress(request)
        .map(e -> {
          e.setLanguage(request.getLanguage());
          // api里关心的是类型，不关心id，ES搜索时按照类型判断
          e.setProvider(adapter.provider().getType());
          return e;
        })
        .doOnSuccess(values -> cacheReverseGeoAddress(request, limitProvider, values)));
  }

  private static GeoAdapter fallbackAdapter(List<GeoAdapter> adapters, String firstProvider) {
    return filterGeoAdapter(adapters, e -> !e.provider().getType().equals(firstProvider));
  }

  private static GeoAdapter firstAdapter(List<GeoAdapter> adapters, String firstProvider) {
    // 注意两个条件是取反的
    return filterGeoAdapter(adapters, e -> e.provider().getType().equals(firstProvider));
  }


  @Nullable
  private static GeoAdapter filterGeoAdapter(List<GeoAdapter> adapters, Predicate<GeoAdapter> predicate) {
    List<GeoAdapter> fallbackAdapters = adapters.stream()
      .filter(predicate)
      .toList();
    //过滤后可能没有了
    if (CollectionUtils.isNotEmpty(fallbackAdapters)) {
      return EgressUtils.roundRobin(fallbackAdapters, reverseGeoAddressCounter);
    }
    return null;
  }

  private static boolean isGoogle(String firstProvider) {
    return MAP_PROVIDER_GOOGLE.equals(firstProvider);
  }

  private void cacheReverseGeoAddress(ReverseGeoRequest request, String provider, GeoAddress src) {
    //我们在做API返回的时候，抹除了一些API不关心的内容，但是缓存的时候要缓存全部的内容。此时借json实现deep clone
    String detail = JsonUtil.toJson(src);
    SingleExecutorUtils.execute(() -> {
      GeoAddress address = JsonUtil.fromJson(detail, GeoAddress.class);
      // 正常不应该走到这里，做一个保护
      if (Objects.isNull(address)) {
        log.warn("geo address is null, do not cache");
        return;
      }
      geoCache.putReverseGeoAddress(reverseGeoCacheKey(request, provider), address);
      geoEsCacheService.cacheReverseGeoAddress(address);
    });
  }

  private String reverseGeoCacheKey(ReverseGeoRequest request, String provider) {
    StringJoiner joiner = new StringJoiner(":");
    joiner.add(Objects.toString(provider, ""));
    joiner.add(Objects.toString(request.getLanguage(), ""));
    joiner.add(Objects.toString(request.getLatitude(), ""));
    joiner.add(Objects.toString(request.getLongitude(), ""));
    joiner.add(Objects.toString(StringUtils.join(request.getPoiTypes(), "|"), ""));
    return joiner.toString();
  }

  private void saveBizLog(FluxTrace trace, Object request, GeoAdapter adapter, String api) {
    SingleExecutorUtils.saveGeoBizLog(trace, request, adapter.provider(), api, "success");
  }


  public Mono<PoiResponse> queryPoiAround(PoiSearchRequest request, String limit) {
    // 如果是指定Google，只能用Google
    String provider = isGoogle(request.getFirstProvider()) ? MAP_PROVIDER_GOOGLE : limit;
    if (request.isCache()) {
      PoiResponse hit = geoCache.getPoiResponse(request);
      if (hit != null) {
        return cacheOrEmpty(hit, EMPTY_POI_RESPONSE::equals);
      }
    }
    return queryPoiAroundAndCache(request, provider);
  }

  private <T> Mono<T> cacheOrEmpty(T obj, Predicate<T> isEmpty) {
    //把空对象转成Mono.empty，为了保持和以前的接口兼容而已
    return isEmpty.test(obj) ? Mono.empty() : Mono.just(obj);
  }

  private Mono<PoiResponse> queryPoiAroundAndCache(PoiSearchRequest request, String provider) {
    List<GeoAdapter> adapters = filterOrderedAdapterBeans(SUPPORT_POI_AROUND, provider);
    if (adapters.isEmpty()) {
      log.warn("poi search not support, no available provider.");
      return Mono.empty();
    }
    GeoAdapter adapter = EgressUtils.roundRobin(adapters, poiCounter);
    Function<FluxTrace, Mono<PoiResponse>> query = t -> adapter
      .queryPoiAround(request)
      .doOnSuccess(values -> geoCache.putPoiResponse(request, Objects.requireNonNullElse(values, EMPTY_POI_RESPONSE)));
    return WebUtils.fluxTrace()
      .doOnNext(trace -> saveBizLog(trace, request, adapter, SUPPORT_POI_AROUND))
      .flatMap(query);
  }

  /**
   * 距离计算服务，计算两点之间的距离
   */
  public Mono<List<PointDistance>> queryDistance(List<LocationPoint> origins, List<LocationPoint> destination, boolean cache) {
    String from = origins.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining("|"));
    String to = destination.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining(";"));
    String cacheKey = from + '\u0004' + to;
    if (cache) {
      List<PointDistance> hit = geoCache.getDistance(cacheKey);
      if (CollectionUtils.isNotEmpty(hit)) {
        return cacheOrEmpty(hit, List::isEmpty);
      }
    }
    return queryDistanceAndDoCache(origins, destination, cacheKey);
  }

  public Mono<List<PointDistance>> queryDistanceAndDoCache(List<LocationPoint> origins, List<LocationPoint> destinations, String cacheKey) {
    List<GeoAdapter> adapters = filterOrderedAdapterBeans(SUPPORT_DISTANCE, "");
    if (adapters.isEmpty()) {
      log.warn("distance not support, no available provider.");
      return Mono.just(List.of());
    }

    GeoAdapter adapter = EgressUtils.roundRobin(adapters, distanceCounter);
    Function<FluxTrace, Mono<List<PointDistance>>> query = t -> adapter
      .queryDistance(origins, destinations)
      .doOnSuccess(values -> geoCache.putDistance(cacheKey, Objects.requireNonNullElseGet(values, List::of)));
    return WebUtils.fluxTrace()
      .doOnNext(trace -> saveBizLog(trace, Tuples.of(origins, destinations), adapter, SUPPORT_DISTANCE))
      .flatMap(query);
  }

  public Mono<PointDistance> queryDriving(DrivingRouteRequest request, boolean cache, String provider) {
    if (cache) {
      PointDistance hit = geoCache.getDriving(request);
      if (hit != null) {
        return cacheOrEmpty(hit, EMPTY_POINT_DISTANCE::equals);
      }
    }
    return queryDrivingAndDoCache(request, provider);
  }

  private Mono<PointDistance> queryDrivingAndDoCache(DrivingRouteRequest request, String provider) {
    List<GeoAdapter> adapters = filterOrderedAdapterBeans(SUPPORT_DRIVING, provider);
    if (adapters.isEmpty()) {
      log.warn("driving not support, no available provider.");
      return Mono.empty();
    }
    GeoAdapter adapter = EgressUtils.roundRobin(adapters, drivingCounter);
    Function<FluxTrace, Mono<PointDistance>> query = t -> adapter
      .queryDriving(request)
      .doOnSuccess(v -> geoCache.putDriving(request, Objects.requireNonNullElse(v, GeoCache.EMPTY_POINT_DISTANCE)));
    return WebUtils.fluxTrace()
      .doOnNext(trace -> saveBizLog(trace, request, adapter, SUPPORT_DRIVING))
      .flatMap(query);
  }

  public Mono<IpLocation> queryIpLocation(IpLocationRequest request) {
    return queryIpLocation(request.getIp(),
      Objects.requireNonNullElse(request.getLanguage(), "zh-CN"),
      Objects.requireNonNullElse(request.getProvider(), MAP_PROVIDER_EMPTY));
  }

  /**
   * 查询IP地址归属地
   */
  public Mono<IpLocation> queryIpLocation(String ip, String language, String provider) {
    //内部地址不要请求服务商了，不支持
    if (IpUtil.isInnerIP(ip)) {
      return Mono.just(defaultIpResponse(ip));
    }
    return queryIpLocationByCache(ip, language, provider)
      .switchIfEmpty(Mono.defer(() -> queryIpLocationByProvider(ip, language, provider)));
  }

  private IpLocation defaultIpResponse(String ip) {
    return IpLocation.builder().ip(ip).build();
  }

  @NotNull
  private Mono<IpLocation> queryIpLocationByProvider(String ip, String language, String provider) {
    // 百度高德只支持中文，如果是其他语言，强制切换到MAXMIND
    if (!MapUtils.isEmptyOrZh(language)) {
      provider = MAP_PROVIDER_MAXMIND;
    }
    return queryIpLocationAndCache(ip, language, provider)
      //如果连国家都没查到的话，基本上服务提供商返回的是一个假的，也走兜底逻辑
      .filter(e -> StringUtils.isNotBlank(e.getCountry()))
      // Maxmind GeoLite2Service做兜底
      .switchIfEmpty(Mono.defer(() -> failQueryIpLocationByMaxmind(ip, language, null)))
      //失败兜底
      .onErrorResume(e -> failQueryIpLocationByMaxmind(ip, language, e))
      //业务侧并不希望返回404，按照老服务接口设计返回一个假的对象
      .switchIfEmpty(Mono.defer(() -> Mono.just(defaultIpResponse(ip))));
  }

  private Mono<IpLocation> queryIpLocationByCache(String ip, String language, String provider) {
    return Mono.justOrEmpty(geoCache.getIpLocation(ip, language))
      .publishOn(SchedulerUtils.IP_SCHEDULER)
      .switchIfEmpty(Mono.defer(() ->
        Mono.fromFuture(CompletableFuture.supplyAsync(() -> geoEsCacheService.queryIpLocation(ip, language, provider), executor))));
  }

  public Mono<IpLocation> failQueryIpLocationByMaxmind(String ip, String language, Throwable e) {
    log.info("query ip {} failed, try fallback maxmind", ip, e);
    return queryIpLocationAndCache(ip, language, MAP_PROVIDER_MAXMIND);
  }

  private Mono<IpLocation> queryIpLocationAndCache(String ip, String language, String provider) {
    List<GeoAdapter> adapters = filterOrderedAdapterBeans(SUPPORT_IP_LOCATION, provider);
    if (!MAP_PROVIDER_MAXMIND.equals(provider)) {
      // 过滤掉 Maxmind GeoLite2Service，只做兜底，第一轮不参与
      adapters = adapters.stream()
        .filter(e -> !e.provider().getType().equals(MAP_PROVIDER_MAXMIND))
        .toList();
    }
    if (adapters.isEmpty()) {
      log.warn("ip location not support, no available provider.");
      return Mono.empty();
    }
    GeoAdapter adapter = EgressUtils.roundRobin(adapters, ipLocationCounter);
    Function<FluxTrace, Mono<IpLocation>> query = t -> adapter
      .queryIpLocation(ip, language)
      .doOnSuccess(v -> cacheIpLocation(ip, language, v));
    return WebUtils.fluxTrace()
      .doOnNext(trace -> saveBizLog(trace, ip, adapter, SUPPORT_IP_LOCATION))
      .flatMap(query);
  }

  private void cacheIpLocation(String ip, String language, IpLocation v) {
    if (Objects.isNull(v) || StringUtils.isBlank(v.getCountry())) {
      return;
    }
    SingleExecutorUtils.execute(() -> {
      geoCache.putIpLocation(ip, language, v);
      geoEsCacheService.cacheIpLocation(v);
    });
  }

  public Mono<LocationPoint> queryLocationPoint(String address, String city, boolean cache, String provider) {
    if (cache) {
      // GeoAddress里肯定包含LocationPoint，所以如果已经有缓存了直接用
      String cacheKey = locationPointCacheKey(address, city, provider);
      GeoAddress geoAddress = geoCache.getGeoAddress(cacheKey);
      if (geoAddress != null && StringUtils.isNotBlank(geoAddress.getAddress())) {
        return Mono.just(LocationPoint.parseLatitudeLongitude(geoAddress.getGeocode()));
      }
      LocationPoint hit = geoCache.getLocationPoint(cacheKey);
      if (hit != null) {
        return cacheOrEmpty(hit, EMPTY_LOCATION_POINT::equals);
      }
    }
    return queryLocationPointAndCache(address, city, provider);
  }

  private Mono<LocationPoint> queryLocationPointAndCache(String address, String city, String provider) {
    List<GeoAdapter> adapters = filterOrderedAdapterBeans(SUPPORT_LOCATION_POINT, provider);
    if (adapters.isEmpty()) {
      log.warn("location point not support, no available provider.");
      return Mono.empty();
    }
    GeoAdapter adapter = EgressUtils.roundRobin(adapters, locationPointCounter);
    String cacheKey = locationPointCacheKey(address, city, provider);
    Function<FluxTrace, Mono<LocationPoint>> query = t -> adapter
      .queryLocationPoint(address, city)
      .doOnSuccess(v -> geoCache.putLocationPoint(cacheKey, Objects.requireNonNullElse(v, new LocationPoint())));
    return WebUtils.fluxTrace()
      .doOnNext(trace -> saveBizLog(trace, cacheKey, adapter, SUPPORT_LOCATION_POINT))
      .flatMap(query);
  }

  private String locationPointCacheKey(String address, String city, String provider) {
    GeoEncodeRequest request = new GeoEncodeRequest(address, city);
    request.setProvider(provider);
    request.setLanguage(Constants.ZH_CN);
    return geoAddressCacheKey(request);
  }

  public Mono<GeoAddress> queryGeoAddress(GeoEncodeRequest request) {
    if (request.isCache()) {
      GeoAddress hit = geoCache.getGeoAddress(geoAddressCacheKey(request));
      if (hit != null && StringUtils.isNotBlank(hit.getGeocode())) {
        return Mono.just(hit);
      }
    }
    return queryGeoAddressAndCache(request);
  }

  private Mono<GeoAddress> queryGeoAddressAndCache(GeoEncodeRequest request) {
    String provider = Objects.requireNonNullElse(request.getProvider(), MAP_PROVIDER_EMPTY);
    List<GeoAdapter> adapters = filterOrderedAdapterBeans(SUPPORT_GEO_ADDRESS, provider);
    if (adapters.isEmpty()) {
      log.warn("geo address not support, no available provider.");
      return Mono.empty();
    }
    GeoAdapter adapter = EgressUtils.roundRobin(adapters, geoAddressCounter);
    String cacheKey = geoAddressCacheKey(request);
    String language = Objects.requireNonNullElse(request.getLanguage(), Constants.ZH_CN);
    Function<FluxTrace, Mono<GeoAddress>> query = t -> adapter
      .queryGeoAddress(request)
      .flatMap(e -> tryTranslateLanguage(e, language))
      .doOnSuccess(v -> geoCache.putGeoAddress(cacheKey, Objects.requireNonNullElse(v, new GeoAddress())));
    return WebUtils.fluxTrace()
      .doOnNext(trace -> saveBizLog(trace, request, adapter, SUPPORT_GEO_ADDRESS))
      .flatMap(query);
  }

  private Mono<GeoAddress> tryTranslateLanguage(GeoAddress geoAddress, String language) {
    //运营商只支持国内的，所以中文不用翻译, 如果是google，也不用翻译
    if (Objects.isNull(geoAddress) || MapUtils.isEmptyOrZh(language) || isGoogle(geoAddress.getProvider())) {
      return Mono.just(geoAddress);
    }
    String geocode = geoAddress.getGeocode();
    if (StringUtils.isBlank(geocode)) {
      log.warn("geocode is blank:{}", geoAddress);
      return Mono.just(geoAddress);
    }
    //借用这个接口翻译一下
    ReverseGeoRequest request = new ReverseGeoRequest();
    LocationPoint point = LocationPoint.parseLatitudeLongitude(geocode);
    request.setLanguage(language);
    request.setRadius(100);
    request.setCache(true);
    request.setLatitude(point.getLatitude());
    request.setLongitude(point.getLongitude());
    return queryReverseGeoAddress(request)
      .switchIfEmpty(Mono.defer(() -> Mono.just(geoAddress)));
  }

  private String geoAddressCacheKey(GeoEncodeRequest request) {
    StringJoiner joiner = new StringJoiner(":");
    joiner.add(Objects.toString(request.getProvider(), ""));
    joiner.add(request.getAddress());
    joiner.add(Objects.toString(request.getCity(), ""));
    joiner.add(Objects.toString(request.getCountry(), ""));
    joiner.add(Objects.toString(request.getLanguage(), ""));
    String key = joiner.toString();
    return key.length() > 64 ? Hashing.goodFastHash(32).hashString(key, StandardCharsets.UTF_8).toString() : key;
  }

  private List<GeoAdapter> filterGeoAdapterBeansByGray(String provider, ReverseGeoRequest request) {
    return filterOrderedAdapterBeans(supportReverseGeo(request), provider)
      .stream()
      .filter(e -> filterByGeoGrayRules(e, request))
      .toList();
  }

  @NotNull
  private String supportReverseGeo(ReverseGeoRequest request) {
    return request.isOverseas() ? SUPPORT_REVERSE_GEO_OVERSEAS : SUPPORT_REVERSE_GEO;
  }

  private boolean filterByGeoGrayRules(GeoAdapter e, ReverseGeoRequest request) {
    // 按照用户租户灰度
    String grayRule = "reverse-geo-" + e.provider().getId();
    // 用EMPTY作为默认占位符，实现全局开关控制
    String userId = StringUtils.defaultIfEmpty(request.getUserId(), "EMPTY");
    boolean rs = gray.isAllow(grayRule, userId);
    log.debug("gray：reverse-geo，rule:{},uid:{},result:{}", grayRule, userId, rs);
    return rs;
  }

  /**
   * 按支持类型过滤Bean，Google比较特殊，单独处理
   */
  private List<GeoAdapter> filterOrderedAdapterBeans(String support, String provider) {
    return EgressUtils.filterRefreshScopeBeans(applicationContext.getBeansOfType(GeoAdapter.class))
      .stream()
      .filter(e -> e.supports().contains(support))
      .filter(e -> filterGoogleOrElse(provider, e))
      .filter(e -> GeoProviderLock.notLocked(e.provider().getId(), support))
      .sorted(Comparator.comparingInt(o -> o.provider().getOrder()))
      .toList();
  }

  private boolean filterGoogleOrElse(String provider, GeoAdapter e) {
    // Google与其他几家都不一样，如果限定了google，指定使用Google，否则其他几家可以随意选择
    String type = e.provider().getType();
    if (StringUtils.isEmpty(provider)) {
      return !type.equals(MAP_PROVIDER_GOOGLE);
    }
    return type.equals(provider);
  }

  public Mono<CoordinateConvertResponse> coordinateConvert(CoordinateConvertRequest request) {
    List<GeoAdapter> adapters = filterOrderedAdapterBeans(SUPPORT_COORDINATE_CONVERT, MAP_PROVIDER_EMPTY);
    if (adapters.isEmpty()) {
      log.warn("coordinate convert not support, no available provider.");
      return Mono.just(fallbackCoordinateConvert(request));
    }
    GeoAdapter adapter = EgressUtils.roundRobin(adapters, coordinateConvertCounter);
    return WebUtils.fluxTrace()
      .doOnNext(trace -> saveBizLog(trace, request, adapter, SUPPORT_COORDINATE_CONVERT))
      .flatMap(e -> adapter.coordinateConvert(request))
      .switchIfEmpty(Mono.defer(() -> Mono.just(fallbackCoordinateConvert(request))))
      .onErrorResume(e -> {
        log.warn("coordinate convert failed", e);
        return Mono.just(fallbackCoordinateConvert(request));
      });
  }

  private CoordinateConvertResponse fallbackCoordinateConvert(CoordinateConvertRequest request) {
    CoordinateConvertResponse response = new CoordinateConvertResponse();
    response.setLongitude(request.getLongitude());
    response.setLatitude(request.getLatitude());
    return response;
  }
}
