package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.AMapApi;
import com.sharecrm.egress.api.BaiduApi;
import com.sharecrm.egress.api.GoogleApi;
import com.sharecrm.egress.api.GoogleRouteApi;
import com.sharecrm.egress.api.HttpExchangeUtils;
import com.sharecrm.egress.api.TencentMapApi;
import com.sharecrm.egress.config.MapProperties;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ExchangeStrategies;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 动态注入高德、Google地图相关的Bean
 */
@Slf4j
@Component
public class GeoBeanRefresher implements InitializingBean {

  private final AtomicLong counter = new AtomicLong();

  private final MapProperties properties;

  private final ConfigurableApplicationContext applicationContext;
  private final ExchangeStrategies strategies;

  public GeoBeanRefresher(MapProperties properties, ConfigurableApplicationContext applicationContext, ExchangeStrategies strategies) {
    this.properties = properties;
    this.applicationContext = applicationContext;
    this.strategies = strategies;
  }

  @Override
  public void afterPropertiesSet() {
    registerAndDestroyBeans();
  }

  /**
   * 配置有变更时刷新bean，动态卸载和更新
   */
  @EventListener
  public void handleRefreshedEvent(RefreshScopeRefreshedEvent event) {
    log.info("config refreshed, refresh geo bean: {}", event);
    registerAndDestroyBeans();
  }

  private synchronized void registerAndDestroyBeans() {
    // 已经存在的旧bean
    Map<String, GeoAdapter> existBeans = applicationContext.getBeansOfType(GeoAdapter.class);
    DefaultListableBeanFactory beanFactory = beanFactory();
    // 注册Bean
    properties.getAmap().forEach(this::initAmapBean);
    properties.getTencent().forEach(this::initTencentBean);
    properties.getBaidu().forEach(this::initBaiduBean);
    properties.getGoogle().forEach(this::initGoogleBean);

    //销毁旧Bean，注意此处可能涉及enabled的切换，所以全都销毁，MaxmindGeoService不是在此注册的，不能销毁
    existBeans.forEach((k, v) -> {
      if (!(v instanceof GeoBeanIgnoreDestroy)) {
        log.info("geo config refresh, destroy bean: {}", k);
        //用destroySingleton，不要用destroyBean，改了记得验证
        beanFactory.destroySingleton(k);
      }
    });

    log.info("geo bean refresh success, beans count:{}", applicationContext.getBeansOfType(GeoAdapter.class).size());

  }

  @NotNull
  private DefaultListableBeanFactory beanFactory() {
    // 只有 DefaultListableBeanFactory 才能有效 destroyBean，ConfigurableListableBeanFactory destroyBean失败
    return (DefaultListableBeanFactory) applicationContext.getBeanFactory();
  }

  private void initGoogleBean(String key, MapProperties.GoogleConfig config) {
    if (!config.isEnabled()) {
      log.debug("google {} disabled", key);
      return;
    }
    log.info("init google bean:{}", config);
    GoogleApi api = HttpExchangeUtils.buildGoogleMapApi(strategies, config);
    GoogleRouteApi googleRouteApi = HttpExchangeUtils.buildGoogleRouteApi(strategies, config);
    GoogleGeoService service = new GoogleGeoService(api, googleRouteApi, config);
    registerSingleton("google.map.auto.bean", service);
  }

  private void initBaiduBean(String key, MapProperties.BaiduConfig config) {
    if (!config.isEnabled()) {
      log.debug("baidu {} disabled", key);
      return;
    }
    log.info("init baidu bean:{}", config);
    BaiduApi api = HttpExchangeUtils.buildBaiduApi(strategies, config);
    BaiduGeoService service = new BaiduGeoService(api, config);
    registerSingleton("baidu.auto.bean", service);
  }

  private void initTencentBean(String key, MapProperties.TencentConfig config) {
    if (!config.isEnabled()) {
      log.debug("tencent {} disabled", key);
      return;
    }
    log.info("init tencent bean:{}", config);

    TencentMapApi api = HttpExchangeUtils.buildTencentMapApi(strategies, config);
    TencentGeoService service = new TencentGeoService(api, config);
    registerSingleton("tencent.auto.bean", service);

  }

  private void initAmapBean(String key, MapProperties.AMapConfig config) {
    if (!config.isEnabled()) {
      log.debug("amap {} disabled", key);
      return;
    }
    log.info("init amap bean:{}", config);
    AMapApi aMapApi = HttpExchangeUtils.buildAMapApi(strategies, config);
    AMapGeoService service = new AMapGeoService(aMapApi, config);
    registerSingleton("amap.auto.bean", service);
  }


  void registerSingleton(String prefix, Object singletonObject) {
    // 注意prefix符合Spring bean name规范，不要随便改
    beanFactory().registerSingleton(prefix + counter.getAndIncrement(), singletonObject);
  }

}
