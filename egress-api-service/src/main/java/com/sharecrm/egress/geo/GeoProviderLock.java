package com.sharecrm.egress.geo;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 如果识别出调用量已达上限，就锁定不要继续调用了
 */
@Slf4j
@UtilityClass
public class GeoProviderLock {

  private static final Map<String, String> locks = new ConcurrentHashMap<>();

  public static void lock(String providerId, String api) {
    log.warn("map api limited, provider:{},api:{}", providerId, api);
    locks.put(key(providerId, api), "lock");
  }

  @NotNull
  private static String key(String providerId, String api) {
    return providerId + ":" + api;
  }

  public static void free() {
    log.info("clean map api locks.");
    locks.clear();
  }

  public static boolean isLocked(String providerId, String api) {
    return locks.containsKey(key(providerId, api));
  }

  public static boolean notLocked(String providerId, String api) {
    return !isLocked(providerId, api);
  }

}
