package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.GoogleApi;
import com.sharecrm.egress.api.GoogleRouteApi;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.GoogleAddressComponent;
import com.sharecrm.egress.entity.GoogleGeoCode;
import com.sharecrm.egress.entity.GoogleGeoLocation;
import com.sharecrm.egress.entity.GoogleLocation;
import com.sharecrm.egress.entity.GooglePoiLocation;
import com.sharecrm.egress.entity.GoogleRoute;
import com.sharecrm.egress.entity.GoogleRouteRequest;
import com.sharecrm.egress.entity.GoogleWayPoint;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.exception.EgressAppException;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_GOOGLE;

/**
 * Google地图服务接口
 */
@Slf4j
public class GoogleGeoService implements GeoAdapter {

  private final GoogleApi googleApi;
  private final GoogleRouteApi googleRouteApi;
  private final MapProperties.GoogleConfig config;

  private final List<String> poiTypes = List.of("airport", "natural_feature", "park", "point_of_interest");

  public GoogleGeoService(GoogleApi googleApi, GoogleRouteApi googleRouteApi, MapProperties.GoogleConfig config) {
    this.googleApi = googleApi;
    this.googleRouteApi = googleRouteApi;
    this.config = config;
  }

  @Override
  public Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request) {
    LocationPoint point = new LocationPoint(request.getLongitude(), request.getLatitude());
    // Google地图API的经纬度参数顺序为：纬度,经度
    String location = point.toLatitudeLongitude();
    String language = MapUtils.googleLanguage(request.getLanguage());
    return googleApi.reverseGeoCode(point.toLatitudeLongitude(), language).mapNotNull(res -> {
      String status = res.getStatus();
      List<GoogleGeoLocation> results = res.getResults();
      if ("OK".equals(status) && results != null && !results.isEmpty()) {
        GoogleGeoLocation detail = results.getFirst();
        GeoAddress address = new GeoAddress();
        address.setGeocode(point.toLatitudeLongitude());
        address.setAddress(detail.getFormattedAddress());
        List<MapPoi> pois = new ArrayList<>();
        Optional.ofNullable(detail.getAddressComponents())
          .ifPresent(components ->
            components.forEach(component -> {
              List<String> types = component.getTypes();
              if (types != null && !types.isEmpty()) {
                types.forEach(type -> {
                  switch (type) {
                    case "country" -> address.setCountry(component.getLongName());
                    case "administrative_area_level_1" -> address.setProvince(component.getLongName());
                    case "locality" -> address.setCity(component.getLongName());
                    case "sublocality" -> address.setDistrict(component.getLongName());
                    case "route" -> address.setStreet(component.getLongName());
                    case "street_number" -> address.setStreetNumber(component.getLongName());
                    case "airport" -> pois.add(addressToPoi(component));
                    case "natural_feature" -> pois.add(addressToPoi(component));
                    case "park" -> pois.add(addressToPoi(component));
                    case "point_of_interest" -> pois.add(addressToPoi(component));
                    default -> log.debug("skip type={}", type);
                  }
                });
              }
            }));
        if (address.getProvince() != null && address.getCity() == null) {
          address.setCity(address.getProvince());
        }
        address.setPois(pois.stream().collect(distinctCollector()));
        log.info("Google地图API查询地址成功，location={}, address={}", location, address);
        return address;
      } else {
        log.warn("Google地图API查询地址失败，status={}, message={}, location={}", status, res.getMessage(), location);
      }
      return null;
    });
  }

  @NotNull
  private static Collector<MapPoi, Object, ArrayList<MapPoi>> distinctCollector() {
    Function<MapPoi, String> compare = p -> p.getName() + ";" + p.getAddress();
    return Collectors.collectingAndThen(Collectors.toCollection(() ->
      new TreeSet<>(Comparator.comparing(compare))), ArrayList::new);
  }

  private MapPoi addressToPoi(GoogleAddressComponent component) {
    MapPoi poi = new MapPoi();
    poi.setProvider(MAP_PROVIDER_GOOGLE);
    poi.setName(component.getShortName());
    poi.setAddress(component.getLongName());
    return poi;
  }

  @Override
  public Mono<GeoAddress> queryGeoAddress(GeoEncodeRequest request) {
    String address = request.getAddress();
    String city = request.getCity();
    return googleApi.geoCode(address, city, null)
      .mapNotNull(res -> {
        String status = res.getStatus();
        List<GoogleGeoLocation> results = res.getResults();
        if ("OK".equals(status) && results != null && !results.isEmpty()) {
          return googleGeoLocationToGeoAddress(address, results);
        } else {
          log.warn("GoogleGeoCode解析失败, address: {}, status: {}, message: {}", address, status, res.getMessage());
          return null;
        }
      });
  }


  @NotNull
  private GeoAddress googleGeoLocationToGeoAddress(String address, List<GoogleGeoLocation> results) {
    GoogleGeoLocation rs = results.getFirst();
    LocationPoint location = rs.getGeometry().getLocation();
    log.info("GoogleGeoCode解析成功, address: {}, location: ({})", address, location);
    List<GoogleAddressComponent> components = Optional.ofNullable(rs.getAddressComponents())
      .orElseGet(Collections::emptyList);
    GeoAddress result = new GeoAddress();
    result.setAddress(address);
    result.setGeocode(location.toLatitudeLongitude());
    result.setCountry(addressComponent(components, "country"));
    result.setProvince(addressComponent(components, "administrative_area_level_1"));
    result.setCity(addressComponent(components, "administrative_area_level_1"));
    result.setDistrict(addressComponent(components, "administrative_area_level_2"));
    result.setStreet(addressComponent(components, "street_address"));
    result.setStreetNumber(addressComponent(components, "plus_code"));
    result.setAdcode(addressComponent(components, "postal_code"));
    result.setPois(components.stream()
      .filter(e -> Optional.ofNullable(e.getTypes())
        .orElseGet(List::of)
        .stream().anyMatch(poiTypes::contains))
      .map(this::addressToPoi)
      .collect(distinctCollector()));
    return result;
  }

  private String addressComponent(List<GoogleAddressComponent> components, String type) {
    return components.stream()
      .filter(e -> Optional.ofNullable(e.getTypes())
        .orElseGet(List::of)
        .stream().anyMatch(s -> s.equals(type)))
      .findFirst()
      .map(GoogleAddressComponent::getLongName)
      .orElse(null);
  }

  @Override
  public Mono<LocationPoint> queryLocationPoint(String address, String city) {
    return queryGeoAddress(new GeoEncodeRequest(address, city))
      .map(e -> LocationPoint.parseLatitudeLongitude(e.getGeocode()));
  }

  @Override
  public Mono<List<PointDistance>> queryDistance(List<LocationPoint> origins, List<LocationPoint> destinations) {
    return Mono.error(new EgressAppException("not support"));
  }

  @Override
  public Mono<PointDistance> queryDriving(DrivingRouteRequest request) {
    GoogleGeoCode origin = request.getFrom().toGoogleGeoCode();
    GoogleGeoCode destination = request.getTo().toGoogleGeoCode();
    List<LocationPoint> points = request.getWaypoints();
    List<GoogleWayPoint> waypoints = null;
    if (points != null && !points.isEmpty()) {
      waypoints = points.stream().map(x -> new GoogleWayPoint(new GoogleLocation(x.toGoogleGeoCode()))).toList();
    }
    GoogleRouteRequest routeRequest = GoogleRouteRequest
      .builder()
      .origin(new GoogleWayPoint(new GoogleLocation(origin)))
      .destination(new GoogleWayPoint(new GoogleLocation(destination)))
      .intermediates(waypoints)
      .build();

    return googleRouteApi.driving(routeRequest, request.getLanguage()).mapNotNull(res -> {
      List<GoogleRoute> routes = res.getRoutes();
      if (routes != null && !routes.isEmpty()) {
        GoogleRoute route = routes.getFirst();
        PointDistance distance = new PointDistance(route.getDistance(), route.toDurationInSeconds());
        log.info("谷歌地图API驾车规划成功，request={}, results={}", request, distance);
        return distance;
      }
      log.warn("谷歌地图API驾车规划失败，request={}", request);
      return null;
    });
  }

  @Override
  public Mono<PoiResponse> queryPoiAround(PoiSearchRequest request) {
    String loc = new LocationPoint(request.getLongitude(), request.getLatitude()).toLatitudeLongitude();
    int radius = request.getRadius();
    String query = MapUtils.join(" ", request.getKeywords());
    String types = MapUtils.join("|", request.getTypes());
    String pageToken = request.getPageToken();
    String language = MapUtils.googleLanguage(request.getLanguage());
    return googleApi.poiAround(loc, radius, query, types, pageToken, language).mapNotNull(res -> {
      String status = res.getStatus();
      List<GooglePoiLocation> data = res.getResults();
      if ("OK".equals(status) && data != null && !data.isEmpty()) {
        List<MapPoi> results = data.stream().map(poi -> {
          MapPoi.MapPoiBuilder builder = MapPoi.builder();
          builder
            .provider(Constants.MAP_PROVIDER_GOOGLE)
            .id(poi.getId())
            .name(poi.getName())
            .location(poi.getGeometry().getLocation())
            .address(poi.getArea())
            .type(MapUtils.join(";", poi.getTypes()));
          return builder.build();
        }).toList();
        log.info("谷歌地图API查询POI成功，status={}, location={}, result size={}", status, loc, results.size());
        return PoiResponse.builder().nextPageToken(null).results(results).build();
      }
      log.warn("谷歌地图API查询POI失败，status={}, message={}, location={}, rs:{}", status, res.getMessage(), loc, res);
      return null;
    });
  }

  @Override
  public Mono<IpLocation> queryIpLocation(String ip, String language) {
    return Mono.error(new EgressAppException("google not support query ip location"));
  }

  @Override
  public List<String> supports() {
    return config.getSupports();
  }

  @Override
  public GeoProvider provider() {
    return config;
  }

  @Override
  public int getWeight() {
    return config.getWeight();
  }
}
