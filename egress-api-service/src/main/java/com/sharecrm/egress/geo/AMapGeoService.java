package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.AMapApi;
import com.sharecrm.egress.api.AMapErrorCode;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.AMapDistanceResult;
import com.sharecrm.egress.entity.AMapGeoAddress;
import com.sharecrm.egress.entity.AMapGeoLocation;
import com.sharecrm.egress.entity.AMapGeoResult;
import com.sharecrm.egress.entity.AMapReverseGeoCode;
import com.sharecrm.egress.entity.AMapRoute;
import com.sharecrm.egress.entity.AmapPoi;
import com.sharecrm.egress.entity.CoordinateConvertRequest;
import com.sharecrm.egress.entity.CoordinateConvertResponse;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sharecrm.egress.api.AMapApi.convertEmptyToNull;
import static com.sharecrm.egress.utils.MapUtils.countryChina;

/**
 * 高德GEO服务接口
 */
@Slf4j
public class AMapGeoService implements GeoAdapter {

  private final AMapApi amapApi;
  private final MapProperties.AMapConfig config;

  public AMapGeoService(AMapApi amapApi, MapProperties.AMapConfig config) {
    this.amapApi = amapApi;
    this.config = config;
  }

  @Override
  public Mono<GeoAddress> queryReverseGeoAddress(ReverseGeoRequest request) {
    LocationPoint point = new LocationPoint(request.getLongitude(), request.getLatitude());
    // 高德地图API的经纬度参数顺序为：经度,纬度
    String location = point.toLongitudeLatitude();
    Integer radius = Objects.requireNonNullElse(request.getRadius(), config.getRadius());
    String language = MapUtils.amapLanguage(request.getLanguage());
    String poiTypes = StringUtils.join(request.getPoiTypes(), "|");
    return amapApi.reverseGeoCode(location, radius, poiTypes, language).mapNotNull(res -> {
      int status = res.getStatus();
      int code = res.getInfoCode();
      AMapReverseGeoCode result = res.getResult();
      if (MapUtils.isAmapHasData(status, code, result)) {
        GeoAddress address = new GeoAddress();
        address.setGeocode(point.toLatitudeLongitude());
        String formattedAddress = result.getFormattedAddress();
        AMapGeoAddress detail = result.getComponent();
        address.setAddress(formattedAddress);
        address.setCountry(detail.getCountry());
        address.setProvince(detail.getProvince());
        address.setCity(isEmptyCity(detail.getCity()) ? detail.getProvince() : detail.getCity());
        address.setCityCode(detail.getCityCode());
        address.setDistrict(detail.getDistrict());
        address.setTown(detail.getTown());
        address.setTownCode(detail.getTownCode());
        address.setAdcode(detail.getAdcode());
        Optional.ofNullable(detail.getStreet())
          .ifPresent(street -> {
            address.setStreet(street.getStreet());
            address.setStreetNumber(street.getNumber());
          });

        address.setPois(Optional.ofNullable(result.getPois()).orElseGet(List::of)
          .stream()
          .map(this::amapPoiToApiPoi)
          .toList());
        log.info("amap ReverseGeoCode success, location={}, address={}", location, formattedAddress);
        return address;
      }
      String message = MapUtils.amapErrorMessage(res.getInfo(), code);
      log.warn("amap ReverseGeoCode failed，status={}, infoCode={}, message={}, location={}", status, code, message, location);
      tryLock(code, SUPPORT_REVERSE_GEO);
      return null;
    });
  }

  private void tryLock(int infoCode, String... api) {
    // 天配额超限，限制访问
    if (infoCode == 10003 || infoCode == 10044 || infoCode == 10045) {
      for (String s : api) {
        GeoProviderLock.lock(config.getId(), s);
      }
    }
  }

  @Override
  public Mono<GeoAddress> queryGeoAddress(GeoEncodeRequest request) {
    String address = request.getAddress();
    String city = request.getCity();
    String language = MapUtils.amapLanguage(request.getLanguage());
    String country = config.isCountryEnabled() ? request.getCountry() : null;
    return amapApi.geoCode(address, city, country, language).mapNotNull(res -> {
      int status = res.getStatus();
      int code = res.getInfoCode();
      if (MapUtils.isAmapHasData(status, code, res.getCount(), "")) {
        return geoRestultToGeoAddress(address, res);
      }
      String message = MapUtils.amapErrorMessage(res.getInfo(), code);
      log.warn("高德GeoCode解析失败, city: {}, address: {}, code: {}, message: {}", city, address, code, message);
      tryLock(code, SUPPORT_GEO_ADDRESS, SUPPORT_LOCATION_POINT);
      return null;
    });
  }

  private GeoAddress geoRestultToGeoAddress(String address, AMapGeoResult res) {
    AMapGeoLocation geo = res.getGeoCodes()[0];
    String location = geo.getLocation();
    //我们业务都依赖经纬度，高德有时候返回的没有经纬度，过滤掉
    if (StringUtils.isEmpty(location)) {
      log.warn("高德GeoCode解析失败, location is empty, address: {}, res: {}", address, res);
      return null;
    }
    log.debug("高德GeoCode解析成功, detail: {}, address: {}, location: ({})", geo, address, location);
    LocationPoint point = LocationPoint.parseLongitudeLatitude(location);
    GeoAddress result = new GeoAddress();
    // Geocode 是 LatitudeLongitude
    result.setGeocode(point.toLatitudeLongitude());
    result.setAddress(address);
    result.setCountry(geo.getCountry());
    result.setProvince(geo.getProvince());
    result.setCity(geo.getCity());
    result.setCityCode(geo.getCityCode());
    result.setDistrict(geo.getDistrict());
    result.setStreet(geo.getStreet());
    result.setStreetNumber(geo.getStreetNumber());
    result.setAdcode(geo.getAdcode());
    result.setTown(ArrayUtils.get(geo.getTownship(), 0));
    return result;
  }

  @Override
  public Mono<LocationPoint> queryLocationPoint(String address, String city) {
    return queryGeoAddress(new GeoEncodeRequest(address, city))
      .map(e -> LocationPoint.parseLatitudeLongitude(e.getGeocode()));
  }

  @Override
  public Mono<List<PointDistance>> queryDistance(List<LocationPoint> origins, List<LocationPoint> destinations) {
    String from = origins.stream().map(LocationPoint::toLongitudeLatitude).collect(Collectors.joining("|"));
    BiFunction<Tuple2<String, String>, AMapDistanceResult, List<PointDistance>> mapper = (tuple, res) -> {
      int status = res.getStatus();
      int code = res.getInfoCode();
      List<PointDistance> results = res.getResults();
      if (MapUtils.isAmapHasData(status, code, results)) {
        log.info("高德地图API查询成功, from={}, to={}, distance={}", tuple.getT1(), tuple.getT2(), results);
        return results;
      }

      String message = MapUtils.amapErrorMessage(res.getInfo(), code);
      log.warn("高德地图API查询失败, from={}, to={}, status={}, code={}, message={}", tuple.getT1(), tuple.getT2(), status, code, message);
      tryLock(code, SUPPORT_DISTANCE);
      return List.of();
    };
    if (destinations.size() == 1) {
      String dst = destinations.getFirst().toLongitudeLatitude();
      return amapApi.distance(from, dst).map(res -> mapper.apply(Tuples.of(from, dst), res));
    } else {
      List<Tuple2<LocationPoint, LocationPoint>> tuples = new ArrayList<>();
      for (LocationPoint src : origins) {
        for (LocationPoint dst : destinations) {
          tuples.add(Tuples.of(src, dst));
        }
      }
      // 高德地图的API，目标地址只能传一个
      return Flux.fromIterable(tuples).flatMapSequential(pair -> {
        String src = pair.getT1().toLongitudeLatitude();
        String dst = pair.getT2().toLongitudeLatitude();
        return amapApi.distance(src, dst).flatMapIterable(res -> mapper.apply(Tuples.of(src, dst), res));
      }).collectList();
    }
  }

  @Override
  public Mono<PointDistance> queryDriving(DrivingRouteRequest request) {
    String origin = request.getFrom().toLongitudeLatitude();
    String destination = request.getTo().toLongitudeLatitude();
    String waypoints = null;
    List<LocationPoint> points = request.getWaypoints();
    if (points != null && !points.isEmpty()) {
      waypoints = points.stream().map(LocationPoint::toLongitudeLatitude).collect(Collectors.joining(";"));
    }
    return amapApi.driving(origin, destination, waypoints).mapNotNull(res -> {
      int status = res.getStatus();
      int code = res.getInfoCode();
      int count = res.getCount();
      AMapRoute data = res.getRoute();
      if (MapUtils.isAmapHasData(status, code, count, data)) {
        List<PointDistance> results = data.getPaths();
        log.debug("高德地图API驾车规划成功，status={}, infoCode={}, message={}, request={}, count={}, results={}", status, code, res.getInfo(), request, count,
          results);
        if (!results.isEmpty()) {
          return results.getFirst();
        }
      }
      String message = MapUtils.amapErrorMessage(res.getInfo(), code);
      log.warn("高德地图API驾车规划失败，status={}, infoCode={}, message={}, request={}", status, code, message, request);
      tryLock(code, SUPPORT_DRIVING);
      return null;
    });
  }

  @Override
  public Mono<PoiResponse> queryPoiAround(PoiSearchRequest request) {
    int radius = request.getRadius();
    LocationPoint point = new LocationPoint(request.getLongitude(), request.getLatitude());
    // 高德地图API的经纬度参数顺序为：经度,纬度
    String location = point.toLongitudeLatitude();

    String keywords = MapUtils.join("|", request.getKeywords());
    String types = MapUtils.join("|", request.getTypes());
    int offset = request.getPageSize();
    int page = request.getPageNum();
    String language = MapUtils.amapLanguage(request.getLanguage());
    return amapApi.poiAround(location, radius, keywords, types, offset, page, language).mapNotNull(res -> {
      int status = res.getStatus();
      int code = res.getInfoCode();
      int count = res.getCount();
      List<AmapPoi> data = res.getPois();

      if (!MapUtils.isAmapSuccess(status, code)) {
        String message = Optional.ofNullable(res.getInfo()).orElseGet(() -> AMapErrorCode.getMessage(code));
        log.warn("高德地图API查询POI失败，status={}, infoCode={}, message={}, location={}, rs:{}", status, code, message, location, res);
        tryLock(code, SUPPORT_POI_AROUND);
        return null;
      }

      if (MapUtils.isAmapHasData(status, code, count, data)) {
        List<MapPoi> results = data.stream()
          .map(this::amapPoiToApiPoi)
          .toList();
        log.info("高德地图API查询POI成功，status={}, infoCode={}, message={}, location={}, count={}", status, code, res.getInfo(), location, count);
        return PoiResponse.builder().count(count).results(results).build();
      }
      String message = MapUtils.amapErrorMessage(res.getInfo(), code);
      log.warn("高德地图API查询POI失败，status={}, infoCode={}, message={}, location={}, rs:{}", status, code, message, location, res);
      tryLock(code, SUPPORT_POI_AROUND);
      return null;
    });
  }

  private MapPoi amapPoiToApiPoi(AmapPoi poi) {
    return MapPoi
      .builder()
      .provider(Constants.MAP_PROVIDER_AMAP)
      .id(poi.getId())
      .name(poi.getName())
      .location(LocationPoint.parseLongitudeLatitude(poi.getLocation()))
      .province(poi.getProvince())
      .city(poi.getCity())
      .district(poi.getDistrict())
      .distance((int) NumberUtils.toDouble(poi.getDistance()))
      .telephone(convertEmptyToNull(poi.getTelephone()))
      .alias(poi.getAlias())
      .tag(poi.getTag())
      .type(poi.getType())
      .address(poi.getAddress())
      .adcode(poi.getAdcode())
      .photoUrl(CollectionUtils.isEmpty(poi.getPhotos()) ? null : poi.getPhotos().getFirst().getUrl())
      .build();
  }

  @Override
  public Mono<IpLocation> queryIpLocation(String ip, String language) {
    return amapApi.ipLocation(ip, MapUtils.amapLanguage(language)).mapNotNull(res -> {
      String province = convertEmptyToNull(res.getProvince());
      String city = convertEmptyToNull(res.getCity());
      int status = res.getStatus();
      int code = res.getInfoCode();
      //有时候返回了成功，但是实际上值都是空的
      if (status == 1 && code == 10000 && !StringUtils.isAllBlank(province, city)) {
        log.debug("AMap IP定位成功, IP: {},  province: {}, city: {}", ip, province, city);
        return IpLocation.builder().ip(ip)
          .country(countryChina(language))
          .province(province)
          .city(city)
          .language(language)
          .provider(provider().getType())
          .build();
      }
      String message = MapUtils.amapErrorMessage(res.getInfo(), code);
      log.info("AMap IP定位为空, code: {}, message: {}, IP: {}, response: {}", code, message, ip, res);
      tryLock(code, SUPPORT_IP_LOCATION);
      return null;
    });
  }

  @Override
  public Mono<CoordinateConvertResponse> coordinateConvert(CoordinateConvertRequest request) {
    LocationPoint point = new LocationPoint(request.getLongitude(), request.getLatitude());
    // 高德地图API的经纬度参数顺序为：经度,纬度
    String location = point.toLongitudeLatitude();
    return amapApi.coordinate(location, request.getSource()).mapNotNull(res -> {
      int status = res.getStatus();
      int code = res.getInfoCode();
      //有时候返回了成功，但是实际上值都是空的
      String locations = res.getLocations();
      if (status == 1 && code == 10000 && StringUtils.isNotBlank(locations)) {
        log.debug("AMap CoordinateConvert成功, request: {},  response: {}", request, res);
        String[] split = locations.split(";");
        String[] lngLat = split[0].split(",", 2);
        CoordinateConvertResponse response = new CoordinateConvertResponse();
        response.setLongitude(Double.parseDouble(lngLat[0]));
        response.setLatitude(Double.parseDouble(lngLat[1]));
        return response;
      }
      String message = MapUtils.amapErrorMessage(res.getInfo(), code);
      log.warn("AMap CoordinateConvert失败, code: {}, message: {}, request: {}", code, message, request);
      tryLock(code, SUPPORT_COORDINATE_CONVERT);
      return null;
    });
  }

  private boolean isEmptyCity(String city) {
    return Objects.isNull(city) || city.isEmpty() || "[]".equals(city);
  }

  @Override
  public List<String> supports() {
    return config.getSupports();
  }

  @Override
  public GeoProvider provider() {
    return config;
  }

  @Override
  public int getWeight() {
    return config.getWeight();
  }
}
