package com.sharecrm.egress.geo;

import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 缓存定时过期
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "sharecrm.api.map.geo.es.task.enabled", havingValue = "true", matchIfMissing = true)
public class GeoEsCacheTask {

  private final GeoEsCacheService service;

  public GeoEsCacheTask(GeoEsCacheService service) {
    this.service = service;
  }

  /**
   * 执行清理动作，凌晨1点清理
   */
  @Scheduled(cron = "${sharecrm.api.map.geo.es.task.cron:0 0 1 * * ?}")
  @SchedulerLock(name = "GeoEsCacheTask", lockAtLeastFor = "10m", lockAtMostFor = "20m")
  public void doExpire() {
    log.info("start geo es cache task.");
    service.clearGeoAddressExpireData();
  }
  
  /**
   * 执行清理动作，凌晨2点清理
   */
  @Scheduled(cron = "${sharecrm.api.map.ip.location.es.task.cron:0 0 2 * * ?}")
  @SchedulerLock(name = "IpLocationEsCacheTask", lockAtLeastFor = "10m", lockAtMostFor = "20m")
  public void clearIpLocationExpireData() {
    log.info("start ip location es cache task.");
    service.clearIpLocationExpireData();
  }

}
