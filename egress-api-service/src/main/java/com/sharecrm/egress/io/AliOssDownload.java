package com.sharecrm.egress.io;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.DownloadFileRequest;
import com.aliyun.oss.model.DownloadFileResult;
import com.aliyun.oss.model.OSSSymlink;
import com.aliyun.oss.model.ObjectMetadata;
import com.google.common.io.Closer;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;

@Slf4j
public class AliOssDownload {
  private AliOssDownload() {
  }

  public static void downloadFromAliOss(File dstFile, AliOssConfig oss, ProxyConfig proxy) throws IOException {
    try (Closer closer = Closer.create()) {
      ClientBuilderConfiguration ossConfig = new ClientBuilderConfiguration();
      ossConfig.setRequestTimeout(60000);
      ossConfig.setRedirectEnable(true);
      // 设定 http 代理信息
      if (proxy != null) {
        configHttpProxy(ossConfig, proxy);
      }
      OSS client = new OSSClientBuilder().build(oss.endpoint(), oss.accessKeyId(), oss.secretAccessKey(), ossConfig);
      closer.register(client::shutdown);
      // 解析文件元数据
      var meta = client.getObjectMetadata(oss.bucketName(), oss.objectName());
      var objectType = meta.getObjectType();
      var target = oss.objectName();
      // 解析文件软链接
      if ("Symlink".equals(objectType)) {
        OSSSymlink symlink = client.getSymlink(oss.bucketName(), oss.objectName());
        target = symlink.getTarget();
      }
      // 下载真实的文件
      DownloadFileRequest request = new DownloadFileRequest(oss.bucketName(), target);
      // Sets the local file to download to
      request.setDownloadFile(dstFile.getAbsolutePath());
      // Sets the concurrent task thread count 5. By default, it's 1.
      request.setTaskNum(5);
      // Sets the part size, by default it's 100K.
      request.setPartSize(1024 * 500L);
      // Enable checkpoint. By default it's false.
      request.setEnableCheckpoint(true);
      try {
        DownloadFileResult result = client.downloadFile(request);
        ObjectMetadata objectMetadata = result.getObjectMetadata();
        DecimalFormat format = new DecimalFormat("#,###");
        log.info("download from aliOSS, bucket: {}, file: {}, bytes: {}", oss.bucketName(), target, format.format(dstFile.length()));
        objectMetadata.getRawMetadata().forEach((k, v) -> log.info("\t{}: {}", k, v));
      } catch (OSSException oe) {
        log.error("Caught an OSSException, which means your request made it to OSS, but was rejected with an error response for some reason.", oe);
      } catch (ClientException ce) {
        log.error("Caught an ClientException, which means the client encountered a serious internal problem while trying to communicate with OSS, " +
                    "such as not being able to access the network.", ce);
      } catch (Throwable e) {
        log.error("download file error", e);
      }
    }
  }

  private static void configHttpProxy(ClientBuilderConfiguration ossConfig, ProxyConfig proxy) {
    if (proxy.host() != null) {
      ossConfig.setProxyHost(proxy.host());
      ossConfig.setProxyPort(proxy.port());
      if (proxy.username() != null) {
        ossConfig.setProxyUsername(proxy.username());
        ossConfig.setProxyPassword(proxy.password());
      }
    }
  }

}
