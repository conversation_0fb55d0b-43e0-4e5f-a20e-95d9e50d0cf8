package com.sharecrm.egress.gateway;

import com.sharecrm.egress.config.ConditionalOnGatewayMapEnabled;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.geo.GeoAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自动为华为地图代理服务增加key和sig签名计算
 */
@Slf4j
@Component
@ConditionalOnGatewayMapEnabled
public class HuaweiWebApiSigGatewayFilterFactory extends HuaweiSigGatewayFilterFactory {

  private final AtomicInteger counter = new AtomicInteger(0);

  public HuaweiWebApiSigGatewayFilterFactory(MapProperties properties) {
    super(properties);
  }

  @Override
  protected boolean filterSupports(MapProperties.HuaweiConfig e) {
    return e.getSupports().contains(GeoAdapter.SUPPORT_GATEWAY_WEB_API);
  }

  @Override
  protected AtomicInteger counter() {
    return counter;
  }

}