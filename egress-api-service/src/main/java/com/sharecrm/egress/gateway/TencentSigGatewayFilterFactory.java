package com.sharecrm.egress.gateway;

import com.sharecrm.egress.api.HttpExchangeUtils;
import com.sharecrm.egress.config.ConditionalOnGatewayMapEnabled;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.geo.GeoAdapter;
import com.sharecrm.egress.utils.EgressUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自动为腾讯地图代理服务增加key和sig签名计算
 */
@Slf4j
@Component
@ConditionalOnGatewayMapEnabled
public class TencentSigGatewayFilterFactory extends AbstractSigGatewayFilterFactory {

  private final MapProperties properties;

  private final AtomicInteger counter = new AtomicInteger(0);

  public TencentSigGatewayFilterFactory(MapProperties properties) {
    this.properties = properties;
    this.preStarter();
  }

  @Override
  protected String appendQueryKeyAndSign(URI uri) {
    StringBuilder query = initQuery(uri);
    MapProperties.TencentConfig cfg = select();
    query.append("key=").append(cfg.getKey());
    String sig = cfg.getSig();
    if (StringUtils.isNotBlank(sig)) {
      URI sigUri = UriComponentsBuilder.fromUri(uri).replaceQuery(query.toString()).build(true).toUri();
      String sigValue = HttpExchangeUtils.tencentSignParam(sig, sigUri);
      query.append("&sig=").append(sigValue);
    }
    return query.toString();
  }

  protected void preStarter() {
    if (CollectionUtils.isEmpty(mapConfigs())) {
      log.info("tencent gateway config is empty, please check your config.");
    }
  }

  private MapProperties.TencentConfig select() {
    return EgressUtils.roundRobin(mapConfigs(), counter);
  }

  @NotNull
  private List<MapProperties.TencentConfig> mapConfigs() {
    return properties.getTencent().values()
      .stream()
      .filter(MapProperties.TencentConfig::isEnabled)
      .filter(e -> e.getSupports().contains(GeoAdapter.SUPPORT_GATEWAY_REST))
      .toList();
  }
}