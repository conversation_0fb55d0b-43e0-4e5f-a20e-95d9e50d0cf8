package com.sharecrm.egress.gateway;

import com.sharecrm.egress.config.ConditionalOnGatewayMapEnabled;
import com.sharecrm.egress.service.ActiveSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 通过cookie校验是否为纷享合法登录用户
 */
@Slf4j
@Component
@ConditionalOnGatewayMapEnabled
public class CookieCheckGatewayFilterFactory extends AbstractGatewayFilterFactory<AbstractGatewayFilterFactory.NameConfig> {

  private final ActiveSessionService activeSessionService;

  public CookieCheckGatewayFilterFactory(ActiveSessionService activeSessionService) {
    super(AbstractGatewayFilterFactory.NameConfig.class);
    this.activeSessionService = activeSessionService;
  }

  @Override
  public List<String> shortcutFieldOrder() {
    return List.of("name");
  }

  @Override
  public GatewayFilter apply(AbstractGatewayFilterFactory.NameConfig config) {
    return (exchange, chain) ->
      activeSessionService.checkCookie(exchange)
        .flatMap(success -> {
          if (success) {
            return chain.filter(exchange);
          }
          log.warn("cookie check failed, will return 401.");
          ServerWebExchangeUtils.setResponseStatus(exchange, HttpStatus.UNAUTHORIZED);
          return exchange.getResponse().setComplete();
        });
  }

}
