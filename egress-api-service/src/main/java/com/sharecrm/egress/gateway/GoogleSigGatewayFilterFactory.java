package com.sharecrm.egress.gateway;

import com.sharecrm.egress.config.ConditionalOnGatewayMapEnabled;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.geo.GeoAdapter;
import com.sharecrm.egress.utils.EgressUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自动为Google地图代理服务增加key和sig签名计算
 */
@Slf4j
@Component
@ConditionalOnGatewayMapEnabled
public class GoogleSigGatewayFilterFactory extends AbstractSigGatewayFilterFactory {

  private final MapProperties properties;

  private final AtomicInteger counter = new AtomicInteger(0);

  public GoogleSigGatewayFilterFactory(MapProperties properties) {
    this.properties = properties;
    this.preStarter();
  }

  @Override
  protected String appendQueryKeyAndSign(URI uri) {
    MapProperties.GoogleConfig cfg = EgressUtils.roundRobin(mapConfigs(), counter);
    StringBuilder query = initQuery(uri);
    query.append("key=").append(cfg.getKey());
    return query.toString();
  }

  @NotNull
  private List<MapProperties.GoogleConfig> mapConfigs() {
    return properties.getGoogle().values()
      .stream()
      .filter(MapProperties.GoogleConfig::isEnabled)
      .filter(e -> e.getSupports().contains(GeoAdapter.SUPPORT_GATEWAY_REST))
      .toList();
  }

  private void preStarter() {
    if (CollectionUtils.isEmpty(mapConfigs())) {
      log.warn("google gateway config is empty, please check your config.");
    }
  }

}