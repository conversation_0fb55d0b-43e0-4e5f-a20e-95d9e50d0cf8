package com.sharecrm.egress.gateway;

import com.sharecrm.egress.utils.NetUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.gateway.config.HttpClientProperties;
import org.springframework.cloud.gateway.filter.NettyRoutingFilter;
import org.springframework.cloud.gateway.filter.headers.HttpHeadersFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;

import java.net.URI;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * 在原有NettyRoutingFilter的基础上，允许定制Http proxy，不同路由使用不同的代理，无定制的使用默认代理
 * 此功能Spring社区正在讨论是否支持，如果官方支持了，我们就不用自己写了
 */
@Slf4j
@Component
@ConditionalOnProperty(name = {"spring.cloud.gateway.enabled"}, matchIfMissing = true)
public class ProxyNettyRoutingFilter extends NettyRoutingFilter {

  public ProxyNettyRoutingFilter(HttpClient httpClient, ObjectProvider<List<HttpHeadersFilter>> headersFiltersProvider, HttpClientProperties properties) {
    super(httpClient, headersFiltersProvider, properties);
  }

  @Override
  protected HttpClient getHttpClient(Route route, ServerWebExchange exchange) {
    //自带的Http client可能配置了默认的代理
    HttpClient client = super.getHttpClient(route, exchange);
    Object proxyUrl = route.getMetadata().get("http-proxy");
    if (Objects.isNull(proxyUrl) || StringUtils.isBlank(proxyUrl.toString())) {
      return client;
    }
    URI uri = NetUtils.uri(proxyUrl.toString());
    return client.proxy(p -> p.type(ProxyProvider.Proxy.HTTP)
      .host(uri.getHost())
      .port(uri.getPort())
      .connectTimeoutMillis(Duration.ofSeconds(5).toMillis()));
  }

  @Override
  public int getOrder() {
    //让此filter先生效
    return NettyRoutingFilter.ORDER - 1;
  }

}
