package com.sharecrm.egress.gateway;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

/**
 * 自动为地图代理服务增加key和sig签名计算
 */
@Slf4j
public abstract class AbstractSigGatewayFilterFactory extends AbstractGatewayFilterFactory<Object> {

  @Override
  public GatewayFilter apply(Object config) {
    return (exchange, chain) -> {
      URI uri = exchange.getRequest().getURI();
      String query = appendQueryKeyAndSign(uri);
      boolean encoded = ServerWebExchangeUtils.containsEncodedParts(uri);
      try {
        URI newUri = UriComponentsBuilder.fromUri(uri).replaceQuery(query).build(encoded).toUri();
        ServerHttpRequest request = exchange.getRequest().mutate().uri(newUri).build();
        return chain.filter(exchange.mutate().request(request).build());
      } catch (RuntimeException e) {
        throw new IllegalStateException("Invalid URI query: \"" + query + "\"");
      }
    };
  }

  protected StringBuilder initQuery(URI uri) {
    String originalQuery = uri.getRawQuery();
    if (StringUtils.isBlank(originalQuery)) {
      return new StringBuilder();
    }
    StringBuilder query = new StringBuilder(originalQuery);
    if (originalQuery.charAt(originalQuery.length() - 1) != '&') {
      query.append('&');
    }
    return query;
  }

  protected abstract String appendQueryKeyAndSign(URI uri);

}