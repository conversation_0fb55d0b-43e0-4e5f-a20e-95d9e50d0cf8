package com.sharecrm.egress.gateway;

import com.sharecrm.egress.config.ConditionalOnGatewayMapEnabled;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.geo.GeoAdapter;
import com.sharecrm.egress.utils.EgressUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自动为华为地图代理服务增加key和sig签名计算
 */
@Slf4j
@Component
@ConditionalOnGatewayMapEnabled
public class HuaweiSigGatewayFilterFactory extends AbstractSigGatewayFilterFactory {

  private final MapProperties properties;

  private final AtomicInteger counter = new AtomicInteger(0);

  public HuaweiSigGatewayFilterFactory(MapProperties properties) {
    this.properties = properties;
    this.preStarter();
  }

  @Override
  protected String appendQueryKeyAndSign(URI uri) {
    MapProperties.HuaweiConfig cfg = EgressUtils.roundRobin(mapConfigs(), counter());
    StringBuilder query = initQuery(uri);
    query.append("key=").append(cfg.getKey());
    return query.toString();
  }

  @NotNull
  private List<MapProperties.HuaweiConfig> mapConfigs() {
    return properties.getHuawei().values()
      .stream()
      .filter(MapProperties.HuaweiConfig::isEnabled)
      .filter(this::filterSupports)
      .toList();
  }

  protected AtomicInteger counter() {
    return counter;
  }

  protected boolean filterSupports(MapProperties.HuaweiConfig e) {
    return e.getSupports().contains(GeoAdapter.SUPPORT_GATEWAY_REST);
  }

  protected void preStarter() {
    if (CollectionUtils.isEmpty(mapConfigs())) {
      log.warn("huawei gateway config is empty, please check your config.");
    }
  }

}