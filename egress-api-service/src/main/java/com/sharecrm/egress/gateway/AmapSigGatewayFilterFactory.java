package com.sharecrm.egress.gateway;

import com.sharecrm.egress.api.HttpExchangeUtils;
import com.sharecrm.egress.config.ConditionalOnGatewayMapEnabled;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.geo.GeoAdapter;
import com.sharecrm.egress.utils.EgressUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自动为高德地图代理服务增加key和sig签名计算
 */
@Slf4j
@Component
@ConditionalOnGatewayMapEnabled
public class AmapSigGatewayFilterFactory extends AbstractSigGatewayFilterFactory {

  private final MapProperties properties;

  private final AtomicInteger counter = new AtomicInteger(0);

  public AmapSigGatewayFilterFactory(MapProperties properties) {
    this.properties = properties;
    this.preStarter();
  }

  @Override
  protected String appendQueryKeyAndSign(URI uri) {
    StringBuilder query = initQuery(uri);
    MapProperties.AMapConfig cfg = select();
    query.append("key=").append(cfg.getKey());
    String sig = cfg.getSig();
    if (StringUtils.isNotBlank(sig)) {
      URI sigUri = UriComponentsBuilder.fromUri(uri).replaceQuery(query.toString()).build(true).toUri();
      String sigValue = HttpExchangeUtils.amapSignParam(sig, sigUri);
      query.append("&sig=").append(sigValue);
    }
    return query.toString();
  }

  protected void preStarter() {
    if (CollectionUtils.isEmpty(mapConfigs())) {
      log.warn("amap gateway config is empty, please check your config.");
    }
  }

  protected MapProperties.AMapConfig select() {
    return EgressUtils.roundRobin(mapConfigs(), counter());
  }

  @NotNull
  private List<MapProperties.AMapConfig> mapConfigs() {
    return properties.getAmap().values()
      .stream()
      .filter(MapProperties.AMapConfig::isEnabled)
      .filter(this::filterSupports)
      .toList();
  }

  protected AtomicInteger counter() {
    return counter;
  }

  protected boolean filterSupports(MapProperties.AMapConfig e) {
    return e.getSupports().contains(GeoAdapter.SUPPORT_GATEWAY_REST);
  }

}