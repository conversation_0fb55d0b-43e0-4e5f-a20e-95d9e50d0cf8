package com.sharecrm.egress.gateway;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.web.reactive.WebFluxProperties;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;

/**
 * spring cloud gateway 不支持设置spring.webflux.base-path，解决此Bug
 * See：<a href="https://github.com/spring-cloud/spring-cloud-gateway/issues/1759">...</a>
 */
@Component
@ConditionalOnProperty(name = {"spring.cloud.gateway.enabled"}, matchIfMissing = true)
public class StripBasePathGatewayFilterFactory extends AbstractGatewayFilterFactory<Object> {

  private final WebFluxProperties properties;

  public StripBasePathGatewayFilterFactory(WebFluxProperties properties) {
    this.properties = properties;
  }

  @Override
  public GatewayFilter apply(Object config) {
    return (exchange, chain) -> {
      String basePath = properties.getBasePath();
      if (StringUtils.isEmpty(basePath) || "/".equals(basePath)) {
        return chain.filter(exchange);
      }
      ServerHttpRequest req = exchange.getRequest();
      String path = req.getURI().getRawPath();
      String newPath = path.replaceFirst(basePath, "");
      ServerHttpRequest request = req.mutate().path(newPath).contextPath("/").build();
      return chain.filter(exchange.mutate().request(request).build());
    };
  }
}