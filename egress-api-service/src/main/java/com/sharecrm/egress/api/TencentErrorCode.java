package com.sharecrm.egress.api;

import java.util.HashMap;
import java.util.Map;

/**
 * 腾讯地图API的错误码
 *
 * <AUTHOR>
 * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/status">错误码</a>
 * <p>
 * IgnoreI18nFile ignore i18n
 */
public abstract class TencentErrorCode {
  private static final Map<Integer, String> MAPPINGS = new HashMap<>();

  /*
   * 状态码 说明
   * 0 结果正常
   * ------------ 配额鉴权相关（非系统错误）
   * 110 请求来源未被授权
   * 111 签名验证失败
   * 112 IP未被授权
   * 113 此功能未被授权
   * 120 此key每秒请求量已达到上限
   * 121 此key每日调用量已达到上限
   * 160 sig参数不支持此请求类型
   * 161 sig参数不支持和非object的POST JSON一起使用
   * 190 无效的KEY
   * 199 此key未开启webservice功能
   * ------------ 参数错误（非系统错误）
   * 301 缺少必要字段key
   * 311 key格式错误
   * 300 缺少必要字段
   * 306 缺少参数
   * 310 参数格式错误
   * 320 参数数据类型错误
   * 330 参数长度错误
   * 351 存在不共存的参数
   * 324 get和post中的同一参数值不相同
   * 326 起终点距离过近
   * 327 附近无公交站
   * 328 无可达公交路线
   * 329 无可达火车路线
   * 331 查询条件过长
   * 332 途径点个数超过限制
   * 333 存在无法吸附的坐标点
   * 335 不支持该城市的公交查询
   * 341 缺少keyword（关键词）
   * 344 附近无火车站（公交）
   * 347 查询无结果
   * 348 参数错误
   * 364 是否扩大搜索参数只能为0或1
   * 365 纬度不能超过±90
   * 366 经度不能超过±180
   * 373 起终点距离超长
   * 374 起终点坐标错误
   * 375 局域网IP无法定位
   * 377 提供的起终点无法规划出导航线路
   * 378 提供的起终点无法规划出步行线路
   * 379 提供的起终点无法规划出公交线路
   * 380 坐标类型必须在有坐标的情况下使用
   * 382 IP无法定位
   * 384 提供的起终点无法规划出骑行线路
   * 387 没有对应的POI
   * 393 没有符合条件的数据
   * 394 错误的查询条件
   * 395 传入参数不合法
   * 396 最多支持200个坐标点，且起终点数目乘积最多为625（距离矩阵）
   * 397 一对多最多支持200个坐标点，多对多最多支持25个坐标点且起终点数目乘积最多为625（距离矩阵）
   * 399 查询关键字错误
   * 400 参数无法解码，请对特殊字符在utf8编码下进行urlencode操作
   * 402 提交的数据格式错误，请确认数据是utf8编码，并对特殊字符进行了urlencode处理
   * 403 不被支持的接口转发
   * 404 错误的请求路径
   * 405 请求方法错误，请更正为：GET
   * 406 请求方法错误，请更正为：POST
   * 407 不被支持的请求方法
   * 408 自定义Referer错误
   * ------------ 以下系统错误（服务质量问题）
   * 500 后端超时
   * 510 后端服务无法连接
   * 520 后端服务请求失败
   * 530 后端服务返回数据解析失败
   * 531 后端服务报错
   * 600 其他
   */

  static {
    MAPPINGS.put(0, "请求成功");
    MAPPINGS.put(110, "请求来源未被授权");
    MAPPINGS.put(111, "签名验证失败");
    MAPPINGS.put(112, "IP未被授权");
    MAPPINGS.put(113, "此功能未被授权");
    MAPPINGS.put(120, "此key每秒请求量已达到上限");
    MAPPINGS.put(121, "此key每日调用量已达到上限");
    MAPPINGS.put(160, "sig参数不支持此请求类型");
    MAPPINGS.put(161, "sig参数不支持和非object的POST JSON一起使用");
    MAPPINGS.put(190, "无效的KEY");
    MAPPINGS.put(199, "此key未开启webservice功能");
    MAPPINGS.put(301, "缺少必要字段key");
    MAPPINGS.put(311, "key格式错误");
    MAPPINGS.put(300, "缺少必要字段");
    MAPPINGS.put(306, "缺少参数");
    MAPPINGS.put(310, "参数格式错误");
    MAPPINGS.put(320, "参数数据类型错误");
    MAPPINGS.put(330, "参数长度错误");
    MAPPINGS.put(351, "存在不共存的参数");
    MAPPINGS.put(324, "get和post中的同一参数值不相同");
    MAPPINGS.put(326, "起终点距离过近");
    MAPPINGS.put(327, "附近无公交站");
    MAPPINGS.put(328, "无可达公交路线");
    MAPPINGS.put(329, "无可达火车路线");
    MAPPINGS.put(331, "查询条件过长");
    MAPPINGS.put(332, "途径点个数超过限制");
    MAPPINGS.put(333, "存在无法吸附的坐标点");
    MAPPINGS.put(335, "不支持该城市的公交查询");
    MAPPINGS.put(341, "缺少keyword（关键词）");
    MAPPINGS.put(344, "附近无火车站（公交）");
    MAPPINGS.put(347, "查询无结果");
    MAPPINGS.put(348, "参数错误");
    MAPPINGS.put(364, "是否扩大搜索参数只能为0或1");
    MAPPINGS.put(365, "纬度不能超过±90");
    MAPPINGS.put(366, "经度不能超过±180");
    MAPPINGS.put(373, "起终点距离超长");
    MAPPINGS.put(374, "起终点坐标错误");
    MAPPINGS.put(375, "局域网IP无法定位");
    MAPPINGS.put(377, "提供的起终点无法规划出导航线路");
    MAPPINGS.put(378, "提供的起终点无法规划出步行线路");
    MAPPINGS.put(379, "提供的起终点无法规划出公交线路");
    MAPPINGS.put(380, "坐标类型必须在有坐标的情况下使用");
    MAPPINGS.put(382, "IP无法定位");
    MAPPINGS.put(384, "提供的起终点无法规划出骑行线路");
    MAPPINGS.put(387, "没有对应的POI");
    MAPPINGS.put(393, "没有符合条件的数据");
    MAPPINGS.put(394, "错误的查询条件");
    MAPPINGS.put(395, "传入参数不合法");
    MAPPINGS.put(396, "最多支持200个坐标点，且起终点数目乘积最多为625（距离矩阵）");
    MAPPINGS.put(397, "一对多最多支持200个坐标点，多对多最多支持25个坐标点且起终点数目乘积最多为625（距离矩阵）");
    MAPPINGS.put(399, "查询关键字错误");
    MAPPINGS.put(400, "参数无法解码，请对特殊字符在utf8编码下进行urlencode操作");
    MAPPINGS.put(402, "提交的数据格式错误，请确认数据是utf8编码，并对特殊字符进行了urlencode处理");
    MAPPINGS.put(403, "不被支持的接口转发");
    MAPPINGS.put(404, "错误的请求路径");
    MAPPINGS.put(405, "请求方法错误，请更正为：GET");
    MAPPINGS.put(406, "请求方法错误，请更正为：POST");
    MAPPINGS.put(407, "不被支持的请求方法");
    MAPPINGS.put(408, "自定义Referer错误");
    MAPPINGS.put(500, "后端超时");
    MAPPINGS.put(510, "后端服务无法连接");
    MAPPINGS.put(520, "后端服务请求失败");
    MAPPINGS.put(530, "后端服务返回数据解析失败");
    MAPPINGS.put(531, "后端服务报错");
    MAPPINGS.put(600, "其他");
  }

  private TencentErrorCode() {
  }

  public static String getMessage(int code) {
    return MAPPINGS.getOrDefault(code, "UNKNOWN");
  }
}
