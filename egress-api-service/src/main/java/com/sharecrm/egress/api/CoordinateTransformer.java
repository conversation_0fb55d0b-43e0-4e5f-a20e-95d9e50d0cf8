package com.sharecrm.egress.api;

/**
 * 多种地图坐标系转换工具类，可以参考 <a href="https://www.lddgo.net/convert/coordinate">在线地图坐标转换</a>
 * <pre>
 * wgs84 84年提出，大地坐标，国际通用坐标系。
 * gcj02 02年提出，火星坐标，经过加密算法。大多数非百度中国地图厂商基本都是使用的火星坐标：高德，腾讯，谷歌中国cn
 * bd09  09年提出，百度坐标，经过火星坐标再次加密，相当于对大地坐标经过了二次加密。百度自己使用
 * 经度：东经为正数，西经为负数。
 * 纬度：北纬为正数，南纬为负数。
 * </pre>
 */
public abstract class CoordinateTransformer {
  // 长半轴
  private static final double A = 6378245.0;
  // 扁率
  private static final double EE = 0.00669342162296594323;
  // π
  private static final double PI = 3.1415926535897932384626;
  private static final double X_PI = 3.14159265358979324 * 3000.0 / 180.0;

  private CoordinateTransformer() {
  }

  /**
   * 百度坐标系(BD-09)转WGS坐标
   *
   * @param lng 百度坐标纬度
   * @param lat 百度坐标经度
   * @return WGS84坐标数组
   */
  public static double[] bd09ToWgs84(double lng, double lat) {
    double[] gcj = bd09ToGcj02(lng, lat);
    return gcj02ToWgs84(gcj[0], gcj[1]);
  }

  /**
   * 百度坐标系(BD-09)转火星坐标系(GCJ-02)
   * <br/>
   * 百度——>谷歌cn、高德、腾讯
   *
   * @param bdLng 百度坐标纬度
   * @param bdLat 百度坐标经度
   * @return 火星坐标数组
   */
  public static double[] bd09ToGcj02(double bdLng, double bdLat) {
    double x = bdLng - 0.0065;
    double y = bdLat - 0.006;
    double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
    double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
    double lng = z * Math.cos(theta);
    double lat = z * Math.sin(theta);
    return new double[] {lng, lat};
  }

  /**
   * GCJ02(火星坐标系)转GPS84
   *
   * @param lng 火星坐标系的经度
   * @param lat 火星坐标系纬度
   * @return WGS84坐标数组
   */
  public static double[] gcj02ToWgs84(double lng, double lat) {
    if (outOfChina(lng, lat)) {
      return new double[] {lng, lat};
    }
    double dLat = transformLat(lng - 105.0, lat - 35.0);
    double dLng = transformLng(lng - 105.0, lat - 35.0);
    double radLat = lat / 180.0 * PI;
    double magic = Math.sin(radLat);
    magic = 1 - EE * magic * magic;
    double sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
    dLng = (dLng * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
    double mgLat = lat + dLat;
    double mgLng = lng + dLng;
    return new double[] {lng * 2 - mgLng, lat * 2 - mgLat};
  }

  /**
   * 判断是否在国内，不在国内不做偏移
   *
   * @param lng 经度，[-180,180]
   * @param lat 维度，[-90,90]
   */
  private static boolean outOfChina(double lng, double lat) {
    return (lng < 72.004 || lng > 137.8347) || (lat < 0.8293 || lat > 55.8271);
  }

  /**
   * 纬度转换
   *
   * @param x 经度，[-180,180]
   * @param y 维度，[-90,90]
   * @return 转换后的纬度
   */
  public static double transformLat(double x, double y) {
    double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
    ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 经度转换
   *
   * @param x 经度，[-180,180]
   * @param y 维度，[-90,90]
   * @return 转换后的经度
   */
  public static double transformLng(double x, double y) {
    double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
    ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * WGS坐标转百度坐标系(BD-09)
   *
   * @param lng WGS84坐标系的经度
   * @param lat WGS84坐标系的纬度
   * @return 百度坐标数组
   */
  public static double[] wgs84ToBd09(double lng, double lat) {
    double[] gcj = wgs84ToGcj02(lng, lat);
    return gcj02ToBd09(gcj[0], gcj[1]);
  }

  /**
   * WGS84转GCJ02(火星坐标系)
   *
   * @param lng WGS84坐标系的经度
   * @param lat WGS84坐标系的纬度
   * @return 火星坐标数组
   */
  public static double[] wgs84ToGcj02(double lng, double lat) {
    if (outOfChina(lng, lat)) {
      return new double[] {lng, lat};
    }
    double dLat = transformLat(lng - 105.0, lat - 35.0);
    double dLng = transformLng(lng - 105.0, lat - 35.0);
    double radLat = lat / 180.0 * PI;
    double magic = Math.sin(radLat);
    magic = 1 - EE * magic * magic;
    double sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
    dLng = (dLng * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
    double mgLat = lat + dLat;
    double mgLng = lng + dLng;
    return new double[] {mgLng, mgLat};
  }

  /**
   * 火星坐标系(GCJ-02)转百度坐标系(BD-09)
   * <br/>
   * 谷歌cn、高德、腾讯——>百度
   *
   * @param lng 火星坐标经度
   * @param lat 火星坐标纬度
   * @return 百度坐标数组
   */
  public static double[] gcj02ToBd09(double lng, double lat) {
    double z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * X_PI);
    double theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * X_PI);
    double bdLng = z * Math.cos(theta) + 0.0065;
    double bdLat = z * Math.sin(theta) + 0.006;
    return new double[] {bdLng, bdLat};
  }

  /**
   * 基于地球半径，计算2个坐标点之间的距离，单位：米
   *
   * @param lng1 经度1
   * @param lat1 纬度1
   * @param lng2 经度2
   * @param lat2 维度2
   * @return 两个点之间的直线距离
   */
  public static double getDistance(double lng1, double lat1, double lng2, double lat2) {
    double radLat1 = rad(lat1);
    double radLat2 = rad(lat2);
    double a = radLat1 - radLat2;
    double b = rad(lng1) - rad(lng2);
    double distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    distance *= A;
    return Math.round(distance * 10000d) / 10000d;
  }

  private static double rad(double d) {
    return d * PI / 180.0D;
  }
}
