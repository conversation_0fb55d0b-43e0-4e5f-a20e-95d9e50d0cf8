package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.CityLocale;
import com.sharecrm.egress.entity.PhoneAreaLangResponse;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

public interface I18nApi {
  @PostExchange("/paas/metadata/api/areainfo/phoneAreaLang")
  Mono<PhoneAreaLangResponse> getI18nLocation(@RequestBody CityLocale location);
}
