package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.GoogleRouteRequest;
import com.sharecrm.egress.entity.GoogleRouteResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 谷歌地图API
 *
 * <AUTHOR>
 */
public interface GoogleRouteApi {
  @PostExchange("/directions/v2:computeRoutes")
  Mono<GoogleRouteResult> driving(@RequestBody GoogleRouteRequest request, @RequestParam(value = "languageCode", required = false) String language);
}
