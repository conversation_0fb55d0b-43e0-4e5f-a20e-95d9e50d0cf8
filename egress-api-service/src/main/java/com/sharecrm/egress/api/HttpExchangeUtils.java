package com.sharecrm.egress.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sharecrm.egress.config.ConnectionProperties;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.utils.NetUtils;
import io.netty.channel.ChannelOption;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.codec.ClientCodecConfigurer;
import org.springframework.http.codec.json.Jackson2CodecSupport;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.netty.Connection;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

import javax.net.ssl.SSLException;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * HttpExchange通用工具类，创建高德、百度、腾讯等Api调用Bean
 */
@Slf4j
@UtilityClass
public class HttpExchangeUtils {

  public static AMapApi buildAMapApi(ExchangeStrategies strategies, MapProperties.AMapConfig config) {
    Map<String, String> params = Map.of("key", config.getKey());
    WebClient client = WebClient
      .builder()
      .baseUrl(config.getApiUrl())
      .exchangeStrategies(strategies)
      .defaultUriVariables(params)
      //根据ak/sk计算sig
      .filter((req, next) -> next.exchange(ClientRequest.from(req)
        .url(buildAmapSigUrl(config, req))
        .build()))
      .clientConnector(connector(config.getConnection()))
      .build();
    HttpServiceProxyFactory factory = httpServiceProxyFactory(config.getConnection().getReadTimeout(), client);
    return factory.createClient(AMapApi.class);
  }

  private URI buildAmapSigUrl(MapProperties.AMapConfig config, ClientRequest request) {
    return buildAmapSigUrl(config.getSig(), request.url());
  }

  private URI buildAmapSigUrl(String sig, URI uri) {
    //高德ak/sk签名算法
    if (StringUtils.isEmpty(sig)) {
      return uri;
    }
    String md5Hex = amapSignParam(sig, uri);
    //此处直接拼接，小心不能用UriComponentsBuilder，他会又编码
    return URI.create(uri + "&sig=" + md5Hex);
  }

  public String amapSignParam(String sig, URI uri) {
    UriComponents build = UriComponentsBuilder.fromUri(uri).build();
    // 签名格式：sig=MD5(请求参数键值对按参数名的升序排序+私钥)。
    String query = sortedQueryParamsAndDecode(build.getQueryParams());
    return md5Hex(query + sig);
  }

  @NotNull
  private String md5Hex(String str) {
    return DigestUtils.md5Hex(str);
  }

  @NotNull
  private String sortedQueryParamsAndDecode(MultiValueMap<String, String> params) {
    return params
      .toSingleValueMap()
      .entrySet()
      .stream()
      .sorted(Map.Entry.comparingByKey())
      .map(e -> e.getKey() + "=" + URLDecoder.decode(e.getValue(), StandardCharsets.UTF_8))
      .collect(Collectors.joining("&"));
  }

  public static BaiduApi buildBaiduApi(ExchangeStrategies strategies,
                                       MapProperties.BaiduConfig config) {
    Map<String, String> params = Map.of("ak", config.getKey());
    WebClient client = WebClient
      .builder()
      .baseUrl(config.getApiUrl())
      .exchangeStrategies(strategies)
      .defaultUriVariables(params)
      //根据ak/sk计算sn
      .filter((req, next) -> next.exchange(ClientRequest.from(req)
        .url(buildBaiduSn(config, req))
        .build()))
      .codecs(addBaiduCodec())
      .clientConnector(connector(config.getConnection()))
      .build();
    HttpServiceProxyFactory factory = httpServiceProxyFactory(config.getConnection().getReadTimeout(), client);
    return factory.createClient(BaiduApi.class);
  }

  @NotNull
  private static Consumer<ClientCodecConfigurer> addBaiduCodec() {
    //百度返回的类型，有 text/javascript;charset=utf-8，增加对这种类型的处理
    return configurer -> {
      ObjectMapper objectMapper = configurer.getReaders().stream()
        .filter(Jackson2JsonDecoder.class::isInstance)
        .map(Jackson2JsonDecoder.class::cast)
        .map(Jackson2CodecSupport::getObjectMapper)
        .findFirst()
        .orElseGet(() -> Jackson2ObjectMapperBuilder.json().build());
      Jackson2JsonDecoder decoder = new Jackson2JsonDecoder(objectMapper, MediaType.ALL);
      configurer.customCodecs().registerWithDefaultConfig(decoder);
    };
  }

  private static URI buildBaiduSn(MapProperties.BaiduConfig config, ClientRequest request) {
    //百度ak/sk签名算法: https://lbsyun.baidu.com/faq/api?title=lbscloud/api/appendix#sn%E8%AE%A1%E7%AE%97%E7%AE%97%E6%B3%95
    String sig = config.getSig();
    if (StringUtils.isEmpty(sig)) {
      return request.url();
    }
    URI uri = request.url();
    UriComponents build = UriComponentsBuilder.fromUri(uri).build();
    //借用UriComponentsBuilder对 value进行encode，对key不encode，参考百度说明
    String whole = uri.getPath() + "?" + build.getQuery() + sig;
    // 对上面wholeStr再作utf8编码
    String encode = URLEncoder.encode(whole, StandardCharsets.UTF_8);
    String md5Hex = md5Hex(encode);
    //此处直接拼接，小心不能用UriComponentsBuilder，他会又编码，注意key和其他几家的key不一样
    return URI.create(uri + "&sn=" + md5Hex);
  }

  public static TencentMapApi buildTencentMapApi(ExchangeStrategies strategies,
                                                 MapProperties.TencentConfig config) {
    Map<String, String> params = Map.of("key", config.getKey());
    WebClient client = WebClient
      .builder()
      .baseUrl(config.getApiUrl())
      .exchangeStrategies(strategies)
      .defaultUriVariables(params)
      //根据ak/sk计算sig
      .filter((req, next) -> next.exchange(ClientRequest.from(req)
        .url(buildTencentSigUrl(config, req))
        .build()))
      .clientConnector(connector(config.getConnection()))
      .build();
    HttpServiceProxyFactory factory = httpServiceProxyFactory(config.getConnection().getReadTimeout(), client);
    return factory.createClient(TencentMapApi.class);
  }

  private URI buildTencentSigUrl(MapProperties.TencentConfig config, ClientRequest request) {
    //ak/sk签名算法: https://lbs.qq.com/faq/serverFaq/webServiceKey
    String sig = config.getSig();
    if (StringUtils.isEmpty(sig)) {
      return request.url();
    }
    URI uri = request.url();
    String md5Hex = tencentSignParam(sig, uri);
    //此处直接拼接，小心不能用UriComponentsBuilder，他会又编码
    return URI.create(uri + "&sig=" + md5Hex);
  }

  public static String tencentSignParam(String sig, URI uri) {
    UriComponents build = UriComponentsBuilder.fromUri(uri).build();
    // 签名格式：sig=MD5(请求参数键值对按参数名的升序排序+私钥)。
    String query = sortedQueryParamsAndDecode(build.getQueryParams());
    return md5Hex(uri.getPath() + "?" + query + sig);
  }

  public static GoogleApi buildGoogleMapApi(ExchangeStrategies strategies,
                                            MapProperties.GoogleConfig config) {
    Map<String, String> params = Map.of("key", config.getKey());
    WebClient client = WebClient
      .builder()
      .baseUrl(config.getMapApiUrl())
      .exchangeStrategies(strategies)
      .defaultUriVariables(params)
      .defaultHeader("Content-Type", "application/json")
      .clientConnector(connector(config.getConnection()))
      .build();
    HttpServiceProxyFactory factory = httpServiceProxyFactory(config.getConnection().getReadTimeout(), client);
    return factory.createClient(GoogleApi.class);
  }

  public static GoogleRouteApi buildGoogleRouteApi(ExchangeStrategies strategies,
                                                   MapProperties.GoogleConfig config) {
    WebClient client = WebClient
      .builder()
      .baseUrl(config.getRouteApiUrl())
      .exchangeStrategies(strategies)
      .defaultHeader("Content-Type", "application/json")
      .defaultHeader("X-Goog-Api-Key", config.getKey())
      .defaultHeader("X-Goog-FieldMask", config.getFieldMask())
      .clientConnector(connector(config.getConnection()))
      .build();
    HttpServiceProxyFactory factory = httpServiceProxyFactory(config.getConnection().getReadTimeout(), client);
    return factory.createClient(GoogleRouteApi.class);
  }

  /**
   * 设置读写超时时间和访问HTTP代理
   */
  @SneakyThrows
  public static ClientHttpConnector connector(ConnectionProperties config) {
    SslContext sslContext = insecureSslContext();
    Consumer<Connection> timeout = connection ->
      connection.addHandlerLast(new ReadTimeoutHandler((int) config.getReadTimeout().toSeconds()))
        .addHandlerLast(new WriteTimeoutHandler((int) config.getWriteTimeout().toSeconds()));

    Boolean keepAlive = config.getKeepAlive();
    HttpClient httpClient = HttpClient
      .create()
      .secure(s -> s.sslContext(sslContext))
      .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, (int) config.getConnectTimeout().toMillis())
      .responseTimeout(config.getReadTimeout())
      .keepAlive(Objects.requireNonNullElse(keepAlive, true))
      .doOnConnected(timeout);

    if (config.isLoggingEnabled()) {
      //启用Log日志，把日志打印到单独的文件里，配合logback里的配置
      httpClient = httpClient.wiretap("reactor.netty.http.client.HttpClient",
        LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);
    }

    if (StringUtils.isNotBlank(config.getProxy())) {
      httpClient = httpClient
        //在代理情况下，如果不指定，默认关闭 keepalive
        .keepAlive(Objects.requireNonNullElse(keepAlive, false))
        .proxy(proxy -> proxy
          .type(ProxyProvider.Proxy.HTTP)
          .socketAddress(NetUtils.proxyAddress(config.getProxy()))
          .nonProxyHosts(config.getNonProxyHosts())
          .connectTimeoutMillis(config.getProxyConnectTimeout().toMillis()));
    }
    return new ReactorClientHttpConnector(httpClient);
  }

  @NotNull
  public static HttpServiceProxyFactory httpServiceProxyFactory(Duration readTimeout, WebClient client) {
    WebClientAdapter adapter = WebClientAdapter.create(client);
    adapter.setBlockTimeout(readTimeout);
    return HttpServiceProxyFactory.builderFor(adapter).build();
  }

  @NotNull
  public static SslContext insecureSslContext() throws SSLException {
    return SslContextBuilder.forClient().trustManager(InsecureTrustManagerFactory.INSTANCE).build();
  }

}
