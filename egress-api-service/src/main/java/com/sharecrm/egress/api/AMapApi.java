package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.AMapAddressResult;
import com.sharecrm.egress.entity.AMapCoordinateResult;
import com.sharecrm.egress.entity.AMapDistanceResult;
import com.sharecrm.egress.entity.AMapDrivingResult;
import com.sharecrm.egress.entity.AMapGeoResult;
import com.sharecrm.egress.entity.AMapIpResult;
import com.sharecrm.egress.entity.AMapPoiResult;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 高德地图API
 *
 * <AUTHOR>
 */
public interface AMapApi {

  /**
   * 把空数组转换为null
   *
   * @param val 值
   * @return 转换后的值
   */
  static String convertEmptyToNull(String val) {
    return "[]".equals(val) || "".equals(val) ? null : val;
  }

  /**
   * 把空数组转换为null
   *
   * @param val 值
   * @return 转换后的值
   */
  static <T> List<T> convertEmptyToNull(List<T> val) {
    return val != null && val.isEmpty() ? null : val;
  }

  /**
   * 把空数组转换为null
   *
   * @param val 值
   * @return 转换后的值
   */
  static <T> T[] convertEmptyToNull(T[] val) {
    return val != null && val.length == 0 ? null : val;
  }

  /**
   * IP定位
   *
   * @param ip IP地址
   * @return IP定位结果
   * @see <a href="https://lbs.amap.com/api/webservice/guide/api/ipconfig">API文档</a>
   */
  @GetExchange("/v3/ip?key={key}")
  Mono<AMapIpResult> ipLocation(@RequestParam("ip") String ip, @RequestParam("language") String language);

  /**
   * 地理编码，提供将结构化地址数据（如：北京市海淀区上地十街十号）转换为对应坐标点（经纬度）功能
   *
   * @param address 地址
   * @param city    城市，如果不传则查询全国
   * @param country 国家的编码，对于海外地图必须传
   * @return 地理编码结果
   * @see <a href="https://lbs.amap.com/api/webservice/guide/api/georegeo">API文档</a>
   */
  @GetExchange("/v3/geocode/geo?key={key}&output=json")
  Mono<AMapGeoResult> geoCode(@RequestParam("address") String address, @RequestParam(value = "city", required = false) String city,
                              @RequestParam(value = "country", required = false) String country, @RequestParam("language") String language);

  /**
   * 逆地理编码，提供将坐标点（经纬度）转换为对应结构化地址数据（如：北京市海淀区上地十街十号）功能
   *
   * @param location 经纬度坐标，格式为"经度,纬度"，例如"116.481488,39.990464"
   * @return 逆地理编码结果
   * @see <a href="https://lbs.amap.com/api/webservice/guide/api/georegeo#regeo">API文档</a>
   */
  @GetExchange("/v3/geocode/regeo?key={key}&output=json&extensions=all")
  Mono<AMapAddressResult> reverseGeoCode(@RequestParam("location") String location, @RequestParam("radius") Integer radius,
                                         @RequestParam(name = "poitype", required = false) String poiType, @RequestParam("language") String language);

  /**
   * 测量距离，提供多个坐标点之间的距离
   *
   * @param origins     出发点支持100个坐标对（公交仅支持20个），坐标对见用“| ”分隔；经度和纬度用","分隔，例如"116.481488,39.990464"
   * @param destination 终点经纬度坐标，格式为"经度,纬度"，例如"116.481488,39.990464"，经纬度小数点不超过6位
   * @return 两点之间的距离，单位为米；预计行驶时间，单位为秒
   * @see <a href="https://lbs.amap.com/api/webservice/guide/api/direction#distance">API文档</a>
   */
  @GetExchange("/v3/distance?key={key}&output=json&type=1")
  Mono<AMapDistanceResult> distance(@RequestParam("origins") String origins, @RequestParam("destination") String destination);

  /**
   * 根据中心点查找圆形周边POI信息，提供根据关键字、城市、分类、边界、排序等条件进行POI信息检索的服务
   *
   * @param location 中心点坐标，格式为“经度,纬度”，例如“116.481488,39.990464”
   * @param radius   查询半径，单位：米；取值范围：0~3000，默认为1000
   * @param keywords 关键字，多个关键字用“|”分隔
   * @param types    查询POI类型，多个类型用“|”分隔，可选值见<a href="https://lbs.amap.com/api/webservice/download">下载链接</a>
   * @param offset   每页记录数据，取值范围：1~25，默认为10
   * @param page     当前页数，取值范围：1~100，默认为1
   * @return POI搜索结果
   * @see <a href="https://lbs.amap.com/api/webservice/guide/api/search/#around">API文档</a>
   */
  @GetExchange("/v3/place/around?key={key}&output=json&show_fields=photos")
  Mono<AMapPoiResult> poiAround(@RequestParam("location") String location,
                                @RequestParam("radius") int radius,
                                @RequestParam("keywords") String keywords,
                                @RequestParam(value = "types", required = false) String types,
                                @RequestParam("offset") int offset,
                                @RequestParam("page") int page,
                                @RequestParam("language") String language);

  /**
   * 驾车路径规划，提供起点和终点的坐标，路径规划的结果为一条有序的经过路线
   *
   * @param origin      起点经纬度。格式为"经度,纬度"，小数点后不得超过6位。
   * @param destination 终点经纬度坐标，格式为"经度,纬度"，例如"116.481488,39.990464"，经纬度小数点不超过6位
   * @param waypoints   途径点，最多支持16个途径点，经度和纬度用","分隔。多个途径点坐标按顺序以英文分号;分隔。例如"114.292068,30.551942;121.499663,31.239977"
   * @return 算路结果
   * @see <a href="https://lbs.amap.com/api/webservice/guide/api/newroute">API文档</a>
   */
  @GetExchange("/v3/direction/driving?key={key}&output=json&strategy=2&nosteps=1")
  Mono<AMapDrivingResult> driving(@RequestParam("origin") String origin,
                                  @RequestParam("destination") String destination,
                                  @RequestParam(value = "waypoints", required = false) String waypoints);


  /**
   * 坐标系统转换
   *
   * @param locations 经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过6位。多个坐标对之间用”|”进行分隔最多支持40对坐标。
   * @param coordsys  原坐标系 可选值： gps; mapbar; baidu; autonavi(不进行转换)
   * @return 转换结果
   * @see <a href="https://lbs.amap.com/api/webservice/guide/api/convert">...</a>
   */
  @GetExchange("/v3/assistant/coordinate/convert?key={key}&output=json")
  Mono<AMapCoordinateResult> coordinate(@RequestParam("locations") String locations,
                                        @RequestParam("coordsys") String coordsys);
}
