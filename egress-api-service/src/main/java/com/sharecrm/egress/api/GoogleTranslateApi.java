package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.GoogleTranslateResult;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

import java.util.List;

public interface GoogleTranslateApi {

  /**
   * See: <a href="https://cloud.google.com/translate/docs/reference/rest/v2/translate#TranslateTextResponseTranslation">...</a>
   */
  @PostExchange("/?key={key}")
  Mono<GoogleTranslateResult> translate(@RequestParam(value = "q") List<String> texts,
                                        @RequestParam(required = false) String source,
                                        @RequestParam String target,
                                        @RequestParam(required = false) String format,
                                        @RequestParam(required = false) String model);
}
