package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.GoogleGeoResult;
import com.sharecrm.egress.entity.GooglePoiResult;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import reactor.core.publisher.Mono;

/**
 * 谷歌地图API
 * <p>
 * 文档参考：
 * <a href="https://developers.google.com/maps/documentation?hl=zh-cn">...</a>
 *
 * <AUTHOR>
 */
public interface GoogleApi {
  /**
   * 调用谷歌地图地理编码API，将结构化地址信息转化为WGS84编码的经纬度信息
   *
   * @param address  结构化地址信息，北京市海淀区上地十街十号
   * @param region   区域或者城市信息
   * @param language 语言
   * @return 地理编码信息
   */
  @GetExchange("/maps/api/geocode/json?key={key}")
  Mono<GoogleGeoResult> geoCode(@RequestParam("address") String address,
                                @RequestParam(value = "region", required = false) String region,
                                @RequestParam(value = "language", defaultValue = "en-US") String language);

  /**
   * 调用谷歌地图逆地址解析的API接口，提供将坐标点（经纬度）转换为对应结构化地址数据（如：北京市海淀区上地十街十号）功能
   *
   * @param latlng   待转换的坐标点，经纬度为WGS84，格式为：“lat<纬度>,lng<经度>”，例如“39.984154,116.307490”
   * @param language 语言
   * @return 解析后的结构化地址数据
   */
  @GetExchange("/maps/api/geocode/json?key={key}")
  Mono<GoogleGeoResult> reverseGeoCode(@RequestParam("latlng") String latlng, @RequestParam(value = "language", defaultValue = "en-US") String language);

  /**
   * 圆形区域检索，以一个中心点和半径进行检索，返回中心点周围指定距离内的POI（Point Of Interest，即兴趣点）信息
   *
   * @param location  经纬度坐标，格式为"纬度,经度"，例如："31.299636,120.562447"
   * @param radius    中心点周围检索半径，单位：米，最大取值为50000
   * @param keyword   检索关键字。圆形区域检索和多边形区域内检索支持多个关键字并集检索，不同关键字间以$符号分隔，最多支持10个关键字检索。如:银行$酒店
   * @param type      检索分类偏好，多个分类用竖线"|"分隔
   * @param pageToken 首次查询返回20条记录，并返回next_page_token，用于下次检索。从发出 next_page_token 到生效，会有几秒钟的延迟。
   * @param language  语言
   * @return 圆形区域检索结果
   * @see <a href=https://developers.google.com/maps/documentation/places/web-service/search-nearby?hl=zh-cn">API文档</a>
   */
  @GetExchange("/maps/api/place/nearbysearch/json?key={key}")
  Mono<GooglePoiResult> poiAround(@RequestParam("location") String location,
                                  @RequestParam("radius") int radius,
                                  @RequestParam("keyword") String keyword,
                                  @RequestParam(value = "type", required = false) String type,
                                  @RequestParam(value = "pageToken", required = false) String pageToken,
                                  @RequestParam(value = "languageCode", defaultValue = "en-US") String language);
}
