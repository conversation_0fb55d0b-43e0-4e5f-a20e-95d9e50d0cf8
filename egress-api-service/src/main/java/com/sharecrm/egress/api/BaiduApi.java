package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.BaiduAddressResult;
import com.sharecrm.egress.entity.BaiduDistanceResult;
import com.sharecrm.egress.entity.BaiduDrivingResult;
import com.sharecrm.egress.entity.BaiduGeoResult;
import com.sharecrm.egress.entity.BaiduIpResult;
import com.sharecrm.egress.entity.BaiduPoiResult;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import reactor.core.publisher.Mono;

/**
 * 百度地图API
 *
 * <AUTHOR>
 */
public interface BaiduApi {
  /**
   * IP定位，可以给出经纬度信息
   *
   * @param ip IP地址
   * @return IP定位结果
   * @see <a href="https://lbsyun.baidu.com/index.php?title=webapi/ip-api">API文档</a>
   */
  @GetExchange("/location/ip?ak={ak}&coor=gcj02")
  Mono<BaiduIpResult> ipLocation(@RequestParam("ip") String ip, @RequestParam("language") String language);

  /**
   * 地理位置解析，可以支持境外的地址解析，但是需要特殊开通。提供将结构化地址数据（如：北京市海淀区上地十街十号）转换为对应坐标点（经纬度）功能
   *
   * @param address 地址
   * @param city    城市名，如果不传则查询全国
   * @return 地理位置解析结果
   * @see <a href="https://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-geocoding">API文档</a>
   */
  @GetExchange("/geocoding/v3/?output=json&ak={ak}&ret_coordtype=gcj02ll")
  Mono<BaiduGeoResult> geoCode(@RequestParam("address") String address, @RequestParam(value = "city", required = false) String city);

  /**
   * 逆地址解析，提供将坐标点（经纬度）转换为对应结构化地址的能力
   *
   * @param location 经纬度坐标，格式为"纬度,经度"，例如："40.056878,116.30815"
   * @return 逆地址解析结果
   * @see <a href="https://lbsyun.baidu.com/faq/api?title=webapi/guide/webservice-geocoding-abroad-base">API文档</a>
   */
  @GetExchange("/reverse_geocoding/v3/?output=json&ak={ak}&ret_coordtype=gcj02ll&coordtype=gcj02ll&poi=0&extensions_town=true&extensions_poi=1")
  Mono<BaiduAddressResult> reverseGeoCode(@RequestParam("location") String location, @RequestParam("radius") Integer radius, @RequestParam("language") String language);

  /**
   * 批量路线规划，提供多个起点和终点之间的驾车、步行、骑行路线规划
   *
   * @param origins      起点坐标，格式为"纬度,经度"，例如："40.056878,116.30815|40.063597,116.364973"
   * @param destinations 终点坐标，格式为"纬度,经度"，例如："40.056878,116.30815|40.063597,116.364973"
   * @return 批量路线规划结果，两点之间的距离，单位为米；预计行驶时间，单位为秒
   * @see <a href="https://lbsyun.baidu.com/faq/api?title=webapi/routchtout-drive">API文档</a>
   */
  @GetExchange("/routematrix/v2/driving?output=json&ak={ak}&coord_type=gcj02&tactics=13")
  Mono<BaiduDistanceResult> distance(@RequestParam("origins") String origins, @RequestParam("destinations") String destinations);

  /**
   * 圆形区域检索，以一个中心点和半径进行检索，返回中心点周围指定距离内的POI（Point Of Interest，即兴趣点）信息
   *
   * @param location 经纬度坐标，格式为"纬度,经度"，例如："31.299636,120.562447"
   * @param radius   中心点周围检索半径，单位：米，最大取值为50000
   * @param query    检索关键字。圆形区域检索和多边形区域内检索支持多个关键字并集检索，不同关键字间以$符号分隔，最多支持10个关键字检索。如:银行$酒店
   * @param tag      检索分类偏好，多个分类以逗号","分隔
   * @param pageNum  分页页码，默认为0，0代表第一页，1代表第二页，以此类推
   * @param pageSize 分页条目数，默认为10条每页，最多返回20条POI数据
   * @return 圆形区域检索结果
   * @see <a href="https://lbsyun.baidu.com/faq/api?title=webapi/guide/webservice-placeapi/circle">API文档</a>
   */
  @GetExchange("/place/v2/search?scope=2&coordtype=2&ret_coordtype=gcj02ll&extensions_adcode=true&photo_show=false&output=json&ak={ak}")
  Mono<BaiduPoiResult> poiAround(@RequestParam("location") String location,
                                 @RequestParam("radius") int radius,
                                 @RequestParam("query") String query,
                                 @RequestParam(value = "tag", required = false) String tag,
                                 @RequestParam(value = "page_size") int pageSize,
                                 @RequestParam(value = "page_num") int pageNum, 
                                 @RequestParam("language") String language);

  /**
   * 驾车路线规划，提供起点和终点之间的驾车路线规划
   *
   * @param origin      起点经纬度，格式为：纬度,经度；小数点后不超过6位，如：40.056878,116.30815
   * @param destination 终点经纬度，格式为：纬度,经度；小数点后不超过6位，如：40.056878,116.30815
   * @param waypoints   途径点坐标串，支持18个以内的有序途径点。多个途径点坐标按顺序以英文竖线符号分隔，示例： 40.465,116.314|40.232,116.352|40.121,116.453
   * @return 驾车路线规划结果，两点之间的距离，单位为米；预计行驶时间，单位为秒
   * @see <a href="https://lbsyun.baidu.com/faq/api?title=webapi/webservice-direction/dirve">API文档</a>
   */
  @GetExchange("/directionlite/v1/driving?output=json&ak={ak}&coord_type=gcj02&ret_coordtype=gcj02&steps_info=0&alternatives=0")
  Mono<BaiduDrivingResult> driving(@RequestParam(value = "origin") String origin,
                                   @RequestParam(value = "destination", required = false) String destination,
                                   @RequestParam(value = "waypoints", required = false) String waypoints);
}
