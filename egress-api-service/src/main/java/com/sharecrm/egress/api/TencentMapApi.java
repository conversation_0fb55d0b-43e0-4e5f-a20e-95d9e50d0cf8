package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.TencentAddressResult;
import com.sharecrm.egress.entity.TencentDistanceResult;
import com.sharecrm.egress.entity.TencentDrivingResult;
import com.sharecrm.egress.entity.TencentGeoResult;
import com.sharecrm.egress.entity.TencentIpResult;
import com.sharecrm.egress.entity.TencentPoiResult;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import reactor.core.publisher.Mono;

/**
 * 腾讯地图的API
 *
 * <AUTHOR>
 * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/webServiceOverview">API文档</a>
 */
public interface TencentMapApi {
  /**
   * 调用腾讯IP地址转换的API接口，可以给出城市级别的经纬度信息
   *
   * @param ip 待转换的IP地址
   * @return 转换后的地址信息
   * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/webServiceIp">IP地址转换API文档</a>
   */
  @GetExchange("/ws/location/v1/ip?key={key}&output=json")
  Mono<TencentIpResult> ipLocation(@RequestParam("ip") String ip, @RequestParam("language") String language);

  /**
   * 调用腾讯地图geo位置转换的API接口，提供将结构化地址数据（如：北京市海淀区上地十街十号）转换为对应坐标点（经纬度）功能
   *
   * @param address 待转换的地址
   * @param city    城市名，如果不传则查询全国
   * @return 转换后的地址信息
   * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/webServiceGeocoder">地理编码API文档</a>
   */
  @GetExchange("/ws/geocoder/v1?key={key}&output=json")
  Mono<TencentGeoResult> geoCode(@RequestParam("address") String address, @RequestParam(value = "city", required = false) String city, @RequestParam("language") String language);

  /**
   * 调用腾讯地图逆地址解析的API接口，提供将坐标点（经纬度）转换为对应结构化地址数据（如：北京市海淀区上地十街十号）功能
   *
   * @param location 待转换的坐标点，经纬度（GCJ02坐标系），格式为：“lat<纬度>,lng<经度>”，例如“39.984154,116.307490”
   * @return 转换后的地址信息
   * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/webServiceGcoder">逆地址解析API文档</a>
   */
  @GetExchange("/ws/geocoder/v1?key={key}&output=json&get_poi=1")
  Mono<TencentAddressResult> reverseGeoCode(@RequestParam("location") String location, @RequestParam("language") String language, @RequestParam("poi_options") String options);

  /**
   * 调用腾讯地图批量距离计算的API接口，提供计算多组起终点的驾车路线距离和行驶时间功能
   *
   * @param from 起点坐标，经纬度（GCJ02坐标系），格式为：“lat1,lng1;lat2,lng2;...”，最多支持100个起点坐标
   * @param to   终点坐标，经纬度（GCJ02坐标系），格式为：“lat1,lng1;lat2,lng2;...”，最多支持100个终点坐标
   * @return 计算结果，包括距离和时间，两点之间的距离，单位为米；预计行驶时间，单位为秒
   * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/webServiceDistance">距离计算API文档</a>
   */
  @GetExchange("/ws/distance/v1/matrix?key={key}&output=json&mode=driving")
  Mono<TencentDistanceResult> distance(@RequestParam("from") String from, @RequestParam("to") String to);

  /**
   * 调用腾讯地图搜索地点的API接口，提供查询指定地区关键字对应的地址
   *
   * @param keyword   待查询的关键字（地点名称）
   * @param boundary  格式：
   *                  boundary=nearby(lat,lng,radius[,auto_extend])
   *                  子参数：
   *                  lat,lng：搜索中心点的经纬度，格式顺序为纬度在前，经度在后
   *                  radius：搜索半径，单位：米，取值范围：10到1000
   *                  auto_extend：[可选] 当前范围无结果时，是否自动扩大范围，取值：0 不扩大
   * @param filter    指定分类筛选，语句格式为：category=分类名1,分类名2,分类词数量建议不超过5个，支持设置分类编码
   * @param pageIndex 页码，从1开始
   * @param pageSize  每页条目数，最大限制为20条
   * @return 搜索结果列表，包含每个地点的名称、地址、经纬度等信息
   * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/webServiceSearch">搜索API文档</a>
   */
  @GetExchange("/ws/place/v1/search?key={key}&output=json")
  Mono<TencentPoiResult> poiAround(@RequestParam("keyword") String keyword,
                                   @RequestParam("boundary") String boundary,
                                   @RequestParam(value = "filter", required = false) String filter,
                                   @RequestParam("page_size") int pageSize,
                                   @RequestParam("page_index") int pageIndex,
                                   @RequestParam("language") String language);

  /**
   * 调用腾讯地图驾车路线规划的API接口，提供起点到终点的驾车路线规划功能.
   *
   * @param from      路径规划的起点坐标，经纬度（GCJ02坐标系），格式为：“lat,lng”。
   * @param to        路径规划的终点坐标，经纬度（GCJ02坐标系），格式为：“lat,lng”。
   * @param waypoints 途经点，格式：lat1,lng1;lat2,lng2;… 最大支持30个，超过30个之后的将被忽略
   * @return 经过规划路线列表。
   * @see <a href="https://lbs.qq.com/service/webService/webServiceGuide/webServiceRoute">路线规划API文档</a>
   */
  @GetExchange("/ws/direction/v1/driving/?key={key}&output=json&no_step=1&get_speed=0&get_mp=0")
  Mono<TencentDrivingResult> driving(@RequestParam("from") String from,
                                     @RequestParam("to") String to,
                                     @RequestParam(value = "waypoints", required = false) String waypoints);
}
