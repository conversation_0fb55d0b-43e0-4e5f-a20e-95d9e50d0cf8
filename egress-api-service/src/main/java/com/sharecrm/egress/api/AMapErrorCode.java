package com.sharecrm.egress.api;

import java.util.HashMap;
import java.util.Map;

/**
 * 高德地图响应错误吗
 *
 * <AUTHOR>
 * @see <a href="https://lbs.amap.com/api/webservice/guide/tools/info">错误码</a>
 * <p>
 * IgnoreI18nFile ignore i18n
 */
public abstract class AMapErrorCode {
  private static final Map<Integer, String> MAPPINGS = new HashMap<>();

  /*
   * 10000 OK 请求正常
   * 10001 INVALID_USER_KEY key不正确或过期
   * 10002 SERVICE_NOT_AVAILABLE 没有权限使用相应的服务或者请求接口的路径拼写错误
   * 10003 DAILY_QUERY_OVER_LIMIT 访问已超出日访问量
   * 10004 ACCESS_TOO_FREQUENT 单位时间内访问过于频繁
   * 10005 INVALID_USER_IP IP白名单出错，发送请求的服务器IP不在IP白名单内
   * 10006 INVALID_USER_DOMAIN 绑定域名无效
   * 10007 INVALID_USER_SIGNATURE 数字签名未通过验证
   * 10008 INVALID_USER_SCODE MD5安全码未通过验证
   * 10009 USERKEY_PLAT_NOMATCH 请求key与绑定平台不符
   * 10010 IP_QUERY_OVER_LIMIT IP访问超限
   * 10011 NOT_SUPPORT_HTTPS 服务不支持https请求
   * 10012 INSUFFICIENT_PRIVILEGES 权限不足，服务请求被拒绝
   * 10013 USER_KEY_RECYCLED Key被删除
   * 10014 QPS_HAS_EXCEEDED_THE_LIMIT 云图服务QPS超限
   * 10015 GATEWAY_TIMEOUT 受单机QPS限流限制
   * 10016 SERVER_IS_BUSY 服务器负载过高
   * 10017 RESOURCE_UNAVAILABLE 所请求的资源不可用
   * 10019 CQPS_HAS_EXCEEDED_THE_LIMIT 使用的某个服务总QPS超限
   * 10020 CKQPS_HAS_EXCEEDED_THE_LIMIT 某个Key使用某个服务接口QPS超出限制
   * 10021 CUQPS_HAS_EXCEEDED_THE_LIMIT 账号使用某个服务接口QPS超出限制
   * 10026 INVALID_REQUEST 账号处于被封禁状态
   * 10029 ABROAD_DAILY_QUERY_OVER_LIMIT 某个Key的QPS超出限制
   * 10041 NO_EFFECTIVE_INTERFACE 请求的接口权限过期
   * 10044 USER_DAILY_QUERY_OVER_LIMIT 账号维度日调用量超出限制
   * 10045 USER_ABROAD_DAILY_QUERY_OVER_LIMIT 账号维度海外服务日调用量超出限制
   * 20000 INVALID_PARAMS 请求参数非法
   * 20001 MISSING_REQUIRED_PARAMS 缺少必填参数
   * 20002 ILLEGAL_REQUEST 请求协议非法
   * 20003 UNKNOWN_ERROR 其他未知错误
   * 20011 INSUFFICIENT_ABROAD_PRIVILEGES 查询坐标或规划点（包括起点、终点、途经点）在海外，但没有海外地图权限
   * 20012 ILLEGAL_CONTENT 查询信息存在非法内容
   * 20800 OUT_OF_SERVICE 规划点（包括起点、终点、途经点）不在中国陆地范围内
   * 20801 NO_ROADS_NEARBY 划点（起点、终点、途经点）附近搜不到路
   * 20802 ROUTE_FAIL 路线计算失败，通常是由于道路连通关系导致
   * 20803 OVER_DIRECTION_RANGE 起点终点距离过长。
   * 300** ENGINE_RESPONSE_DATA_ERROR 服务响应失败。
   * 40000 QUOTA_PLAN_RUN_OUT 余额耗尽
   * 40001 GEOFENCE_MAX_COUNT_REACHED 围栏个数达到上限
   * 40002 SERVICE_EXPIRED 购买服务到期
   * 40003 ABROAD_QUOTA_PLAN_RUN_OUT 海外服务余额耗尽
   */
  static {
    MAPPINGS.put(10000, "请求正常");
    MAPPINGS.put(10001, "key不正确或过期");
    MAPPINGS.put(10002, "没有权限使用相应的服务或者请求接口的路径拼写错误");
    MAPPINGS.put(10003, "访问已超出日访问量");
    MAPPINGS.put(10004, "单位时间内访问过于频繁");
    MAPPINGS.put(10005, "IP白名单出错，请求服务的IP不在IP白名单内");
    MAPPINGS.put(10006, "绑定域名无效");
    MAPPINGS.put(10007, "数字签名未通过验证");
    MAPPINGS.put(10008, "MD5安全码未通过验证");
    MAPPINGS.put(10009, "请求key与绑定平台不符");
    MAPPINGS.put(10010, "IP访问超限");
    MAPPINGS.put(10011, "服务不支持https请求");
    MAPPINGS.put(10012, "权限不足，服务请求被拒绝");
    MAPPINGS.put(10013, "Key被删除");
    MAPPINGS.put(10014, "云图服务QPS超限");
    MAPPINGS.put(10015, "受单机QPS限流限制");
    MAPPINGS.put(10016, "服务器负载过高");
    MAPPINGS.put(10017, "所请求的资源不可用");
    MAPPINGS.put(10019, "使用的某个服务总QPS超限");
    MAPPINGS.put(10020, "某个Key使用某个服务接口QPS超出限制");
    MAPPINGS.put(10021, "账号使用某个服务接口QPS超出限制");
    MAPPINGS.put(10026, "账号处于被封禁状态");
    MAPPINGS.put(10029, "某个Key的QPS超出限制");
    MAPPINGS.put(10041, "请求的接口权限过期");
    MAPPINGS.put(10044, "账号维度日调用量超出限制");
    MAPPINGS.put(10045, "账号维度海外服务日调用量超出限制");
    MAPPINGS.put(20000, "请求参数非法");
    MAPPINGS.put(20001, "缺少必填参数");
    MAPPINGS.put(20002, "请求协议非法");
    MAPPINGS.put(20003, "其他未知错误");
    MAPPINGS.put(20011, "查询坐标或规划点（包括起点、终点、途经点）在海外，但没有海外地图权限");
    MAPPINGS.put(20012, "查询信息存在非法内容");
    MAPPINGS.put(20800, "规划点（包括起点、终点、途经点）不在中国陆地范围内");
    MAPPINGS.put(20801, "划点（起点、终点、途经点）附近搜不到路");
    MAPPINGS.put(20802, "路线计算失败，通常是由于道路连通关系导致");
    MAPPINGS.put(20803, "起点终点距离过长。");
    MAPPINGS.put(30000, "服务响应失败，请检查参数");
    MAPPINGS.put(30001, "缺少必填参数，请检查参数是否有遗漏");
    MAPPINGS.put(30002, "请求协议非法，请检查请求协议是否正确");
    MAPPINGS.put(30003, "其他未知错误，请检查参数是否正确");
    MAPPINGS.put(30004, "访问已超出月访问量，请前往官网购买或升级套餐");
    MAPPINGS.put(30005, "无权限使用相应的服务或者请求接口的路径拼写错误，请检查所用key与绑定平台是否匹配以及所请求接口与绑定平台是否匹配");
    MAPPINGS.put(30006, "服务响应超时，请稍后重试");
    MAPPINGS.put(40000, "余额耗尽");
    MAPPINGS.put(40001, "围栏个数达到上限");
    MAPPINGS.put(40002, "购买服务到期");
    MAPPINGS.put(40003, "海外服务余额耗尽");
  }

  private AMapErrorCode() {
  }

  public static String getMessage(int code) {
    String message = MAPPINGS.get(code);
    if (message == null) {
      message = String.valueOf(code).startsWith("300") ? "引擎响应失败，请查询请求参数" : "UNKNOWN";
    }
    return message;
  }
}
