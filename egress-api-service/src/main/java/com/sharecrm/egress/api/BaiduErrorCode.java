package com.sharecrm.egress.api;

import java.util.HashMap;
import java.util.Map;

/**
 * This class contains error codes specific to the Baidu API.
 *
 * <AUTHOR>
 * @see <a href="https://lbsyun.baidu.com/index.php?title=webapi/ip-api">错误码</a>
 * <p>
 * IgnoreI18nFile ignore i18n
 */
public abstract class BaiduErrorCode {
  private static final Map<Integer, String> MAPPINGS = new HashMap<>();

  /*
   * 状态码 定义
   * 0 正常
   * 1 服务器内部错误
   * 10 上传内容超过8M
   * 101 AK参数不存在
   * 102 Mcode参数不存在，mobile类型mcode参数必需
   * 200 APP不存在，AK有误请检查再重试
   * 201 APP被用户自己禁用，请在控制台解禁
   * 202 APP被管理员删除
   * 203 APP类型错误
   * 210 APP IP校验失败
   * 211 APP SN校验失败
   * 220 APP Referer校验失败
   * 230 APP Mcode码校验失败
   * 240 APP 服务被禁用
   * 250 用户不存在
   * 251 用户被自己删除
   * 252 用户被管理员删除
   * 260 服务不存在
   * 261 服务被禁用
   * 301 永久配额超限，限制访问
   * 302 天配额超限，限制访问
   * 401 当前并发量已经超过约定并发配额，限制访问
   * 402 当前并发量已经超过约定并发配额，并且服务总并发量也已经超过设定的总并发配额，限制访问
   * 1001 没有IPv6地址访问的权限
   */
  static {
    MAPPINGS.put(0, "正常");
    MAPPINGS.put(1, "服务器内部错误");
    MAPPINGS.put(2, "参数无效");
    MAPPINGS.put(3, "权限校验失败");
    MAPPINGS.put(4, "配额校验失败");
    MAPPINGS.put(5, "ak不存在或者非法");
    MAPPINGS.put(7, "无返回结果");
    MAPPINGS.put(10, "上传内容超过8M");
    MAPPINGS.put(101, "AK参数不存在");
    MAPPINGS.put(102, "不通过白名单或者安全码不对");
    MAPPINGS.put(200, "APP不存在，AK有误请检查再重试");
    MAPPINGS.put(201, "APP被用户自己禁用，请在控制台解禁");
    MAPPINGS.put(202, "APP被管理员删除");
    MAPPINGS.put(203, "APP类型错误");
    MAPPINGS.put(210, "APP IP校验失败");
    MAPPINGS.put(211, "APP SN校验失败");
    MAPPINGS.put(220, "APP Referer校验失败");
    MAPPINGS.put(230, "APP Mcode码校验失败");
    MAPPINGS.put(240, "APP 服务被禁用");
    MAPPINGS.put(250, "用户不存在");
    MAPPINGS.put(251, "用户被自己删除");
    MAPPINGS.put(252, "用户被管理员删除");
    MAPPINGS.put(260, "服务不存在");
    MAPPINGS.put(261, "服务被禁用");
    MAPPINGS.put(301, "永久配额超限，限制访问");
    MAPPINGS.put(302, "天配额超限，限制访问");
    MAPPINGS.put(401, "当前并发量已经超过约定并发配额，限制访问");
    MAPPINGS.put(402, "当前并发量已经超过约定并发配额，并且服务总并发量也已经超过设定的总并发配额，限制访问");
    MAPPINGS.put(1001, "没有IPv6地址访问的权限");
  }

  private BaiduErrorCode() {
  }

  public static String getMessage(int code) {
    String message = MAPPINGS.get(code);
    if (message == null && code > 100) {
      String val = String.valueOf(code);
      if (val.startsWith("2")) {
        return "无权限";
      } else if (val.startsWith("3")) {
        return "配额校验失败";
      } else if (val.startsWith("4")) {
        return "并发超过限制";
      } else {
        return "未知错误";
      }
    }
    return MAPPINGS.getOrDefault(code, "UNKNOWN");
  }
}
