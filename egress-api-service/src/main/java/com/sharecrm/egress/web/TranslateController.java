package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.TranslateRequest;
import com.sharecrm.egress.entity.TranslateResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.TranslateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 文本翻译
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@RestController
@RequestMapping("/translates")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "sharecrm.api.translate.google.enabled", havingValue = "true", matchIfMissing = true)
public class TranslateController {

  private final TranslateService translateService;

  /**
   * 文本翻译，目前是对接google翻译，注意翻译失败返回的是200成功，但是词条结果是空
   *
   * @param request 请求体
   * @return 发送结果，成功返回200
   */
  @PostMapping
  @Tag(name = "translate", description = "文本翻译")
  @Operation(summary = "文本翻译", description = "文本翻译，一次最多128个词条，注意翻译失败返回的虽然是200成功，但是词条结果可能为空。注意语言定义使用的是`ISO-639 代码`，可能和我们定义的不同。",
    responses = {
      @ApiResponse(responseCode = "200", description = "词条翻译结果，注意翻译失败返回的是200成功，但是词条结果可能空")
    })
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = TranslateRequest.class),
    examples = @ExampleObject(value = "{\"texts\":[\"你好\",\"卫星实验室\"],\"target\":\"en\",\"source\": \"zh\"}")))
  public Mono<ResponseEntity<EgressApiResponse<TranslateResponse>>> translate(@Validated @RequestBody Mono<TranslateRequest> request) {
    return translateService.translate(request)
      .map(e -> ResponseEntity.ok(EgressApiResponse.ok(e)));
  }

}
