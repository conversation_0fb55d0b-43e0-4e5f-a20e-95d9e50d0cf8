package com.sharecrm.egress.web;


import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.AsrProvider;
import com.sharecrm.egress.service.AsrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/v1/asr")
@ConditionalOnBean(AsrProvider.class)
public class ASRController {
  private final AsrService asrService;

  public ASRController(AsrService asrService) {
    this.asrService = asrService;
  }

  @PostMapping("/sentence-recognition")
  @Tag(name = "asr", description = "语音识别")
  @Operation(summary = "一句话识别", description = "一句话识别，音频文件大小不超过3M，时长不超过60s。",
    responses = {
      @ApiResponse(responseCode = "200", description = "解析结果，一直返回Http 200，注意如果Tencent解析失败可能返回Http Code是200，但是内部code是400"),
    })
  public Mono<ResponseEntity<EgressApiResponse<TencentSpeechResponse>>> speechRecognition(@RequestBody Mono<byte[]> bytes) {
    return asrService.speechRecognition(bytes)
      .map(ResponseEntity::ok)
      .switchIfEmpty(Mono.defer(() -> Mono.just(ResponseEntity.ok(EgressApiResponse.ok(new TencentSpeechResponse())))));
  }

  @PostMapping("/rec-task")
  @Tag(name = "asr", description = "语音识别")
  @Operation(summary = "创建录音转文字任务", description = "创建录音转文字任务，异步转换，返回任务ID，请根据返回的任务ID查询转换结果",
    responses = {
      @ApiResponse(responseCode = "200", description = "提交任务成功"),
      @ApiResponse(responseCode = "400", description = "参数错误，云厂商校验错误、余额不足等"),
      @ApiResponse(responseCode = "500", description = "服务端错误"),
    })
  public Mono<ResponseEntity<EgressApiResponse<AsrCreateRecTaskResponse>>> createRecTask(@RequestBody Mono<AsrCreateRecTaskRequest> request) {
    return asrService.createRecTask(request)
      .map(ResponseEntity::ok)
      .switchIfEmpty(Mono.defer(() -> Mono.just(ResponseEntity.internalServerError().build())));
  }

  @GetMapping("/rec-task")
  @Tag(name = "asr", description = "语音识别")
  @Operation(summary = "查询录音转文字结果", description = "查询录音转文字结果，根据任务ID查询转换结果，注意只有状态是success时才有文本结果，结果只保留一年",
    responses = {
      @ApiResponse(responseCode = "200", description = "转换文字结果"),
      @ApiResponse(responseCode = "404", description = "找不到"),
    })
  public Mono<ResponseEntity<EgressApiResponse<AsrQueryRecTaskResponse>>> queryRecTask(@RequestParam("taskId") String taskId) {
    return asrService.queryRecTask(taskId)
      .map(ResponseEntity::ok)
      .switchIfEmpty(Mono.defer(() -> Mono.just(ResponseEntity.notFound().build())));
  }

}
