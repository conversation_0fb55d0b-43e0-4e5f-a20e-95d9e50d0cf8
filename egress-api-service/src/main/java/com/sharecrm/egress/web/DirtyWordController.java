package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.DirtyWordsRequest;
import com.sharecrm.egress.entity.HasDirtyWordsResponse;
import com.sharecrm.egress.entity.ParseDirtyWordsItem;
import com.sharecrm.egress.entity.ParseDirtyWordsResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.DirtyWordService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 脏词检测服务对外接口
 */
@RestController
@RequestMapping("/api/v2/dirty-words")
@RequiredArgsConstructor
public class DirtyWordController {

  private final DirtyWordService dirtyWordService;

  /**
   * 解析脏词，返回解析到的脏词列表
   *
   * @param request 脏词请求体
   * @return 脏词解析结果
   */
  @PostMapping("/parse")
  @Tag(name = "dirty-word", description = "脏词检测服务")
  public Mono<ResponseEntity<EgressApiResponse<ParseDirtyWordsResponse>>> parse(@Validated @RequestBody DirtyWordsRequest request) {
    return Mono.just(dirtyWordService.parse(request));
  }

  /**
   * 判断是否含有脏词，返回简单的boolean值
   *
   * @param request 脏词请求体
   * @return 脏词解析结果
   */
  @PostMapping("/has")
  @Tag(name = "dirty-word", description = "脏词检测服务")
  public Mono<ResponseEntity<EgressApiResponse<HasDirtyWordsResponse>>> hasDirtyWords(@Validated @RequestBody DirtyWordsRequest request) {
    return Mono.just(dirtyWordService.hasDirtyWords(request));
  }

  @PostMapping(value = "/parse/{group}", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
  @Tag(name = "dirty-word", description = "脏词检测服务（给老函数服务用，其他人不要用）")
  public Mono<ResponseEntity<List<ParseDirtyWordsItem>>> parseFunction(@PathVariable String group, @RequestBody String text) {
    return Mono.just(ResponseEntity.ok(dirtyWordService.parseForFunction(group, text)));
  }

}
