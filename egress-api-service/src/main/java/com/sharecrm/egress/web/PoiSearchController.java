package com.sharecrm.egress.web;

import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.Constants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_EMPTY;

/**
 * 搜索POI信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api/v2/poi", produces = "application/json;charset=UTF-8")
public class PoiSearchController {
  private static final String ZH_CN = "zh-CN";

  private static final Function<Throwable, Mono<ResponseEntity<EgressApiResponse<PoiResponse>>>> INTERNAL_ERROR = e -> Mono
    .just(ResponseEntity
      .internalServerError().body(new EgressApiResponse<>(500, e.getMessage())));

  private final GeoService geoService;

  public PoiSearchController(GeoService geoService) {
    this.geoService = geoService;
  }

  @PostMapping("/search")
  @Tag(name = "poi", description = "搜索POI信息")
  @Operation(summary = "搜索POI信息", description = "搜索中心点坐标的圆形区域内的OI信息", operationId = "search")
  public Mono<ResponseEntity<EgressApiResponse<PoiResponse>>> search(@Validated @RequestBody PoiSearchRequest request) {
    return geoService.queryPoiAround(request, MAP_PROVIDER_EMPTY)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  @GetMapping("/around")
  @Tag(name = "poi", description = "搜索POI信息")
  @Operation(summary = "搜索POI信息", description = "搜索中心点坐标的圆形区域内的OI信息", operationId = "around", parameters = {
    @Parameter(name = "latLng", description = "中心点坐标", example = "39.976809,116.332463", required = true),
    @Parameter(name = "radius", description = "搜索半径", example = "2000", required = true),
    @Parameter(name = "keyword", description = "搜索关键字", example = "银行", required = true),
    @Parameter(name = "types", description = "搜索类型，每个地图服务商的类型不一致，需要分别处理"),
    @Parameter(name = "pageSize", description = "每页大小", example = "10"),
    @Parameter(name = "pageNum", description = "页码", example = "1"),
    @Parameter(name = "cache", description = "是否使用缓存")})
  public Mono<ResponseEntity<EgressApiResponse<PoiResponse>>> around(@RequestParam String latLng,
                                                                     @RequestParam int radius,
                                                                     @RequestParam String keyword,
                                                                     @RequestParam(required = false) String types,
                                                                     @RequestParam(defaultValue = "10") int pageSize,
                                                                     @RequestParam(defaultValue = "1") int pageNum,
                                                                     @RequestParam(defaultValue = "true") boolean cache) {
    PoiSearchRequest request = createSearchRequest(latLng, radius, keyword, types, pageSize, pageNum, null, ZH_CN, cache);
    return geoService.queryPoiAround(request, MAP_PROVIDER_EMPTY)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  @GetMapping("/around/amap")
  @Tag(name = "poi", description = "搜索POI信息")
  @Operation(summary = "用高德地图搜索POI信息", description = "搜索中心点坐标的圆形区域内的OI信息", operationId = "aroundByAmap", parameters = {
    @Parameter(name = "latLng", description = "中心点坐标", example = "39.976809,116.332463", required = true),
    @Parameter(name = "radius", description = "搜索半径", example = "2000", required = true),
    @Parameter(name = "keyword", description = "搜索关键字", example = "银行", required = true),
    @Parameter(name = "types", description = "搜索类型"),
    @Parameter(name = "pageSize", description = "每页大小", example = "10"),
    @Parameter(name = "pageNum", description = "页码", example = "1"),
    @Parameter(name = "cache", description = "是否使用缓存")})
  public Mono<ResponseEntity<EgressApiResponse<PoiResponse>>> aroundByAmap(@RequestParam String latLng,
                                                                           @RequestParam int radius,
                                                                           @RequestParam String keyword,
                                                                           @RequestParam(required = false) String types,
                                                                           @RequestParam(defaultValue = "10") int pageSize,
                                                                           @RequestParam(defaultValue = "1") int pageNum,
                                                                           @RequestParam(defaultValue = "true") boolean cache) {
    PoiSearchRequest request = createSearchRequest(latLng, radius, keyword, types, pageSize, pageNum, null, ZH_CN, cache);
    return geoService.queryPoiAround(request, Constants.MAP_PROVIDER_AMAP)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  @GetMapping("/around/baidu")
  @Tag(name = "poi", description = "搜索POI信息")
  @Operation(summary = "用百度地图搜索POI信息", description = "搜索中心点坐标的圆形区域内的OI信息", operationId = "aroundByBaidu", parameters = {
    @Parameter(name = "latLng", description = "中心点坐标", example = "39.976809,116.332463", required = true),
    @Parameter(name = "radius", description = "搜索半径", example = "2000", required = true),
    @Parameter(name = "keyword", description = "搜索关键字", example = "银行", required = true),
    @Parameter(name = "types", description = "搜索类型"),
    @Parameter(name = "pageSize", description = "每页大小", example = "10"),
    @Parameter(name = "pageNum", description = "页码", example = "1"),
    @Parameter(name = "cache", description = "是否使用缓存")})
  public Mono<ResponseEntity<EgressApiResponse<PoiResponse>>> aroundByBaidu(@RequestParam String latLng,
                                                                            @RequestParam int radius,
                                                                            @RequestParam String keyword,
                                                                            @RequestParam(required = false) String types,
                                                                            @RequestParam(defaultValue = "10") int pageSize,
                                                                            @RequestParam(defaultValue = "1") int pageNum,
                                                                            @RequestParam(defaultValue = "true") boolean cache) {
    PoiSearchRequest request = createSearchRequest(latLng, radius, keyword, types, pageSize, pageNum, null, ZH_CN, cache);
    return geoService.queryPoiAround(request, Constants.MAP_PROVIDER_BAIDU)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  @GetMapping("/around/tencent")
  @Tag(name = "poi", description = "搜索POI信息")
  @Operation(summary = "用腾讯地图搜索POI信息", description = "搜索中心点坐标的圆形区域内的OI信息", operationId = "aroundByTencent", parameters = {
    @Parameter(name = "latLng", description = "中心点坐标", example = "39.976809,116.332463", required = true),
    @Parameter(name = "radius", description = "搜索半径", example = "2000", required = true),
    @Parameter(name = "keyword", description = "搜索关键字", example = "银行", required = true),
    @Parameter(name = "types", description = "搜索类型"),
    @Parameter(name = "pageSize", description = "每页大小", example = "10"),
    @Parameter(name = "pageNum", description = "页码", example = "1"),
    @Parameter(name = "cache", description = "是否使用缓存")})
  public Mono<ResponseEntity<EgressApiResponse<PoiResponse>>> aroundByTencent(@RequestParam String latLng,
                                                                              @RequestParam int radius,
                                                                              @RequestParam String keyword,
                                                                              @RequestParam(required = false) String types,
                                                                              @RequestParam(defaultValue = "10") int pageSize,
                                                                              @RequestParam(defaultValue = "1") int pageNum,
                                                                              @RequestParam(defaultValue = "true") boolean cache) {
    PoiSearchRequest request = createSearchRequest(latLng, radius, keyword, types, pageSize, pageNum, null, ZH_CN, cache);
    return geoService.queryPoiAround(request, Constants.MAP_PROVIDER_TENCENT)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  @GetMapping("/around/google")
  @Tag(name = "poi", description = "搜索POI信息")
  @Operation(summary = "用谷歌地图搜索POI信息", description = "搜索中心点坐标的圆形区域内的OI信息", operationId = "aroundByGoogle", parameters = {
    @Parameter(name = "latLng", description = "中心点坐标", example = "39.976809,116.332463", required = true),
    @Parameter(name = "radius", description = "搜索半径", example = "2000", required = true),
    @Parameter(name = "keyword", description = "搜索关键字", example = "银行", required = true),
    @Parameter(name = "types", description = "搜索类型"),
    @Parameter(name = "pageToken", description = "翻页Token"),
    @Parameter(name = "language", description = "语言", example = "zh-CN"),
    @Parameter(name = "cache", description = "是否使用缓存")})
  public Mono<ResponseEntity<EgressApiResponse<PoiResponse>>> aroundByGoogle(@RequestParam String latLng,
                                                                             @RequestParam int radius,
                                                                             @RequestParam String keyword,
                                                                             @RequestParam(required = false) String types,
                                                                             @RequestParam(required = false) String pageToken,
                                                                             @RequestParam(required = false) String language,
                                                                             @RequestParam(defaultValue = "true") boolean cache) {
    PoiSearchRequest request = createSearchRequest(latLng, radius, keyword, types, 0, 0, pageToken, language, cache);
    return geoService.queryPoiAround(request, Constants.MAP_PROVIDER_GOOGLE)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  private static PoiSearchRequest createSearchRequest(String latLng, int radius, String keyword, String types,
                                                      int pageSize, int pageNum, String pageToken, String language, boolean cache) {
    LocationPoint point = LocationPoint.parseLatitudeLongitude(latLng);
    CharMatcher matcher = CharMatcher.anyOf("|;,");
    List<String> keys = Splitter.on(matcher).splitToList(keyword);
    List<String> cats = Splitter.on(matcher).splitToList(Optional.ofNullable(types).orElse(""));
    PoiSearchRequest request = new PoiSearchRequest();
    request.setLongitude(point.getLongitude());
    request.setLatitude(point.getLatitude());
    request.setRadius(radius);
    request.setKeywords(keys);
    request.setTypes(cats);
    request.setPageSize(pageSize);
    request.setPageNum(pageNum);
    request.setPageToken(pageToken);
    request.setLanguage(language);
    request.setCache(cache);
    return request;
  }
}
