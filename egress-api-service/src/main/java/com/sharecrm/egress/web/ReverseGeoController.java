package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.Constants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 逆地址解析，提供将经纬度信息转换为地址信息的服务
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/v2/reverse-geocode", produces = "application/json;charset=UTF-8")
public class ReverseGeoController {

  private final GeoService service;

  public ReverseGeoController(GeoService service) {
    this.service = service;
  }

  @PostMapping("/reverse")
  public Mono<ResponseEntity<EgressApiResponse<GeoAddress>>> decodeByPost(@Validated @RequestBody Mono<ReverseGeoRequest> req) {
    return req
      .flatMap(request -> service.queryReverseGeoAddress(request)
        .switchIfEmpty(Mono.defer(() -> Mono.just(fallback(request.getLatitude() + "," + request.getLongitude()))))
        .onErrorResume(e -> {
          log.error("reverse geocode failed. will return fallback empty", e);
          return Mono.just(fallback(request.getLatitude() + "," + request.getLongitude()));
        })
      )
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok);
  }

  @GetMapping("/decode")
  @Tag(name = "decode", description = "经纬度查询地址信息，逆地址解析")
  @Tag(name = "geocode", description = "经纬度编码")
  @Operation(summary = "逆地址解析", description = "将经纬度信息转换为地址信息", operationId = "decode", parameters = {
    @Parameter(name = "latLng", description = "经纬度信息，格式：纬度,经度。例如：39.990464,116.481488", example = "39.990464,116.481488", required = true),
    @Parameter(name = "cache", description = "是否使用缓存，默认为true")
  })
  public Mono<ResponseEntity<EgressApiResponse<GeoAddress>>> decode(@RequestParam String latLng,
                                                                    @RequestParam(defaultValue = "true") boolean cache) {
    return service.queryReverseGeoAddress(defaultRequest(latLng, cache))
      .switchIfEmpty(Mono.defer(() -> Mono.just(fallback(latLng))))
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(this::error);
  }

  private ReverseGeoRequest defaultRequest(String latLng, boolean cache) {
    LocationPoint point = LocationPoint.parseLatitudeLongitude(latLng);
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(point.getLatitude());
    request.setLongitude(point.getLongitude());
    request.setRadius(0);
    request.setCache(cache);
    return request;
  }


  @GetMapping("/decode/google")
  @Tag(name = "decode", description = "经纬度查询地址信息，逆地址解析")
  @Tag(name = "geocode", description = "经纬度编码")
  @Operation(summary = "逆地址解析(Google)", description = "将经纬度信息转换为地址信息", operationId = "decodeGoogle", parameters = {
    @Parameter(name = "latLng", description = "经纬度信息，格式：纬度,经度。例如：39.990464,116.481488", example = "39.990464,116.481488", required = true),
    @Parameter(name = "cache", description = "是否使用缓存，默认为true")
  })
  public Mono<ResponseEntity<EgressApiResponse<GeoAddress>>> queryGoogle(@RequestParam String latLng,
                                                                         @RequestParam(defaultValue = "true") boolean cache) {
    ReverseGeoRequest request = defaultRequest(latLng, cache);
    return service.queryReverseGeoAddress(request, Constants.MAP_PROVIDER_GOOGLE)
      .switchIfEmpty(Mono.defer(() -> Mono.just(fallback(latLng))))
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(this::error);
  }

  /**
   * 批量逆地址解析
   *
   * @param codes 逗号分隔的经纬度字符串，格式："维度,经度"。例如：39.990464,116.481488；多个经纬度用顿号";"分隔
   * @return 地址信息列表
   */
  @Tag(name = "decode", description = "经纬度查询地址信息，逆地址解析")
  @Tag(name = "geocode", description = "经纬度编码")
  @Operation(summary = "批量逆地址解析", description = "将经纬度信息转换为地址信息", operationId = "batchDecode", parameters = {
    @Parameter(name = "codes", description = "经纬度信息，格式：纬度,经度。例如：39.990464,116.481488", example = "39.990464,116.481488;40.041304,116.275304", required = true)
  })
  @GetMapping(value = "/batch-decode")
  public Mono<ResponseEntity<EgressApiResponse<List<GeoAddress>>>> batchDecode(@RequestParam String codes) {
    return Mono.just(codes)
      .map(s -> s.split(";"))
      .flatMapMany(Flux::just)
      .map(String::trim)
      .filter(s -> !s.isEmpty())
      .distinct()
      .flatMap(this::queryOrFallback)
      .collectList()
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .defaultIfEmpty(ResponseEntity.status(404).body(new EgressApiResponse<>(404, "Not Found")))
      .onErrorResume(
        e -> Mono.just(ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, e.getMessage()))));
  }

  @NotNull
  private Mono<GeoAddress> queryOrFallback(String latLng) {
    ReverseGeoRequest request = defaultRequest(latLng, true);
    return service.queryReverseGeoAddress(request)
      .switchIfEmpty(Mono.defer(() -> Mono.just(fallback(latLng))))
      .onErrorReturn(fallback(latLng));
  }

  private GeoAddress fallback(String latLng) {
    log.info("geo address not found, use fallback empty object");
    //很多业务并不希望返回404，咱们很多框架比如Restful或者Http Client 都把404视为错误需要业务方自己捕捉异常
    GeoAddress address = new GeoAddress();
    address.setGeocode(latLng);
    return address;
  }

  private Mono<ResponseEntity<EgressApiResponse<GeoAddress>>> error(Throwable e) {
    return Mono.just(ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, e.getMessage())));
  }

}
