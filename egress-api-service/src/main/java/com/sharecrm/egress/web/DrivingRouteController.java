package com.sharecrm.egress.web;

import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.Constants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Function;

/**
 * 驾车路线起始位置之间距离测算
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api/v2/route", produces = "application/json;charset=UTF-8")
public class DrivingRouteController {
  private static final Function<Throwable, Mono<ResponseEntity<EgressApiResponse<PointDistance>>>> INTERNAL_ERROR = e -> Mono
    .just(ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, e.getMessage())));

  private final GeoService geoService;

  public DrivingRouteController(GeoService geoService) {
    this.geoService = geoService;
  }


  /**
   * 驾车路线起始位置之间距离测算
   *
   * @param from      起始经纬度，格式：lat,lng
   * @param to        目的地经纬度，格式：lat,lng
   * @param waypoints 途径点（最多16个）经纬度，格式：lat1,lng1;lat2,lng2;lat3,lng3
   * @param cache     是否使用缓存
   * @return 路线信息
   */
  @GetMapping("/driving")
  @Tag(name = "driving", description = "驾车路线起始位置之间距离测算")
  @Operation(summary = "驾车路线起始位置之间距离测算", description = "驾车路线起始位置之间距离测算，可能会因为路况信息变化而有所不同，不易长时间缓存计算结果", operationId = "driving", parameters = {
    @Parameter(name = "from", description = "起始经纬度，格式：lat,lng", example = "39.889049,116.325275", required = true),
    @Parameter(name = "to", description = "目的地经纬度，格式：lat,lng", example = "39.976809,116.332463", required = true),
    @Parameter(name = "waypoints", description = "途径点（最多16个）经纬度，格式：lat1,lng1;lat2,lng2;lat3,lng3"),
    @Parameter(name = "cache", description = "是否使用缓存")
  })
  public Mono<ResponseEntity<EgressApiResponse<PointDistance>>> driving(@RequestParam String from,
                                                                        @RequestParam String to,
                                                                        @RequestParam(required = false) String waypoints,
                                                                        @RequestParam(defaultValue = "true") boolean cache) {
    DrivingRouteRequest.DrivingRouteRequestBuilder builder = defaultRequestBuilder(from, to, waypoints);
    return geoService.queryDriving(builder.build(), cache, "")
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  /**
   * 使用谷歌API测算驾车路线起始位置之间距离
   *
   * @param from      起始经纬度，格式：lat,lng
   * @param to        目的地经纬度，格式：lat,lng
   * @param waypoints 途径点（最多16个）经纬度，格式：lat1,lng1;lat2,lng2;lat3,lng3
   * @param cache     是否使用缓存
   * @return 路线信息
   */
  @GetMapping("/driving/google")
  @Tag(name = "driving", description = "使用谷歌API测算驾车路线起始位置之间距离")
  @Operation(summary = "驾车路线起始位置之间距离测算", description = "驾车路线起始位置之间距离测算，可能会因为路况信息变化而有所不同，不易长时间缓存计算结果", operationId = "driving", parameters = {
    @Parameter(name = "from", description = "起始经纬度，格式：lat,lng", example = "39.889049,116.325275", required = true),
    @Parameter(name = "to", description = "目的地经纬度，格式：lat,lng", example = "39.976809,116.332463", required = true),
    @Parameter(name = "waypoints", description = "途径点（最多16个）经纬度，格式：lat1,lng1;lat2,lng2;lat3,lng3"),
    @Parameter(name = "cache", description = "是否使用缓存")
  })
  public Mono<ResponseEntity<EgressApiResponse<PointDistance>>> drivingByGoogle(@RequestParam String from,
                                                                                @RequestParam String to,
                                                                                @RequestParam(required = false) String waypoints,
                                                                                @RequestParam(defaultValue = "true") boolean cache) {
    DrivingRouteRequest.DrivingRouteRequestBuilder builder = defaultRequestBuilder(from, to, waypoints);
    //限制必须使用Google
    return geoService.queryDriving(builder.build(), cache, Constants.MAP_PROVIDER_GOOGLE)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(404, "Not Found"))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  private DrivingRouteRequest.DrivingRouteRequestBuilder defaultRequestBuilder(String from, String to, String waypoints) {
    LocationPoint origin = LocationPoint.parseLatitudeLongitude(from);
    LocationPoint destination = LocationPoint.parseLatitudeLongitude(to);
    DrivingRouteRequest.DrivingRouteRequestBuilder builder = DrivingRouteRequest.builder().from(origin).to(destination);
    CharMatcher matcher = CharMatcher.anyOf(";|");
    if (waypoints != null && !waypoints.isEmpty()) {
      List<LocationPoint> points = Lists.newArrayList();
      Splitter.on(matcher).split(waypoints).forEach(s -> points.add(LocationPoint.parseLatitudeLongitude(s)));
      builder.waypoints(points);
    }
    return builder;
  }
}
