package com.sharecrm.egress.web;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.sms.SmsSender;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.SingleExecutorUtils;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 短信状态回调，对外暴露由短信服务商主动调用
 */
@Slf4j
@Hidden
@RestController
@RequestMapping("/api/v2/sms-callback")
@ConditionalOnProperty(name = "sharecrm.api.sms.callback.enabled", havingValue = "true", matchIfMissing = true)
public class SmsCallbackController {

  private final SmsProperties properties;
  private final ApplicationContext applicationContext;

  public SmsCallbackController(SmsProperties properties, ApplicationContext applicationContext) {
    this.properties = properties;
    this.applicationContext = applicationContext;
  }

  /**
   * 蒙牛短信状态回调，永远返回200
   */
  @Hidden
  @PostMapping("/mengniu/messages/{messageType}")
  public Mono<ResponseEntity<String>> mengniuSmsStatusCallback(@PathVariable("messageType") String messageType, @RequestBody String body, ServerWebExchange exchange) {
    // 无论啥结果，都返回成功
    return smsStatusCallback(properties.getMengniu().getId(), messageType, body, exchange);
  }

  /**
   * 其他云（目前只有华为云）短信状态回调，永远返回200
   */
  @Hidden
  @PostMapping(value = "/providers/{id}/{ext}", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE, MediaType.TEXT_XML_VALUE})
  public Mono<ResponseEntity<String>> smsStatusCallback(@PathVariable("id") String id, @PathVariable("ext") String ext, @RequestBody String body, ServerWebExchange exchange) {
    SingleExecutorUtils.execute(() ->
      SmsUtils.filterSmsSenderBeans(applicationContext.getBeansOfType(SmsSender.class))
        .stream()
        .filter(s -> s.provider().getId().equals(id))
        .forEach(s -> s.smsStatusCallback(ext, body, exchange)));
    return Mono.just(ResponseEntity.ok("OK"));
  }

  /**
   * 其他云（目前只有华为云）短信状态回调，永远返回200
   */
  @Hidden
  @PostMapping(value = "/providers/{id}/{ext}", consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE}, produces = MediaType.APPLICATION_JSON_VALUE)
  public Mono<ResponseEntity<String>> smsStatusCallbackFormData(@PathVariable("id") String id, @PathVariable("ext") String ext, ServerWebExchange exchange) {
    return exchange.getFormData()
      .flatMap(params -> {
        try {
          return smsStatusCallback(id, ext, JsonUtil.getGson().toJson(params.toSingleValueMap()), exchange);
        } catch (Exception e) {
          log.warn("get sms callback params error", e);
          return Mono.just(ResponseEntity.ok("OK"));
        }
      });
  }
}
