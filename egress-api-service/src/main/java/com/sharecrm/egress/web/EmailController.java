package com.sharecrm.egress.web;

import com.sharecrm.egress.email.EmailService;
import com.sharecrm.egress.entity.EmailInnerRequest;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 邮件服务，提供发送邮件接口
 */
@RestController
@RequestMapping("/api/v2/emails")
@RequiredArgsConstructor
public class EmailController {

  private final EmailService emailService;

  /**
   * 发送邮件，注意是异步发送，返回成功代表参数校验通过，不代表邮件发送成功
   *
   * @param request 请求体
   * @return 发送结果，成功返回200，失败或部分成功返回400
   */
  @PostMapping
  @Tag(name = "email", description = "邮件服务")
  public Mono<ResponseEntity<EgressApiResponse<EmailSendResponse>>> sendEmail(@Validated @RequestBody EmailSendRequest request) {
    return emailService.sendEmail(request)
      .map(this::response);
  }

  /**
   * 内部接口，业务不要用。
   * 发送邮件，注意是异步发送，返回成功代表参数校验通过，不代表邮件发送成功
   *
   * @param request 请求体
   * @return 发送结果，成功返回200，失败或部分成功返回400
   */
  @PostMapping("/sender")
  public Mono<ResponseEntity<EgressApiResponse<EmailSendResponse>>> sendInnerEmail(@Validated @RequestBody EmailInnerRequest request) {
    return emailService.sendInnerEmail(request)
      .map(this::response);
  }

  @NotNull
  private ResponseEntity<EgressApiResponse<EmailSendResponse>> response(EmailSendResponse e) {
    if (e.isSuccess()) {
      return ResponseEntity.ok(EgressApiResponse.ok(e));
    }
    return ResponseEntity.badRequest().body(EgressApiResponse.badRequest(e));
  }

}
