package com.sharecrm.egress.web;

import com.sharecrm.egress.config.ConditionalOnMobileEnabled;
import com.sharecrm.egress.entity.MobileLocation;
import com.sharecrm.egress.entity.MobileQueryBatchReq;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.MobileService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RestController
@ConditionalOnMobileEnabled
@RequestMapping("/api/v2/mobile")
public class MobileController {

  private final MobileService mobileService;

  public MobileController(MobileService mobileService) {
    this.mobileService = mobileService;
  }

  /**
   * 手机号码归属地查询
   *
   * @param mobile 手机号码
   * @return 归属地信息
   */
  @Tag(name = "mobile", description = "手机号码归属地查询")
  @Operation(summary = "根据手机号码的前7位解析归属地和运营商", description = "手机号码归属地查询", operationId = "query", parameters = {
    @Parameter(name = "mobile", description = "手机号码，必须是完整的11位数字号码", example = "13567894321", required = true),
    @Parameter(name = "language", description = "国际化语言信息", example = "zh-CN")})
  @GetMapping("/query")
  public Mono<ResponseEntity<EgressApiResponse<MobileLocation>>> query(@RequestParam String mobile,
                                                                       @RequestParam(defaultValue = "zh-CN") String language) {
    return queryOrDefaultEmpty(mobile, language)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok);
  }

  /**
   * 批量解析手机号的归属地信息
   *
   * @param mobiles 地址列表，多个地址用顿号";"分隔
   * @return 批量地址解析结果
   */
  @Tag(name = "mobile", description = "手机号码归属地查询")
  @Operation(summary = "批量解析手机号的归属地信息", description = "手机号归属地信息", operationId = "batchQuery", parameters = {
    @Parameter(name = "mobiles", description = "手机号,多个手机号之间用英文逗号分隔", example = "13167894321,13567894322,13867894323", required = true),
    @Parameter(name = "language", description = "国际化语言信息", example = "zh-CN")})
  @GetMapping(value = "/batch-query")
  public Mono<ResponseEntity<EgressApiResponse<List<MobileLocation>>>> batchQuery(@RequestParam(name = "mobiles") String mobiles,
                                                                                  @RequestParam(defaultValue = "zh-CN", name = "language") String language) {
    return Mono.just(mobiles)
      .map(s -> s.split(","))
      .flatMapMany(Flux::just)
      .map(String::trim)
      .filter(s -> !s.isEmpty())
      .distinct()
      .flatMap(phone -> queryOrDefaultEmpty(phone, language))
      .collectList()
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok);
  }

  /**
   * 函数服务使用
   */
  @Hidden
  @Tag(name = "mobile", description = "手机号码归属地查询")
  @GetMapping("/rest/query")
  public Mono<ResponseEntity<MobileLocation>> restQueryForProxy(@RequestParam String mobile,
                                                                @RequestParam(defaultValue = "zh-CN") String language) {
    return queryOrDefaultEmpty(mobile, language)
      .map(ResponseEntity::ok);
  }

  /**
   * 函数服务使用
   */
  @Hidden
  @Tag(name = "mobile", description = "手机号码归属地查询")
  @PostMapping("/rest/query-batch")
  public Mono<ResponseEntity<List<MobileLocation>>> restBatchQueryForProxy(@RequestBody MobileQueryBatchReq queryBatch,
                                                                @RequestParam(defaultValue = "zh-CN") String language) {
    return Mono.just(queryBatch.getMobiles())
      .map(s -> s.split(","))
      .flatMapMany(Flux::just)
      .map(String::trim)
      .filter(s -> !s.isEmpty())
      .distinct()
      .flatMap(phone -> queryOrDefaultEmpty(phone, language))
      .collectList()
      .map(ResponseEntity::ok);
  }

  @NotNull
  private Mono<MobileLocation> queryOrDefaultEmpty(String mobile, String language) {
    return mobileService.query(mobile, language)
      .defaultIfEmpty(new MobileLocation(mobile))
      .onErrorResume(e -> {
        log.warn("query mobile location failed.", e);
        return Mono.just(new MobileLocation(mobile));
      });
  }
}
