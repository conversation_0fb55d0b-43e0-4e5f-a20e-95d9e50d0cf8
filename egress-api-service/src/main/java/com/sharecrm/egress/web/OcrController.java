package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.OcrProvider;
import com.sharecrm.egress.service.OcrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 文字识别（Optical Character Recognition，OCR ）服务，提供图片、卡证、名片、发票等常用图片转文字服务
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@RestController
@RequestMapping("/api/v2/ocr")
@ConditionalOnBean(OcrProvider.class)
public class OcrController {

  private final OcrService ocrService;

  public OcrController(OcrService ocrService) {
    this.ocrService = ocrService;
  }

  /**
   * 名片识别
   *
   * @param request 请求体
   * @return 识别结果，一直返回Http 200，注意如果Tencent解析失败可能返回 Http Code是200，但是内部code是400
   */
  @PostMapping("/business-cards")
  @Tag(name = "ocr", description = "名片识别")
  @Operation(summary = "名片识别", description = "名片识别，注意图片经Base64编码后不超过 7M，图片越大解析时间越长",
    responses = {
      @ApiResponse(responseCode = "200", description = "解析结果，一直返回Http 200，注意如果Tencent解析失败可能返回Http Code是200，但是内部code是400"),
    })
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = BusinessCardRequest.class),
    examples = @ExampleObject(value = "{\"imageBase64\":\"图片的 Base64 值，经Base64编码后不超过 7M，支持的图片格式：PNG、JPG、JPEG\"}")))
  public Mono<ResponseEntity<EgressApiResponse<BusinessCardResponse>>> businessCard(@RequestBody Mono<BusinessCardRequest> request) {
    return ocrService.businessCard(request)
      .map(ResponseEntity::ok)
      //返回一个虚假的空的，有些业务不希望返回null
      .switchIfEmpty(Mono.defer(() -> Mono.just(fallback())));
  }

  @NotNull
  private static ResponseEntity<EgressApiResponse<BusinessCardResponse>> fallback() {
    log.info("business card response is empty, will return a fallback instance");
    return ResponseEntity.ok().body(EgressApiResponse.ok(new BusinessCardResponse()));
  }

}
