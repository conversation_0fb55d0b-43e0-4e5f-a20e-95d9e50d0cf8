package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.GeoIpLocation;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.IpLocationRequest;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.Constants;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_EMPTY;

/**
 * IP
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@RestController
@RequestMapping("/api/v2/ip")
public class IpLocationController {
  private final GeoService geoService;

  public IpLocationController(GeoService geoService) {
    this.geoService = geoService;
  }

  @PostMapping("/query-location")
  @Tag(name = "location", description = "IP地址解析")
  public Mono<ResponseEntity<EgressApiResponse<IpLocation>>> queryByPost(@Validated @RequestBody IpLocationRequest request) {
    return geoService.queryIpLocation(request)
      .mapNotNull(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(errorResume());
  }

  @GetMapping("/location")
  @Tag(name = "location", description = "IP地址解析")
  @Operation(summary = "IP地址解析", description = "IP地址解析", operationId = "location",
    parameters = {@Parameter(name = "ip", description = "IP地址", example = "***********", required = true)})
  public Mono<ResponseEntity<EgressApiResponse<IpLocation>>> query(String ip, @RequestParam(name = "language", defaultValue = "zh-CN") String language) {
    return geoService.queryIpLocation(ip, language, MAP_PROVIDER_EMPTY)
      .mapNotNull(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(errorResume());
  }

  @GetMapping("/location/amap")
  @Tag(name = "location", description = "IP地址解析")
  @Operation(summary = "使用高德API做IP地址解析（只支持国内IP，只支持中文语言）", description = "使用高德API做IP地址解析", operationId = "location",
    parameters = {@Parameter(name = "ip", description = "IP地址", example = "***********", required = true)})
  public Mono<ResponseEntity<EgressApiResponse<IpLocation>>> queryAmap(String ip, @RequestParam(name = "language", defaultValue = "zh-CN") String language) {
    return geoService.queryIpLocation(ip, language, Constants.MAP_PROVIDER_AMAP)
      .mapNotNull(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(errorResume());
  }

  @GetMapping("/location/baidu")
  @Tag(name = "location", description = "IP地址解析")
  @Operation(summary = "使用百度API做IP地址解析（只支持国内IP，只支持中文语言）", description = "使用百度API做IP地址解析", operationId = "location",
    parameters = {@Parameter(name = "ip", description = "IP地址", example = "***********")})
  public Mono<ResponseEntity<EgressApiResponse<IpLocation>>> queryBaidu(String ip, @RequestParam(name = "language", defaultValue = "zh-CN") String language) {
    return geoService.queryIpLocation(ip, language, Constants.MAP_PROVIDER_BAIDU)
      .mapNotNull(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(errorResume());
  }

  @NotNull
  private static Function<Throwable, Mono<? extends ResponseEntity<EgressApiResponse<IpLocation>>>> errorResume() {
    return e -> Mono.just(ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, e.getMessage())));
  }

  @GetMapping("/location/tencent")
  @Tag(name = "location", description = "IP地址解析")
  @Operation(summary = "使用腾讯API做IP地址解析（只支持国内IP，只支持中文语言）", description = "使用腾讯API做IP地址解析", operationId = "location",
    parameters = {@Parameter(name = "ip", description = "IP地址", example = "***********")})
  public Mono<ResponseEntity<EgressApiResponse<IpLocation>>> queryTencent(String ip, @RequestParam(name = "language", defaultValue = "zh-CN") String language) {
    return geoService.queryIpLocation(ip, language, Constants.MAP_PROVIDER_TENCENT)
      .mapNotNull(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(errorResume());
  }

  @GetMapping("/location/database")
  @Tag(name = "location", description = "IP地址解析")
  @Operation(summary = "使用GeoLite本地Database文件做IP地址解析", description = "使用GeoLite本地Database文件做IP地址解析", operationId = "location",
    parameters = {@Parameter(name = "ip", description = "IP地址", example = "***********")})
  public Mono<ResponseEntity<EgressApiResponse<IpLocation>>> queryGeoLite(String ip, @RequestParam(name = "language", defaultValue = "zh-CN") String language) {
    return geoService.queryIpLocation(ip, language, Constants.MAP_PROVIDER_MAXMIND)
      .mapNotNull(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(errorResume());
  }

  @GetMapping("/locations")
  @Tag(name = "location", description = "IP地址解析")
  @Operation(summary = "批量IP地址解析", description = "批量IP地址解析，使用英文逗号\",\"做分割", operationId = "location",
    parameters = {@Parameter(name = "ips", description = "IP地址列表", example = "***********,************,*************")})
  public Mono<ResponseEntity<EgressApiResponse<List<IpLocation>>>> batchQuery(String ips, @RequestParam(name = "language", defaultValue = "zh-CN") String language) {
    List<String> clean = Arrays.stream(ips.split(",")).map(String::trim).filter(v -> !v.isEmpty()).distinct().toList();
    return Flux
      .fromIterable(clean)
      .flatMap(ip -> geoService.queryIpLocation(ip, language, MAP_PROVIDER_EMPTY).onErrorReturn(new IpLocation(ip)))
      .parallel()
      .runOn(Schedulers.parallel())
      .sequential()
      .collectList()
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(e -> Mono.just(ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, e.getMessage()))));
  }

  /**
   * 为兼容老geo-ip-location服务的接口，因为有函数调用不能随便改结构体
   */
  @Hidden
  @GetMapping({"/find", "/rest/find"})
  public Mono<ResponseEntity<GeoIpLocation>> findByIp(@RequestParam String ip) {
    return geoService
      .queryIpLocation(ip, "zh-CN", MAP_PROVIDER_EMPTY)
      .map(ipl -> {
        GeoIpLocation rs = new GeoIpLocation(ipl.getIp());
        rs.setCountry(ipl.getCountry());
        rs.setProvince(ipl.getProvince());
        rs.setCity(ipl.getCity());
        rs.setRegion(ipl.getDistrict());
        return rs;
      })
      .map(ResponseEntity::ok);
  }

}
