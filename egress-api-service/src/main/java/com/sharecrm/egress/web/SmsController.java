package com.sharecrm.egress.web;

import com.facishare.sms.api.model.arg.SendSMSArg;
import com.facishare.sms.api.model.result.SendSMSServiceResult;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.entity.SmsPageRequest;
import com.sharecrm.egress.entity.SmsPageResult;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsSendFunctionReq;
import com.sharecrm.egress.entity.SmsSendFunctionRes;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.sms.SmsService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 * 短信服务，提供发送短信发送验证码等接口。 intl：国际短信； internal:国内短信；code-call：语音验证码；tts：文本转语音
 */
@Slf4j
@RestController
@ConditionalOnSmsEnabled
@RequestMapping("/api/v2/sms")
public class SmsController {

  private final SmsService smsService;

  public SmsController(SmsService smsService) {
    this.smsService = smsService;
  }

  /**
   * 发送国际短信
   *
   * @param request 短信请求体
   * @return 短信发送结果，成功返回200，失败或部分成功返回400
   */
  @PostMapping("/intl")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "发送国际短信", description = "发送国际短信，支持多个手机号，最多200个，一次手机号不要太多否则接口可能超时",
    responses = {
      @ApiResponse(responseCode = "200", description = "accepted"),
      @ApiResponse(responseCode = "400", description = "参数校验不合法")
    })
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = SmsSendRequest.class),
    examples = @ExampleObject(value = "{\"phones\":[\"008613545678901\"],\"content\":\"hello world\",\"bizName\": \"qixin\"}")))
  public Mono<ResponseEntity<EgressApiResponse<SmsSendResponse>>> sendIntl(@Validated @RequestBody SmsSendRequest request, ServerWebExchange exchange) {
    //国际短信
    return smsService.sendIntlSms(request, exchange)
      .map(this::response);
  }

  /**
   * 发送国内短信
   *
   * @param request 短信请求体
   * @return 短信发送结果
   */
  @PostMapping("/internal")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "国内短信", description = "发送国内短信，支持多个手机号，最多200个，一次手机号不要太多否则接口可能超时",
    responses = {
      @ApiResponse(responseCode = "200", description = "accepted"),
      @ApiResponse(responseCode = "400", description = "参数校验不合法")
    })
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = SmsSendRequest.class),
    examples = @ExampleObject(value = "{\"phones\":[\"13545678901\"],\"content\":\"hello world\",\"bizName\": \"qixin\"}")))
  public Mono<ResponseEntity<EgressApiResponse<SmsSendResponse>>> sendChineseSms(@Validated @RequestBody SmsSendRequest request, ServerWebExchange exchange) {
    //国内短信
    return smsService.sendChineseSms(request, exchange)
      .map(this::response);
  }

  /**
   * 发送语音验证码，把短信验证码转为TTS语音
   *
   * @param request 短信请求体
   * @return 短信发送结果
   */
  @PostMapping("/code-call")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "发送语音验证码，把短信验证码转为TTS语音", description = "发送语音验证码，把短信验证码转为TTS语音，支持多个手机号，最多200个",
    responses = {
      @ApiResponse(responseCode = "200", description = "accepted"),
      @ApiResponse(responseCode = "400", description = "参数校验不合法")
    })
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = SmsSendRequest.class),
    examples = @ExampleObject(value = "{\"phones\":[\"13545678901\"],\"content\":\"546789\",\"bizName\": \"qixin\"}")))
  public Mono<ResponseEntity<EgressApiResponse<SmsSendResponse>>> sendCodeCall(@Validated @RequestBody SmsSendRequest request, ServerWebExchange exchange) {
    return smsService.sendCodeCall(request, exchange)
      .map(this::response);
  }

  /**
   * 发送TTS语音
   *
   * @param request 短信请求体
   * @return 短信发送结果
   */
  @PostMapping("/tts")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "发送TTS语音", description = "发送TTS语音，支持多个手机号，最多200个,templateId单独找研发人员要",
    responses = {
      @ApiResponse(responseCode = "200", description = "accepted"),
      @ApiResponse(responseCode = "400", description = "参数校验不合法")
    })
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(schema = @Schema(implementation = TTSSendRequest.class),
    examples = @ExampleObject(value = "{\"phones\":[\"13545678901\"],\"templateId\": 300,\"templateParams\":[\"123456\"],\"bizName\": \"qixin\"}")))
  public Mono<ResponseEntity<EgressApiResponse<SmsSendResponse>>> sendTts(@Validated @RequestBody TTSSendRequest request, ServerWebExchange exchange) {
    return smsService.sendTts(request, exchange)
      .map(this::response);
  }

  @PostMapping("/template-send")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "按模板发送短信", description = "按模板发送短信",
    responses = {
      @ApiResponse(responseCode = "200", description = "返回200"),
      @ApiResponse(responseCode = "400", description = "参数校验不合法")
    })
  public Mono<ResponseEntity<EgressApiResponse<SmsSendResponse>>> sendByTemplate(@Validated @RequestBody SmsSendByTemplate request, ServerWebExchange exchange) {
    return smsService.sendByTemplate(request, exchange)
      .map(this::response);
  }

  @PostMapping("/templates")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "增加短信模板", description = "增加短信模板",
    responses = {
      @ApiResponse(responseCode = "200", description = "返回200，不一定代表模板审核通过，还需要校验模板详情中的内容"),
      @ApiResponse(responseCode = "400", description = "参数校验不合法")
    })
  public Mono<ResponseEntity<EgressApiResponse<SmsTemplateDetail>>> addTemplate(@Validated @RequestBody SmsTemplateRequest request, ServerWebExchange exchange) {
    return smsService.addTemplate(request, exchange)
      .map(ResponseEntity::ok);
  }

  @GetMapping("/templates")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "查询短信模板列表", description = "查询短信模板列表",
    responses = {
      @ApiResponse(responseCode = "200", description = "返回200")
    })
  public Mono<ResponseEntity<EgressApiResponse<List<SmsTemplateDetail>>>> queryTemplateList(@Validated Mono<SmsTemplateQuery> request, ServerWebExchange exchange) {
    return request.flatMap(e -> smsService.queryTemplates(e, exchange))
      .map(EgressApiResponse::ok)
      .map(ResponseEntity::ok);
  }

  @GetMapping("/templates/{id}")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "按ID查询短信模板详情", description = "按ID查询短信模板详情",
    responses = {
      @ApiResponse(responseCode = "200", description = "永远返回200,如果找不到，内部code才是404")
    })
  public Mono<ResponseEntity<EgressApiResponse<SmsTemplateDetail>>> queryTemplateById(@PathVariable("id") String id, ServerWebExchange exchange) {
    return smsService.queryTemplate(id, exchange)
      .map(EgressApiResponse::ok)
      .switchIfEmpty(Mono.defer(() -> Mono.just(EgressApiResponse.notFound())))
      .map(ResponseEntity::ok);
  }

  @NotNull
  private ResponseEntity<EgressApiResponse<SmsSendResponse>> response(SmsSendResponse e) {
    return ResponseEntity.ok(EgressApiResponse.ok(e));
  }

  /**
   * 查询短信发送记录，公开接口，隐藏敏感信息
   */
  @GetMapping("/status")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "分页查询短信发送记录", description = "分页查询短信发送记录",
    responses = {
      @ApiResponse(responseCode = "200", description = "返回200")
    })
  public Mono<ResponseEntity<SmsPageResult>> querySmsStatus(Mono<SmsPageRequest> request) {
    return request.flatMap(smsService::querySmsStatus)
      .map(ResponseEntity::ok);
  }

  /**
   * 查询短信历史记录，私有接口，给Java Console用于问题定位
   *
   * @param request 过滤参数
   * @return 短信历史记录
   */
  @Hidden
  @PostMapping("/histories")
  @Tag(name = "sms", description = "短信服务")
  public Mono<ResponseEntity<SmsPageResult>> queryHistories(@RequestBody Mono<SmsPageRequest> request) {
    return request.flatMap(smsService::queryHistories)
      .map(ResponseEntity::ok);
  }

  @GetMapping("/providers")
  @Tag(name = "sms", description = "短信服务")
  @Operation(summary = "按EI查询是否有专属的短信通道", description = "按EI查询是否有专属的短信通道，只返回专属通道",
    responses = {
      @ApiResponse(responseCode = "200", description = "返回200")
    })
  public Mono<ResponseEntity<EgressApiResponse<List<SmsProvider>>>> queryExclusiveProvider(@RequestParam("ei") Integer enterpriseId, ServerWebExchange exchange) {
    return smsService.queryExclusiveProvider(enterpriseId, exchange)
      .map(EgressApiResponse::ok)
      .map(ResponseEntity::ok);
  }

  @Hidden
  @PostMapping("/api/json/send")
  @Tag(name = "sms", description = "短信服务-只能发送国内短信，只给函数服务调用")
  public Mono<ResponseEntity<SmsSendFunctionRes>> sendInternalFunction(@Validated @RequestBody SmsSendFunctionReq request, ServerWebExchange exchange) {
    //国内短信
    return sendForFunction(request, exchange);
  }

  @Hidden
  @GetMapping("/api/json/send")
  @Tag(name = "sms", description = "短信服务-只能发送国内短信，只给函数服务调用")
  public Mono<ResponseEntity<SmsSendFunctionRes>> sendInternalFunctionByGet(@RequestParam(required = false) String phone, @RequestParam(required = false) String param, ServerWebExchange exchange) {
    //函数服务不允许返回错误，但是又有非法值
    if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(param)) {
      return Mono.just(ResponseEntity.ok(new SmsSendFunctionRes(false, "param is empty")));
    }
    SmsSendFunctionReq req = new SmsSendFunctionReq();
    req.setParam(param);
    req.setPhone(phone);
    return sendForFunction(req, exchange);
  }

  @Hidden
  @PostMapping(name = "/api/send", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE, consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @Tag(name = "sms", description = "短信服务-只能发送国内短信，只给老的.Net服务用")
  public Mono<ResponseEntity<byte[]>> sendDotNetStream(byte[] content, ServerWebExchange exchange) {
    SendSMSArg sendSMSArg = new SendSMSArg();
    sendSMSArg.fromProto(content);
    log.info("send sms by stream api: {}", sendSMSArg);
    SmsSendRequest req = new SmsSendRequest();
    req.setContent(sendSMSArg.getParam());
    req.setPhones(Arrays.stream(sendSMSArg.getPhone().split(",")).toList());
    req.setBizName("sms-stream");
    return smsService.sendChineseSms(req, exchange)
      .map(e -> new SendSMSServiceResult(e.isSuccess(), e.getMessage()))
      .map(e -> {
        byte[] datas = e.toProto();
        return ResponseEntity.status(HttpStatus.OK)
          .header("Content-Length", datas.length + "")
          .header("FSI-Status", "Success")
          .header("FSI-Content", "ProtoBuf")
          .body(datas);
      });
  }

  private Mono<ResponseEntity<SmsSendFunctionRes>> sendForFunction(SmsSendFunctionReq request, ServerWebExchange exchange) {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent(request.getParam());
    req.setPhones(Arrays.stream(request.getPhone().split(",")).toList());
    req.setBizName("paas-function");
    return smsService.sendChineseSms(req, exchange)
      .map(e -> new SmsSendFunctionRes(e.isSuccess(), e.getMessage()))
      .map(ResponseEntity::ok);
  }


}
