package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.CoordinateConvertRequest;
import com.sharecrm.egress.entity.CoordinateConvertResponse;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 坐标系转换接口
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/v2/coordinate")
public class CoordinateConvertController {

  private final GeoService service;

  public CoordinateConvertController(GeoService service) {
    this.service = service;
  }

  @PostMapping("/convert")
  public Mono<ResponseEntity<EgressApiResponse<CoordinateConvertResponse>>> convert(@Validated @RequestBody Mono<CoordinateConvertRequest> req) {
    return req
      .flatMap(service::coordinateConvert)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok);
  }


}
