package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.LocationPointForApl;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.Constants;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.sharecrm.egress.utils.Constants.MAP_PROVIDER_EMPTY;

/**
 * 地理位置解析服务，提供将结构化地址数据（如：北京市海淀区上地十街十号）转换为对应坐标点（经纬度）功能。
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api/v2/geocode", produces = "application/json;charset=UTF-8")
public class GeoCodeController {

  private final GeoService geoService;

  public GeoCodeController(GeoService geoService) {
    this.geoService = geoService;
  }

  @GetMapping("/encode")
  @Tag(name = "encode", description = "地址解析到经纬度")
  @Operation(summary = "地址解析到经纬度", description = "地址解析到经纬度", operationId = "encode", parameters = {
    @Parameter(name = "address", description = "地址", example = "北京市海淀区知春路甲63号", required = true),
    @Parameter(name = "city", description = "城市", example = "北京市"),
    @Parameter(name = "cache", description = "是否使用缓存")
  })
  public Mono<ResponseEntity<EgressApiResponse<LocationPoint>>> encode(@RequestParam String address,
                                                                       @RequestParam(required = false) String city,
                                                                       @RequestParam(defaultValue = "true") boolean cache) {
    return geoService.queryLocationPoint(address, city, cache, MAP_PROVIDER_EMPTY)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(400, "codec failed"))
      .onErrorResume(e -> Mono.just(new EgressApiResponse<>(500, "internal error")))
      .map(ResponseEntity::ok);
  }

  /**
   * 单独为老版本APL函数兼容的接口，与常规的接口不同的是，返回的Http Code都是200，结构体也不一样
   */
  @Hidden
  @PostMapping("/dec")
  public Mono<ResponseEntity<EgressApiResponse<LocationPointForApl>>> decForApl(@RequestParam String address,
                                                                                @RequestParam(required = false) String city,
                                                                                @RequestParam(defaultValue = "true") boolean cache) {
    return geoService.queryLocationPoint(address, city, cache, MAP_PROVIDER_EMPTY)
      // 注意这里消息体结构和常规的不同
      .map(e -> new LocationPointForApl(e.getLongitude(), e.getLatitude()))
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(400, "codec failed"))
      .onErrorResume(e -> Mono.just(new EgressApiResponse<>(500, "internal error")))
      .map(ResponseEntity::ok);
  }

  @GetMapping("/encode/google")
  @Tag(name = "encode", description = "地址解析到经纬度")
  @Operation(summary = "使用谷歌API做地址解析", description = "使用谷歌API做地址解析", operationId = "googleEncode", parameters = {
    @Parameter(name = "address", description = "地址", example = "北京市海淀区知春路甲63号", required = true),
    @Parameter(name = "city", description = "城市", example = "北京市"),
    @Parameter(name = "cache", description = "是否使用缓存")
  })
  public Mono<ResponseEntity<EgressApiResponse<LocationPoint>>> queryGoogle(@RequestParam String address,
                                                                            @RequestParam(required = false) String city,
                                                                            @RequestParam(defaultValue = "true") boolean cache) {
    return geoService.queryLocationPoint(address, city, cache, Constants.MAP_PROVIDER_GOOGLE)
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .defaultIfEmpty(new EgressApiResponse<>(400, "codec failed"))
      .onErrorResume(e -> Mono.just(new EgressApiResponse<>(500, "internal error")))
      .map(ResponseEntity::ok);
  }

  /**
   * 批量地址解析
   *
   * @param addresses 地址列表，多个地址用顿号";"分隔
   * @return 批量地址解析结果
   */
  @Tag(name = "encode", description = "地址解析到经纬度")
  @Operation(summary = "批量地址解析", description = "批量地址解析", operationId = "batchEncode", parameters = {
    @Parameter(name = "addresses", description = "地址列表，多个地址用顿号';'分隔", example = "北京市海淀区知春路甲63号;北京市海淀区上地十街十号", required = true)
  })
  @GetMapping(value = "/batch-encode")
  public Mono<ResponseEntity<EgressApiResponse<List<GeoAddress>>>> batchEncode(@RequestParam String addresses) {
    return Mono.just(addresses)
      .map(s -> s.split(";"))
      .flatMapMany(Flux::just)
      .map(String::trim)
      .filter(s -> !s.isEmpty())
      .distinct()
      .flatMap(this::queryOrFallback)
      .collectList()
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok)
      .onErrorResume(
        e -> Mono.just(ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, e.getMessage()))));
  }

  @NotNull
  private Mono<GeoAddress> queryOrFallback(String address) {
    return geoService.queryGeoAddress(new GeoEncodeRequest(address))
      .defaultIfEmpty(new GeoAddress(address))
      .onErrorReturn(new GeoAddress(address));
  }

  @GetMapping(value = "/geo-address")
  @Tag(name = "encode", description = "地址解析到经纬度以及规范化的省市区位置")
  @Operation(summary = "地址解析到经纬度以及规范化的省市区位置", description = "地址解析到经纬度以及规范化的省市区位置", operationId = "geo-address", parameters = {
    @Parameter(name = "address", description = "地址", example = "北京市海淀区知春路甲63号", required = true),
    @Parameter(name = "city", description = "城市", example = "北京市"),
    @Parameter(name = "cache", description = "是否使用缓存")
  })
  public Mono<ResponseEntity<EgressApiResponse<GeoAddress>>> geoAddress(@RequestParam String address,
                                                                        @RequestParam(required = false) String city,
                                                                        @RequestParam(defaultValue = "true") boolean cache) {
    GeoEncodeRequest req = new GeoEncodeRequest(address, city);
    req.setCache(cache);
    req.setLanguage(Constants.ZH_CN);
    return geoService.queryGeoAddress(req)
      .defaultIfEmpty(new GeoAddress(address))
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok);
  }

  @GetMapping(value = "/geo-address/google")
  @Tag(name = "encode", description = "地址解析到经纬度以及规范化的省市区位置")
  @Operation(summary = "地址解析到经纬度以及规范化的省市区位置", description = "地址解析到经纬度以及规范化的省市区位置", operationId = "geo-address", parameters = {
    @Parameter(name = "address", description = "地址", example = "北京市海淀区知春路甲63号", required = true),
    @Parameter(name = "city", description = "城市", example = "北京市"),
    @Parameter(name = "cache", description = "是否使用缓存")
  })
  public Mono<ResponseEntity<EgressApiResponse<GeoAddress>>> geoAddressGoogle(@RequestParam String address,
                                                                              @RequestParam(required = false) String city,
                                                                              @RequestParam(defaultValue = "true") boolean cache) {
    GeoEncodeRequest req = new GeoEncodeRequest(address, city);
    req.setCache(cache);
    req.setLanguage(Constants.ZH_CN);
    req.setProvider(Constants.MAP_PROVIDER_GOOGLE);
    return geoService.queryGeoAddress(req)
      .defaultIfEmpty(new GeoAddress(address))
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok);
  }

  @PostMapping(value = "/geo-address-encode")
  @Tag(name = "encode", description = "地址解析到经纬度以及规范化的省市区位置")
  public Mono<ResponseEntity<EgressApiResponse<GeoAddress>>> geoAddressPost(@Validated @RequestBody GeoEncodeRequest request) {
    //多语: 高德不支持、腾讯不支持
    return geoService.queryGeoAddress(request)
      .defaultIfEmpty(new GeoAddress(request.getAddress()))
      .map(data -> new EgressApiResponse<>(200, "ok", data))
      .map(ResponseEntity::ok);
  }

}
