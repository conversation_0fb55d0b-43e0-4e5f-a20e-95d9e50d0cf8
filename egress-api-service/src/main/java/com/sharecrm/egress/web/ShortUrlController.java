package com.sharecrm.egress.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sharecrm.egress.config.ConditionalOnShortUrlEnabled;
import com.sharecrm.egress.entity.ShortUrl;
import com.sharecrm.egress.entity.ShortUrlBatchCreateRequest;
import com.sharecrm.egress.entity.ShortUrlBatchCreateResponse;
import com.sharecrm.egress.entity.ShortUrlCreateRequest;
import com.sharecrm.egress.entity.ShortUrlCreateResponse;
import com.sharecrm.egress.entity.ShortUrlOriginalRequest;
import com.sharecrm.egress.entity.ShortUrlOriginalResponse;
import com.sharecrm.egress.entity.ShortUrlQueryRequest;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.ShortUrlService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import static com.sharecrm.egress.utils.ShortUrlUtils.readWelcomeHtml;
import static org.springframework.http.HttpHeaders.USER_AGENT;
import static org.springframework.http.HttpStatus.FOUND;
import static org.springframework.http.HttpStatus.NOT_ACCEPTABLE;

/**
 * 短链服务接口，短链和长链互相转换。注意这里的private接口函数会用，不要随便改。
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@RestController
@RequestMapping("/api/v2")
@RequiredArgsConstructor
@ConditionalOnShortUrlEnabled
public class ShortUrlController {

  private final ShortUrlService shortUrlService;

  /**
   * 根据短链code获取原始链接，注意返回码是302直接让页面跳转。
   * <p>
   * 注意public-short-urls会直接公开到外网不要放其他接口。 内部服务请使用private-short-urls接口。
   * <p/>
   *
   * @param code      短链code
   * @param userAgent Header中Agent
   * @return 302 page or 404
   */
  @Hidden
  @GetMapping("/public-short-urls/{code}")
  public Mono<ResponseEntity<ShortUrlOriginalResponse>> redirectOriginalUrl(@PathVariable("code") String code,
                                                                            @RequestHeader(value = USER_AGENT, required = false) String userAgent) {
    return shortUrlService.getOriginalUrl(code, userAgent)
      .map(s -> ResponseEntity.status(FOUND)
        .header("Location", s.getOriginalUrl())
        .body(s))
      .switchIfEmpty(Mono.just(ResponseEntity.notFound().build()));
  }

  @Hidden
  @GetMapping(value = {"/public-short-urls", "/public-short-urls/", "/public-short-urls/index.html"}, consumes = {MediaType.ALL_VALUE})
  public Mono<ResponseEntity<String>> indexHtml() {
    return Mono.just(ResponseEntity
      .status(HttpStatus.OK)
      .header("Content-Type", "text/html; charset=utf-8")
      .body(readWelcomeHtml())
    );
  }

  @Hidden
  @GetMapping(value = "/public-short-urls/robots.txt", consumes = MediaType.ALL_VALUE)
  public Mono<ResponseEntity<String>> robots() {
    return Mono.just(ResponseEntity.ok("User-agent: *\nDisallow: /"));
  }

  @Hidden
  @PostMapping(value = "/public-short-urls/**", consumes = MediaType.ALL_VALUE)
  public Mono<ResponseEntity<String>> rejectBadBoy() {
    //短链服务 public-short-urls 公开到公网，总有一些人想攻击下试试，返回200让他们开心
    return Mono.just(ResponseEntity.ok().build());
  }

  /**
   * 根据长链接转成短链接，请求体是长链地址
   *
   * @param request 请求体是长链地址
   * @return 转换后的短链地址
   */
  @PostMapping("/private-short-urls")
  @Tag(name = "short-url", description = "短链接转换服务")
  @Operation(summary = "根据长链接转成短链接",
    responses = {
      @ApiResponse(responseCode = "200", description = "转换后的短链地址"),
      @ApiResponse(responseCode = "400", description = "地址超长，最长2000个字符"),
      @ApiResponse(responseCode = "406", description = "短链地址冲突")
    })
  @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json",
    schema = @Schema(implementation = ShortUrlCreateRequest.class),
    examples = @ExampleObject(value = "{\"url\":\"https://www.baidu.com\"}")))
  public Mono<ResponseEntity<EgressApiResponse<ShortUrlCreateResponse>>> create(@Validated @RequestBody ShortUrlCreateRequest request) {
    return shortUrlService.createShortUrl(request)
      .map(ResponseEntity::ok)
      .switchIfEmpty(Mono.just(ResponseEntity.status(NOT_ACCEPTABLE).build()));
  }

  /**
   * 批量根据长链接转成短链接，请求体是长链地址。请求体中一次最多支持1000个地址，另外注意批量结果中，如果某个失败，他对应的返回值为null，其他的正确的地址会正常返回。
   *
   * @param request 请求体是长链地址
   * @return 转换后的短链地址
   */
  @PostMapping("/private-short-urls/batch")
  @Tag(name = "short-url", description = "短链接批量转换服务")
  @Operation(summary = "根据长链接批量转成短链接",
    responses = {
      @ApiResponse(responseCode = "200", description = "转换后的短链地址")
    })
  public Mono<ResponseEntity<EgressApiResponse<ShortUrlBatchCreateResponse>>> batchCreate(@Validated @RequestBody ShortUrlBatchCreateRequest request) {
    return shortUrlService.batchCreateShortUrl(request)
      .map(ResponseEntity::ok)
      .switchIfEmpty(Mono.just(ResponseEntity.status(NOT_ACCEPTABLE).build()));
  }

  @PostMapping("/private-short-urls/original")
  @Tag(name = "short-url", description = "根据短链6位码获取原始长链接地址")
  @Operation(summary = "根据短链6位码获取原始长链接地址",
    responses = {
      @ApiResponse(responseCode = "200", description = "转换后的长链地址")
    })
  public Mono<ResponseEntity<EgressApiResponse<ShortUrlOriginalResponse>>> getOriginalUrl(@Validated @RequestBody ShortUrlOriginalRequest request,
                                                                                          @RequestHeader(value = USER_AGENT, required = false) String userAgent) {
    return getByCode(request.getCode(), userAgent);
  }

  @NotNull
  private Mono<ResponseEntity<EgressApiResponse<ShortUrlOriginalResponse>>> getByCode(String code, String userAgent) {
    return shortUrlService.getOriginalUrl(code, userAgent)
      .map(s -> ResponseEntity.ok(EgressApiResponse.ok(s)))
      .switchIfEmpty(Mono.just(ResponseEntity
        .status(404)
        .body(EgressApiResponse.notFound())));
  }

  /**
   * 根据短链接获取原始长链接地址
   *
   * @param code      短链6位码
   * @param userAgent Header中Agent，接口调用基本不需要主动传
   * @return 原始长链接地址，未找到返回404
   */
  @GetMapping("/private-short-urls/{code}")
  @Tag(name = "short-url", description = "短链接转换服务")
  @Operation(summary = "根据短链6位码获取原始长链接地址",
    parameters = {@io.swagger.v3.oas.annotations.Parameter(name = "code", description = "短链6位码", required = true)},
    responses = {
      @ApiResponse(responseCode = "200", description = "转换后的长链地址"),
      @ApiResponse(responseCode = "404", description = "根据指定的code找不到地址")
    })
  public Mono<ResponseEntity<EgressApiResponse<ShortUrlOriginalResponse>>> getOriginalUrl(@PathVariable("code") String code,
                                                                                          @RequestHeader(value = USER_AGENT, required = false) String userAgent) {
    return getByCode(code, userAgent);
  }

  @Hidden
  @GetMapping("/private-sync-urls/{code}")
  public Mono<ResponseEntity<EgressApiResponse<ShortUrl>>> queryOriginalUrl(@PathVariable("code") String code) {
    return shortUrlService.queryOriginal(code)
      .map(s -> ResponseEntity.ok(EgressApiResponse.ok(s)));
  }

  @Hidden
  @PostMapping("/private-sync-urls")
  public Mono<ResponseEntity<EgressApiResponse<ShortUrl>>> post(@Validated @RequestBody ShortUrlCreateRequest request) {
    return shortUrlService.createUrl(request)
      .map(ResponseEntity::ok)
      .switchIfEmpty(Mono.just(ResponseEntity.status(NOT_ACCEPTABLE).build()));
  }

  @Hidden
  @PostMapping("/private-sync-urls/page-query")
  public Mono<ResponseEntity<Page<ShortUrl>>> pageQuery(@Validated @RequestBody Mono<ShortUrlQueryRequest> request) {
    return shortUrlService.pageQuery(request)
      .map(ResponseEntity::ok);
  }

  @Hidden
  @GetMapping("/private-sync-urls/do-sync")
  public Mono<ResponseEntity<String>> doSync(@RequestParam("start") String start, @RequestParam("end") String end) {
    return shortUrlService.syncFromRemote(start, end)
      .map(ResponseEntity::ok);
  }

}
