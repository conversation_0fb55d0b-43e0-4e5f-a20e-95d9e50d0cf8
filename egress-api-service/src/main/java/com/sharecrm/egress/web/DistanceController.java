package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * 距离计算服务，按照驾驶距离计算
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api/v2/distance", produces = "application/json;charset=UTF-8")
public class DistanceController {

  private static final Function<Throwable, Mono<ResponseEntity<EgressApiResponse<List<PointDistance>>>>> INTERNAL_ERROR = e -> Mono
    .just(
      ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, e.getMessage())));

  private final GeoService geoService;

  public DistanceController(GeoService geoService) {
    this.geoService = geoService;
  }

  /**
   * 计算两点之间的距离
   *
   * @param from  起点坐标，多个坐标用分号";"分隔，坐标格式为：维度,经度，示例：40.056878,116.30815;40.063597,116.364973
   * @param to    终点坐标，多个坐标用分号";"分隔，坐标格式为：维度,经度，示例：40.056878,116.30815;40.063597,116.364973
   * @param cache 是否使用缓存
   * @return 距离计算结果，数组形式。数组中的每个元素代表一个起点和一个终点的检索结果。顺序依次为（以2起点2终点为例）：
   * from1-to1, from1-to2, from2-to1, from2-to2
   */
  @GetMapping("/matrix")
  @Tag(name = "distance", description = "距离计算服务")
  @Operation(summary = "计算两点之间的距离", description = "计算两点之间的距离，支持多个坐标点之间的计算。距离计算结果，数组形式。数组中的每个元素代表一个起点和一个终点的检索结果。顺序依次为（以2起点2终点为例）：from1-to1,from1-to2,from2-to1,from2-to2", operationId = "matrix", parameters = {
    @Parameter(name = "from", description = "起点坐标，多个坐标用分号\";\"分隔，坐标格式为：维度,经度，示例：40.056878,116.30815;40.063597,116.364973", required = true),
    @Parameter(name = "to", description = "终点坐标，多个坐标用分号\";\"分隔，坐标格式为：维度,经度，示例：39.990464,116.481488", required = true),
    @Parameter(name = "cache", description = "是否使用缓存")})
  public Mono<ResponseEntity<EgressApiResponse<List<PointDistance>>>> matrix(@RequestParam String from,
                                                                             @RequestParam String to,
                                                                             @RequestParam(defaultValue = "true") boolean cache) {
    List<LocationPoint> origins = parsePoints(from);
    List<LocationPoint> destinations = parsePoints(to);
    return geoService
      .queryDistance(origins, destinations, cache)
      .defaultIfEmpty(List.of())
      .map(data -> new EgressApiResponse<>(200, "success", data))
      .map(ResponseEntity::ok)
      .onErrorResume(INTERNAL_ERROR);
  }

  private List<LocationPoint> parsePoints(String points) {
    return Stream.of(points.split(";")).map(LocationPoint::parseLatitudeLongitude).toList();
  }
}
