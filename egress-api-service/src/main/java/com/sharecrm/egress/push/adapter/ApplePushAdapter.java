package com.sharecrm.egress.push.adapter;

import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.ApnsClientBuilder;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.auth.ApnsSigningKey;
import com.eatthepath.pushy.apns.proxy.HttpProxyHandlerFactory;
import com.eatthepath.pushy.apns.util.ApnsPayloadBuilder;
import com.eatthepath.pushy.apns.util.SimpleApnsPayloadBuilder;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.concurrent.PushNotificationFuture;
import com.google.common.base.Strings;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.config.NotifyProperties.ApplePushConfig;
import com.sharecrm.egress.entity.PayloadDTO;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.exception.ThirdPushClientException;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.NetUtils;
import com.sharecrm.egress.utils.VersionUtil;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.net.URI;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 苹果Pushy框架使用
 */
@Slf4j
public class ApplePushAdapter {

  private ApnsClient apnsClient;
  private final ApplePushConfig config;

  public ApplePushAdapter(ApplePushConfig config) {
    this.config = config;
    this.init();
  }

  private void init() {
    try {
      apnsClient = build(config);
    } catch (Exception e) {
      //连接失败也允许启动，否则其他网络出现问题后其他服务都不可用
      log.error("apple apns client init error", e);
    }
  }

  public PushNotificationFuture<SimpleApnsPushNotification, PushNotificationResponse<SimpleApnsPushNotification>> pushToServer(PushMessageDTO messageDTO) {
    SimpleApnsPushNotification pushNotification = apnsPushNotificationRequest(messageDTO);
    return apnsClient.sendNotification(pushNotification);
  }

  @NotNull
  SimpleApnsPushNotification apnsPushNotificationRequest(PushMessageDTO messageDTO) {
    if (StringUtils.isEmpty(messageDTO.getOriginalMessage())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }
    if (Objects.isNull(apnsClient)) {
      throw new ThirdPushClientException("apns client is null, may be network can not connect");
    }

    Map<String, Object> extraData = new HashMap<>();
    PayloadDTO payloadDTO = PayloadDTO.Convert.from(messageDTO);
    messageDTO.setPushContent(JsonUtil.getGson(true).toJson(payloadDTO));
    extraData.put("ea", messageDTO.getEnterpriseAccount());
    extraData.put("uid", messageDTO.getEmployeeId());
    String imageUrl = messageDTO.getApnsData() == null ? null
      : Strings.emptyToNull(messageDTO.getApnsData().getImageUrl());
    extraData.put("imageUrl", imageUrl);
    extraData.put("biz-path", messageDTO.getBizPath());
    extraData.put("biz-data", messageDTO.getBizData());
    extraData.put("interaction", messageDTO.getInteraction());
    extraData.put("channelId", messageDTO.getChannelId());
    ApnsPayloadBuilder apnsPayload = new SimpleApnsPayloadBuilder().setAlertBody(
        messageDTO.getOriginalMessage())
      .setBadgeNumber(messageDTO.getUnreadNumber())
      .setCategoryName(messageDTO.getCategory())
      .addCustomProperty("type", messageDTO.getMessageType())
      .addCustomProperty("extra-data", extraData);
    String soundName = null;
    if (messageDTO.isWithSound()) {
      soundName = messageDTO.getSoundName().indexOf(".") > 0 ? messageDTO.getSoundName()
        : messageDTO.getSoundName() + ".wav";
    }
    apnsPayload.setSound(soundName);
    //不同版本ios的客户端， 不同的数据处理
    if (VersionUtil.getIosMajorVersion(messageDTO.getOsVersion()) > 9) {
      apnsPayload.setAlertTitle(messageDTO.getRegionTitle());
      apnsPayload.setAlertBody(messageDTO.combineTitleAndSummary());
    }
    //图文推送相关设置
    apnsPayload.setMutableContent(imageUrl != null);
    return new SimpleApnsPushNotification(messageDTO.getToken(), config.getTopic(), apnsPayload.build());
  }

  private ApnsClient build(NotifyProperties.ApplePushConfig config)
    throws IOException, NoSuchAlgorithmException, InvalidKeyException {
    try (InputStream stream = new ClassPathResource(config.getApnsTokenPath()).getInputStream();) {
      ApnsSigningKey signingKey = ApnsSigningKey.loadFromInputStream(stream, config.getTeamId(), config.getKeyId());
      String apnsServer = config.isSandbox() ? ApnsClientBuilder.DEVELOPMENT_APNS_HOST : ApnsClientBuilder.PRODUCTION_APNS_HOST;
      ApnsClientBuilder apnsClientBuilder = new ApnsClientBuilder()
        .setSigningKey(signingKey).setApnsServer(apnsServer)
        .setConcurrentConnections(config.getMaxConnections())
        .setEventLoopGroup(new NioEventLoopGroup(2));
      if (Objects.nonNull(config.getConnectTimeout())) {
        apnsClientBuilder.setConnectionTimeout(Duration.ofMillis(config.getConnectTimeout()));
      }
      String httpProxy = config.getProxy();
      if (StringUtils.isNotBlank(httpProxy)) {
        URI uri = NetUtils.uri(httpProxy);
        SocketAddress address = new InetSocketAddress(uri.getHost(), uri.getPort());
        apnsClientBuilder.setProxyHandlerFactory(new HttpProxyHandlerFactory(address));
      }
      if (config.getEventLoopGroup() != null) {
        EventLoopGroup eventLoopGroup = new NioEventLoopGroup(config.getEventLoopGroup());
        apnsClientBuilder.setEventLoopGroup(eventLoopGroup);
      }
      if (config.getIdlePingInterval() != null) {
        apnsClientBuilder.setIdlePingInterval(Duration.ofMillis(config.getIdlePingInterval()));
      }
      return apnsClientBuilder.build();
    }

  }

}
