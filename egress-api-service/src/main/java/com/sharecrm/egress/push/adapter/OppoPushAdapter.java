package com.sharecrm.egress.push.adapter;

import com.google.common.base.Strings;
import com.oppo.push.server.Notification;
import com.oppo.push.server.Result;
import com.oppo.push.server.Sender;
import com.oppo.push.server.Target;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PayloadDTO;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.exception.ThirdPushClientException;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * oppo
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Component
@ConditionalOnNotifyEnabled
public class OppoPushAdapter {

  private static final int TITLE_LIMIT_LENGTH = 48;
  private static final int DESC_LIMIT_LENGTH = 180;

  private final Supplier<Sender> sender;

  public OppoPushAdapter(Supplier<Sender> sender) {
    this.sender = sender;
  }

  public Result pushToServer(PushMessageDTO messageDTO) throws Throwable {
    if (StringUtils.isEmpty(messageDTO.getSummary())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }
    return sendByNotification(messageDTO);
  }

  /**
   * 通知栏模式
   *
   * @return Result
   */
  private Result sendByNotification(PushMessageDTO messageDTO) throws Exception {
    Sender send = sender.get();
    if (Objects.isNull(send)) {
      log.warn("oppo client is null, may be network can not connect.");
      throw new ThirdPushClientException("oppo client is null, may be network can not connect.");
    }

    messageDTO.setNotifyPushType(Constants.NOTIFY_PUSH_TYPE_1);

    PayloadDTO payloadDTO = PayloadDTO.Convert.from(messageDTO);
    String title = messageDTO.getRegionTitle();
    if (Strings.isNullOrEmpty(title)) {
      log.info("title-miss, id: {}", messageDTO.getId());
      title = Constants.FS_PRODUCT_NAME;
    }
    if (title.length() > TITLE_LIMIT_LENGTH) {
      title = title.substring(0, TITLE_LIMIT_LENGTH);
    }

    String description = messageDTO.combineTitleAndSummary();
    
    if (description.length() > DESC_LIMIT_LENGTH) {
      description = description.substring(0, DESC_LIMIT_LENGTH);
    }

    Notification notification = new Notification();
    notification.setChannelId("Notification_ChannelId_01");
    notification.setTitle(title);
    notification.setSubTitle(Constants.FS_PRODUCT_NAME);
    notification.setContent(description);
    notification.setAppMessageId(messageDTO.getId());
    notification.setClickActionType(1);
    notification.setClickActionActivity("com.fxiaoke.host.OPPONotifyView");
    notification.setShowTimeType(0);
    notification.setOffLine(true);
    notification.setOffLineTtl(3600);
    notification.setTimeZone("GMT+08:00");
    notification.setNetworkType(0);
    notification.setActionParameters(
      JsonUtil.getGson(true).toJson(Map.of("payload", payloadDTO)));
    Target target = Target.build(messageDTO.getToken());
    messageDTO.setPushContent(JsonUtil.getGson(false).toJson(notification));

    return send.unicastNotification(notification, target);
  }

}
