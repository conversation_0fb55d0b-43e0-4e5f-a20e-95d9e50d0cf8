package com.sharecrm.egress.push.adapter;

import lombok.Data;

@Data
public class HuaWeiPushResult {

  private String code;
  private String msg;
  private String requestId;

  /**
   * 是否成功
   *
   * @return boolean
   */
  public boolean isSuccess() {
    return "80000000".equals(code);
  }

  /**
   * 是否无效token
   *
   * @return boolean
   */
  public boolean isInvalidToken() {
    return "80100002".equals(code);
  }
}
