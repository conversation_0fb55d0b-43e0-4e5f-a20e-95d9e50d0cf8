package com.sharecrm.egress.push.adapter;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.auth.http.HttpTransportFactory;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PayloadDTO;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Firebase Cloud Messaging (FCM) can be used to send messages to clients on iOS, Android and Web.
 * <p>
 * This sample uses FCM to send two types of messages to clients that are subscribed to the `news` topic. One type of message is a simple notification message
 * (display message). The other is a notification message (display notification) with platform specific customizations, for example, a badge is added to
 * messages that are sent to iOS devices.
 */
@Slf4j
@Component
@ConditionalOnNotifyEnabled
public class FcmPushAdapter {

  private static final String BASE_URL = "https://fcm.googleapis.com";
  private static final String MESSAGING_SCOPE = "https://www.googleapis.com/auth/firebase.messaging";
  public static final String MESSAGE_KEY = "message";
  private static final MediaType MEDIA_TYPE_JSON = MediaType.parse("application/json; charset=UTF-8");

  public static final String TOKEN_REDIS_KEY = "egress:fs-notify-push-google-fcm";

  private final OkHttpSupport httpClient;
  private final NotifyProperties properties;
  private final StringRedisTemplate redisTemplate;

  public FcmPushAdapter(@Qualifier("httpSupportForFCM") OkHttpSupport httpClient, NotifyProperties properties, StringRedisTemplate redisTemplate) {
    this.httpClient = httpClient;
    this.properties = properties;
    this.redisTemplate = redisTemplate;
  }


  private String getSendEndpoint() {
    return "/v1/projects/" + config().getProjectId() + "/messages:send";
  }

  private NotifyProperties.FcmConfig config() {
    return properties.getFcm();
  }

  private String accessToken() throws IOException {
    String token = redisTemplate.opsForValue().get(TOKEN_REDIS_KEY);
    if (StringUtils.isNotBlank(token)) {
      return token;
    }
    AccessToken accessToken = refreshAccessToken();
    String tokenValue = accessToken.getTokenValue();
    Date expirationTime = accessToken.getExpirationTime();
    long timeout = expirationTime.getTime() - System.currentTimeMillis() - 3000;
    log.info("google fcm token expiration:{}, timeout:{}", expirationTime, timeout);
    redisTemplate.opsForValue().set(TOKEN_REDIS_KEY, tokenValue, Duration.ofMillis(timeout));
    return tokenValue;
  }

  /**
   * @return Access token.
   */
  private AccessToken refreshAccessToken() throws IOException {
    GoogleCredentials googleCredentials;
    //配置代理
    String fcmProxy = config().getProxy();
    List<String> scopes = List.of(MESSAGING_SCOPE);
    try (InputStream auth = new ClassPathResource(config().getAuthFile()).getInputStream()) {
      if (StringUtils.isNotBlank(fcmProxy)) {
        log.info("google fcm with proxy: {}", fcmProxy);
        HttpHost proxy = HttpHost.create(fcmProxy.trim());
        HttpTransportFactory hf = () -> new NetHttpTransport.Builder()
          .setProxy(new Proxy(Proxy.Type.HTTP, InetSocketAddress.createUnresolved(proxy.getHostName(), proxy.getPort())))
          .build();
        googleCredentials = GoogleCredentials.fromStream(auth, hf).createScoped(scopes);
      } else {
        log.info("google fcm without proxy");
        googleCredentials = GoogleCredentials
          .fromStream(auth)
          .createScoped(scopes);
      }
      googleCredentials.refreshIfExpired();
      return googleCredentials.getAccessToken();
    }

  }

  /**
   * Build the body of an FCM request. This body defines the common notification object as well as platform specific customizations using the android and apns
   * objects.
   * <p>
   * 文档地址： <a href="https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages">...</a>
   *
   * @return JSON representation of the FCM request body.
   */
  JSONObject buildMessage(PushMessageDTO msg) {
    JSONObject jNotificationMessage = buildNotificationMessage(msg);
    JSONObject messagePayload = jNotificationMessage.getJSONObject(MESSAGE_KEY);
    messagePayload.put("android", buildAndroidPayload(msg));
    jNotificationMessage.put(MESSAGE_KEY, messagePayload);
    return jNotificationMessage;
  }

  /**
   * Build the android payload that will customize how a message is received on Android.
   *
   * @return android payload of an FCM request.
   */
  JSONObject buildAndroidPayload(PushMessageDTO msg) {
    JSONObject notification = new JSONObject();
    if (!Strings.isEmpty(config().getClickAction())) {
      notification.put("click_action", config().getClickAction());
    }
    if (msg.isWithSound()) {
      if ("default".equals(msg.getSoundName())) {
        notification.put("default_sound", Boolean.TRUE);
      } else {
        notification.put("sound", msg.getSoundName());
      }
    }
    if (msg.isVibrate()) {
      notification.put("default_vibrate_timings", Boolean.TRUE);
    }
    notification.put("visibility", "PUBLIC");

    JSONObject android = new JSONObject();
    android.put("ttl", "7200s");
    if (!Strings.isEmpty(config().getPackageName())) {
      android.put("restricted_package_name", config().getPackageName());
    }

    PayloadDTO payloadDTO = PayloadDTO.Convert.from(msg);
    Map<String, String> data = new HashMap<>();
    data.put("bizData", JSON.toJSONString(payloadDTO));
    android.put("data", data);
    android.put("notification", notification);
    return android;
  }

  /**
   * Construct the body of a notification message request.
   *
   * @return JSON of notification message.
   */
  JSONObject buildNotificationMessage(PushMessageDTO msg) {
    JSONObject notification = new JSONObject();
    notification.put("title", Strings.isEmpty(msg.getRegionTitle()) ? Constants.FS_PRODUCT_NAME
      : msg.getRegionTitle());
    notification.put("body", msg.combineTitleAndSummary());

    JSONObject message = new JSONObject();
    message.put("notification", notification);
    message.put("token", msg.getToken());

    JSONObject fcm = new JSONObject();
    fcm.put(MESSAGE_KEY, message);
    return fcm;
  }


  /**
   * Send a message that uses the common FCM fields to send a notification message to all platforms. Also platform specific overrides are used to customize how
   * the message is received on Android and iOS.
   */
  public PushResult pushToServer(PushMessageDTO msg) throws IOException {
    String bodyStr = buildMessage(msg).toString();
    RequestBody reqBody = RequestBody.create(bodyStr, MEDIA_TYPE_JSON);
    Request request = new Request.Builder()
      .addHeader("Authorization", "Bearer " + accessToken())
      .addHeader("Content-Type", "application/json; UTF-8")
      .url(BASE_URL + getSendEndpoint())
      .post(reqBody)
      .build();

    String response = (String) httpClient.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        return Objects.requireNonNull(response.body()).string();
      }
    });
    msg.setPushContent(bodyStr);
    PushResult resp = JsonUtil.getGson(true).fromJson(response, PushResult.class);
    resp.setCode(Optional.ofNullable(resp.getError()).map(PushResultError::getCode)
      .orElse("200"));
    return resp;
  }


  @Data
  public static class PushResult {
    private String code;
    private PushResultError error;

    /**
     * 是否成功
     *
     * @return boolean
     */
    public boolean isSuccess() {
      return "200".equals(code);
    }

    /**
     * 是否无效token
     *
     * @return boolean
     */
    public boolean isInvalidToken() {
      return error != null && error.getMessage() != null && error.getMessage().contains("token is not a valid");
    }
  }

  @Data
  public static class PushResultError {
    private String code;
    private String message = "";
    private String status = "";
  }
}
