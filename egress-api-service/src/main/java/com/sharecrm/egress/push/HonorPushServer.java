package com.sharecrm.egress.push;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.HonorPushAdapter;
import com.sharecrm.egress.push.adapter.honor.HonorPushResult;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 荣耀消息推送
 * <p>
 * see：<a href="https://developer.honor.com/cn/docs/11002/guides/cloud-overview">...</a>
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class HonorPushServer extends PushServer {

  private final HonorPushAdapter adapter;

  public HonorPushServer(NotifyProperties properties,
                         @Qualifier("honorHttpSupport") OkHttpSupport httpClient) {
    this.adapter = new HonorPushAdapter(properties.getHonor(), httpClient);
  }

  @Override
  protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  void pushTask(PushMessageDTO messageDTO) {
    try {
      HonorPushResult pushResult = adapter.pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Throwable e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }

  private void resultHandler(HonorPushResult pushResult, PushMessageDTO messageDTO) {
    Integer code = Objects.requireNonNullElse(pushResult.getCode(), 500);
    messageDTO.setResultCode(code + "");
    messageDTO.setResultInfo(pushResult.getMessage());
    messageDTO.setPushedTime(System.currentTimeMillis());
    messageDTO.setPushStatus(200 == code ? PushRecord.PUSH_STATUS_SUCCESS : PushRecord.PUSH_STATUS_FAILURE);
    Optional.ofNullable(pushResult.getData())
            .ifPresent(data -> {
              messageDTO.setRequestId(data.getRequestId());
              messageDTO.setPushStatus(data.isSendResult() ? PushRecord.PUSH_STATUS_SUCCESS : PushRecord.PUSH_STATUS_FAILURE);
            });
    super.saveRecord(messageDTO);
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_HONOR);
  }
}
