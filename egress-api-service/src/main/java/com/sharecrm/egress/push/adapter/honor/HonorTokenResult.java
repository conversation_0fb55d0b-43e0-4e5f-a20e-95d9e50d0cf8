package com.sharecrm.egress.push.adapter.honor;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.gexin.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class HonorTokenResult {

  @JsonProperty("access_token")
  @SerializedName("access_token")
  @JSONField(name = "access_token")
  private String accessToken;
  @JsonProperty("expires_in")
  @SerializedName("expires_in")
  @JSONField(name = "expires_in")
  private Integer expiresIn;
}
