package com.sharecrm.egress.push;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.sharecrm.egress.dao.NotifyDao;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.exception.ThirdPushClientException;
import com.sharecrm.egress.exception.ThirdPushException;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;

import java.util.Base64;
import java.util.List;

/**
 * PushServer基类
 */
@Slf4j
public abstract class PushServer {

  protected static final int MESSAGE_EXPIRED_TIME = 30 * 60 * 1000;

  @Autowired
  @Qualifier("taskExecutor")
  protected TaskExecutor executor;

  @Autowired
  private PNSTokenService tokenService;

  @Autowired
  private NotifyDao notifyDao;

  /**
   * 支持的pushServerSource
   *
   * @return pushServerSources
   */
  public abstract List<String> pushServerSources();

  /**
   * 消息推送，具体由子类实现
   */
  protected abstract void send(final PushMessageDTO pushMessageDTO) throws Throwable;

  /**
   * 消息推送
   *
   * @param pushMessageDTO 消息
   */
  public void push(final PushMessageDTO pushMessageDTO) {
    pushMessageDTO.setId(NanoIdUtils.randomNanoId());
    try {
      //1. validate
      messageValidate(pushMessageDTO);
      //2. push
      send(pushMessageDTO);

    } catch (Throwable e) {
      this.saveException(pushMessageDTO, e);
    }
  }

  void messageValidate(final PushMessageDTO pushMessageDTO) {
    if (timeExpire(pushMessageDTO)) {
      log.warn("notify push message time expired. {}", pushMessageDTO);
      throw new ThirdPushException("TIME_EXPIRED");
    }
  }

  /**
   * 消息过期验证
   *
   * @param pushMessageDTO 消息
   * @return true: 过期
   */
  private boolean timeExpire(final PushMessageDTO pushMessageDTO) {
    return (System.currentTimeMillis() - pushMessageDTO.getProduceTime()) > MESSAGE_EXPIRED_TIME;
  }

  protected void removeAndroidTokenFromOMS(PushMessageDTO message) {
    tokenService.removeAndroidTokenFromOMS(message.getEnterpriseAccount(), message.getToken());
  }

  protected void removeAppleTokenFromOMS(PushMessageDTO message) {
    tokenService.removeAppleTokenFromOMS(message.getEnterpriseAccount(), message.getToken(), false);
  }

  protected void saveRecord(PushMessageDTO messageDTO) {
    notifyDao.save(messageToRecord(messageDTO));
  }

  protected void saveException(PushMessageDTO message, Throwable e) {
    // ThirdPushClientException都是网络连接问题，一般全部都是错误，不能记数据库会直接把库打爆
    if (e instanceof ThirdPushClientException) {
      log.warn("notify push failed.", e);
      return;
    }

    PushRecord pushRecord = messageToRecord(message);
    pushRecord.setStatus("" + PushRecord.PUSH_STATUS_EXCEPTION);
    pushRecord.setResultCode("exception");
    pushRecord.setResultInfo(e.getMessage());
    notifyDao.save(pushRecord);
  }


  protected String lightEncode(long currentTimeMillis, String salt, String context) {
    if (StringUtils.isEmpty(context)) {
      return "";
    }
    //伪装的加密，实际上是base64，但是copy全文无法直接decode
    Base64.Encoder encoder = Base64.getEncoder();
    // 去掉前2位让base64无法直接decode
    String saltEncode = encoder.encodeToString((currentTimeMillis + salt).getBytes()).substring(2);
    String contextEncode = encoder.encodeToString(context.getBytes());
    return saltEncode + salt + contextEncode;
  }

  PushRecord messageToRecord(PushMessageDTO dto) {
    PushRecord pushRecord = new PushRecord();
    long currentTimeMillis = System.currentTimeMillis();
    pushRecord.setStamp(currentTimeMillis);
    pushRecord.setApp(Constants.APP_NAME);
    pushRecord.setServerIp(Constants.IP);
    pushRecord.setProfile(Constants.PROFILE);
    pushRecord.setObjectId(dto.getId());
    pushRecord.setToken(dto.getToken());
    pushRecord.setEa(dto.getEnterpriseAccount());
    pushRecord.setUid("" + dto.getEmployeeId());
    pushRecord.setConsumerMessage(lightEncode(currentTimeMillis, "cmg", dto.getConsumerMessage()));
    pushRecord.setTitle(lightEncode(currentTimeMillis, "ttl", dto.getTitle()));
    pushRecord.setSummary(lightEncode(currentTimeMillis, "smy", dto.getSummary()));
    pushRecord.setPushContent(lightEncode(currentTimeMillis, "ctt", dto.getPushContent()));
    pushRecord.setPushServerSource(dto.getPushServerSource());
    pushRecord.setPushedTime(dto.getPushedTime());
    pushRecord.setConsumerTime(dto.getConsumerTime());
    pushRecord.setProduceTime(dto.getProduceTime());
    pushRecord.setStatus(pushStatusStr(dto.getPushStatus()));
    pushRecord.setResultCode(dto.getResultCode());
    pushRecord.setResultInfo(dto.getResultInfo());
    pushRecord.setCost(dto.getPushedTime() - dto.getConsumerTime());
    pushRecord.setRequestId(dto.getRequestId());
    pushRecord.setMqMsgId(dto.getMqMsgId());
    pushRecord.setOsVersion(dto.getOsVersion());
    pushRecord.setAppVersion(dto.getAppVersion());
    pushRecord.setNotifyPushType(dto.getNotifyPushType());
    return pushRecord;
  }

  private String pushStatusStr(int status) {
    return switch (status) {
      case PushRecord.PUSH_STATUS_SUCCESS -> "success";
      case PushRecord.PUSH_STATUS_FAILURE -> "failure";
      case PushRecord.PUSH_STATUS_EXCEPTION -> "exception";
      default -> "" + status;
    };
  }

}
