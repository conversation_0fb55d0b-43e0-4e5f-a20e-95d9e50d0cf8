package com.sharecrm.egress.push;

import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.push.adapter.ApplePushAdapter;
import com.sharecrm.egress.utils.Constants;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 针对 IOS 平台的Push Provider，内测通道
 */
@Service
@ConditionalOnNotifyEnabled
public class AppleEntPushServer extends ApplePushServer {

  private final ApplePushAdapter applePushAdapter;

  public AppleEntPushServer(NotifyProperties properties) {
    this.applePushAdapter = new ApplePushAdapter(properties.getAppleEnt());
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_APPLE_ENT);
  }

  @Override
  protected ApplePushAdapter adapter() {
    return this.applePushAdapter;
  }

}
