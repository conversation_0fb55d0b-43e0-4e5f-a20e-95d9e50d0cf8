package com.sharecrm.egress.push.adapter.harmony;

import lombok.Data;

@Data
public class HarmonyPushOptions {

  /**
   * 消息缓存时间，单位是秒
   */
  private Integer ttl;

  /**
   * 用户设备离线时，Push服务器对离线消息缓存机制的控制方式，用户设备上线后缓存消息会再次下发，取值如下：
   * <p>
   * 0：对每个应用发送到该用户设备的离线消息只会缓存最新的一条
   * -1：对所有离线消息都缓存（默认值）
   * 1~100：离线消息缓存分组标识，对离线消息进行分组缓存，每个应用每一组最多缓存一条离线消息
   * 如果您发送了10条消息，其中前5条的collapseKey为1，后5条的collapseKey为2，那么待用户上线后collapseKey为1和2的分别下发最新的一条消息给最终用户。
   */
  private Integer collapseKey;

  private String biTag;

}
