package com.sharecrm.egress.push;

import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.VivoPushAdapter;
import com.sharecrm.egress.push.adapter.VivoPushAdapter.SendResult;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * VIVO消息推送
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class VivoPushServer extends PushServer {

  private final VivoPushAdapter pushAdapter;

  public VivoPushServer(VivoPushAdapter pushAdapter) {
    this.pushAdapter = pushAdapter;
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_VIVO);
  }

  @Override
  protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      SendResult pushResult = pushAdapter.pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Throwable e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }

  private void resultHandler(SendResult result, PushMessageDTO messageDTO) {
    long currTimestamp = System.currentTimeMillis();

    if (result.getResult() == 0) {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_SUCCESS);
    } else {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_FAILURE);
    }
    messageDTO.setResultCode(String.valueOf(result.getResult()));
    messageDTO.setResultInfo(result.getDesc());
    messageDTO.setPushedTime(currTimestamp);
    messageDTO.setRequestId(result.getTaskId());

    super.saveRecord(messageDTO);

    if (result.getResult() == 10302) {
      super.removeAndroidTokenFromOMS(messageDTO);
    }
  }


}

