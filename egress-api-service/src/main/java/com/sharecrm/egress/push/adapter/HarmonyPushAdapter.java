package com.sharecrm.egress.push.adapter;


import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.sharecrm.egress.config.NotifyProperties.HarmonyPushConfig;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.push.adapter.harmony.HarmonyAlertPayload;
import com.sharecrm.egress.push.adapter.harmony.HarmonyPayloadBadge;
import com.sharecrm.egress.push.adapter.harmony.HarmonyPayloadClickAction;
import com.sharecrm.egress.push.adapter.harmony.HarmonyPayloadNotification;
import com.sharecrm.egress.push.adapter.harmony.HarmonyPushOptions;
import com.sharecrm.egress.push.adapter.harmony.HarmonyPushRequest;
import com.sharecrm.egress.push.adapter.harmony.HarmonyPushTarget;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.JwtGenerator;
import com.sharecrm.egress.utils.NumUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 华为鸿蒙消息推送
 * <p>
 * see：<a href="https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/push-scenariozed-api-request-struct-V5">...</a>
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
public class HarmonyPushAdapter {

  private final HarmonyPushConfig pushConfig;

  private final OkHttpSupport httpClient;

  private final JwtGenerator jwt;

  private final AsyncLoadingCache<String, String> cache;

  public HarmonyPushAdapter(HarmonyPushConfig pushConfig, OkHttpSupport httpClient) {
    this.pushConfig = pushConfig;
    this.httpClient = httpClient;
    this.jwt = new JwtGenerator(pushConfig.getJwt());
    this.cache = initCache(pushConfig);
  }

  @NotNull
  private AsyncLoadingCache<String, String> initCache(HarmonyPushConfig pushConfig) {
    return Caffeine.newBuilder()
      //默认一个小时，过期时间稍微减少一些
      .expireAfterWrite(pushConfig.getJwt().getTimeout().toSeconds() - 180, TimeUnit.SECONDS)
      .maximumSize(100)
      .buildAsync(new CacheLoader<>() {
        @Override
        public @Nullable String load(String key) {
          return jwt.createJwt();
        }
      });
  }

  public HuaWeiPushResult pushToServer(PushMessageDTO messageDTO) {
    if (StringUtils.isEmpty(messageDTO.getSummary())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }
    return sendByNotification(messageDTO);
  }

  private HuaWeiPushResult sendByNotification(PushMessageDTO dto) {
    try {
      dto.setNotifyPushType(Constants.NOTIFY_PUSH_TYPE_1);
      Map<String, String> headers = Map.of(
        "Authorization", "Bearer " + cache.get("token").get(),
        "push-type", pushConfig.getPushType());
      HarmonyPushRequest body = new HarmonyPushRequest(payload(dto), pushTarget(dto), defaultPushOptions());
      Request request = WebUtils.okHttpJsonPost(pushConfig.getPushUrl(), body, headers);
      //设置回去用于日志记录
      dto.setPushContent(JsonUtil.toJson(body));
      return httpClient.parseObject(request, new TypeReference<>() {
      });
    } catch (InterruptedException ex) {
      Thread.currentThread().interrupt();
      log.warn("harmony push failed.", ex);
      HuaWeiPushResult failed = new HuaWeiPushResult();
      failed.setCode("500");
      failed.setMsg(ex.getMessage());
      return failed;
    } catch (Exception e) {
      log.warn("harmony push failed.", e);
      HuaWeiPushResult failed = new HuaWeiPushResult();
      failed.setCode("500");
      failed.setMsg(e.getMessage());
      return failed;
    }
  }

  private HarmonyAlertPayload payload(PushMessageDTO dto) {
    HarmonyAlertPayload payload = new HarmonyAlertPayload();
    HarmonyPayloadNotification notification = new HarmonyPayloadNotification();
    notification.setCategory("IM");

    String description = dto.combineTitleAndSummary();
    notification.setTitle(StringUtils.defaultIfEmpty(dto.getRegionTitle(), Constants.FS_PRODUCT_NAME));
    notification.setBody(description);
    notification.setStyle(0);

    HarmonyPayloadClickAction clickAction = new HarmonyPayloadClickAction();
    clickAction.setActionType(1);
    String uri = "fxiaoke://applink?bizPath=%s&bizData=%s".formatted(dto.getBizPath(), JsonUtil.toJson(dto.getBizData()));
    clickAction.setUri(uri);
    notification.setClickAction(clickAction);

    HarmonyPayloadBadge badge = new HarmonyPayloadBadge();
    badge.setAddNum(1);
    // 范围必须在 [1-99] 之间
    badge.setSetNum(NumUtils.range(1, 99, dto.getUnreadNumber()));
    notification.setBadge(badge);

    payload.setNotification(notification);
    return payload;
  }

  private HarmonyPushTarget pushTarget(PushMessageDTO messageDTO) {
    HarmonyPushTarget target = new HarmonyPushTarget();
    target.setToken(List.of(messageDTO.getToken()));
    return target;
  }

  private HarmonyPushOptions defaultPushOptions() {
    HarmonyPushOptions pushOptions = new HarmonyPushOptions();
    pushOptions.setTtl(1800);
    pushOptions.setCollapseKey(0);
    pushOptions.setBiTag("todo");
    return pushOptions;
  }

}
