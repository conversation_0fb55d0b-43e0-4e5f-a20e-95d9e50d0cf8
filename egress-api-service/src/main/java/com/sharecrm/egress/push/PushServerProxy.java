package com.sharecrm.egress.push;

import com.google.common.base.Strings;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.ThirdPushException;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

/**
 * 消息推送商代理
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class PushServerProxy {

  private final ObjectProvider<PushServer> serverObjectProvider;

  public PushServerProxy(ObjectProvider<PushServer> serverObjectProvider) {
    this.serverObjectProvider = serverObjectProvider;
  }

  public void push(PushMessageDTO messageDTO) {
    getRealPushFactory(messageDTO)
      .orElseThrow(() -> new ThirdPushException("can't get push server"))
      .push(messageDTO);
  }

  private Optional<PushServer> getRealPushFactory(PushMessageDTO messageDTO) {
    String pushServerSource = messageDTO.getPushServerSource();
    if (Strings.isNullOrEmpty(pushServerSource)) {
      log.warn("pushServerSource is null | {}", messageDTO);
      return Optional.empty();
    }
    String source = pushServerSource.trim();
    return serverObjectProvider.stream()
      .filter(s -> s.pushServerSources().contains(source))
      .findFirst();
  }


}
