package com.sharecrm.egress.push;


import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.XiaoMiPushAdapter;
import com.sharecrm.egress.utils.Constants;
import com.xiaomi.push.sdk.ErrorCode;
import com.xiaomi.xmpush.server.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小米推送
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class Xiao<PERSON>iPushServer extends PushServer {

  private final XiaoMiPushAdapter xiaoMiPushAdapter;

  public XiaoMiPushServer(XiaoMiPushAdapter xiaoMiPushAdapter) {
    this.xiaoMiPushAdapter = xiaoMiPushAdapter;
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_XIAOMI);
  }

  @Override
  protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      Result pushResult = xiaoMiPushAdapter.pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Throwable e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }

  private void resultHandler(Result result, PushMessageDTO messageDTO) {
    long currTimestamp = System.currentTimeMillis();

    if (result.getErrorCode() == ErrorCode.Success) {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_SUCCESS);
    } else {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_FAILURE);
    }
    messageDTO.setResultCode(String.valueOf(result.getErrorCode().getValue()));
    messageDTO.setResultInfo(result.getErrorCode().getName());
    messageDTO.setPushedTime(currTimestamp);
    messageDTO.setRequestId(result.getMessageId());
    super.saveRecord(messageDTO);

    if (result.getErrorCode().getValue() == 20301) {
      super.removeAndroidTokenFromOMS(messageDTO);
    }
  }

}

