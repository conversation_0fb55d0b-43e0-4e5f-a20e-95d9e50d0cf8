package com.sharecrm.egress.push;


import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 空药包，忽略旧终端不支持的类型，只记录日志不推送
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class EmptyPushServer extends PushServer {

  @Override
  protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
    // do nothing is ok
  }

  @Override
  public void push(PushMessageDTO pushMessageDTO) {
    log.debug("unsupported push message, source: {},ea:{}, uid:{},mqMsgId:{}",
      pushMessageDTO.getPushServerSource(),
      pushMessageDTO.getEnterpriseAccount(),
      pushMessageDTO.getEmployeeId(),
      pushMessageDTO.getMqMsgId());
  }

  @Override
  public List<String> pushServerSources() {
    // 都是废弃的推送源
    return List.of(Constants.PUSH_SERVER_SOURCE_APPLE_PUSHKIT_PROD, Constants.PUSH_SERVER_SOURCE_APPLE_PUSHKIT_ENT);
  }
}

