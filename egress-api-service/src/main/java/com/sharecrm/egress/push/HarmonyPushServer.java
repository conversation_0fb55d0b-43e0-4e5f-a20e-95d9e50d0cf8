package com.sharecrm.egress.push;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.HarmonyPushAdapter;
import com.sharecrm.egress.push.adapter.HuaWeiPushResult;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 华为鸿蒙消息推送
 * <p>
 * see：<a href="https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/push-scenariozed-api-request-struct-V5">...</a>
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class HarmonyPushServer extends PushServer {

  private final HarmonyPushAdapter adapter;

  public HarmonyPushServer(NotifyProperties properties,
                           @Qualifier("huaweiHttpSupport") OkHttpSupport httpClient) {
    this.adapter = new HarmonyPushAdapter(properties.getHarmony(), httpClient);
  }


  @Override
  protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      HuaWeiPushResult pushResult = adapter.pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Throwable e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }

  private void resultHandler(HuaWeiPushResult pushResult, PushMessageDTO messageDTO) {
    messageDTO.setResultCode(pushResult.getCode());
    messageDTO.setResultInfo(pushResult.getMsg());
    messageDTO.setPushedTime(System.currentTimeMillis());
    messageDTO.setRequestId(pushResult.getRequestId());
    messageDTO.setPushStatus(pushResult.isSuccess() ? PushRecord.PUSH_STATUS_SUCCESS : PushRecord.PUSH_STATUS_FAILURE);
    super.saveRecord(messageDTO);
    if (pushResult.isInvalidToken()) {
      super.removeAndroidTokenFromOMS(messageDTO);
    }
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_HARMONY);
  }
}
