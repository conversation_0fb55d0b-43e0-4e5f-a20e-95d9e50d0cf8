package com.sharecrm.egress.push;

import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.HuaWeiPushAdapter;
import com.sharecrm.egress.push.adapter.HuaWeiPushResult;

/**
 * 华为推送（HMS-PUSH）
 */
public abstract class HuaWeiPushServer extends PushServer {

  @Override
  public void send(final PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      HuaWeiPushResult pushResult = adapter().pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Throwable e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }

  protected abstract HuaWeiPushAdapter adapter();

  private void resultHandler(HuaWeiPushResult pushResult, PushMessageDTO messageDTO) {

    messageDTO.setResultCode(pushResult.getCode());
    messageDTO.setResultInfo(pushResult.getMsg());
    messageDTO.setPushedTime(System.currentTimeMillis());
    messageDTO.setRequestId(pushResult.getRequestId());
    if (pushResult.isSuccess()) {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_SUCCESS);
    } else {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_FAILURE);
    }
    super.saveRecord(messageDTO);
    if (pushResult.isInvalidToken()) {
      super.removeAndroidTokenFromOMS(messageDTO);
    }
  }

}
