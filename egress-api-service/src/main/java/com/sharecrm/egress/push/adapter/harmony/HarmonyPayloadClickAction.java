package com.sharecrm.egress.push.adapter.harmony;

import lombok.Data;

import java.util.Map;

@Data
public class HarmonyPayloadClickAction {

  /**
   * 消息点击后的行为。
   * <p>
   * 0：打开应用首页
   * <p>
   * 1：打开应用自定义页面
   * <p>
   * 3：清除通知
   * <p>
   * 5：打开拨号界面
   */
  private Integer actionType = 0;

  /**
   * 应用内置页面ability对应的action。当actionType为1时，字段uri和action至少填写一个。
   */
  private String action;
  /**
   * 应用内置页面ability对应的uri，uri对象内部结构请参见skills标签。当actionType为1时，字段uri和action至少填写一个。当存在多个Ability时，分别填写不同Ability的action和uri，优先使用action查找对应的应用内置页面。
   */
  private String uri;
 
  private Map<String, Object> data;

}
