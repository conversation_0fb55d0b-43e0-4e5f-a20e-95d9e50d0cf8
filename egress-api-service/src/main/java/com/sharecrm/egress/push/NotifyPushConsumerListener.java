package com.sharecrm.egress.push;

import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.fxiaoke.rocketmq.listener.RocketListenerConcurrently;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

/**
 * 消息推送服务消费端
 */
@Slf4j
@Component
@ConditionalOnNotifyEnabled
public class NotifyPushConsumerListener implements RocketListenerConcurrently {

  private final PushServerProxy pushServerProxy;
  private final NotifyProperties properties;

  public NotifyPushConsumerListener(PushServerProxy pushServerProxy, NotifyProperties properties) {
    this.pushServerProxy = pushServerProxy;
    this.properties = properties;
  }

  @Override
  public void consumeMessage(MessageExt msg) {
    try {
      ThirdPartPushCommonMessage thirdMessage = new ThirdPartPushCommonMessage();
      thirdMessage.fromProto(msg.getBody());
      PushMessageDTO ret = PushMessageDTO.Convert.from(thirdMessage);
      ret.setMqMsgId(msg.getMsgId());
      push(ret);
    } catch (Throwable e) {
      log.warn("convert messages failed.", e);
    }
  }


  @Override
  public ConsumeConcurrentlyStatus fallback(MessageExt msg, ConsumeConcurrentlyContext context, Exception ex) {
    // 记录warn信息，丢弃掉消息
    log.warn("consume messages failed, dropped message.", ex);
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  private void push(PushMessageDTO ret) {
    try {
      // 为了蒙牛临时灰度，功能稳定后即可删除此判断逻辑
      if (isSkipSource(ret.getPushServerSource())) {
        log.info("skip for this source: {}", ret.getToken());
        return;
      }

      pushServerProxy.push(ret);
    } catch (Exception e) {
      log.warn("message consume | type: {} | source: {} | tk: {}",
        ret.getMessageType(),
        ret.getPushServerSource(),
        ret.getToken());
    }
  }

  private boolean isSkipSource(String source) {
    if (source.contains("APPLE") && properties.isSkipApple()) {
      return true;
    }
    return properties.isSkipAndroid();
  }

}
