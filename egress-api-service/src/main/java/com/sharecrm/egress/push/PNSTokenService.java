package com.sharecrm.egress.push;

import com.facishare.oms.api.model.Platform;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 删除失效的设备token，当前暂不会真的删除，只做记录，实际上删了更好。不删除的问题在于消息会重复发送，无效的设备失败，多一些失败记录
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class PNSTokenService {
  private final NotifyProperties properties;
  private final OkHttpSupport httpClient;

  public PNSTokenService(NotifyProperties properties, @Qualifier("okHttpNotify") OkHttpSupport httpClient) {
    this.properties = properties;
    this.httpClient = httpClient;
  }

  public void removeAppleTokenFromOMS(String enterpriseAccount, String token, boolean isEnterprise) {
    this.removeFromOMS(enterpriseAccount, token, Platform.PLATFORM_IOS, isEnterprise);
  }

  public void removeAndroidTokenFromOMS(String enterpriseAccount, String token) {
    this.removeFromOMS(enterpriseAccount, token, Platform.PLATFORM_ANDROID, false);
  }

  private void removeFromOMS(String account, String token, byte platform, boolean isEnterprise) {
    //当前没有真的删
    log.info("pns token remove, ea:{},token:{},platform:{},isEnterprise:{}", account, token, platform, isEnterprise);

    String tokenUrl = properties.getTokenUrl();
    if (StringUtils.isBlank(tokenUrl)) {
      return;
    }
    try {
      TokenRemoveRequest request = new TokenRemoveRequest();
      request.setEnterprise(isEnterprise);
      request.setEnterpriseAccount(account);
      request.setToken(token);
      request.setPlatform(platform);
      String contentType = "application/octet-stream";
      //老接口，等新接口稳定后去掉此兼容逻辑
      if (tokenUrl.contains("thirdpush")) {
        contentType = "application/json";
      }
      httpClient.execute(WebUtils.okHttpPost(tokenUrl, request, contentType, Map.of()));
    } catch (Exception e) {
      log.error("remove pns token error", e);
    }

  }

}
