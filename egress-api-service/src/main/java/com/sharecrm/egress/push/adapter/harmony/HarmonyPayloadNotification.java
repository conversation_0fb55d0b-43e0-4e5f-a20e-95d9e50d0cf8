package com.sharecrm.egress.push.adapter.harmony;

import lombok.Data;

@Data
public class HarmonyPayloadNotification {

  private String category;
  private String title;
  /**
   * 通知消息内容
   */
  private String body;

  /**
   * 通知右侧大图标URL，URL使用的协议必须是HTTPS协议，且应通过图片风控验证后的downloadUrl。
   */
  private String image;

  private Integer style = 0;

  private HarmonyPayloadClickAction clickAction;
  private HarmonyPayloadBadge badge;

}
