package com.sharecrm.egress.push.adapter;

import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.google.common.base.Strings;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.config.NotifyProperties.VivoPushConfig;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.WebUtils;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * VIVO push
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Component
@ConditionalOnNotifyEnabled
public class VivoPushAdapter {

  private AuthToken authToken = new AuthToken();

  private final OkHttpSupport client;
  private final NotifyProperties properties;

  public VivoPushAdapter(@Qualifier("vivoHttpSupport") OkHttpSupport client, NotifyProperties properties) {
    this.client = client;
    this.properties = properties;
  }

  public SendResult pushToServer(PushMessageDTO messageDTO) throws Throwable {
    if (StringUtils.isEmpty(messageDTO.getSummary())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }
    return sendByNotification(messageDTO);
  }

  /**
   * 通知栏模式
   */
  private SendResult sendByNotification(PushMessageDTO messageDTO) {
    Payload payload = messageToPayload(messageDTO);
    messageDTO.setPushContent(JsonUtil.getGson(true).toJson(payload));
    String url = properties.getVivo().getApiUrl() + "/message/send";
    Request request = WebUtils.okHttpJsonPost(url, payload, Map.of("authToken", this.createAuthToken()));
    return client.parseObject(request, new TypeReference<>() {
    });
  }

  Payload messageToPayload(PushMessageDTO messageDTO) {
    messageDTO.setNotifyPushType(Constants.NOTIFY_PUSH_TYPE_1);

    String title = messageDTO.getRegionTitle();
    if (Strings.isNullOrEmpty(title)) {
      log.info("title-miss, id: {}", messageDTO.getId());
      title = Constants.FS_PRODUCT_NAME;
    }
    String description = messageDTO.combineTitleAndSummary();
    // 通知类型 1:无，2:响铃，3:振动，4:响铃和振动
    int notifyType = notifyType(messageDTO);

    Map<String, Object> customArgs = new HashMap<>(6);
    customArgs.put("enterpriseAccount", messageDTO.getEnterpriseAccount());
    customArgs.put("employeeId", messageDTO.getEmployeeId());
    customArgs.put("bizPath", messageDTO.getBizPath());
    customArgs.put("data", messageDTO.getBizData());
    customArgs.put("pushMessageId", messageDTO.getId());
    customArgs.put("regionId", messageDTO.getRegionId());

    String skipContent = "fxiaoke://applink?bizPath=%s&bizData=%s".formatted(messageDTO.getBizPath(), JsonUtil.toJson(messageDTO.getBizData()));

    Payload.PayloadBuilder build = Payload.builder()
      .regId(messageDTO.getToken())
      .notifyType(notifyType)
      // category消息类型： 当前只支持 "即时消息 IM" 这一种类型
      .category("IM")
      .title(title.length() > 20 ? title.substring(0, 20) : title)
      .content(description.length() > 40 ? description.substring(0, 40) : description)
      .timeToLive(3600)
      .skipType(3)
      .skipContent(skipContent)
      .clientCustomMap(customArgs)
      .networkType(-1)
      .requestId(UUID.randomUUID().toString());
    return build.build();
  }

  private int notifyType(PushMessageDTO messageDTO) {
    if (messageDTO.isVibrate() && messageDTO.isWithSound()) {
      return 4;
    }
    if (messageDTO.isVibrate()) {
      return 3;
    }
    if (messageDTO.isWithSound()) {
      return 2;
    }
    return 1;
  }

  private String createAuthToken() {
    if (authToken.isExpire() || authToken.getAuthToken() == null) {
      VivoPushConfig pushConfig = properties.getVivo();
      Integer appId = pushConfig.getAppId();
      String appKey = pushConfig.getAppKey().trim();
      String appSecret = pushConfig.getAppSecret().trim();
      Long timestamp = System.currentTimeMillis();
      String sign = DigestUtils.md5Hex(appId + appKey + timestamp + appSecret);

      Map<String, Object> bodyMap = new HashMap<>(4);
      bodyMap.put("appId", pushConfig.getAppId());
      bodyMap.put("appKey", pushConfig.getAppKey());
      bodyMap.put("timestamp", timestamp);
      bodyMap.put("sign", sign);
      Request request = WebUtils.okHttpJsonPost(pushConfig.getApiUrl() + "/message/auth", bodyMap, Map.of());
      this.authToken = client.parseObject(request, new TypeReference<>() {
      });
    }
    return authToken.getAuthToken();
  }

  @Builder
  @Getter
  @Setter
  @Accessors(chain = true)
  public static class Payload {

    private String regId;
    private String alias;
    private Integer notifyType;
    private String category;
    private String title;
    private String content;
    private Integer timeToLive;
    private Integer skipType;
    private String skipContent;
    private Integer networkType;
    private Map<String, Object> clientCustomMap;
    private Map<String, String> extra;
    private String requestId;
  }

  @Getter
  @Setter
  @Accessors(chain = true)
  public static class SendResult {

    private Integer result;
    private String desc;
    private String taskId;
  }


  @Data
  public static class AuthToken {
    private String desc;
    private String authToken;
    private final long createTimestamp = System.currentTimeMillis();

    public boolean isExpire() {
      //官方定义的过期时间是一天，具体参考官方文档
      return System.currentTimeMillis() - createTimestamp > 23 * 60 * 60 * 1000L;
    }
  }
}
