package com.sharecrm.egress.push.adapter.harmony;

import lombok.Data;

@Data
public class HarmonyPayloadBadge {

  /**
   * 应用角标累加数字（大于0小于100的整数），非应用角标实际显示数字。
   * <p>
   * 说明
   * 某应用当前有N条未读消息，若addNum设置为3，则每发一次消息，应用角标显示的数字累加3，为N+3。
   * 当不传入addNum时默认值为0，角标不会增加。
   */
  private Integer addNum;
  
  /**
   * 角标设置数字（大于等于0小于100的整数），应用角标实际显示数字。
   * <p>
   * 说明
   * setNum优先级高于addNum：
   * <p>
   * 若setNum<0，说明未下发setNum，则本次以addNum为准。此时判断若addNum>=0，则发布通知时携带addNum字段；否则不携带。
   * 若setNum>=0，说明下发了setNum，则本次以setNum为准。发布通知时不携带addNum字段。
   */
  private Integer setNum;

}
