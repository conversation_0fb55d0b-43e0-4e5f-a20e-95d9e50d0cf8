package com.sharecrm.egress.push;

import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.FcmPushAdapter;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Google FCM
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class FcmPushServer extends PushServer {

  private final FcmPushAdapter fcmPushAdapter;

  public FcmPushServer(FcmPushAdapter fcmPushAdapter) {
    this.fcmPushAdapter = fcmPushAdapter;
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_FCM);
  }

  @Override
  protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      FcmPushAdapter.PushResult pushResult = fcmPushAdapter.pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Exception e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }

  private void resultHandler(FcmPushAdapter.PushResult pushResult, PushMessageDTO messageDTO) {
    messageDTO.setResultCode(String.valueOf(pushResult.getCode()));
    messageDTO.setResultInfo(Optional.ofNullable(pushResult.getError()).map(FcmPushAdapter.PushResultError::getMessage).orElse(""));
    messageDTO.setPushedTime(System.currentTimeMillis());
    if (pushResult.isSuccess()) {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_SUCCESS);
    } else {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_FAILURE);
    }
    super.saveRecord(messageDTO);
    if (pushResult.isInvalidToken()) {
      super.removeAndroidTokenFromOMS(messageDTO);
    }
  }

}
