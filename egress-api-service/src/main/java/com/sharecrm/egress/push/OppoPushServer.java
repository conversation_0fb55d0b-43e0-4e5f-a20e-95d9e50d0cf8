package com.sharecrm.egress.push;


import com.oppo.push.server.Result;
import com.oppo.push.server.ReturnCode;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.OppoPushAdapter;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * OPPO推送服务
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class OppoPushServer extends PushServer {

  private final OppoPushAdapter oppoPushAdapter;

  public OppoPushServer(OppoPushAdapter oppoPushAdapter) {
    this.oppoPushAdapter = oppoPushAdapter;
  }

  @Override
  protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      Result pushResult = oppoPushAdapter.pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Throwable e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }

  private void resultHandler(Result result, PushMessageDTO messageDTO) {
    ReturnCode returnCode = result.getReturnCode();
    if (Objects.nonNull(returnCode)) {
      if (returnCode.getCode() == 0) {
        messageDTO.setPushStatus(PushRecord.PUSH_STATUS_SUCCESS);
      } else {
        messageDTO.setPushStatus(PushRecord.PUSH_STATUS_FAILURE);
      }
      messageDTO.setResultCode(String.valueOf(returnCode.getCode()));
      messageDTO.setResultInfo(returnCode.getMessage());

      if (returnCode.getCode() == 11) {
        super.removeAndroidTokenFromOMS(messageDTO);
      }
    }

    messageDTO.setRequestId(result.getMessageId());
    messageDTO.setPushedTime(System.currentTimeMillis());
    super.saveRecord(messageDTO);
  }


  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_OPPO);
  }
}

