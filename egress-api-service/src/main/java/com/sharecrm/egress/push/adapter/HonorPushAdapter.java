package com.sharecrm.egress.push.adapter;


import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.sharecrm.egress.config.NotifyProperties.HonorPushConfig;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.push.adapter.honor.HonorAlertAndroid;
import com.sharecrm.egress.push.adapter.honor.HonorAlertAndroidNotification;
import com.sharecrm.egress.push.adapter.honor.HonorAlertBadge;
import com.sharecrm.egress.push.adapter.honor.HonorAlertClickAction;
import com.sharecrm.egress.push.adapter.honor.HonorPushRequest;
import com.sharecrm.egress.push.adapter.honor.HonorPushResult;
import com.sharecrm.egress.push.adapter.honor.HonorTokenResult;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.NumUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 荣耀消息推送
 * <p>
 * see：<a href="https://developer.honor.com/cn/docs/11002/guides/cloud-overview">...</a>
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
public class HonorPushAdapter {

  private final HonorPushConfig pushConfig;

  private final OkHttpSupport httpClient;

  private final AsyncLoadingCache<String, String> cache;

  public HonorPushAdapter(HonorPushConfig pushConfig, OkHttpSupport httpClient) {
    this.pushConfig = pushConfig;
    this.httpClient = httpClient;
    this.cache = initCache(pushConfig);
  }

  @NotNull
  private AsyncLoadingCache<String, String> initCache(HonorPushConfig pushConfig) {
    return Caffeine.newBuilder()
      //默认一个小时，过期时间稍微减少一些
      .expireAfterWrite(pushConfig.getTokenTimeout())
      .maximumSize(10000)
      .buildAsync(key -> loadAccessToken());
  }

  private String loadAccessToken() {
    try {
      Request request = tokenRequest();
      HonorTokenResult resp = (HonorTokenResult) httpClient.syncExecute(request, new SyncCallback() {
        @Override
        public HonorTokenResult response(Response response) throws Exception {
          String body = Objects.requireNonNull(response.body()).string();
          log.debug("honor access token response: {}", body);
          return JsonUtil.fromJson(body, HonorTokenResult.class);
        }
      });
      return Optional.ofNullable(resp).map(HonorTokenResult::getAccessToken).orElse("");
    } catch (Exception e) {
      log.warn("load honor access token failed", e);
      return "";
    }
  }

  @NotNull
  private Request tokenRequest() {
    RequestBody formBody = new FormBody.Builder()
      .add("grant_type", "client_credentials")
      .add("client_secret", pushConfig.getClientSecret())
      .add("client_id", pushConfig.getClientId())
      .build();
    return new Request.Builder()
      .url(pushConfig.getTokenUrl())
      .post(formBody)
      .build();
  }

  public HonorPushResult pushToServer(PushMessageDTO messageDTO) {
    if (StringUtils.isEmpty(messageDTO.getSummary())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }
    return sendByNotification(messageDTO);
  }

  HonorPushResult sendByNotification(PushMessageDTO dto) {
    try {
      dto.setNotifyPushType(Constants.NOTIFY_PUSH_TYPE_1);
      Map<String, String> headers = Map.of(
        "Content-Type", "application/json",
        "Authorization", "Bearer " + cache.get("token").get(),
        "timestamp", System.currentTimeMillis() + "");
      HonorPushRequest body = new HonorPushRequest(null, android(dto), Set.of(dto.getToken()));
      Request request = WebUtils.okHttpJsonPost(pushConfig.getPushUrl(), body, headers);
      //设置回去用于日志记录
      dto.setPushContent(JsonUtil.toJson(body));
      return httpClient.parseObject(request, new TypeReference<>() {
      });
    } catch (InterruptedException ex) {
      Thread.currentThread().interrupt();
      log.warn("honor push failed.", ex);
      return failedMessage(ex);
    } catch (Exception e) {
      log.warn("honor push failed.", e);
      return failedMessage(e);
    }
  }

  @NotNull
  private HonorPushResult failedMessage(Exception ex) {
    HonorPushResult failed = new HonorPushResult();
    failed.setCode(500);
    failed.setMessage(ex.getMessage());
    return failed;
  }

  private HonorAlertAndroid android(PushMessageDTO dto) {
    HonorAlertAndroidNotification notification = new HonorAlertAndroidNotification();
    notification.setTitle(StringUtils.defaultIfEmpty(dto.getRegionTitle(), Constants.FS_PRODUCT_NAME));

    String description = dto.combineTitleAndSummary();
    notification.setBody(description);
    String uri = "fxiaoke://applink?bizPath=%s&bizData=%s".formatted(dto.getBizPath(), JsonUtil.toJson(dto.getBizData()));
    notification.setClickAction(new HonorAlertClickAction(1, uri));

    HonorAlertBadge badge = new HonorAlertBadge();
    badge.setAddNum(1);
    badge.setSetNum(NumUtils.range(1, 99, dto.getUnreadNumber()));
    badge.setBadgeClass(pushConfig.getBadgeClass());

    notification.setBadge(badge);
    return new HonorAlertAndroid(notification);
  }

}
