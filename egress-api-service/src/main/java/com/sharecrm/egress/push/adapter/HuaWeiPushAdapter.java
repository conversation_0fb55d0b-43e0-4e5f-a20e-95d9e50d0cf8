package com.sharecrm.egress.push.adapter;


import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sharecrm.egress.config.NotifyProperties.HuaWeiPushConfig;
import com.sharecrm.egress.entity.PayloadDTO;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.NumUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * huawei
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
public class HuaWeiPushAdapter {

  private final HuaWeiPushConfig pushConfig;

  private final OkHttpSupport httpClient;

  /**
   * 下发通知消息的认证Token
   */
  private volatile String accessToken;

  /**
   * accessToken的过期时间
   */
  private long tokenExpiredTime;

  public HuaWeiPushAdapter(HuaWeiPushConfig pushConfig, OkHttpSupport httpClient) {
    this.pushConfig = pushConfig;
    this.httpClient = httpClient;
  }

  public HuaWeiPushResult pushToServer(PushMessageDTO messageDTO) {
    if (StringUtils.isEmpty(messageDTO.getSummary())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }
    //如果是免打扰 或者 属于旧版华为推送Token, 那么采用透传模式发送消息
    if (messageDTO.isNoDisturb() || Constants.PUSH_SERVER_SOURCE_HUAWEI.contains(messageDTO.getPushServerSource()) ||
      Constants.PUSH_SERVER_SOURCE_HUAWEI_ENT.contains(messageDTO.getPushServerSource())) {
      return sendByTransmission(messageDTO);
    } else {
      return sendByNotification(messageDTO);
    }
  }


  /**
   * 透传模式
   */
  private HuaWeiPushResult sendByTransmission(PushMessageDTO messageDTO) {
    String bodyJson = sendByTransmissionRequest(messageDTO);
    return httpPost(bodyJson);
  }

  String sendByTransmissionRequest(PushMessageDTO messageDTO) {
    messageDTO.setNotifyPushType(Constants.NOTIFY_PUSH_TYPE_2);
    PayloadDTO payloadDTO = PayloadDTO.Convert.from(messageDTO);

    String payLoad = JsonUtil.getGson().toJson(payloadDTO);
    Map<String, Object> bodyMap = ImmutableMap
      .<String, Object>builder()
      .put("message", ImmutableMap
        .<String, Object>builder()
        .put("token", Lists.newArrayList(messageDTO.getToken()))
        .put("data", payLoad)
        .put("android", ImmutableMap.<String, Object>builder()
          .put("collapse_key", 0)
          .put("ttl", "1800")
          .build())
        .build())
      .build();

    String bodyJson = JsonUtil.getGson().toJson(bodyMap);
    messageDTO.setPushContent(bodyJson);
    return bodyJson;
  }

  /**
   * 通知栏模式
   */
  private HuaWeiPushResult sendByNotification(PushMessageDTO messageDTO) {
    String bodyJson = sendByNotificationRequest(messageDTO);
    return httpPost(bodyJson);
  }

  String sendByNotificationRequest(PushMessageDTO messageDTO) {
    messageDTO.setNotifyPushType(Constants.NOTIFY_PUSH_TYPE_1);
    PayloadDTO payloadDTO = PayloadDTO.Convert.from(messageDTO);

    //-------------------------------  这兼容的搞得我的看不明白了， 请从代码自行理解
    String description = messageDTO.combineTitleAndSummary();
    String title = messageDTO.getRegionTitle();
    //通知栏消息的标题和内容
    if (Strings.isNullOrEmpty(title)) {
      log.info("title-miss, id: {}", messageDTO.getId());
      title = Constants.FS_PRODUCT_NAME;
    }
    //------------------------------------------
    boolean isInnerTest = messageDTO.getPushServerSource().endsWith("_ENT");
    String packageName = isInnerTest ? "com.facishare.fsneice" : "com.facishare.fs";
    String intent = String.format(
      "intent://com.facishare.fs/hw_notify_detail?payload=%s#Intent;scheme=fxiaoke;action=android.intent.action.VIEW;launchFlags=0x4000000;package=%s;end",
      JsonUtil.getGson(true).toJson(payloadDTO), packageName
    );

    // badge add_num 范围必须在 [1-99] 之间
    int badgeNum = NumUtils.range(1, 99, messageDTO.getUnreadNumber());

    ImmutableMap<String, Object> android = ImmutableMap.<String, Object>builder()
      .put("collapse_key", 0)
      .put("ttl", "1800")
      .put("bi_tag", "todo")
      // category 目前我们还无法区分类型，固定为IM类型
      .put("category", "IM")
      .put("notification", ImmutableMap.<String, Object>builder()
        .put("title", title)
        .put("body", description)
        .put("sound", messageDTO.getSoundName())
        .put("use_default_vibrate", true)
        .put("click_action", ImmutableMap.<String, Object>builder()
          .put("type", 1)
          .put("intent", intent)
          .build())
        .put("badge", ImmutableMap.<String, Object>builder()
          .put("add_num", 1)
          .put("set_num", badgeNum)
          .put("class", "com.fxiaoke.host.IndexActivity")
          .build())
        .build())
      .build();
    Map<String, Object> bodyMap = ImmutableMap.<String, Object>builder()
      .put("message", ImmutableMap.<String, Object>builder()
        .put("token", Lists.newArrayList(messageDTO.getToken()))
        .put("data", "")
        .put("android", android)
        .build())
      .build();

    String bodyJson = JsonUtil.getGson().toJson(bodyMap);
    messageDTO.setPushContent(bodyJson);
    return bodyJson;
  }

  private HuaWeiPushResult httpPost(String bodyJson) {
    Map<String, String> headers = Map.of("Authorization", "Bearer " + cacheOrRefreshToken());
    Request req = WebUtils.okHttpJsonPost(pushConfig.getPushUrl(), bodyJson, headers);
    return httpClient.parseObject(req, new TypeReference<>() {
    });
  }

  private String cacheOrRefreshToken() {
    //校验Token是否已过期
    if (tokenExpiredTime > System.currentTimeMillis() && StringUtils.isNotBlank(accessToken)) {
      return accessToken;
    }
    RequestBody formBody = new FormBody.Builder()
      .add("grant_type", "client_credentials")
      .add("client_secret", pushConfig.getAppKey())
      .add("client_id", pushConfig.getAppId())
      .build();
    Request request = new Request.Builder()
      .url(pushConfig.getTokenUrl())
      .post(formBody)
      .build();

    String response = (String) httpClient.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        return Objects.requireNonNull(response.body()).string();
      }
    });
    TokenResult tokenResult = JsonUtil.getGson(true).fromJson(response, TokenResult.class);
    accessToken = tokenResult.access_token.replace("\\\\", "");
    log.info("refreshToken: tokenResult: {}, parse: {}", tokenResult, accessToken);
    tokenExpiredTime = (System.currentTimeMillis() + tokenResult.getExpires_in() * 1000) - 5 * 60 * 1000;
    return accessToken;
  }

  @Data
  private static class TokenResult {
    private String access_token;
    private Integer expires_in;
    private String scope;
    private String error;
    private String error_description;
  }

}
