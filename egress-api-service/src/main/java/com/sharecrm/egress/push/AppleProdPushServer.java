package com.sharecrm.egress.push;


import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.push.adapter.ApplePushAdapter;
import com.sharecrm.egress.utils.Constants;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 针对 IOS 平台的Push Provider，生产通道
 */
@Service
@ConditionalOnNotifyEnabled
public class AppleProdPushServer extends ApplePushServer {

  private final ApplePushAdapter applePushAdapter;

  public AppleProdPushServer(NotifyProperties properties) {
    this.applePushAdapter = new ApplePushAdapter(properties.getAppleProd());
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_APPLE_PROD);
  }

  @Override
  protected ApplePushAdapter adapter() {
    return this.applePushAdapter;
  }
}
