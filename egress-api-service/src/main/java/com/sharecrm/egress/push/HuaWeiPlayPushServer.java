package com.sharecrm.egress.push;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.push.adapter.HuaWeiPushAdapter;
import com.sharecrm.egress.utils.Constants;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@ConditionalOnNotifyEnabled
public class HuaWeiPlayPushServer extends HuaWeiPushServer {

  private final HuaWeiPushAdapter huaWeiPushAdapter;

  public HuaWeiPlayPushServer(NotifyProperties properties,
                              @Qualifier("huaweiHttpSupport") OkHttpSupport httpClient) {
    this.huaWeiPushAdapter = new HuaWeiPushAdapter(properties.getHuaweiPlay(), httpClient);
  }

  @Override
  protected HuaWeiPushAdapter adapter() {
    return this.huaWeiPushAdapter;
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_HUAWEI_V2_PLAY);
  }

}
