package com.sharecrm.egress.push.adapter;

import com.google.common.base.Strings;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PayloadDTO;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.exception.ThirdPushClientException;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.VersionUtil;
import com.xiaomi.xmpush.server.Message;
import com.xiaomi.xmpush.server.Result;
import com.xiaomi.xmpush.server.Sender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.function.Supplier;

/**
 * xiaomi
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Component
@ConditionalOnNotifyEnabled
public class XiaoMiPushAdapter {

  private static final int DESC_LIMIT_LENGTH = 120;

  private final Supplier<Sender> sender;
  private final NotifyProperties properties;

  public XiaoMiPushAdapter(Supplier<Sender> sender, NotifyProperties properties) {
    this.sender = sender;
    this.properties = properties;
  }

  public Result pushToServer(PushMessageDTO messageDTO) throws Throwable {
    if (StringUtils.isEmpty(messageDTO.getSummary())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }
    return sendByNotification(messageDTO);
  }

  /**
   * 通知栏模式
   *
   * @return 推送结果
   */
  private Result sendByNotification(PushMessageDTO messageDTO) throws Throwable {
    Sender send = sender.get();
    if (Objects.isNull(send)) {
      throw new ThirdPushClientException("xiaomi client is null, may be network can not connect.");
    }

    messageDTO.setNotifyPushType(Constants.NOTIFY_PUSH_TYPE_1);

    PayloadDTO payloadDTO = PayloadDTO.Convert.from(messageDTO);
    String payload = JsonUtil.getGson(true).toJson(payloadDTO);


    boolean aggregation = StringUtils.isNotEmpty(messageDTO.getRegionId());
    int unreadNumber = aggregation ? messageDTO.getRegionUnreadNumber() : messageDTO.getUnreadNumber();
    String description = messageDTO.combineTitleAndSummary();
    if (description.length() > DESC_LIMIT_LENGTH) {
      description = description.substring(0, DESC_LIMIT_LENGTH);
    }
    //-1:All，1:提示音, 2:震动, 4:led灯光
    int notifyType = (messageDTO.isWithSound() ? 1 : 0);
    notifyType = notifyType > 0 ? notifyType + 4 : notifyType; //有声音或震动，则开启led灯光

    String title = messageDTO.getRegionTitle();
    if (Strings.isNullOrEmpty(title)) {
      log.info("title-miss, id: {}", messageDTO.getId());
      title = Constants.FS_PRODUCT_NAME;
    }

    Message.Builder builder = new Message.Builder()  //通知栏消息
      .title(title)
      .description(description)
      .notifyType(notifyType)
      .passThrough(0)
      //notifyId必须位正数，否则负数的消息展现会互相覆盖，这点在小米推送文档上没有描述
      .notifyId(Math.abs(Objects.hashCode(messageDTO.getRegionId())))
      .payload(payload) //终端会基于此内容做一些处理操作
      .restrictedPackageNames(properties.getXiaomi().getPackageNames().toArray(new String[0]))
      .extra(com.xiaomi.xmpush.server.Constants.EXTRA_PARAM_NOTIFY_FOREGROUND, "1")
      .extra("message_count", String.valueOf(unreadNumber))
      // channel_id 125867对应IM消息类型，需要在小米平台先申请好
      .extra("channel_id", "125867")
      //自定义声音， 该功能为6.2版本需求， 待6.2上线再开启
      .timeToLive(2 * 60 * 60 * 1000L);

    // EXTRA_PARAM_NOTIFY_EFFECT 支持预定义消息
    builder.extra(com.xiaomi.xmpush.server.Constants.EXTRA_PARAM_NOTIFY_EFFECT, com.xiaomi.xmpush.server.Constants.NOTIFY_ACTIVITY);
    String uri = "fxiaoke://applink?bizPath=%s&bizData=%s".formatted(messageDTO.getBizPath(), JsonUtil.toJson(messageDTO.getBizData()));
    log.debug("xiaomi EXTRA_PARAM_INTENT_URI uri:{}", uri);
    builder.extra(com.xiaomi.xmpush.server.Constants.EXTRA_PARAM_INTENT_URI, uri);

    if (!"default".equals(messageDTO.getSoundName())) {
      String prePath = VersionUtil.isNeiCe(messageDTO.getAppVersion()) ? "com.facishare.fsneice" : "com.facishare.fs";
      String soundName = "android.resource://" + prePath + "/raw/" + messageDTO.getSoundName();
      builder.extra(com.xiaomi.xmpush.server.Constants.EXTRA_PARAM_SOUND_URI, soundName);
    }
    Message message = builder.build();
    messageDTO.setPushContent(message.toString());
    return send.send(message, messageDTO.getToken(), 2);
  }

}
