package com.sharecrm.egress.push;

import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.concurrent.PushNotificationFuture;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.exception.ThirdPushException;
import com.sharecrm.egress.push.adapter.ApplePushAdapter;
import com.sharecrm.egress.utils.StupidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 针对 IOS 平台的Push Provider
 */
@Slf4j
public abstract class ApplePushServer extends PushServer {

  private static final String NO_ERROR = "0";

  @Override
  public void send(final PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      PushNotificationFuture<SimpleApnsPushNotification, PushNotificationResponse<SimpleApnsPushNotification>> future = adapter()
        .pushToServer(messageDTO);
      future.whenCompleteAsync((response, cause) -> {
        if (response != null) {
          processResponse(messageDTO, response);
        } else {
          log.warn("ios send failed, do retry | msgId:{} | tk:{}  ", messageDTO.getMqMsgId(),
            messageDTO.getToken(), cause);
          //do retry, ignore result
          StupidUtils.sleep(2000);
          adapter().pushToServer(messageDTO);
        }
      });
    } catch (Throwable e) {
      super.saveException(messageDTO, e);
    }
  }

  private void processResponse(PushMessageDTO messageDTO,
                               PushNotificationResponse<SimpleApnsPushNotification> response) {
    if (response.isAccepted()) {
      messageDTO.setPushStatus(PushRecord.PUSH_STATUS_SUCCESS);
      messageDTO.setResultCode(NO_ERROR);
      messageDTO.setResultInfo("NO_ERROR");
      super.saveRecord(messageDTO);
    } else {
      String rejectionReason = response.getRejectionReason().orElse("Unknown");
      doLog(messageDTO, rejectionReason);
      super.saveException(messageDTO, new ThirdPushException(rejectionReason));
      if ("InvalidProviderToken".equals(rejectionReason)) {
        super.removeAppleTokenFromOMS(messageDTO);
      }
    }
  }

  private void doLog(PushMessageDTO messageDTO, String rejectionReason) {
    if (isIgnoreReason(rejectionReason)) {
      log.info("ios push failed,msgId:{},ea:{},rej:{}",
        messageDTO.getMqMsgId(),
        messageDTO.getEnterpriseAccount(),
        rejectionReason);
    } else {
      log.warn("ios push failed,msgId:{},ea:{},rej:{}",
        messageDTO.getMqMsgId(),
        messageDTO.getEnterpriseAccount(),
        rejectionReason);
    }
  }

  private boolean isIgnoreReason(String rejectionReason) {
    if (StringUtils.contains(rejectionReason, "Unregistered")) {
      return true;
    }
    return StringUtils.contains(rejectionReason, "DeviceToken");
  }

  protected abstract ApplePushAdapter adapter();

}
