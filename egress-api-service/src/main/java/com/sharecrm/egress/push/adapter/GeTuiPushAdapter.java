package com.sharecrm.egress.push.adapter;

import com.gexin.rp.sdk.base.IPushResult;
import com.gexin.rp.sdk.base.impl.SingleMessage;
import com.gexin.rp.sdk.base.impl.Target;
import com.gexin.rp.sdk.exceptions.RequestException;
import com.gexin.rp.sdk.http.IGtPush;
import com.gexin.rp.sdk.template.TransmissionTemplate;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PayloadDTO;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.exception.ThirdPushClientException;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.VersionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.net.HttpRetryException;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * Created by wuzh on 2016/8/3.
 */
@Slf4j
@Component
@ConditionalOnNotifyEnabled
public class GeTuiPushAdapter {

  private final NotifyProperties properties;
  private final Supplier<IGtPush> pushSupplier;

  public GeTuiPushAdapter(NotifyProperties properties, Supplier<IGtPush> pushSupplier) {
    this.properties = properties;
    this.pushSupplier = pushSupplier;
  }


  public IPushResult pushToServer(PushMessageDTO messageDTO) throws Throwable {
    IGtPush push = pushSupplier.get();
    if (Objects.isNull(push)) {
      throw new ThirdPushClientException("getui client is null, may be network can not connect.");
    }

    TransmissionTemplate template = new TransmissionTemplate();
    template.setAppId(properties.getGetui().getAppId());
    template.setAppkey(properties.getGetui().getAppKey());
    template.setTransmissionType(2);

    if (StringUtils.isEmpty(messageDTO.getSummary())) {
      throw new MessageInvalidException("MESSAGE_CONTENT_EMPTY");
    }

    PayloadDTO payloadDTO = PayloadDTO.Convert.from(messageDTO);

    int appV = VersionUtil.getAppVersion(messageDTO.getAppVersion());
    if (0 < appV && appV < 551000) {
      payloadDTO.setTitle(Constants.FS_PRODUCT_NAME);
      payloadDTO.setSummary(messageDTO.getOriginalMessage());
    }
    String content = JsonUtil.getGson(true).toJson(payloadDTO);

    messageDTO.setPushContent(content);

    template.setTransmissionContent(content);

    SingleMessage message = new SingleMessage();
    message.setData(template);
    message.setOffline(true);
    message.setOfflineExpireTime(3600_000);  //单位：毫秒

    Target target = new Target();
    target.setAppId(properties.getGetui().getAppId());
    target.setClientId(messageDTO.getToken());

    try {
      return push.pushMessageToSingle(message, target);
    } catch (RuntimeException e) {
      logDetail(messageDTO, message, target, e);
      throw e;
    }
  }

  private void logDetail(PushMessageDTO messageDTO, SingleMessage message, Target target,
                         RuntimeException e) {
    log.warn("MQMSgId:{},SingleMessage: {}, target: {}",
      messageDTO.getMqMsgId()
      , JsonUtil.getGson(true).toJson(message)
      , JsonUtil.getGson(true).toJson(target));
    log.warn("getui push failed", e);
    //打印response code，这个code被封装吞没了
    if (e instanceof RequestException re && re.getCause() instanceof HttpRetryException hre) {
      log.warn("HttpRetryException responseCode: {}", hre.responseCode());
    }
  }

}
