package com.sharecrm.egress.push;


import com.gexin.rp.sdk.base.IPushResult;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.push.adapter.GeTuiPushAdapter;
import com.sharecrm.egress.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 针对Android平台的Push Provider, 基于第三方推送平台“个推”实现
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class GeTuiPushServer extends PushServer {

  private final GeTuiPushAdapter geTuiPushAdapter;

  public GeTuiPushServer(GeTuiPushAdapter geTuiPushAdapter) {
    this.geTuiPushAdapter = geTuiPushAdapter;
  }

  @Override
  public List<String> pushServerSources() {
    return List.of(Constants.PUSH_SERVER_SOURCE_GETUI);
  }

  @Override
  public void send(final PushMessageDTO pushMessageDTO) throws Throwable {
    executor.execute(() -> pushTask(pushMessageDTO));
  }

  private void pushTask(PushMessageDTO messageDTO) {
    try {
      IPushResult pushResult = geTuiPushAdapter.pushToServer(messageDTO);
      resultHandler(pushResult, messageDTO);
    } catch (Throwable e) {
      super.saveRecord(messageDTO);
      super.saveException(messageDTO, e);
    }
  }


  void resultHandler(IPushResult pushResult, PushMessageDTO messageDTO) {
    Map<String, Object> respMap = pushResult.getResponse();
    String resultCode = respMap.get("result").toString().toLowerCase();
    String status = respMap.getOrDefault("status", "").toString();
    int pushStatus = PushRecord.PUSH_STATUS_SUCCESS;
    if (!"ok".equals(resultCode)) {
      pushStatus = PushRecord.PUSH_STATUS_FAILURE;
    }

    //save entity
    messageDTO.setPushedTime(System.currentTimeMillis());
    messageDTO.setPushStatus(pushStatus);
    messageDTO.setResultCode(resultCode);
    messageDTO.setResultInfo(status);
    messageDTO.setRequestId(pushResult.getMessageId());
    super.saveRecord(messageDTO);
    if ("TokenMD5NoUsers".equalsIgnoreCase(resultCode)) {
      super.removeAndroidTokenFromOMS(messageDTO);
    }
  }

}

