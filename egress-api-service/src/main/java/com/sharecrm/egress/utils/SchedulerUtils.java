package com.sharecrm.egress.utils;

import lombok.extern.slf4j.Slf4j;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;

@Slf4j
public class SchedulerUtils {

  public static Scheduler SHORT_URL_SCHEDULER = Schedulers.newBoundedElastic(32, 1000, "short-worker", 600, true);
  public static Scheduler SMS_SCHEDULER = Schedulers.newBoundedElastic(32, 1000, "sms-worker", 600, true);
  public static Scheduler ASR_SCHEDULER = Schedulers.newBoundedElastic(32, 1000, "asr-worker", 600, true);
  public static Scheduler IP_SCHEDULER = Schedulers.newBoundedElastic(32, 1000, "ip-worker", 600, true);

  public static void resetUrlScheduler(int maxThreads, int maxTaskQueuedPerThread, Duration ttl) {
    SHORT_URL_SCHEDULER = Schedulers.newBoundedElastic(maxThreads, maxTaskQueuedPerThread, "short-worker", (int) ttl.toSeconds(), true);
  }

  public static void resetSmsScheduler(int maxThreads, int maxTaskQueuedPerThread, Duration ttl) {
    SMS_SCHEDULER = Schedulers.newBoundedElastic(maxThreads, maxTaskQueuedPerThread, "sms-worker", (int) ttl.toSeconds(), true);
  }

  public static void resetAsrScheduler(int maxThreads, int maxTaskQueuedPerThread, Duration ttl) {
    ASR_SCHEDULER = Schedulers.newBoundedElastic(maxThreads, maxTaskQueuedPerThread, "asr-worker", (int) ttl.toSeconds(), true);
  }

  public static void resetIpScheduler(int maxThreads, int maxTaskQueuedPerThread, Duration ttl) {
    IP_SCHEDULER = Schedulers.newBoundedElastic(maxThreads, maxTaskQueuedPerThread, "ip-worker", (int) ttl.toSeconds(), true);
  }

}
