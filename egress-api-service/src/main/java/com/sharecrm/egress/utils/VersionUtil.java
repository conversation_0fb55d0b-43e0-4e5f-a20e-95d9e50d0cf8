package com.sharecrm.egress.utils;

import com.google.common.base.Strings;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * Created by wuzh on 2016/7/29.
 */
@Slf4j
@UtilityClass
public class VersionUtil {
  /**
   * 最大版本号
   */
  public static final int MAX_VERSION = 10000000;

  /**
   * 获取IOS系统的主版本值
   *
   * @param version version
   * @return 版本值，特殊值 -1，表示获取版本值失败
   */
  public static int getIosMajorVersion(String version) {
    int ret = -1;
    if (!Strings.isNullOrEmpty(version)) {
      String majorVersion = version.split("\\.")[0];
      return NumberUtils.toInt(majorVersion, -1);
    }
    return ret;
  }

  /**
   * 获取app版本值
   *
   * @param appVersion appVersion
   * @return 版本值， 目前为6位数字，如：610000 ,特殊值 -1，表示获取版本值失败
   */
  public static int getAppVersion(String appVersion) {
    int ret = -1;
    if (!Strings.isNullOrEmpty(appVersion)) {
      int appV = NumberUtils.toInt(appVersion, -1);
      // 客户端为了测试， 在测试的app版本值中添加了100前缀（比如：100610000）， 以下的操作完全是为了兼容这种特殊情况
      if (appV > MAX_VERSION) {
        appV = appV % MAX_VERSION;
      }
      ret = appV;
    }

    return ret;
  }

  /**
   * 通过app版本号来判定终端是否为内测版
   *
   * @param appVersion appVersion
   * @return bool
   */
  public static boolean isNeiCe(String appVersion) {
    int appV = NumberUtils.toInt(appVersion, -1);
    // 内测版本的app，在版本值中添加了100前缀
    return appV > MAX_VERSION;
  }
}
