package com.sharecrm.egress.utils;

import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import com.sharecrm.egress.config.FluxTrace;
import com.sharecrm.egress.entity.GeoBizLog;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.entity.SmsBizLog;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.geo.GeoProvider;
import com.sharecrm.egress.sms.SmsUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 单线程ThreadPoolExecutor异步执行任务，注意任务超出后会直接丢弃，只能处理允许丢弃的任务
 */
@Slf4j
@UtilityClass
public class SingleExecutorUtils {

  private static final ExecutorService executor = new ThreadPoolExecutor(1, 8, 10, TimeUnit.MINUTES,
    new ArrayBlockingQueue<>(20000), (r, exec) -> log.warn("single executor dropped task: {} {}.", r, exec));

  public static <T> void sendBizLog(Supplier<T> supplier) {
    executor.execute(() -> BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(supplier.get())));
  }

  public static void execute(Runnable task) {
    executor.execute(task);
  }

  public static void saveSmsBizLog(SmsMongoEntity entity) {
    Supplier<SmsBizLog> supplier = () -> {
      SmsBizLog bizLog = new SmsBizLog();
      bizLog.setStamp(System.currentTimeMillis());
      bizLog.setApp(Constants.APP_NAME);
      bizLog.setServerIp(Constants.IP);
      bizLog.setProfile(Constants.PROFILE);
      bizLog.setTenantId(entity.getEnterpriseId());
      bizLog.setSmsType(entity.getSmsType());
      bizLog.setBizName(entity.getBizName());
      bizLog.setPhone(entity.getPhone());
      bizLog.setStatus(entity.getStatus() + "");
      bizLog.setMessage(entity.getMessage());
      bizLog.setProviderId(entity.getProviderId());
      bizLog.setProviderName(entity.getProviderName());
      //content需要脱敏
      bizLog.setContent(SmsUtils.sensitiveMaskerContent(entity.getContent()));
      bizLog.setEncrypted(SmsUtils.encryptContent(entity.getContent()));
      bizLog.setInternational(entity.getInternational() + "");
      bizLog.setMsgId(entity.getMsgId());
      bizLog.setSerialId(entity.getSerialId());
      bizLog.setSmsLength(entity.getSmsLength());
      bizLog.setSmsSize(entity.getSmsSize());
      bizLog.setReplyStatus("" + entity.getReplyStatus());
      bizLog.setReplyMessage(entity.getReplyMessage());
      bizLog.setReplyCode(entity.getReplyCode());
      return bizLog;
    };
    sendBizLog(supplier);
  }

  public static void saveGeoBizLog(FluxTrace trace, Object request, GeoProvider provider, String api, String status) {
    //异步线程池执行，不要丢了trace信息
    Supplier<GeoBizLog> supplier = () -> {
      GeoBizLog bizLog = new GeoBizLog();
      bizLog.setStamp(System.currentTimeMillis());
      bizLog.setApp(Constants.APP_NAME);
      bizLog.setServerIp(Constants.IP);
      bizLog.setProfile(Constants.PROFILE);
      bizLog.setObjectId(provider.getId());
      bizLog.setObjectApiName(api);
      bizLog.setProviderType(provider.getType());
      if (Objects.nonNull(request)) {
        bizLog.setMessage(JsonUtil.toJson(request));
      }
      //默认caller
      String caller = "EMPTY";
      if (Objects.nonNull(trace)) {
        caller = Objects.toString(trace.getCaller(), "EMPTY");
        bizLog.setBizName(caller);
        bizLog.setPlatform(caller);
        bizLog.setTenantId(trace.getEi());
        bizLog.setEa(trace.getEa());
        bizLog.setUid(trace.getUid());
        bizLog.setTraceId(trace.getTraceId());
      }
      if (request instanceof ReverseGeoRequest req) {
        bizLog.setBizName(Objects.toString(req.getBizName(), caller));
        bizLog.setPlatform(Objects.toString(req.getPlatform(), caller));
      }
      if (request instanceof PoiSearchRequest req) {
        bizLog.setBizName(Objects.toString(req.getBizName(), caller));
      }
      if (request instanceof GeoEncodeRequest req) {
        bizLog.setBizName(Objects.toString(req.getBizName(), caller));
      }
      bizLog.setStatus(status);
      return bizLog;
    };
    sendBizLog(supplier);
  }

}
