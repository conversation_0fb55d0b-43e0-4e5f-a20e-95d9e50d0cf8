package com.sharecrm.egress.utils;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.net.InetSocketAddress;
import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@UtilityClass
public class NetUtils {

  public static URI uri(String address) {
    // http://xxxx:port
    if (!address.contains("//")) {
      // 补充前面的协议，这样才能正常解析成一个URI
      address = "http://" + address;
    }
    return URI.create(address.trim());
  }

  public static InetSocketAddress proxyAddress(String uri) {
    return proxyAddress(uri, -1);
  }

  public static InetSocketAddress proxyAddress(String host, int port) {
    URI uri = uri(host);
    return InetSocketAddress.createUnresolved(uri.getHost(), port > 0 ? port : uri.getPort());
  }

  /**
   * Sentinel url 归整，只保留前4级，注意如果此处规则有变更需要同步修改Sentinel的规则定义
   *
   * @param url url
   * @return clean url
   */
  public static String cleanUrl(String url) {
    if (url.contains("/actuator")) {
      return StringUtils.EMPTY;
    }
    return Arrays.stream(url.split("/"))
      .limit(5)
      .collect(Collectors.joining("/"));
  }

  public static String maskQuery(String query) {
    if (query == null || query.isEmpty()) {
      return null;
    }
    List<String> tokens = List.of("key", "ak", "sn", "token");
    String[] pairs = query.split("&");
    for (int i = 0; i < pairs.length; i++) {
      String[] pair = pairs[i].split("=");
      if (pair.length == 2 && tokens.contains(pair[0])) {
        pairs[i] = pair[0] + "=" + maskToken(pair[1]);
      }
    }
    return String.join("&", pairs);
  }

  public static String maskToken(String token) {
    return token != null && token.length() > 8 ? token.substring(0, 3) + "***" + token.substring(token.length() - 4) : token;
  }

}
