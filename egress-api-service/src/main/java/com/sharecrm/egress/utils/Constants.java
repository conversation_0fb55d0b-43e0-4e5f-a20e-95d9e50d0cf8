package com.sharecrm.egress.utils;

import com.fxiaoke.common.IpUtil;
import com.github.autoconf.helper.ConfigHelper;

/**
 * 统一常量定义
 * <p>
 * IgnoreI18nFile ignore i18n
 */
public interface Constants {

  String IP = IpUtil.getSiteLocalIp();
  String APP_NAME = ConfigHelper.getProcessInfo().getName();
  String PROFILE = ConfigHelper.getProcessInfo().getProfile();

  String FS_PRODUCT_NAME = "纷享销客";
  String PUSH_SERVER_SOURCE_APPLE_PROD = "APPLE_PROD";
  String PUSH_SERVER_SOURCE_APPLE_ENT = "APPLE_ENT";
  String PUSH_SERVER_SOURCE_APPLE_EN_ENT = "APPLE_EN_ENT";
  String PUSH_SERVER_SOURCE_APPLE_EN_PROD = "APPLE_EN_PROD";
  String PUSH_SERVER_SOURCE_APPLE_PUSHKIT_PROD = "APPLE_PUSHKIT_PROD";
  String PUSH_SERVER_SOURCE_APPLE_PUSHKIT_ENT = "APPLE_PUSHKIT_ENT";
  String PUSH_SERVER_SOURCE_GETUI = "ANDROID_GETUI";
  String PUSH_SERVER_SOURCE_XIAOMI = "ANDROID_XIAOMI";
  String PUSH_SERVER_SOURCE_HUAWEI = "ANDROID_HUAWEI";
  String PUSH_SERVER_SOURCE_HUAWEI_ENT = "ANDROID_HUAWEI_ENT";
  String PUSH_SERVER_SOURCE_HUAWEI_V2 = "ANDROID_HUAWEI_V2";
  String PUSH_SERVER_SOURCE_HUAWEI_V2_ENT = "ANDROID_HUAWEI_V2_ENT";
  String PUSH_SERVER_SOURCE_HUAWEI_V2_PLAY = "ANDROID_HUAWEI_V2_PLAY";
  String PUSH_SERVER_SOURCE_OPPO = "ANDROID_OPPO";
  String PUSH_SERVER_SOURCE_VIVO = "ANDROID_VIVO";
  String PUSH_SERVER_SOURCE_FCM = "ANDROID_FCM";
  String PUSH_SERVER_SOURCE_HONOR = "ANDROID_HONOR";

  /**
   * 华为鸿蒙
   */
  String PUSH_SERVER_SOURCE_HARMONY = "HM_HUAWEI";

  String NOTIFY_PUSH_TYPE_1 = "通知栏";
  String NOTIFY_PUSH_TYPE_2 = "透传";

  /**
   * 短信metrics指标名字
   */
  String METRICS_SMS = "fs.sms.send";

  String METRICS_EMAIL = "fs.email.send";

  String METRICS_OCR = "fs.ocr";

  String METRICS_ASR = "fs.asr";

  String METRICS_TRANSLATE = "fs.translate";

  String CHINA = "中国";
  String CHINA_EN = "China";

  /**
   * 地图服务自由选择供应商，空字符占位
   */
  String MAP_PROVIDER_EMPTY = "";

  String MAP_PROVIDER_GOOGLE = "google";
  String MAP_PROVIDER_BAIDU = "baidu";
  String MAP_PROVIDER_TENCENT = "tencent";
  String MAP_PROVIDER_AMAP = "amap";
  String MAP_PROVIDER_HUAWEI = "huawei";

  /**
   * Maxmind GeoLite2，本地数据类型
   */
  String MAP_PROVIDER_MAXMIND = "maxmind";

  String ZH_CN = "zh-CN";

}
