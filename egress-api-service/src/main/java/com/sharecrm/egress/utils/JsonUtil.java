package com.sharecrm.egress.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@UtilityClass
public class JsonUtil {

  private static final Gson defaultGson = new GsonBuilder()
    //注意时间格式不要随意变动，并且最好和jackson配置保持一致
    .setDateFormat("yyyy-MM-dd HH:mm:ss")
    .registerTypeAdapter(ObjectId.class, new ObjectIdTypeAdapter()).create();
  private static final Gson includeNullGson = new GsonBuilder()
    .setDateFormat("yyyy-MM-dd HH:mm:ss")
    .registerTypeAdapter(ObjectId.class, new ObjectIdTypeAdapter()).serializeNulls()
    .create();

  public static Gson getGson() {
    return getGson(false);
  }

  public static Gson getGson(boolean includeNull) {
    if (includeNull) {
      return includeNullGson;
    } else {
      return defaultGson;
    }
  }

  public static String mapToJsonOrNull(Map<String, String> map) {
    if (Objects.nonNull(map) && !map.isEmpty()) {
      //注意这儿是json String
      return getGson().toJson(map);
    }
    return null;
  }

  public static Map<String, String> jsonToMap(String json) {
    if (StringUtils.isEmpty(json)) {
      return new HashMap<>();
    }
    return getGson().fromJson(json, new TypeToken<Map<String, String>>() {
    }.getType());
  }

  public static <T> T fromJson(String json, Type typeOfT) {
    return getGson().fromJson(json, typeOfT);
  }

  public static List<Map<String, String>> jsonToListMap(String json) {
    if (StringUtils.isEmpty(json)) {
      return List.of();
    }
    return fromJson(json, new TypeToken<List<Map<String, String>>>() {
    }.getType());
  }


  public static String toJson(Object src) {
    return defaultGson.toJson(src);
  }

  public static byte[] toBytes(Object src) {
    return toJson(src).getBytes(StandardCharsets.UTF_8);
  }

  private static class ObjectIdTypeAdapter extends TypeAdapter<ObjectId> {

    @Override
    public void write(JsonWriter out, ObjectId value) throws IOException {
      if (Objects.isNull(value)) {
        out.nullValue();
      } else {
        out.value(value.toHexString());
      }
    }

    @Override
    public ObjectId read(JsonReader in) throws IOException {
      if (in.hasNext()) {
        String value = in.nextString();
        return new ObjectId(value);
      }
      return null;
    }
  }
}
