package com.sharecrm.egress.utils;

import com.sharecrm.egress.entity.JwtProperties;
import io.jsonwebtoken.Jwts;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Date;

/**
 * 华为鸿蒙，根据自定义private key，生成 jwt
 * <p>
 * 目前仅给华为鸿蒙用，请参考：<a href="https://developer.huawei.com/consumer/cn/doc/harmonyos-guides-V5/push-jwt-token-V5">...</a>
 */
public class JwtGenerator {

  private final JwtProperties conf;
  private final RSAPrivateKey privateKey;

  public JwtGenerator(JwtProperties conf) {
    this.conf = conf;
    this.privateKey = generatePrivateKey();
  }

  public String createJwt() {
    Date now = new Date();
    return Jwts.builder()
      .issuedAt(now)
      .issuer(conf.getSubAccount())
      .signWith(privateKey, Jwts.SIG.PS256)
      .audience()
      .add(conf.getTokenUri())
      .and()
      .expiration(new Date(now.getTime() + conf.getTimeout().toMillis()))
      .header()
      .keyId(conf.getKeyId())
      .type("JWT")
      .and()
      .compact();
  }

  private RSAPrivateKey generatePrivateKey() {
    try {
      String base64Key = conf.getPrivateKey();
      PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(base64Key.getBytes(StandardCharsets.UTF_8)));
      KeyFactory keyFactory = KeyFactory.getInstance("RSA");
      return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
    } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
      throw new RuntimeException(e);
    }
  }
}