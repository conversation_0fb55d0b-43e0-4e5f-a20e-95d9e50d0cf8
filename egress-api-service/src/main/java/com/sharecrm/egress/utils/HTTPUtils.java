package com.sharecrm.egress.utils;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.config.SocketConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import java.util.Objects;

/**
 * 移动梦网专用，不要用于其他场景
 */
@Slf4j
@UtilityClass
public class HTTPUtils {
  public static final int MAX_TOTAL = 512;
  public static final int DEFAULT_MAX_PER_ROUTE = 100;
  private static final int SOCKET_TIMEOUT = 5000;
  private static final int CONNECTION_TIMEOUT = 5000;
  private static CloseableHttpClient httpClient;

  static {
    ConfigFactory.getConfig("fs-sms-httputils", HTTPUtils::loadConfig);
  }

  private static void loadConfig(IConfig config) {
    log.info("init HttpClient");
    int socketTimeout = config.getInt("socketTimeout", SOCKET_TIMEOUT);
    int connectionTimeout = config.getInt("connectionTimeout", CONNECTION_TIMEOUT);
    int maxTotal = config.getInt("maxTotal", MAX_TOTAL);
    int defaultMaxPerRoute = config.getInt("defaultMaxPerRoute", DEFAULT_MAX_PER_ROUTE);
    String proxyHost = config.get("proxyHost");
    int p = config.getInt("proxyPort", -1);
    Integer proxyPort = p == -1 ? null : p;
    log.info("loading HTTPUtils Config");
    log.info("socketTimeout:{}", socketTimeout);
    log.info("connectionTimeout:{}", connectionTimeout);
    log.info("maxTotal:{}", maxTotal);
    log.info("defaultMaxPerRoute:{}", defaultMaxPerRoute);
    log.info("proxyHost:{}", proxyHost);
    log.info("proxyPort:{}", proxyPort);
    httpClient = createHttpClient(socketTimeout, connectionTimeout, maxTotal, defaultMaxPerRoute, proxyHost, proxyPort);
    log.info("loading HTTPUtils Config end");
  }

  private static CloseableHttpClient createHttpClient(int socketTimeout, int connectionTimeout, int maxTotal, int defaultMaxPerRoute, String proxyHost, Integer proxyPort) {
    PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
    connectionManager.setMaxTotal(maxTotal);
    connectionManager.setDefaultMaxPerRoute(defaultMaxPerRoute);

    SocketConfig.Builder sb = SocketConfig.custom();
    sb.setSoKeepAlive(true);
    sb.setTcpNoDelay(true);
    connectionManager.setDefaultSocketConfig(sb.build());

    HttpClientBuilder hb = HttpClientBuilder.create();
    hb.setConnectionManager(connectionManager);

    RequestConfig.Builder rb = RequestConfig.custom();
    rb.setSocketTimeout(socketTimeout);
    rb.setConnectTimeout(connectionTimeout);

    //k8s应用访问外网需要走代理
    if (Objects.nonNull(proxyHost) && Objects.nonNull(proxyPort)) {
      hb.setDefaultRequestConfig(rb.build());
      hb.setProxy(new HttpHost(proxyHost, proxyPort));
    }

    return hb.build();
  }

  public static byte[] get(String requestURL) throws Exception {
    HttpGet get = new HttpGet(requestURL);
    try (CloseableHttpResponse response = httpClient.execute(get)) {
      return IOUtils.toByteArray(response.getEntity().getContent());
    }
  }

}
