package com.sharecrm.egress.utils;

import lombok.experimental.UtilityClass;

import java.util.concurrent.TimeUnit;

@UtilityClass
public class StupidUtils {

  public static void sleep(long millis) {
    sleep(TimeUnit.MILLISECONDS, millis);
  }

  public static void sleep(TimeUnit unit, long timeout) {
    try {
      unit.sleep(timeout);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
  }

}
