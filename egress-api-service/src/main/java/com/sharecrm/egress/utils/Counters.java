package com.sharecrm.egress.utils;

import lombok.experimental.UtilityClass;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@UtilityClass
public class Counters {

  private static final Map<String, AtomicInteger> maps = new ConcurrentHashMap<>();

  public static AtomicInteger get(String counter) {
    return maps.computeIfAbsent(counter, k -> new AtomicInteger(0));
  }

}
