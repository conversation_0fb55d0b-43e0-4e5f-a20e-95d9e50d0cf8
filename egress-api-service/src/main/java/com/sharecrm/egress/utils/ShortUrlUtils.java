package com.sharecrm.egress.utils;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.google.common.collect.Lists;
import com.sharecrm.egress.entity.ShortUrl;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 短链服务工具类
 */
@UtilityClass
public class ShortUrlUtils {

  private static final Pattern androidPat = Pattern.compile("\\bandroid|Nexus\\b", Pattern.CASE_INSENSITIVE);
  private static final Pattern iosPat = Pattern.compile("ip(hone|od|ad)", Pattern.CASE_INSENSITIVE);

  private static final DateTimeFormatter SYNC_TIME_FORMATER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

  /**
   * 指定随机字符范围
   */
  private final char[] codeArray = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a',
    'b', 'c', 'd',
    'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
    'w', 'x', 'y', 'z'};


  public boolean isDeleteExpire(ShortUrl shortUrl, int defaultDeleteDay) {
    if (Objects.isNull(shortUrl.getDeleted()) || shortUrl.getDeleted() <= 1) {
      return false;
    }
    LocalDateTime now = LocalDateTime.now();
    LocalDateTime expireTime = expireTime(shortUrl);
    return now.isAfter(expireTime.plusDays(defaultDeleteDay));
  }

  private static LocalDateTime expireTime(ShortUrl shortUrl) {
    return LocalDateTime.ofInstant(
      Instant.ofEpochMilli(shortUrl.getDeleted()), ZoneId.systemDefault());
  }

  public boolean isExpire(ShortUrl shortUrl, Map<String, Integer> expireRules, int defaultExpireDay) {
    // 0根据规则判断过期时间，-1永不过期，大于1按指定时间过期
    long timeout = Objects.requireNonNullElse(shortUrl.getTimeout(), 0L);
    //永不过期
    if (timeout == -1) {
      return false;
    }
    LocalDateTime now = LocalDateTime.now();
    LocalDateTime createTime = createTime(shortUrl);
    //指定的时间
    if (timeout > 1) {
      return now.isAfter(createTime.plusSeconds(timeout));
    }
    //按url规则确定过期时间
    int expireDay = expireRules
      .entrySet()
      .stream()
      .filter(entry -> shortUrl.getUrl().contains(entry.getKey()))
      .map(Map.Entry::getValue)
      //可能匹配多个规则，取最长有效期
      .max(Integer::compareTo)
      .orElse(defaultExpireDay);

    return now.isAfter(createTime.plusDays(expireDay));
  }

  private LocalDateTime createTime(ShortUrl shortUrl) {
    return LocalDateTime.ofInstant(
      Instant.ofEpochMilli(shortUrl.getCreateTime()), ZoneId.systemDefault());
  }

  public String specialUrl(String code, String userAgent) {
    if (StringUtils.isBlank(userAgent)) {
      return code;
    }
    String url = code.toLowerCase();
    boolean testVersion = url.equals("nc");
    boolean normalVersion = List.of("a", "app").contains(url);
    if (testVersion || normalVersion) {
      if (androidPat.matcher(userAgent).find()) {
        return normalVersion ? "1002" : "4811";
      }
      if (iosPat.matcher(userAgent).find()) {
        return normalVersion ? "1001" : "4812";
      }
    }
    return code;
  }

  public static List<Date> splitDateSet(String start, String end, int minPart) {
    Date startDate = Date.from(LocalDateTime.parse(start, SYNC_TIME_FORMATER).atZone(ZoneId.systemDefault()).toInstant());
    Date endDate = Date.from(LocalDateTime.parse(end, SYNC_TIME_FORMATER).atZone(ZoneId.systemDefault()).toInstant());

    List<Date> dates = Lists.newLinkedList();
    Calendar startCalendar = Calendar.getInstance();
    startCalendar.setTime(startDate);

    Calendar endCalendar = Calendar.getInstance();
    endCalendar.setTime(endDate);
    // 按指定区间的时候差异不能太大，加1
    endCalendar.add(Calendar.MINUTE, 1);
    while ((startCalendar.getTime().getTime() < endCalendar.getTime().getTime())) {
      dates.add(startCalendar.getTime());
      startCalendar.add(Calendar.MINUTE, minPart);
    }
    dates.add(startCalendar.getTime());
    return dates;
  }

  public String randomCode(int length) {
    return NanoIdUtils.randomNanoId(NanoIdUtils.DEFAULT_NUMBER_GENERATOR, codeArray, length);
  }

  public static String readWelcomeHtml() {
    try (InputStream stream = new ClassPathResource("static/welcome-short-url.html").getInputStream()) {
      return new String(stream.readAllBytes(), StandardCharsets.UTF_8);
    } catch (IOException e) {
      return "";
    }
  }

}
