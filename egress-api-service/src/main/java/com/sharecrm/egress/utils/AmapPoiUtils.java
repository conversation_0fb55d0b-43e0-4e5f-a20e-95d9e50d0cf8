package com.sharecrm.egress.utils;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.sharecrm.egress.entity.AmapPoiCode;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@UtilityClass
public class AmapPoiUtils {
  private static final String CMS_NAME = "fs-amap-poi-code";
  private static final Map<String, AmapPoiCode> caches = new ConcurrentHashMap<>();

  static {
    ConfigFactory.getConfig(CMS_NAME, AmapPoiUtils::load, true);
  }

  private static void load(IConfig config) {
    config.getLines()
      .stream()
      //第一行是标题
      .skip(1)
      .forEach(AmapPoiUtils::lineToCodeObject);
    log.info("load amap poi code, size: {}", caches.size());
  }

  private static void lineToCodeObject(String line) {
    String[] split = line.split("\t");
    if (split.length < 8) {
      log.warn("amap poi code line invalid: {}", line);
      return;
    }
    AmapPoiCode code = new AmapPoiCode();
    code.setCode(split[1]);
    code.setZhBig(split[2]);
    code.setZhMid(split[3]);
    code.setZhSub(split[4]);
    code.setEnBig(split[5]);
    code.setEnMid(split[6]);
    code.setEnSub(split[7]);
    caches.put(split[1], code);
  }

  public static Optional<AmapPoiCode> nameToCode(String name) {
    if (StringUtils.isBlank(name)) {
      return Optional.empty();
    }
    return caches.values().stream()
      .filter(e -> name.equals(e.zhFullName()) || name.equals(e.enFullName()))
      .findFirst();
  }

}
