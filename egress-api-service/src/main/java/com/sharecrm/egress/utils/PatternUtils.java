package com.sharecrm.egress.utils;

import lombok.experimental.UtilityClass;
import org.springframework.util.PatternMatchUtils;

import java.util.Collection;
import java.util.Objects;

@UtilityClass
public class PatternUtils {

  /**
   * 支持普通的星号匹配，如果匹配失败了，再用标准的Java正则匹配。因为标准Java正则匹配*的方式太麻烦了。
   *
   * @param pattern 正则规则
   * @param str     需要匹配的内容
   * @return 是否匹配
   */
  public static boolean simpleMatch(String pattern, String str) {
    if (Objects.isNull(pattern) || Objects.isNull(str)) {
      return false;
    }
    try {
      // 支持普通的星号匹配，如果匹配失败了，再用标准的Java正则匹配
      return PatternMatchUtils.simpleMatch(pattern, str) || str.matches(pattern);
    } catch (Exception e) {
      //保护一下正则，有些正则配置有问题可能报错
      return false;
    }
  }

  public static boolean simpleAnyMatch(Collection<String> pattern, String str) {
    if (Objects.isNull(pattern) || Objects.isNull(str)) {
      return false;
    }
    // 支持普通的星号匹配，如果匹配失败了，再用标准的Java正则匹配
    if (PatternMatchUtils.simpleMatch(pattern.toArray(new String[0]), str)) {
      return true;
    }
    for (String p : pattern) {
      if (simpleMatch(p, str)) {
        return true;
      }
    }
    return false;
  }
}
