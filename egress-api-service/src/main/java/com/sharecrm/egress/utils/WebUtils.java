package com.sharecrm.egress.utils;

import com.alibaba.fastjson.JSON;
import com.sharecrm.egress.config.FluxTrace;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.util.context.ContextView;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@UtilityClass
public class WebUtils {

  private static final AtomicLong COUNTER = new AtomicLong();

  private static final List<String> eaHeaders = Arrays.asList("x-fs-ea", "X-fs-Enterprise-Account");
  private static final List<String> eiHeaders = Arrays.asList("x-fs-ei", "X-fs-Enterprise-Id");

  public static Request okHttpJsonPost(String url, Object request, Map<String, String> headers) {
    return okHttpPost(url, request, "application/json", headers);
  }

  public static Request okHttpPost(String url, Object request, String contentType, Map<String, String> headers) {
    RequestBody body = "application/octet-stream".equals(contentType) ? streamBody(request) : jsonBody(request);
    Request.Builder builder = new Request.Builder()
      .url(url)
      .header("Content-Type", contentType)
      .post(body);
    if (MapUtils.isNotEmpty(headers)) {
      headers.forEach(builder::header);
    }
    return builder.build();
  }

  public static RequestBody streamBody(Object request) {
    MediaType contentType = MediaType.parse("application/octet-stream");
    //有些接口是已经转换好了json，不能再转了
    if (request instanceof String json) {
      return RequestBody.create(json.getBytes(StandardCharsets.UTF_8), contentType);
    }
    return RequestBody.create(JSON.toJSONBytes(request), contentType);
  }

  public static RequestBody jsonBody(Object request) {
    MediaType contentType = MediaType.parse("application/json; charset=UTF-8");
    //有些接口是已经转换好了json，不能再转了
    if (request instanceof String json) {
      return RequestBody.create(json, contentType);
    }
    return RequestBody.create(JSON.toJSONString(request), contentType);
  }

  public static String getEaFromHeader(ServerWebExchange exchange) {
    HttpHeaders headers = exchange.getRequest().getHeaders();
    return firstNotBlankValue(eaHeaders, headers).orElse(null);
  }

  public static String getEiFromHeader(ServerWebExchange exchange) {
    HttpHeaders headers = exchange.getRequest().getHeaders();
    return firstNotBlankValue(eiHeaders, headers).orElse(null);
  }

  public static long getEiNumFromHeader(ServerWebExchange exchange) {
    String ei = getEiFromHeader(exchange);
    return NumberUtils.toLong(ei, 0);
  }

  /**
   * 获取trace信息，注意虽然是静态方法，不能在异步线程池中执行，与TraceWebFilter设置对应
   */
  public static Mono<FluxTrace> fluxTrace() {
    return Mono.deferContextual(ctx -> {
      FluxTrace trace = new FluxTrace();
      trace.setEi(firstNotBlankTrace(eiHeaders, ctx));
      trace.setEa(firstNotBlankTrace(eaHeaders, ctx));
      trace.setUid(firstNotBlankTrace(List.of("userId"), ctx));
      trace.setEmployeeId(firstNotBlankTrace(List.of("X-fs-Employee-Id"), ctx));
      trace.setCaller(firstNotBlankTrace(List.of("x-peer-name"), ctx));
      trace.setTraceId(firstNotBlankTrace(List.of("X-fs-Trace-Id"), ctx));
      return Mono.just(trace);
    });
  }


  public static Optional<String> firstNotBlankValue(List<String> keys, MultiValueMap<String, String> map) {
    for (String key : keys) {
      String val = map.getFirst(key);
      if (StringUtils.isNotBlank(val)) {
        return Optional.of(val);
      }
    }
    return Optional.empty();
  }

  @NotNull
  private static String firstNotBlankTrace(List<String> keys, ContextView ctx) {
    for (String key : keys) {
      String val = ctx.getOrDefault(key, "");
      if (StringUtils.isNotBlank(val)) {
        return val;
      }
    }
    return "";
  }

  public static String traceId() {
    return Constants.APP_NAME + '/' + String.format("%08x", COUNTER.incrementAndGet());
  }

}
