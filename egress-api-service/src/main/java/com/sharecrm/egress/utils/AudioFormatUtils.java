package com.sharecrm.egress.utils;

import com.sharecrm.egress.entity.AudioFormat;

public class AudioFormatUtils {
  
  private AudioFormatUtils () {
    // Prevent instantiation
  }

  /**
   * 检测音频格式
   * @param data 音频文件的字节数组
   * @return 音频格式字符串
   */
  public static AudioFormat detectFormat(byte[] data) {
    
    if (data == null || data.length < 16) {
      return AudioFormat.OTHER;
    }

    if (isWAV(data)) return AudioFormat.WAV;
    if (isSpeex(data)) return AudioFormat.SPEEX;
    if (isSILK(data)) return AudioFormat.SILK;
    if (isMP3Strict(data)) return AudioFormat.MP3;
    if (isAMR(data)) return AudioFormat.AMR;
    if (isAACStrict(data)) return AudioFormat.AAC;
    if (isM4AStrict(data)) return AudioFormat.M4A;
    if (isOggOpus(data)) return AudioFormat.OGG_OPUS;
    
    // PCM格式没有特定的标识符，通常是未压缩的音频数据
    // 由于调用该接口会有格式说明,因此我们假定业务方已知悉,所以不能检测的格式默认为PCM
    return AudioFormat.PCM;
  }

  private static boolean isWAV(byte[] data) {
    return data[0] == 0x52 && // R
      data[1] == 0x49 && // I
      data[2] == 0x46 && // F
      data[3] == 0x46 && // F
      data[8] == 0x57 && // W
      data[9] == 0x41 && // A
      data[10] == 0x56 && // V
      data[11] == 0x45;  // E
  }
  
  private static boolean isMP3Strict(byte[] data) {
    // 检查ID3标签
    boolean hasID3 = 
      data[0] == 0x49 && 
      data[1] == 0x44 && 
      data[2] == 0x33 && 
      data[3] >= 0x02 && 
      data[3] <= 0x04; // 次版本号

    // 检查MP3帧同步和头信息
    boolean hasValidFrame = false;
    if ((data[0] & 0xFF) == 0xFF && (data[1] & 0xF0) == 0xF0) {
      // 验证比特率和采样率
      int bitrateIndex = (data[2] & 0xF0) >> 4;
      int samplerIndex = (data[2] & 0x0C) >> 2;
      hasValidFrame = bitrateIndex != 0x0F && samplerIndex != 0x03;
    }

    return hasID3 || hasValidFrame;
  }
  
  // 增强的AAC检测
  private static boolean isAACStrict(byte[] data) {
    // ADTS帧头检查
    boolean hasADTS = (data[0] & 0xFF) == 0xFF &&
      ((data[1] & 0xF6) == 0xF0) &&
      ((data[2] & 0x3C) >> 2) != 15; // 采样率检查

    // 验证帧长度
    if (hasADTS) {
      int frameLength = ((data[3] & 0x03) << 11) |
        ((data[4] & 0xFF) << 3) |
        ((data[5] & 0xE0) >> 5);
      return frameLength > 7 && frameLength <= data.length;
    }

    return false;
  }
  
  private static boolean isOggOpus(byte[] data) {
    return data[0] == 0x4F && // O
      data[1] == 0x67 && // g
      data[2] == 0x67 && // g
      data[3] == 0x53;   // S
  }
  
  private static boolean isM4AStrict(byte[] data) {
    // 检查第一个块的大小（前4个字节）
    int blockSize = ((data[0] & 0xFF) << 24) |
      ((data[1] & 0xFF) << 16) |
      ((data[2] & 0xFF) << 8) |
      (data[3] & 0xFF);

    // 检查 'ftyp' 标识符（偏移量4-7）
    boolean hasFtyp = data[4] == 0x66 && // f
      data[5] == 0x74 && // t
      data[6] == 0x79 && // y
      data[7] == 0x70;   // p

    // 检查文件类型（偏移量8-11）
    boolean hasValidType = false;
    if (data.length >= 12) {
      // 检查是否为常见的M4A相关类型
      hasValidType = (
        // 'M4A '
        (data[8] == 0x4D && data[9] == 0x34 && data[10] == 0x41 && data[11] == 0x20) ||
          // 'mp42'
          (data[8] == 0x6D && data[9] == 0x70 && data[10] == 0x34 && data[11] == 0x32)
      );
    }

    // 块大小应该是合理的值且大于8字节
    return blockSize > 8 && hasFtyp && hasValidType;
  }

  private static boolean isAMR(byte[] data) {
    return data[0] == 0x23 && // #
      data[1] == 0x21 && // !
      data[2] == 0x41 && // A
      data[3] == 0x4D && // M
      data[4] == 0x52;   // R
  }

  private static boolean isSILK(byte[] data) {
    return data[0] == 0x23 && // #
      data[1] == 0x21 && // !
      data[2] == 0x53 && // S
      data[3] == 0x49 && // I
      data[4] == 0x4C && // L
      data[5] == 0x4B;   // K
  }

  private static boolean isSpeex(byte[] data) {
    return data[0] == 0x53 && // S
      data[1] == 0x70 && // p
      data[2] == 0x65 && // e
      data[3] == 0x65 && // e
      data[4] == 0x78 && // x
      data[5] == 0x20;   // 空格
  }
}
