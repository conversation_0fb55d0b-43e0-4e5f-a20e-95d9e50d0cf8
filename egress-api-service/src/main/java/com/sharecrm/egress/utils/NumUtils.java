package com.sharecrm.egress.utils;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;

@Slf4j
@UtilityClass
public class NumUtils {

  /**
   * 将一个数字保持在最小最大值之间（含边界值）
   */
  public static int range(int min, int max, int num) {
    return NumberUtils.max(min, NumberUtils.min(max, num));
  }

  public static Long allowNullOrElse(Long num, String defaultValue) {
    return Objects.nonNull(num) ? num : toLongOrNull(defaultValue);
  }

  public static Long toLongOrNull(String num) {
    if (StringUtils.isEmpty(num)) {
      return null;
    }
    try {
      return Long.parseLong(num);
    } catch (NumberFormatException e) {
      log.warn("parse long failed.", e);
      return null;
    }
  }


}
