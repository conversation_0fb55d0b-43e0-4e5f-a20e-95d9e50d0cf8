package com.sharecrm.egress.utils;

import com.sharecrm.egress.api.AMapErrorCode;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 地图用工具类
 */
@UtilityClass
public class MapUtils {

  /**
   * 高德地图返回是否成功（成功只是状态成功，比如SK校验没有问题，不代表返回的有数据，有时候可能查询不到数据）
   *
   * @param status status
   * @param code   code
   * @return status和code都匹配才成功
   */
  public boolean isAmapSuccess(int status, int code) {
    return status == 1 && code == 10000;
  }

  /**
   * 高德地图返回是否真的有数据
   *
   * @param status status
   * @param code   code
   * @param data   data校验不为null
   * @return status和code都匹配才成功
   */
  public boolean isAmapHasData(int status, int code, Object data) {
    return isAmapHasData(status, code, 1, data);
  }

  /**
   * 高德地图返回是否真的有数据
   *
   * @param status status
   * @param code   code
   * @param count  总条数，校验大于0
   * @param data   data校验不为null
   * @return status和code都匹配才成功
   */
  public boolean isAmapHasData(int status, int code, int count, Object data) {
    return isAmapSuccess(status, code) && count > 0 && data != null;
  }

  /**
   * 高德错误消息转换
   */
  public String amapErrorMessage(String info, int code) {
    return Optional.ofNullable(info).orElseGet(() -> AMapErrorCode.getMessage(code));
  }

  public String join(String delimiter, List<String> list) {
    if (list == null || list.isEmpty()) {
      return null;
    }
    return String.join(delimiter, list);
  }

  /**
   * 转成高德地图多语言，支持的语言比较少，只支持简体中文和英文，非中文都用英文
   *
   * @param language 我们纷享定义的多语
   * @return 高德地图需要的多语言
   */
  public static String amapLanguage(String language) {
    // 非中文都用英文
    return isEmptyOrZh(language) ? Constants.ZH_CN : "en";
  }

  public static boolean isEmptyOrZh(String language) {
    return StringUtils.isEmpty(language) || language.toLowerCase().startsWith("zh");
  }

  public static String baiduLanguage(String language) {
    // 非中文都用英文
    return isEmptyOrZh(language) ? Constants.ZH_CN : "en";
  }

  public static String tencentLanguage(String language) {
    // 非中文都用英文
    return isEmptyOrZh(language) ? Constants.ZH_CN : "en";
  }

  public static String maxmindLanguage(String language) {
    String rs = StringUtils.defaultIfEmpty(language, Constants.ZH_CN);
    //目前库里 只有en，没有en-US
    if (rs.startsWith("en-")) {
      return "en";
    }
    // 这个库的语言玩的真花
    if ("zh".equalsIgnoreCase(language)) {
      return Constants.ZH_CN;
    }
    return rs;
  }

  public static String googleLanguage(String language) {
    //Google不填语言的时候默认用英文，其他都以原始语言为主，Google基本上都能自动翻译
    return StringUtils.defaultIfEmpty(language, "en-US");
  }

  public static boolean isChina(String country) {
    if (Objects.isNull(country)) {
      return false;
    }
    return Constants.CHINA.equals(country) || Constants.CHINA_EN.equalsIgnoreCase(country);
  }

  public static String countryChina(String language) {
    return isEmptyOrZh(language) ? Constants.CHINA : Constants.CHINA_EN;
  }

}
