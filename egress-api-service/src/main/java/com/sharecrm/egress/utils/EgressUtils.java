package com.sharecrm.egress.utils;

import com.sharecrm.egress.config.Weighted;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.RandomUtils;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

/**
 * 无法归类的utils
 */
@UtilityClass
public class EgressUtils {

  /**
   * 使用@RefreshScope注解的Bean，会创建两个Bean，比如 一个叫 scopedTarget.hexagonTencentSmsSender ，一个叫 hexagonTencentSmsSender，去重，并保留RefreshScope的代理类
   */
  public static <T> List<T> filterRefreshScopeBeans(Map<String, T> beansOfType) {
    return beansOfType
      .entrySet()
      .stream()
      .filter(Predicate.not(e -> e.getKey().startsWith("scopedTarget.")))
      .map(Map.Entry::getValue)
      .toList();
  }

  /**
   * 加权 roundRobin 算法。
   * <p>
   * 请注意，这里为了简化对counter的依赖，我们只做原始的加权轮训，没有再引入随机或最大公约数，所以这里的权重不要用大数字，权重数字限定在10以内。
   * <p>
   * A:100 B:50 是100次A，50次B
   * A:2 B:1 是2次A，1次B
   * 以上虽然权重结果是一样的，但是连续100次都使用A会造成较大压力，和使用2次A1次B的轮训效果是不一样的。
   */
  public static <T> T roundRobin(List<T> servers, AtomicInteger counter) {
    int totalWeight = servers.stream()
      .mapToInt(EgressUtils::weight)
      .sum();
    int currentCount = counter.getAndIncrement() % totalWeight;
    int cumulativeWeight = 0;
    for (T server : servers) {
      cumulativeWeight += weight(server);
      if (currentCount < cumulativeWeight) {
        return server;
      }
    }
    //正常不应该走到这里来，托底而已
    return servers.getFirst();
  }

  private static <T> int weight(T v) {
    return v instanceof Weighted val ? Math.max(val.getWeight(), 1) : 1;
  }

  public static Instant nextDayStart() {
    ZonedDateTime zdt = Instant.now().atZone(ZoneId.systemDefault());
    ZonedDateTime nextDayStart = zdt.toLocalDate()
      .plusDays(1) // 加1天
      .atStartOfDay(zdt.getZone())// 当天0点
      //加点随机数避开redis key同时过期
      .plusSeconds(RandomUtils.secure().randomInt(0, 60));
    return nextDayStart.toInstant();
  }

}
