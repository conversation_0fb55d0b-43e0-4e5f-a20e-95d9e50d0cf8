package com.sharecrm.egress.exception;


/**
 * 消息推送异常封装
 */
public class MessageInvalidException extends ThirdPushException {

  public MessageInvalidException() {
    super();
  }

  public MessageInvalidException(String message) {
    super(message);
  }

  public MessageInvalidException(Throwable cause) {
    super(cause);
  }

  public MessageInvalidException(String m, Throwable c) {
    super(m, c);
  }
}
