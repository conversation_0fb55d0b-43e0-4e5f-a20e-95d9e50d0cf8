package com.sharecrm.egress.service;

import com.sharecrm.egress.config.SchedulerProperties;
import com.sharecrm.egress.utils.SchedulerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * 后面看情况确认是否实现配置自动刷新，刷新是否导致已有线程中断
 */
@Slf4j
@Component
public class SchedulerUtilsEvent implements InitializingBean {

  private final SchedulerProperties properties;

  public SchedulerUtilsEvent(SchedulerProperties properties) {
    this.properties = properties;
  }

  @Override
  public void afterPropertiesSet() {
    SchedulerUtils.resetUrlScheduler(properties.getUrl().getMaxThreads(), properties.getUrl().getMaxTaskQueuedPerThread(), properties.getUrl().getTtl());
    SchedulerUtils.resetSmsScheduler(properties.getSms().getMaxThreads(), properties.getSms().getMaxTaskQueuedPerThread(), properties.getSms().getTtl());
    SchedulerUtils.resetAsrScheduler(properties.getAsr().getMaxThreads(), properties.getAsr().getMaxTaskQueuedPerThread(), properties.getAsr().getTtl());
    SchedulerUtils.resetIpScheduler(properties.getIp().getMaxThreads(), properties.getIp().getMaxTaskQueuedPerThread(), properties.getIp().getTtl());
  }

}
