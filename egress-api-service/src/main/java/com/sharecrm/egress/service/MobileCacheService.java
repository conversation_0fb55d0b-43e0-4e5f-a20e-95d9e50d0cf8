package com.sharecrm.egress.service;

import com.fxiaoke.common.StopWatch;
import com.fxiaoke.common.StringToInt;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.io.Closer;
import com.sharecrm.egress.config.ConditionalOnMobileEnabled;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.LocationDTO;
import com.sharecrm.egress.entity.LocationSerializer;
import com.sharecrm.egress.entity.MobileLocation;
import com.sharecrm.egress.io.AliOssConfig;
import com.sharecrm.egress.io.AliOssDownload;
import com.sharecrm.egress.io.ProxyConfig;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.ByteOrderMark;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.MemoryUnit;
import org.ehcache.core.internal.statistics.DefaultStatisticsService;
import org.ehcache.core.spi.service.StatisticsService;
import org.ehcache.impl.config.persistence.CacheManagerPersistenceConfiguration;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.GZIPInputStream;

import static com.sharecrm.egress.utils.NetUtils.uri;

/**
 * 从服务器下载配置文件（定期更新），解析并初始化缓存。 为了减少内存占用，使用 ehcache 的 3 级缓存架构。 为了减少内存占用，通过字典表将字符串转换为整数。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnMobileEnabled
public class MobileCacheService {

  private static final String CACHE_NAME = "mobile";
  private CacheManager manager;
  private Cache<Integer, LocationDTO> cache;
  private StringToInt mapping;

  private final AtomicBoolean started = new AtomicBoolean(false);

  private final MapProperties.MaxmindConfig config;

  public MobileCacheService(MapProperties properties) {
    this.config = properties.getMaxmind();
  }

  @PostConstruct
  void init() throws IOException {
    StopWatch sw = new StopWatch("mobile-cache-init");
    Path path = ConfigHelper.getConfigPath().resolve("mobile-cache");

    //如果已经挂载到默认目录就不再从网络下载，挂载稳定后网络下载功能考虑废弃
    String sharedPath = System.getenv("SHARED_EXT_HOME");
    if (StringUtils.isNotBlank(sharedPath)) {
      log.debug("mobile file cache use shared path: {}", sharedPath);
      path = Path.of(sharedPath).resolve("mobile-cache");
    }

    // 创建缓存目录，避免重复初始化
    if (started.compareAndSet(false, true)) {
      sw.start("init-ehcache");
      createCache(path.toFile());
      sw.stop();
    }

    // 检查 mapping 文件是否存在
    var mappingFile = path.resolve("mapping-words.data").toFile();
    if (mappingFile.exists() && mappingFile.length() > 0) {
      sw.start("reload-mapping-words");
      mapping = StringToInt.fromBytes(Files.readAllBytes(mappingFile.toPath()));
      sw.stop();
    } else {
      mapping = new StringToInt();
    }

    try {
      // 实际上是gz文件，历史原因叫tgz，稳定后只留mobile.txt.gz即可  
      File dstFile = path.resolve("mobile.txt.gz").toFile();
      // 下载老文件，以后废弃
      if (!dstFile.exists()) {
        log.warn("Deprecated mobile.txt.tgz, will remove next version");
        dstFile = path.resolve("mobile.txt.tgz").toFile();
      }
      if (!dstFile.exists()) {
        downloadConfigFile(dstFile, sw);
      }
      // 检查一个号段确认是否已经初始化过
      var isEmptyCache = cache.get(1350000) == null;
      if (isEmptyCache) {
        parseMobileConfigFile(dstFile, sw);
        // 持久化数据字典
        Files.write(mappingFile.toPath(), mapping.toBytes());
      }
    } finally {
      if (sw.getTotalTimeSeconds() > 1) {
        log.info("{}", sw.prettyPrint());
      }
    }
  }

  private void downloadConfigFile(File dstFile, StopWatch sw) throws IOException {
    if (StringUtils.isNotBlank(config.getAliOssAccessKeyId())) {
      var ossConfig = new AliOssConfig(config.getAliOssEndpoint(), config.getAliOssAccessKeyId(), config.getAliOssSecretAccessKey(),
        config.getAliOssBucketName(), "egress-api-service/ip138-mobile.txt.gz");
      sw.start("download-from-ali-oss");
      ProxyConfig proxyConfig = null;
      if (StringUtils.isNotBlank(config.getProxy())) {
        URI uri = uri(config.getProxy());
        proxyConfig = new ProxyConfig(uri.getHost(), uri.getPort(), null, null);
      }
      try {
        AliOssDownload.downloadFromAliOss(dstFile, ossConfig, proxyConfig);
      } finally {
        sw.stop();
      }
    }
  }

  private void createCache(File cacheDir) {
    var persist = new CacheManagerPersistenceConfiguration(cacheDir);
    StatisticsService statService = new DefaultStatisticsService();
    manager = CacheManagerBuilder
      .newCacheManagerBuilder()
      .withSerializer(LocationDTO.class, LocationSerializer.class)
      .with(persist)
      .using(statService)
      .build(true);
    var resource = ResourcePoolsBuilder.heap(20000).offheap(16, MemoryUnit.MB).disk(100, MemoryUnit.MB, true);
    var expiry = ExpiryPolicyBuilder.noExpiration();
    var builder = CacheConfigurationBuilder.newCacheConfigurationBuilder(Integer.class, LocationDTO.class, resource).withExpiry(expiry);
    cache = manager.createCache(CACHE_NAME, builder);
  }

  @PreDestroy
  void close() {
    manager.close();
  }

  @SuppressWarnings("UnstableApiUsage")
  private void parseMobileConfigFile(File dstFile, StopWatch sw) throws IOException {
    sw.start("parse-and-init-cache");
    try (Closer closer = Closer.create()) {
      //可检测多种类型，并剔除bom
      BOMInputStream in = BOMInputStream
        .builder()
        .setByteOrderMarks(new ByteOrderMark[]{ByteOrderMark.UTF_8, ByteOrderMark.UTF_16LE, ByteOrderMark.UTF_16BE})
        .setInclude(false)
        .setInputStream(new GZIPInputStream(new FileInputStream(dstFile)))
        .setCharset(StandardCharsets.UTF_8)
        .setBufferSize(1024 * 8)
        .asSupplier()
        .get();
      closer.register(in);
      BufferedReader reader = new BufferedReader(new InputStreamReader(in));
      closer.register(reader);
      AtomicInteger count = new AtomicInteger();
      reader.lines().forEach(line -> lineToCache(line, count));
      log.info("parsed {} segments", count.get());
    } finally {
      sw.stop();
    }
  }

  private void lineToCache(String line, AtomicInteger count) {
    MobileLocation loc = lineToLocation(line);
    if (loc == null) {
      return;
    }
    count.incrementAndGet();
    putIntoCache(loc);
  }

  @Nullable
  MobileLocation lineToLocation(String line) {
    // 格式：1983947,0394,河南,周口,移动
    String[] parts = line.split(",");
    if (parts.length != 5) {
      log.warn("invalid line: {}", line);
      return null;
    }
    MobileLocation loc = new MobileLocation();
    loc.setMobile(parts[0]);
    loc.setCode(parts[1]);
    loc.setProvince(parts[2]);
    loc.setCity(parts[3]);
    loc.setCarrier(parts[4]);
    loc.setOperator(parts[4]);
    return loc;
  }

  /**
   * 将手机号的前7位和归属地和运营商信息保存到缓存中
   *
   * @param loc 归属地和运营商信息
   */
  void putIntoCache(MobileLocation loc) {
    int key = Integer.parseInt(loc.getMobile());
    var val = loc.toLocationDTO(mapping::encode);
    cache.put(key, val);
  }

  /**
   * 使用手机号的前 7 位来查找归属地和运营商信息，如果找不到则返回 null
   *
   * @param prefix7 手机号前 7 位
   * @return 归属地和运营商信息
   */
  public Mono<MobileLocation> getAsync(String prefix7) {
    int key = Integer.parseInt(prefix7);
    return Mono.justOrEmpty(config.getExt())
      .mapNotNull(ext -> ext.get(key))
      .switchIfEmpty(Mono.defer(() -> Mono
        .fromCallable(() -> cache.get(key))
        .mapNotNull(this::decodeMobileLocation)));
  }

  private MobileLocation decodeMobileLocation(LocationDTO dto) {
    String carrier = mapping.decode(dto.getCarrier());
    return MobileLocation
      .builder()
      .province(mapping.decode(dto.getProvince()))
      .city(mapping.decode(dto.getCity()))
      .carrier(carrier)
      .operator(carrier)
      .code(mapping.decode(dto.getCode()))
      .build();
  }

}
