package com.sharecrm.egress.service;

import com.fxiaoke.words.dto.HitWord;
import com.fxiaoke.words.exception.ParseException;
import com.fxiaoke.words.service.DirtyWordParser;
import com.sharecrm.egress.entity.DirtyWordsRequest;
import com.sharecrm.egress.entity.HasDirtyWordsResponse;
import com.sharecrm.egress.entity.ParseDirtyWordsItem;
import com.sharecrm.egress.entity.ParseDirtyWordsResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Slf4j
@Service
public class DirtyWordService {

  private static final String DIRTY_WORDS_GROUP_SMS = "sms";

  public List<ParseDirtyWordsItem> parseForFunction(String group, String text) {
    try {
      Collection<HitWord> parse = DirtyWordParser.parse(group, text);
      return parse.stream().map(this::build).toList();
    } catch (ParseException e) {
      log.warn("hit dirty words error", e);
      return List.of();
    }
  }

  private ParseDirtyWordsItem build(HitWord bean) {
    String desc = String.format("position index: [%d - %d]", bean.getStart(), bean.getEnd());
    return new ParseDirtyWordsItem(bean.getKeyword(), desc);
  }

  public ResponseEntity<EgressApiResponse<ParseDirtyWordsResponse>> parse(DirtyWordsRequest request) {
    try {
      Collection<HitWord> parse = DirtyWordParser.parse(request.getGroup(), request.getText());
      ParseDirtyWordsResponse response = new ParseDirtyWordsResponse();
      response.setHitWords(parse.stream().toList());
      return ResponseEntity.ok(EgressApiResponse.ok(response));
    } catch (ParseException e) {
      log.warn("hit dirty words error", e);
      return ResponseEntity.badRequest().body(EgressApiResponse.badRequest());
    }
  }

  public ResponseEntity<EgressApiResponse<HasDirtyWordsResponse>> hasDirtyWords(DirtyWordsRequest request) {
    try {
      HasDirtyWordsResponse response = new HasDirtyWordsResponse();
      response.setHasDirtyWords(hasDirtyWords(request.getGroup(), request.getText()));
      return ResponseEntity.ok(EgressApiResponse.ok(response));
    } catch (ParseException e) {
      log.warn("hit dirty words error", e);
      return ResponseEntity.badRequest().body(EgressApiResponse.badRequest());
    }
  }

  public boolean hasDirtyWords(String group, String text) throws ParseException {
    if (StringUtils.isAnyBlank(group, text)) {
      return false;
    }
    return DirtyWordParser.hasDirtyWords(group, text);
  }

  public boolean smsHasDirtyWords(String text) {
    try {
      return hasDirtyWords(DIRTY_WORDS_GROUP_SMS, text);
    } catch (ParseException e) {
      log.warn("sms has dirty words error", e);
      //脏词服务出错，也允许发送短信，尽量保证服务可用
      return false;
    }
  }

}
