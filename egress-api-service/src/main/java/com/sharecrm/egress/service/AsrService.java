package com.sharecrm.egress.service;

import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.SchedulerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class AsrService {
  private final ObjectProvider<AsrProvider> asrProviders;

  public AsrService(ObjectProvider<AsrProvider> asrProviders) {
    this.asrProviders = asrProviders;
  }

  public Mono<EgressApiResponse<TencentSpeechResponse>> speechRecognition(Mono<byte[]> request) {
    return request.zipWith(Mono.justOrEmpty(asrProviders.getIfAvailable()))
      .publishOn(SchedulerUtils.ASR_SCHEDULER)
      .map(objects -> objects.getT2().speechRecognition(objects.getT1()));
  }

  public Mono<EgressApiResponse<AsrCreateRecTaskResponse>> createRecTask(Mono<AsrCreateRecTaskRequest> request) {
    return request.zipWith(Mono.justOrEmpty(asrProviders.getIfAvailable()))
      .publishOn(SchedulerUtils.ASR_SCHEDULER)
      .map(objects -> objects.getT2().createRecTask(objects.getT1()));
  }

  public Mono<EgressApiResponse<AsrQueryRecTaskResponse>> queryRecTask(String taskId) {
    return Mono.just(taskId).zipWith(Mono.justOrEmpty(asrProviders.getIfAvailable()))
      .publishOn(SchedulerUtils.ASR_SCHEDULER)
      .map(objects -> objects.getT2().queryRecTask(objects.getT1()));
  }

}

 
