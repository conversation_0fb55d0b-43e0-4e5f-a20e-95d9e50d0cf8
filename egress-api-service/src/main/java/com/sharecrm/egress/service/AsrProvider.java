package com.sharecrm.egress.service;

import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;

public interface AsrProvider {

  EgressApiResponse<TencentSpeechResponse> speechRecognition(byte[] dataBytes);

  EgressApiResponse<AsrCreateRecTaskResponse> createRecTask(AsrCreateRecTaskRequest request);

  EgressApiResponse<AsrQueryRecTaskResponse> queryRecTask(String taskId);

}
