package com.sharecrm.egress.service;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.AsrProperties;
import com.sharecrm.egress.dao.AsrDao;
import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.AsrRecTaskDto;
import com.sharecrm.egress.entity.AsrRecTaskEntity;
import com.sharecrm.egress.entity.AudioFormat;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.AudioFormatUtils;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.JsonUtil;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskRequest;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskResponse;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusRequest;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusResponse;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionRequest;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionResponse;
import com.tencentcloudapi.asr.v20190614.models.TaskStatus;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.micrometer.core.annotation.Counted;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.Date;
import java.util.Objects;

import static com.sharecrm.egress.entity.AsrRecTaskDto.MQ_TAG_ASR_TASK_TENCENT;
import static com.sharecrm.egress.entity.AsrRecTaskDto.MQ_TOPIC_ASR_TASK;

@Slf4j
@Component
@ConditionalOnProperty(name = "sharecrm.api.asr.tencent.enabled", havingValue = "true")
public class AsrTencentProvider implements AsrProvider {

  private static final Long SOURCE_TYPE_URL = 0L;
  private static final Long SOURCE_TYPE_DATA = 1L;

  private static final String PROVIDER_ID_TENCENT = "tencent";

  private final AsrClient client;
  private final AsrProperties properties;
  private final AsrDao asrDao;
  private final AutoConfMQProducer producer;

  public AsrTencentProvider(AsrClient client, AsrProperties properties, AsrDao asrDao,
                            @Qualifier("asrRocketMQProducer") AutoConfMQProducer producer) {
    this.client = client;
    this.properties = properties;
    this.asrDao = asrDao;
    this.producer = producer;
  }

  @Override
  @Counted(value = Constants.METRICS_ASR, extraTags = {"channel", "tencent"})
  public EgressApiResponse<TencentSpeechResponse> speechRecognition(byte[] dataBytes) {
    try {
      TencentSpeechResponse response = new TencentSpeechResponse();
      SentenceRecognitionRequest request = new SentenceRecognitionRequest();
      // 引擎模型类型。 "16k_zh" 表示 非电话场景中文通用的意思
      request.setEngSerViceType("16k_zh");
      // 语音数据来源  0：语音 URL；1：语音数据（post body） 在这里我们选择语音数据的形式上传。 当sourceType设置为1 的时候， data和dataLen属性必须设置
      request.setSourceType(SOURCE_TYPE_DATA);
      // 上传到腾讯云的语音数据的类型 ，还支持wav、pcm、ogg-opus、speex、silk、mp3、m4a、aac、amr 这种格式的文档。 注意： 如果你的VoiceFormat设置成ogg-opus, 如果上传一个wav文件可以成功，如果设置成wav，上传ogg-opus则不行
      // ，所以上传之前保险起见需要确认上传方上传的字节数组是以何种类型文件转过来的。
      
      // 根据音频数据的格式，设置对应的音频格式
      AudioFormat audioFormat = AudioFormatUtils.detectFormat(dataBytes);
      if (audioFormat==AudioFormat.OTHER){
        log.warn("tencent speech recognition audio format is not supported, format:{}", audioFormat);
        return new EgressApiResponse<>(HttpStatus.BAD_REQUEST.value(), "audio format is not supported.");
      }
      request.setVoiceFormat(audioFormat.getFormat());
      
      // 上传的数据， 腾讯云需要接受的数据的转换流程  xxx.wav -> byte 数组 -> base64加密的String数据
      String data = Base64.getEncoder().encodeToString(dataBytes);
      // 数据长度
      long dataLen = data.length();
      request.setData(data);
      request.setDataLen(dataLen);
      SentenceRecognitionResponse sentenceRecognitionResponse = client.SentenceRecognition(request);
      // 判断 sentenceRecognitionResponse 是否为空
      if (Objects.isNull(sentenceRecognitionResponse) || Objects.isNull(sentenceRecognitionResponse.getResult())) {
        log.warn("tencent speech recognition response is empty");
        return EgressApiResponse.ok(response);
      }
      String result = sentenceRecognitionResponse.getResult();
      response.setContent(result);
      return EgressApiResponse.ok(response);
    } catch (TencentCloudSDKException e) {
      log.warn("tencent speech recognition call failed, code:{}.", e.getErrorCode(), e);
      return new EgressApiResponse<>(HttpStatus.BAD_REQUEST.value(), "speech recognition call failed.");
    }
  }

  @Override
  @Counted(value = Constants.METRICS_ASR, extraTags = {"channel", "tencent"})
  public EgressApiResponse<AsrCreateRecTaskResponse> createRecTask(AsrCreateRecTaskRequest request) {
    try {
      AsrCreateRecTaskResponse result = new AsrCreateRecTaskResponse();
      CreateRecTaskRequest req = new CreateRecTaskRequest();

      if (StringUtils.isNotBlank(request.getUrl())) {
        req.setSourceType(SOURCE_TYPE_URL);
        req.setUrl(request.getUrl());
      } else {
        req.setSourceType(SOURCE_TYPE_DATA);
        byte[] data = Objects.requireNonNull(request.getData(), "request data should not be empty");
        req.setDataLen((long) data.length);
        req.setData(Base64.getEncoder().encodeToString(data));
      }
      req.setEngineModelType(Objects.requireNonNullElse(request.getEngineModelType(), properties.getTencent().getDefaultRecEngine()));
      Integer channelNum = Objects.requireNonNullElse(request.getChannelNum(), AsrCreateRecTaskRequest.CHANNEL_NUM_ONE);
      req.setChannelNum(Long.valueOf(channelNum));
      // 是否开启说话人分离 0：不开启；
      req.setSpeakerDiarization(Long.valueOf(Objects.requireNonNullElse(request.getSpeakerDiarization(), 0)));
      // 说话人分离人数, 需配合开启说话人分离使用，不开启无效，取值范围：0-10，0：自动分离（最多分离出20个人）
      req.setSpeakerNumber(Long.valueOf(Objects.requireNonNullElse(request.getSpeakerNumber(), 0)));
      //识别结果返回样式 0：基础识别结果（仅包含有效人声时间戳，无词粒度的详细识别结果）
      req.setResTextFormat(Long.valueOf(Objects.requireNonNullElse(request.getResTextFormat(), 0)));

      //双声道时这几个只能用默认值，否则腾讯会报400错误
      if (AsrCreateRecTaskRequest.CHANNEL_NUM_TWO.equals(channelNum)) {
        req.setSpeakerDiarization(0L);
        req.setSpeakerNumber(0L);
      }
      
      CreateRecTaskResponse rsp = client.CreateRecTask(req);
      // 判断 rsp 是否为空
      if (Objects.isNull(rsp) || Objects.isNull(rsp.getData())) {
        log.warn("tencent create rec task response is empty");
        return EgressApiResponse.internalServerError(result);
      }
      String taskId = NanoIdUtils.randomNanoId();
      result.setTaskId(taskId);

      AsrRecTaskEntity entity = new AsrRecTaskEntity();
      entity.setTaskId(taskId);
      entity.setProviderTaskId("" + rsp.getData().getTaskId());
      entity.setUrl(request.getUrl());
      entity.setProviderId(PROVIDER_ID_TENCENT);
      entity.setProviderName(PROVIDER_ID_TENCENT);
      entity.setStatus(AsrQueryRecTaskResponse.STATUS_INIT);
      asrDao.save(entity);
      log.info("tencent create rec task success :{}", result);
      return EgressApiResponse.ok(result);
    } catch (Exception e) {
      log.error("tencent create rec task failed.", e);
      return new EgressApiResponse<>(HttpStatus.BAD_REQUEST.value(), "create rec task server failed.");
    }
  }

  @Override
  public EgressApiResponse<AsrQueryRecTaskResponse> queryRecTask(String taskId) {
    AsrRecTaskEntity entity = asrDao.queryByTaskId(taskId);
    if (Objects.isNull(entity)) {
      return EgressApiResponse.notFound();
    }
    AsrQueryRecTaskResponse response = new AsrQueryRecTaskResponse();
    response.setTaskId(entity.getTaskId());
    response.setStatus(entity.getStatus());
    response.setText(entity.getText());
    response.setMessage(entity.getMessage());
    response.setAudioDuration(entity.getAudioDuration());
    return EgressApiResponse.ok(response);
  }

  /**
   * 查询并更新录音转换结果
   */
  public void updateRecTaskStatus() {
    try {
      asrDao.queryWithNoReplay(PROVIDER_ID_TENCENT)
        .forEach(this::queryAndUpdate);
    } catch (Exception e) {
      log.warn("tencent update rec task status failed.", e);
    }
  }

  private void queryAndUpdate(AsrRecTaskEntity entity) {
    try {
      DescribeTaskStatusRequest req = new DescribeTaskStatusRequest();
      req.setTaskId(Long.valueOf(entity.getProviderTaskId()));
      DescribeTaskStatusResponse response = client.DescribeTaskStatus(req);
      TaskStatus data = response.getData();
      if (Objects.isNull(data)) {
        log.warn("tencent update rec task status failed. data is null. task id:{}", entity.getTaskId());
        return;
      }
      log.debug("tencent ast response data :{}", JsonUtil.toJson(data));
      String status = data.getStatusStr();
      log.info("tencent query rec task id:{}, status:{}", entity.getTaskId(), status);
      entity.setStatus(status);
      entity.setMessage(data.getErrorMsg());
      entity.setText(data.getResult());
      Float duration = data.getAudioDuration();
      if (Objects.nonNull(duration)) {
        entity.setAudioDuration((long) (duration * 1000));
      }
      entity.setUpdateTime(new Date());
      asrDao.save(entity);

      //转换后的文本内容不能通过MQ发送，可能超过MQ消息体最大值
      AsrRecTaskDto dto = new AsrRecTaskDto();
      dto.setTaskId(entity.getTaskId());
      dto.setStatus(status);
      dto.setMessage(data.getErrorMsg());
      dto.setAudioDuration(entity.getAudioDuration());
      producer.send(new Message(MQ_TOPIC_ASR_TASK, MQ_TAG_ASR_TASK_TENCENT, JsonUtil.toBytes(dto)));

    } catch (TencentCloudSDKException e) {
      log.warn("tencent update rec task status failed.", e);
    }
  }

}
