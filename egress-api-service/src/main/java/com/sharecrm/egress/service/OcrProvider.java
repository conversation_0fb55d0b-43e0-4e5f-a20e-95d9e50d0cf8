package com.sharecrm.egress.service;

import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import org.springframework.core.Ordered;

/**
 * 文字识别（Optical Character Recognition，OCR ）服务，提供图片、卡证、名片、发票等常用图片转文字服务
 */
public interface OcrProvider extends Ordered {

  String id();

  /**
   * 名片识别
   *
   * @param req 名片识别请求体
   * @return 名片识别结果
   */
  EgressApiResponse<BusinessCardResponse> businessCard(BusinessCardRequest req);

  @Override
  default int getOrder() {
    return 0;
  }
  
}
