package com.sharecrm.egress.service;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import com.sharecrm.egress.api.I18nApi;
import com.sharecrm.egress.entity.CityLocale;
import com.sharecrm.egress.entity.PhoneAreaLang;
import jakarta.annotation.PostConstruct;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.index.qual.NonNegative;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

/**
 * 多语服务，对接paas元数据的省市区多语数据
 *
 * @see <a href="https://wiki.firstshare.cn/pages/viewpage.action?pageId=186054180">参考文档</a>
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Service
public class I18nLocationService {
  private final I18nApi i18nApi;
  private Map<String, String> ispMapping;
  private Map<String, String> langMapping;

  private AsyncLoadingCache<String, PhoneAreaLang> cache;

  private static final PhoneAreaLang EMPTY = new PhoneAreaLang();

  @Value("${sharecrm.api.i18n.empty-timeout:2s}")
  private Duration emptyTimeout = Duration.ofSeconds(2);

  @Value("${sharecrm.api.i18n.cache-timeout:12h}")
  private Duration cacheTimeout = Duration.ofHours(12);

  public I18nLocationService(I18nApi i18nApi) {
    this.i18nApi = i18nApi;
  }

  @PostConstruct
  void init() {
    // 异步加载多语言转换数据
    cache = Caffeine.newBuilder()
      .maximumSize(20000)
      .expireAfter(emptyCacheExpire())
      .buildAsync((key, executor) -> asyncLoadFromRemote(key));

    // 兼容多种语言写法
    langMapping = new HashMap<>();
    Stream.of("zh_CN,zh-CN,zhCN,zh_cn,zh-cn".split(",")).forEach(lang -> langMapping.put(lang, "CN"));
    Stream.of("zh_TW,zh-TW,zhTW,zh_tw,zh-tw".split(",")).forEach(lang -> langMapping.put(lang, "TW"));
    Stream.of("zh_HK,zh-HK,zhHK,zh_hk,zh-hk".split(",")).forEach(lang -> langMapping.put(lang, "TW"));
    Stream.of("en,en-US,en_US".split(",")).forEach(lang -> langMapping.put(lang, "en"));

    // 兼容多种运营商写法
    ispMapping = new HashMap<>();
    ispMapping.put("CN@联通", "联通");
    ispMapping.put("CN@移动", "移动");
    ispMapping.put("CN@电信", "电信");

    ispMapping.put("TW@联通", "聯通");
    ispMapping.put("TW@移动", "移動");
    ispMapping.put("TW@电信", "電信");

    ispMapping.put("en@联通", "China Unicom");
    ispMapping.put("en@移动", "China Mobile");
    ispMapping.put("en@电信", "China Telecom");

    //默认值
    ispMapping.put("D@联通", "China Unicom");
    ispMapping.put("D@移动", "China Mobile");
    ispMapping.put("D@电信", "China Telecom");
  }

  private Expiry<String, PhoneAreaLang> emptyCacheExpire() {
    return new Expiry<>() {
      @Override
      public long expireAfterCreate(String key, PhoneAreaLang value, long currentTime) {
        return EMPTY.equals(value) ? emptyTimeout.toNanos() : cacheTimeout.toNanos();
      }

      @Override
      public long expireAfterUpdate(String key, PhoneAreaLang value, long currentTime, @NonNegative long currentDuration) {
        return currentDuration;
      }

      @Override
      public long expireAfterRead(String key, PhoneAreaLang value, long currentTime, @NonNegative long currentDuration) {
        return currentDuration;
      }
    };
  }

  private @NotNull CompletableFuture<PhoneAreaLang> asyncLoadFromRemote(String key) {
    return i18nApi.getI18nLocation(keyToCityLocale(key))
      .mapNotNull(e -> {
        if (Objects.isNull(e.getResult())) {
          log.warn("failed to query i18n location: {}", e);
          return EMPTY;
        }
        return e.getResult();
      })
      .onErrorResume(e -> {
        log.warn("failed to query i18n location", e);
        return Mono.just(EMPTY);
      })
      .toFuture();
  }

  private CityLocale keyToCityLocale(String key) {
    String[] parts = key.split("@");
    var loc = new CityLocale();
    loc.setProvince(parts[0]);
    loc.setCity(parts[1]);
    loc.setLanguage(parts[2]);
    return loc;
  }

  /**
   * 查询多语数据
   *
   * @param province 省份
   * @param city     城市
   * @param carrier  运营商
   * @param language 语言
   * @return 多语数据
   */
  public Mono<CityLocale> translate(String province, String city, String carrier, String language) {
    String key = String.join("@", province, city, language);
    return Mono.fromCompletionStage(cache.get(key))
      .map(loc -> {
        CityLocale locale = new CityLocale();
        locale.setProvince(StringUtils.defaultIfEmpty(loc.getProvinceLang(), province));
        locale.setCity(StringUtils.defaultIfEmpty(loc.getCityLang(), city));
        locale.setLanguage(StringUtils.defaultIfEmpty(loc.getLang(), language));
        // 运营商国际化描述
        if (StringUtils.isNotEmpty(carrier)) {
          var isp = Optional.ofNullable(langMapping.get(language)).orElse("D") + '@' + carrier;
          String cr = ispMapping.get(isp);
          locale.setCarrier(cr);
          locale.setOperator(cr);
        }
        return locale;
      });
  }
}