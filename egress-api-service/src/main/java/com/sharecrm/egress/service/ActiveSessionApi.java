package com.sharecrm.egress.service;

import com.sharecrm.egress.entity.CookieToAuthArgument;
import com.sharecrm.egress.entity.CookieToAuthResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 纷享账号cookie认证
 */
public interface ActiveSessionApi {

  @PostExchange("/ActiveSession/AuthorizeService/CookieToAuthXC")
  Mono<CookieToAuthResult> cookieToAuthXC(@RequestBody CookieToAuthArgument argument);

}
