package com.sharecrm.egress.service;

import net.javacrumbs.shedlock.core.DefaultLockingTaskExecutor;
import net.javacrumbs.shedlock.core.LockConfiguration;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.core.LockingTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;

@Service
public class LockingExecutor {

  private final LockingTaskExecutor executor;

  public LockingExecutor(LockProvider lockProvider) {
    this.executor = new DefaultLockingTaskExecutor(lockProvider);
  }

  public void execute(Runnable task, String lockName) {
    execute(task, lockName, Duration.ofMinutes(20), Duration.ofMinutes(10));
  }

  public void execute(Runnable task, String lockName, Duration lockAtMostFor, Duration lockAtLeastFor) {
    Instant createdAt = Instant.now();
    executor.executeWithLock(task, new LockConfiguration(createdAt, lockName, lockAtMostFor, lockAtLeastFor));
  }

}
