package com.sharecrm.egress.service;

import com.sharecrm.egress.config.ConditionalOnMobileEnabled;
import com.sharecrm.egress.config.EgressProperties;
import com.sharecrm.egress.entity.MobileLocation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * 根据手机号码的前7位查找归属地和运营商信息
 *
 * @see <a href="https://www.modb.pro/db/81124">手机号码解析规则</a>
 */
@Service
@ConditionalOnMobileEnabled
public class MobileService {
  private final MobileCacheService cacheService;
  private final I18nLocationService i18nService;
  private final EgressProperties properties;


  public MobileService(MobileCacheService cacheService, I18nLocationService i18nService, EgressProperties properties) {
    this.cacheService = cacheService;
    this.i18nService = i18nService;
    this.properties = properties;
  }

  public Mono<MobileLocation> query(String mobile, String language) {
    if (!isValidMobile(mobile)) {
      return Mono.empty();
    }
    String prefix7 = mobile.substring(0, 7);
    String lang = StringUtils.defaultIfEmpty(language, "zh-CN");
    return cacheService.getAsync(prefix7)
      .doOnNext(data -> {
        data.setMobile(mobile);
        // 直辖市的 city=province
        if (StringUtils.isEmpty(data.getCity())) {
          data.setCity(data.getProvince());
        }
        if (StringUtils.isEmpty(data.getOperator())) {
          data.setOperator(data.getCarrier());
        }
        // 巴州=巴音郭楞蒙古自治州
        data.setCity(findFullName(data.getCity()));
      })
      //填充i18n字段
      .zipWhen(loc -> i18nService.translate(loc.getProvince(), loc.getCity(), loc.getCarrier(), lang))
      .map(tuple -> {
        tuple.getT1().setI18n(tuple.getT2());
        return tuple.getT1();
      });
  }

  /**
   * 城市数据处理服务
   * 需求描述：业务侧通过当前服务获取到的城市名是简称（比如：巴州），然后业务拿着这个简称去元数据系统的省市区编码库查询城市编码时，
   * 由于编码库存储的城市名为全称（比如：巴音郭楞蒙古自治州），这就会导致数据查不到。
   * 为了应对这种场景，这里维护一些 "城市简称 转 城市全称" 的数据和接口
   */
  private String findFullName(String city) {
    return properties.getCityFullNames().getOrDefault(Objects.requireNonNullElse(city, ""), city);
  }

  private boolean isValidMobile(String mobile) {
    return NumberUtils.isDigits(mobile) && mobile.length() == 11;
  }
}
