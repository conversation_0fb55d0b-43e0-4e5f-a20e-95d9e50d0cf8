package com.sharecrm.egress.service;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.autoconf.ConfigFactory;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.sharecrm.egress.config.ConditionalOnShortUrlEnabled;
import com.sharecrm.egress.config.ShortUrlProperties;
import com.sharecrm.egress.dao.ShortUrlMapper;
import com.sharecrm.egress.entity.ShortUrl;
import com.sharecrm.egress.entity.ShortUrlBatchCreateRequest;
import com.sharecrm.egress.entity.ShortUrlBatchCreateResponse;
import com.sharecrm.egress.entity.ShortUrlCreateRequest;
import com.sharecrm.egress.entity.ShortUrlCreateResponse;
import com.sharecrm.egress.entity.ShortUrlOriginalResponse;
import com.sharecrm.egress.entity.ShortUrlQueryRequest;
import com.sharecrm.egress.exception.EgressAppException;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.SchedulerUtils;
import com.sharecrm.egress.utils.ShortUrlUtils;
import com.sharecrm.egress.utils.SingleExecutorUtils;
import com.sharecrm.egress.utils.StupidUtils;
import com.sharecrm.egress.utils.WebUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

import static org.apache.commons.lang3.StringUtils.defaultIfEmpty;

@Slf4j
@Service
@ConditionalOnShortUrlEnabled
public class ShortUrlDefaultService implements ShortUrlService {

  /**
   * 公司级一些固定的链接地址
   */
  private Map<String, String> ossValue = new HashMap<>();

  private final ShortUrlProperties properties;
  private final ShortUrlMapper shortUrlMapper;
  private final TransactionTemplate transactionTemplate;
  private final RestTemplate restTemplate = new RestTemplate();

  private final AsyncLoadingCache<String, ShortUrl> cache = Caffeine.newBuilder()
    .expireAfterWrite(3, TimeUnit.DAYS)
    .maximumSize(20000)
    .buildAsync(this::selectByCode);

  public ShortUrlDefaultService(ShortUrlProperties properties, ShortUrlMapper shortUrlMapper, TransactionTemplate transactionTemplate) {
    this.properties = properties;
    this.shortUrlMapper = shortUrlMapper;
    this.transactionTemplate = transactionTemplate;
  }

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("fs-shorturl-static-config",
      config -> ossValue = new HashMap<>(config.getAll()));
    log.info("properties {}", properties);
  }

  public Mono<Page<ShortUrl>> pageQuery(Mono<ShortUrlQueryRequest> request) {
    return request
      .publishOn(SchedulerUtils.SHORT_URL_SCHEDULER)
      .map(req -> {
        Page<ShortUrl> page = new Page<>(req.getCurrent(), req.getSize());
        return shortUrlMapper.selectPage(page,
          Wrappers.<ShortUrl>lambdaQuery().eq(ShortUrl::getDeleted, 0)
            .and(i -> i.ge(ShortUrl::getCreateTime, req.getStartTime())
              .and(e -> e.le(ShortUrl::getCreateTime, req.getEndTime()))));
      });
  }

  public Mono<String> syncFromRemote(String start, String end) {
    if (StringUtils.isBlank(properties.getProxyEgressUrl())) {
      return Mono.just("Error: proxyEgressUrl is empty");
    }
    SingleExecutorUtils.execute(() -> doSync(start, end));
    return Mono.just("start:" + start + ",end:" + end);
  }

  private void doSync(String start, String end) {
    //时间分段，每段30分钟,循环遍历所有
    log.info("short url sync start:{},end:{}", start, end);
    List<Date> dates = ShortUrlUtils.splitDateSet(start, end, 60);
    for (int i = 0; i < dates.size() - 2; i++) {
      Date startDate = dates.get(i);
      Date endDate = dates.get(i + 1);
      int pageNo = 1;
      while (true) {
        log.info("short url sync start:{},end:{}, page:{}", startDate, endDate, pageNo);
        ShortUrlQueryRequest req = new ShortUrlQueryRequest();
        req.setCurrent(pageNo);
        req.setSize(100);
        req.setStartTime(startDate.getTime());
        req.setEndTime(endDate.getTime());
        try {
          String api = properties.getProxyEgressUrl() + "/api/v2/private-sync-urls/page-query";
          ResponseEntity<Page<ShortUrl>> rs = restTemplate.exchange(api, HttpMethod.POST, new HttpEntity<>(req), new ParameterizedTypeReference<>() {
          });
          Page<ShortUrl> page = rs.getBody();
          if (Objects.isNull(page)) {
            log.info("short url sync page is null, request:{}", req);
            break;
          }
          log.info("short url sync page total:{}", page.getTotal());
          List<ShortUrl> records = page.getRecords();
          if (CollectionUtils.isEmpty(records)) {
            log.info("short url sync records is empty, request:{}", req);
            break;
          }
          records.forEach(this::saveLocalIfNotExist);
          log.info("short url sync size:{}", records.size());
        } catch (Exception e) {
          log.warn("short url remote sync failed, request:{}", req, e);
          break;
        }
        pageNo++;
      }
      StupidUtils.sleep(properties.getSyncSetSleep().toMillis());
    }
    log.info("short url sync end, start:{},end:{}, all database total:{}", start, end, shortUrlMapper.selectCount(Wrappers.emptyWrapper()));
  }

  private void saveLocalIfNotExist(ShortUrl entity) {
    try {
      StupidUtils.sleep(properties.getSyncSingleSleep().toMillis());
      String code = entity.getCode();
      Optional<ShortUrl> select = shortUrlMapper.selectByCode(code);
      if (select.isPresent()) {
        log.info("sync short url code is exist:{}", code);
        return;
      }
      shortUrlMapper.insert(entity);
      log.info("sync new short url entity:{}", entity);
    } catch (Exception e) {
      log.warn("failed to sync short url:{}", entity, e);
    }
  }

  public Mono<ShortUrl> queryOriginal(String code) {
    return Mono.fromCompletionStage(cache.get(code));
  }

  public Mono<ShortUrlOriginalResponse> getOriginalUrl(String code, String userAgent) {
    return Mono.just(ShortUrlUtils.specialUrl(code, userAgent))
      .flatMap(this::originalUrl)
      .map(ShortUrlOriginalResponse::new);
  }

  public Mono<EgressApiResponse<ShortUrlBatchCreateResponse>> batchCreateShortUrl(ShortUrlBatchCreateRequest req) {
    return WebUtils.fluxTrace()
      .publishOn(SchedulerUtils.SHORT_URL_SCHEDULER)
      .map(trace -> tryCreateBatchUrl(req.getUrls(), defaultIfEmpty(req.getTenantId(), trace.getEi()), req.getTimeout()))
      .map(EgressApiResponse::ok);
  }

  private ShortUrlBatchCreateResponse tryCreateBatchUrl(List<String> urls, String tenantId, Long timeout) {
    Map<String, ShortUrlCreateResponse> result = new HashMap<>();
    urls.forEach(url -> result.put(url, Optional.ofNullable(tryCreateOneUrl(url, tenantId, timeout))
      .map(this::responseShortUrl)
      .orElse(new ShortUrlCreateResponse("", ""))));
    return new ShortUrlBatchCreateResponse(result);
  }

  public Mono<EgressApiResponse<ShortUrlCreateResponse>> createShortUrl(ShortUrlCreateRequest req) {
    return WebUtils.fluxTrace()
      .publishOn(SchedulerUtils.SHORT_URL_SCHEDULER)
      .mapNotNull(trace -> tryCreateOneUrl(req.getUrl(), defaultIfEmpty(req.getTenantId(), trace.getEi()), req.getTimeout()))
      .map(this::responseShortUrl)
      //以前是返回空，虽然逻辑有问题，但是不敢改，怕调用方出错，等摸清坑谁了再改
      .switchIfEmpty(Mono.defer(() -> Mono.just(new ShortUrlCreateResponse("", ""))))
      .map(EgressApiResponse::ok);
  }

  private ShortUrlCreateResponse responseShortUrl(ShortUrl shortUrl) {
    String code = shortUrl.getCode();
    return new ShortUrlCreateResponse(domainPrefix() + "/" + code, code);
  }

  public Mono<EgressApiResponse<ShortUrl>> createUrl(ShortUrlCreateRequest req) {
    return WebUtils.fluxTrace()
      .publishOn(SchedulerUtils.SHORT_URL_SCHEDULER)
      .mapNotNull(trace -> tryCreateOneUrl(req.getUrl(), defaultIfEmpty(req.getTenantId(), trace.getEi()), req.getTimeout()))
      .map(EgressApiResponse::ok);
  }

  private ShortUrl tryCreateOneUrl(String url, String tenantId, Long timeout) {
    //数据库限制了最大长度是2000
    if (StringUtils.isBlank(url) || url.length() > 2000) {
      return null;
    }
    for (int i = 0; i < NumberUtils.max(properties.getMaxRetry(), 3); i++) {
      try {
        ShortUrl rs = transactionTemplate.execute(s -> doCreate(url, tenantId, timeout));
        if (Objects.nonNull(rs)) {
          return rs;
        }
        StupidUtils.sleep(100);
      } catch (Exception e) {
        log.warn("create short url failed.", e);
      }
      log.info("create short url, try count {}, url:{}", i, url);
    }
    //以前是返回空，虽然逻辑有问题，但是不敢改，怕调用方出错，等摸清坑谁了再改
    log.error("create short url failed, url:{}", url);
    return null;
  }

  private ShortUrl doCreate(String url, String tenantId, Long timeout) {
    if (isDoubleWrite()) {
      ShortUrl shortUrl = doCreateRemote(url, tenantId, timeout);
      saveLocal(shortUrl);
      return shortUrl;
    } else {
      return doCreateLocal(url, tenantId, timeout);
    }
  }

  private void saveLocal(ShortUrl entity) {
    try {
      shortUrlMapper.insert(entity);
      log.info("create short url entity:{}", entity);
    } catch (Exception e) {
      //可能并发，抛出异常继续重试
      log.warn("failed to create short url, will do retry, url:{}", entity, e);
      //随便抛出一个Runtime，允许TransactionTemplate处理事务
      throw new EgressAppException("failed to create short url");
    }
  }

  private ShortUrl doCreateRemote(String url, String tenantId, Long timeout) {
    ShortUrlCreateRequest req = new ShortUrlCreateRequest();
    req.setUrl(url);
    req.setTenantId(tenantId);
    req.setTimeout(timeout);
    try {
      String api = properties.getProxyEgressUrl() + "/api/v2/private-sync-urls";
      ResponseEntity<EgressApiResponse<ShortUrl>> rs = restTemplate.exchange(api, HttpMethod.POST, new HttpEntity<>(req), new ParameterizedTypeReference<>() {
      });
      ShortUrl shortUrl = Optional.ofNullable(rs.getBody())
        .map(EgressApiResponse::getData)
        .orElseThrow(() -> new EgressAppException("failed to create short url remote"));
      log.info("short url remote create success:{}", shortUrl);
      return shortUrl;
    } catch (Exception e) {
      log.warn("short url remote create failed, url:{}", url, e);
      throw new EgressAppException("failed to create short url remote");
    }
  }

  private boolean isDoubleWrite() {
    return properties.isDoubleWriteEnabled() && StringUtils.isNotBlank(properties.getProxyEgressUrl());
  }

  private ShortUrl doCreateLocal(String url, String tenantId, Long timeout) {
    try {
      String code = ShortUrlUtils.randomCode(properties.getCodeLength());
      Optional<ShortUrl> select = shortUrlMapper.selectByCode(code);
      if (select.isPresent()) {
        log.info("create short url code is exist:{}", select.get());
        return null;
      }
      ShortUrl entity = new ShortUrl();
      entity.setCode(code);
      entity.setDeleted(0L);
      // PG 不支持\u0000，需要替换掉     
      entity.setUrl(url.replace("\u0000", ""));
      entity.setCreateTime(System.currentTimeMillis());
      entity.setTenantId(tenantId);
      entity.setTimeout(Objects.requireNonNullElse(timeout, 0L));
      shortUrlMapper.insert(entity);
      log.debug("create a short url :{}", entity);
      return entity;
    } catch (Exception e) {
      //可能并发，抛出异常继续重试
      log.warn("failed to create short url, will do retry, url:{}", url, e);
      //随便抛出一个Runtime，允许TransactionTemplate处理事务
      throw new EgressAppException("failed to create short url");
    }
  }

  private String domainPrefix() {
    return properties.getDomain();
  }

  private Mono<String> originalUrl(String shortUrl) {
    //目前只有4位和6位，有一些非法请求提前过滤掉
    int length = shortUrl.length();
    if (length != 4 && length != properties.getCodeLength()) {
      log.info("url length validate failed:{}", shortUrl);
      return Mono.empty();
    }
    // 从配置中心读取，固定的链接地址
    if (length == 4) {
      return Mono.justOrEmpty(ossValue.get(shortUrl));
    }
    return Mono.fromCompletionStage(cache.get(shortUrl))
      // 处理 timeout
      .filter(Predicate.not(this::isTimeout))
      .map(ShortUrl::getUrl)
      .filter(StringUtils::isNotBlank);
  }

  private boolean isTimeout(ShortUrl shortUrl) {
    boolean expire = ShortUrlUtils.isExpire(shortUrl, properties.getExpireRule(), properties.getDefaultExpireDay());
    if (expire) {
      log.warn("short url timeout:{}", shortUrl);
    }
    long timeout = Objects.requireNonNullElse(shortUrl.getTimeout(), 0L);
    // properties里只控制纷享自定义规则的，不能控制那些已经主动指定了过期时间的
    return (properties.isTimeoutEnabled() || timeout > 0) && expire;
  }

  public ShortUrl selectByCode(String code) {
    return shortUrlMapper.selectByCode(code)
      .orElseGet(() -> {
          if (isDoubleRead()) {
            return tryReadAndSaveAgain(code);
          }
          log.warn("short url not found, code:{}", code);
          return null;
        }
      );
  }

  private ShortUrl tryReadAndSaveAgain(String code) {
    try {
      log.warn("try short url remote query, code:{}", code);
      String url = properties.getProxyEgressUrl() + "/api/v2/private-sync-urls/" + code;
      ResponseEntity<EgressApiResponse<ShortUrl>> rs = restTemplate.exchange(url, HttpMethod.GET, ResponseEntity.EMPTY, new ParameterizedTypeReference<>() {
      });
      ShortUrl shortUrl = Optional.ofNullable(rs.getBody())
        .map(EgressApiResponse::getData)
        .orElse(null);
      log.info("short url remote query result:{}", shortUrl);
      //再重新保存到本地
      trySaveLocal(shortUrl);
      return shortUrl;
    } catch (Exception e) {
      log.warn("short url remote query failed, code:{}", code, e);
    }
    return null;
  }

  private void trySaveLocal(ShortUrl shortUrl) {
    try {
      if (Objects.nonNull(shortUrl)) {
        saveLocal(shortUrl);
      }
    } catch (Exception e) {
      log.warn("failed to save local short url:{}", shortUrl, e);
    }
  }

  private boolean isDoubleRead() {
    return properties.isDoubleReadEnabled() && StringUtils.isNotBlank(properties.getProxyEgressUrl());
  }

}
