package com.sharecrm.egress.service;

import com.huaweicloud.sdk.ocr.v1.OcrClient;
import com.huaweicloud.sdk.ocr.v1.model.BusinessCardRequestBody;
import com.huaweicloud.sdk.ocr.v1.model.BusinessCardResult;
import com.huaweicloud.sdk.ocr.v1.model.RecognizeBusinessCardRequest;
import com.huaweicloud.sdk.ocr.v1.model.RecognizeBusinessCardResponse;
import com.sharecrm.egress.config.ConditionalOnHuaweiOcrEnabled;
import com.sharecrm.egress.config.OcrProperties;
import com.sharecrm.egress.entity.BusinessCardKey;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.Constants;
import io.micrometer.core.annotation.Counted;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sharecrm.egress.entity.BusinessCardKey.firstNotBlankAlias;

/**
 * 华为云文字识别（Optical Character Recognition，OCR ）服务，提供图片、卡证、名片、发票等常用图片转文字服务
 * <p>
 * 注意此服务默认接口请求频率限制：10次/秒
 * <p>
 * 文档参考：<a href="https://support.huaweicloud.com/ocr/index.html">...</a>
 */
@Slf4j
@Component
@ConditionalOnHuaweiOcrEnabled
public class OcrHuaweiProvider implements OcrProvider {

  private final Supplier<OcrClient> client;

  private final OcrProperties properties;

  public OcrHuaweiProvider(Supplier<OcrClient> client, OcrProperties properties) {
    this.client = client;
    this.properties = properties;
  }

  private OcrProperties.HuaweiConfig config() {
    return properties.getHuawei();
  }

  /**
   * {@inheritDoc}
   */
  @Override
  @Counted(value = Constants.METRICS_OCR, extraTags = {"channel", "huawei"})
  public EgressApiResponse<BusinessCardResponse> businessCard(BusinessCardRequest req) {
    try {
      BusinessCardResponse rs = new BusinessCardResponse();
      RecognizeBusinessCardRequest request = businessCardRequest(req);
      RecognizeBusinessCardResponse response = Objects.requireNonNull(client.get()).recognizeBusinessCard(request);
      BusinessCardResult cardInfo = response.getResult();
      if (HttpStatus.OK.value() != response.getHttpStatusCode() || Objects.isNull(cardInfo)) {
        log.warn("huawei business card response is failed: {}", response.getHttpStatusCode());
        return EgressApiResponse.ok(null);
      }

      log.info("huawei business card response: {}", cardInfo);

      Map<String, String> extended = new HashMap<>();
      Stream.ofNullable(cardInfo.getExtraInfoList())
        .flatMap(List::stream)
        .forEach(extraInfo -> extended.put(extraInfo.getItem(), extraInfo.getValue()));

      rs.setName(adapterName(cardInfo.getName()));
      rs.setEmail(joinTrim(cardInfo.getEmail()));
      rs.setCompany(adapterCompany(cardInfo.getCompany()));
      rs.setDepartment(joinTrim(cardInfo.getDepartment()));
      rs.setTitle(joinTrim(cardInfo.getTitle()));
      rs.setAddress(joinTrim(cardInfo.getAddress()));
      adapterPhones(rs, cardInfo);
      rs.setFax(joinTrim(cardInfo.getFax()));
      rs.setQq(firstNotBlankAlias(extended, BusinessCardKey.QQ));
      rs.setWechat(firstNotBlankAlias(extended, BusinessCardKey.WECHAT));
      //有些名片，邮箱和网址在一起，可能会都识别成网址，我们主动拆分出来
      adapterWebsites(rs, cardInfo);
      rs.setExtended(extended);
      return EgressApiResponse.ok(rs);
    } catch (Exception e) {
      log.warn("huawei business card call failed.", e);
      return new EgressApiResponse<>(HttpStatus.BAD_REQUEST.value(), "business card provider call failed");
    }
  }

  private String adapterName(List<String> name) {
    List<String> names = trimFilterValues(name);
    if (CollectionUtils.isEmpty(names)) {
      return "";
    }
    String first = names.getFirst().trim();
    // 国人的习惯是只保留中文
    if (first.length() > 1 && SmsUtils.containChinese(first)) {
      return first;
    }
    return String.join(StringUtils.SPACE, names);
  }

  private String adapterCompany(List<String> company) {
    //一般名片上先印一个大大的公司简称，再印一个全称，过滤掉简称
    List<String> filtered = trimFilterValues(company);
    List<String> result = new ArrayList<>();
    for (String item : filtered) {
      //非自己并且被其他字段已包含
      boolean isDuplicate = filtered.stream().anyMatch(s -> StringUtils.containsIgnoreCase(s, item) && !s.equals(item));
      if (isDuplicate) {
        continue;
      }
      result.add(item);
    }
    return String.join(StringUtils.SPACE, result);
  }

  private void adapterWebsites(BusinessCardResponse rs, BusinessCardResult cardInfo) {
    Map<Boolean, List<String>> partitioned = trimFilterValues(cardInfo.getWebsite())
      .stream()
      .collect(Collectors.partitioningBy(s -> s.contains("@")));
    //不包含@的
    rs.setUrl(joinTrim(partitioned.get(false).stream()
      //如果不包含.过滤掉，有些网址识别不全
      .filter(Predicate.not(s -> StringUtils.containsNone(s, "\\.")))
      .toList()));
    // 包含@的
    List<String> emails = partitioned.get(true);
    if (CollectionUtils.isNotEmpty(emails)) {
      rs.setEmail(StringUtils.defaultIfEmpty(rs.getEmail(), joinTrim(emails)));
    }
  }

  private void adapterPhones(BusinessCardResponse rs, BusinessCardResult cardInfo) {
    List<String> phones = trimFilterValues(cardInfo.getPhone())
      .stream()
      // 有时候括号识别为了第一个手机号
      .filter(s -> !StringUtils.equalsAny(s, "(", ")", "（", "）"))
      //去除空白符
      .map(s -> s.replaceAll("\\s+", ""))
      .map(s -> s.replaceAll("·", ""))
      .toList();
    if (CollectionUtils.isEmpty(phones)) {
      return;
    }
    //第一个11位作为手机号
    String mobile = phones.stream().filter(SmsUtils::tryGuessMobile)
      //如果是国内11位手机号，优先取国内手机号，因为有些国内座机号也可能是合法的海外手机号
      .min((o1, o2) -> {
        int v1 = SmsUtils.isChinaMobile(o1) ? -1 : 0;
        int v2 = SmsUtils.isChinaMobile(o2) ? -1 : 1;
        return v1 - v2;
      })
      .orElse("");
    rs.setMobile(mobile);
    rs.setTelephone(phones.stream()
      .filter(Predicate.not(e -> e.equals(mobile)))
      .collect(Collectors.joining(",")));
  }

  private List<String> trimFilterValues(List<String> iterable) {
    return Stream.ofNullable(iterable)
      .flatMap(List::stream)
      .filter(Objects::nonNull)
      .map(String::trim)
      // 名片喜欢用 / 分割不同字段，有时候把 / 也识别成内容
      .map(s -> StringUtils.removeStart(StringUtils.removeEnd(s, "/"), "/"))
      // 去除 . 和 :
      .map(s -> StringUtils.removeStart(s, "."))
      .map(s -> StringUtils.removeStart(s, ":"))
      .map(s -> StringUtils.removeStart(s, "："))
      .map(String::trim)
      .filter(Predicate.not(String::isEmpty))
      //有些名片多语言多种写法，会有多个，去重
      .distinct()
      .toList();
  }

  private String joinTrim(List<String> iterable) {
    return String.join(StringUtils.SPACE, trimFilterValues(iterable));
  }

  @NotNull
  private RecognizeBusinessCardRequest businessCardRequest(BusinessCardRequest req) {
    RecognizeBusinessCardRequest request = new RecognizeBusinessCardRequest();
    BusinessCardRequestBody body = new BusinessCardRequestBody();
    body.setDetectDirection(true);
    String imageBase64 = req.getImageBase64();
    //优先用imageBase64
    if (StringUtils.isNotBlank(imageBase64)) {
      //华为不能带有前缀 data:image/jpeg;base64,
      if (imageBase64.contains(";base64,")) {
        imageBase64 = imageBase64.substring(imageBase64.indexOf(",") + 1);
      }
      body.setImage(imageBase64);
    } else {
      body.setImage(Base64.encodeBase64String(req.getImageBytes()));
    }
    request.withBody(body);
    return request;
  }

  @Override
  public String id() {
    return config().getId();
  }

  @Override
  public int getOrder() {
    return config().getOrder();
  }
}
