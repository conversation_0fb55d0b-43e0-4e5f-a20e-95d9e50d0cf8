package com.sharecrm.egress.service;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.EgressUtils;
import com.sharecrm.egress.utils.SchedulerUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.util.Comparator;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文字识别（Optical Character Recognition，OCR ）服务，提供名片识别服务
 */
@Slf4j
@Service
public class OcrService {

  private final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("egress-ocr");

  private final ApplicationContext applicationContext;

  public OcrService(ApplicationContext applicationContext) {
    this.applicationContext = applicationContext;
  }

  /**
   * 名片识别
   *
   * @param req 名片识别请求体
   * @return 名片识别结果
   */
  public Mono<EgressApiResponse<BusinessCardResponse>> businessCard(Mono<BusinessCardRequest> req) {
    return WebUtils.fluxTrace()
      .zipWith(req)
      .map(tpl -> {
        BusinessCardRequest request = tpl.getT2();
        request.setUserId(StringUtils.defaultIfBlank(request.getUserId(), tpl.getT1().getUid()));
        return request;
      })
      .publishOn(SchedulerUtils.ASR_SCHEDULER)
      .zipWhen(r -> Mono.justOrEmpty(firstProvider(r.getUserId())))
      .map(this::doRequest)
      .map(e -> EgressApiResponse.ok(Objects.requireNonNullElse(e.getData(), new BusinessCardResponse())));
  }

  private EgressApiResponse<BusinessCardResponse> doRequest(Tuple2<BusinessCardRequest, OcrProvider> tuple2) {
    EgressApiResponse<BusinessCardResponse> first = tuple2.getT2().businessCard(tuple2.getT1());
    BusinessCardResponse firstData = first.getData();
    if (!shouldRetry(first)) {
      log.info("business card response is ok:{}", firstData);
      return first;
    }
    Optional<OcrProvider> fallbackProvider = fallbackProvider(tuple2.getT2().id(), tuple2.getT1().getUserId());
    if (fallbackProvider.isEmpty()) {
      return first;
    }
    EgressApiResponse<BusinessCardResponse> fallback = fallbackProvider.get().businessCard(tuple2.getT1());
    BusinessCardResponse fallbackData = fallback.getData();
    if (Objects.isNull(fallbackData)) {
      return first;
    }
    if (Objects.isNull(firstData)) {
      return fallback;
    }
    return EgressApiResponse.ok(mergeData(firstData, fallbackData));
  }

  private BusinessCardResponse mergeData(BusinessCardResponse first, BusinessCardResponse fallback) {
    // 合并,以第一个优先级为最高
    first.setName(StringUtils.defaultIfBlank(first.getName(), fallback.getName()));
    first.setEmail(StringUtils.defaultIfBlank(first.getEmail(), fallback.getEmail()));
    first.setCompany(StringUtils.defaultIfBlank(first.getCompany(), fallback.getCompany()));
    first.setTitle(StringUtils.defaultIfBlank(first.getTitle(), fallback.getTitle()));
    first.setDepartment(StringUtils.defaultIfBlank(first.getDepartment(), fallback.getDepartment()));
    first.setAddress(StringUtils.defaultIfBlank(first.getAddress(), fallback.getAddress()));
    first.setMobile(StringUtils.defaultIfBlank(first.getMobile(), fallback.getMobile()));
    first.setTelephone(StringUtils.defaultIfBlank(first.getTelephone(), fallback.getTelephone()));
    first.setFax(StringUtils.defaultIfBlank(first.getFax(), fallback.getFax()));
    first.setQq(StringUtils.defaultIfBlank(first.getQq(), fallback.getQq()));
    first.setWechat(StringUtils.defaultIfBlank(first.getWechat(), fallback.getWechat()));
    first.setUrl(StringUtils.defaultIfBlank(first.getUrl(), fallback.getUrl()));
    first.setExtended(Stream.concat(
        first.getExtended().entrySet().stream(),
        fallback.getExtended().entrySet().stream())
      .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (o1, o2) -> o1)));
    return first;
  }

  private boolean shouldRetry(EgressApiResponse<BusinessCardResponse> rs) {
    BusinessCardResponse data = rs.getData();
    if (Objects.isNull(data)) {
      return true;
    }
    return StringUtils.isBlank(data.getUrl()) ||
      StringUtils.isBlank(data.getMobile()) ||
      StringUtils.isBlank(data.getCompany()) ||
      StringUtils.isBlank(data.getAddress()) ||
      StringUtils.isBlank(data.getName()) ||
      StringUtils.isBlank(data.getEmail());
  }

  private Optional<OcrProvider> firstProvider(String userId) {
    return orderedAdapterBeans(null, userId)
      .findFirst();
  }

  private Optional<OcrProvider> fallbackProvider(String exclude, String userId) {
    return orderedAdapterBeans(exclude, userId)
      .findFirst();
  }

  private Stream<OcrProvider> orderedAdapterBeans(String exclude, String userId) {
    return EgressUtils.filterRefreshScopeBeans(applicationContext.getBeansOfType(OcrProvider.class))
      .stream()
      .filter(e -> Objects.isNull(exclude) || !exclude.equals(e.id()))
      //为了其他专属云保险起见允许使用默认的tencent，以后去掉灰度
      .filter(e -> "tencent".equals(e.id()) || gray.isAllow("ocr-" + e.id(), StringUtils.defaultIfEmpty(userId, "EMPTY")))
      .sorted(Comparator.comparingInt(Ordered::getOrder));
  }

}
