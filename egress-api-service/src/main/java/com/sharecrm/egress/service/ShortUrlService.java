package com.sharecrm.egress.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sharecrm.egress.entity.ShortUrl;
import com.sharecrm.egress.entity.ShortUrlBatchCreateRequest;
import com.sharecrm.egress.entity.ShortUrlBatchCreateResponse;
import com.sharecrm.egress.entity.ShortUrlCreateRequest;
import com.sharecrm.egress.entity.ShortUrlCreateResponse;
import com.sharecrm.egress.entity.ShortUrlOriginalResponse;
import com.sharecrm.egress.entity.ShortUrlQueryRequest;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import reactor.core.publisher.Mono;

public interface ShortUrlService {

  Mono<ShortUrlOriginalResponse> getOriginalUrl(String code, String userAgent);

  Mono<EgressApiResponse<ShortUrlBatchCreateResponse>> batchCreateShortUrl(ShortUrlBatchCreateRequest req);

  Mono<EgressApiResponse<ShortUrlCreateResponse>> createShortUrl(ShortUrlCreateRequest req);

  Mono<EgressApiResponse<ShortUrl>> createUrl(ShortUrlCreateRequest req);

  Mono<ShortUrl> queryOriginal(String code);

  Mono<Page<ShortUrl>> pageQuery(Mono<ShortUrlQueryRequest> request);

  Mono<String> syncFromRemote(String start, String end);
}
