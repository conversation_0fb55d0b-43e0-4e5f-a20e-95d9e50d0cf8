package com.sharecrm.egress.service;

import com.fxiaoke.common.StopWatch;
import com.github.autoconf.helper.ConfigHelper;
import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import com.maxmind.geoip2.record.Country;
import com.sharecrm.egress.config.ConditionalMaxmindMapEnabled;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.geo.GeoProvider;
import com.sharecrm.egress.io.AliOssConfig;
import com.sharecrm.egress.io.AliOssDownload;
import com.sharecrm.egress.io.ProxyConfig;
import com.sharecrm.egress.utils.MapUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Stream;
import java.util.zip.GZIPInputStream;

import static com.sharecrm.egress.utils.NetUtils.uri;

/**
 * 使用maxmind的免费ip地址库查询位置信息
 *
 * <AUTHOR>
 * @see <a href="https://www.maxmind.com/en/accounts/399083/geoip/downloads">下载地址</a>
 */
@Slf4j
@Service
@ConditionalMaxmindMapEnabled
public class GeoLite2Service {

  private DatabaseReader reader;

  private final MapProperties.MaxmindConfig config;

  public GeoLite2Service(MapProperties properties) {
    this.config = properties.getMaxmind();
  }

  @PostConstruct
  void init() throws IOException {
    StopWatch sw = new StopWatch("ip-cache-init");
    Path ipCache = ConfigHelper.getConfigPath().resolve("ip-cache");
    //如果已经挂载到默认目录就不再从网络下载，挂载稳定后网络下载功能考虑废弃
    String sharedPath = System.getenv("SHARED_EXT_HOME");
    if (StringUtils.isNotBlank(sharedPath)) {
      log.debug("geo file cache use shared path: {}", sharedPath);
      ipCache = Path.of(sharedPath).resolve("ip-cache");
    }
    if (!ipCache.toFile().exists()) {
      Files.createDirectories(ipCache);
    }

    // 下载远程配置文件
    Path ipFile = ipCache.resolve("GeoLite2-City.tar.gz");
    File dstFile = ipFile.toFile();
    if (!dstFile.exists()) {
      downloadConfigFile(dstFile, sw);
    }

    // 解压缩文件
    File dbFile = ipCache.resolve("GeoLite2-City.mmdb").toFile();
    if (!dbFile.exists()) {
      sw.start("extract-db-file");
      extractDbFile(dstFile, dbFile);
      sw.stop();
    }

    // 构建reader
    sw.start("create-db-reader");
    reader = new DatabaseReader.Builder(dbFile).withCache(new CHMCache(2048)).build();
    sw.stop();

    if (sw.getTotalTimeSeconds() > 1) {
      log.info("{}", sw.prettyPrint());
    }
  }

  @PreDestroy
  void close() throws IOException {
    reader.close();
  }

  public GeoProvider provider() {
    return config;
  }

  private void extractDbFile(File dstFile, File dbFile) {
    try (var tar = new TarArchiveInputStream(new GZIPInputStream(new FileInputStream(dstFile)))) {
      TarArchiveEntry entry;
      while ((entry = tar.getNextTarEntry()) != null) {
        if (entry.getName().endsWith(".mmdb")) {
          Files.copy(tar, dbFile.toPath());
          break;
        }
      }
    } catch (IOException e) {
      log.error("extract db file error", e);
    }
  }

  private void downloadConfigFile(File dstFile, StopWatch sw) throws IOException {
    // 下载文件
    sw.start("download-from-ali-oss");
    var ossConfig = new AliOssConfig(config.getAliOssEndpoint(), config.getAliOssAccessKeyId(), config.getAliOssSecretAccessKey(),
      config.getAliOssBucketName(), "egress-api-service/GeoLite2-City.tar.gz");
    String proxy = config.getProxy();
    ProxyConfig proxyConfig = null;
    if (StringUtils.isNotBlank(proxy)) {
      URI uri = uri(proxy);
      proxyConfig = new ProxyConfig(uri.getHost(), uri.getPort(), null, null);
    }
    try {
      AliOssDownload.downloadFromAliOss(dstFile, ossConfig, proxyConfig);
    } finally {
      sw.stop();
    }
  }

  IpLocation lookup(String ipAddress, String language) {
    try {
      var ip = InetAddress.getByName(ipAddress);
      var hit = reader.tryCity(ip);
      if (hit.isEmpty()) {
        return null;
      }
      var response = hit.get();
      String lang = MapUtils.maxmindLanguage(language);

      IpLocation.IpLocationBuilder builder = IpLocation.builder().ip(ipAddress)
        .city(getCity(response, lang));

      Stream.of((Supplier<Country>) response::getCountry, response::getRegisteredCountry, response::getRepresentedCountry)
        .map(Supplier::get)
        .filter(Objects::nonNull)
        .findFirst()
        .ifPresent(e -> {
          builder.country(getNameWithFallback(e.getNames(), lang));
          builder.isoCode(e.getIsoCode());
        });
      return builder.build();
    } catch (IOException | GeoIp2Exception e) {
      log.error("Failed to lookup ip address: {}", ipAddress, e);
    }
    return null;
  }

  private String getCity(CityResponse response, String locale) {
    City city = response.getCity();
    return city != null ? getNameWithFallback(city.getNames(), locale) : null;
  }

  private String getNameWithFallback(Map<String, String> names, String locale) {
    //有时候已经找到数据了，但是因为没有语言值查不到数据，使用英文语言托底
    if (Objects.isNull(names)) {
      return null;
    }
    String name = names.get(locale);
    return Objects.nonNull(name) ? name : names.get("en");
  }

  public Mono<IpLocation> query(String ipAddress, String language) {
    return Mono.fromCallable(() -> lookup(ipAddress, language));
  }
}
