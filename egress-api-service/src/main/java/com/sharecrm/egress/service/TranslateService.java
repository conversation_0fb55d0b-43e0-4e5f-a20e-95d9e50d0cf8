package com.sharecrm.egress.service;

import com.sharecrm.egress.api.GoogleTranslateApi;
import com.sharecrm.egress.config.TranslateProperties;
import com.sharecrm.egress.entity.GoogleTranslateData;
import com.sharecrm.egress.entity.GoogleTranslateResult;
import com.sharecrm.egress.entity.GoogleTranslation;
import com.sharecrm.egress.entity.TranslateRequest;
import com.sharecrm.egress.entity.TranslateResponse;
import com.sharecrm.egress.utils.Constants;
import io.micrometer.core.annotation.Counted;
import io.micrometer.core.instrument.Metrics;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * google 翻译。
 * <p>
 * 支持的语言列表： <a href="https://cloud.google.com/translate/docs/languages?hl=zh-cn">...</a>
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "sharecrm.api.translate.google.enabled", havingValue = "true", matchIfMissing = true)
public class TranslateService {

  private final GoogleTranslateApi api;
  private final TranslateProperties properties;

  public TranslateService(GoogleTranslateApi api, TranslateProperties properties) {
    this.api = api;
    this.properties = properties;
  }

  @Counted(value = Constants.METRICS_TRANSLATE, extraTags = {"channel", "google"})
  public Mono<TranslateResponse> translate(Mono<TranslateRequest> request) {
    return request.flatMap(doTranslate())
      .map(this::translateResponse);
  }

  @NotNull
  private Function<TranslateRequest, Mono<? extends GoogleTranslateResult>> doTranslate() {
    return req -> api.translate(req.getTexts(), req.getSource(), req.getTarget(), req.getFormat(), model())
      .onErrorResume(e -> {
        // 失败返回原值
        failure(e);
        return Mono.just(mockTranslateResult(req));
      });
  }

  @NotNull
  private GoogleTranslateResult mockTranslateResult(TranslateRequest req) {
    // 翻译失败的情况下，原值返回，模拟返回值
    GoogleTranslateResult rs = new GoogleTranslateResult();
    GoogleTranslateData data = new GoogleTranslateData();
    List<GoogleTranslation> collect = req.getTexts().stream()
      .map(s -> {
        GoogleTranslation trs = new GoogleTranslation();
        trs.setTranslatedText(s);
        trs.setModel(model());
        trs.setDetectedSourceLanguage(req.getSource());
        return trs;
      }).toList();
    data.setTranslations(collect);
    rs.setData(data);
    return rs;
  }

  private void failure(Throwable e) {
    log.warn("google translate failed.", e);
    Metrics.counter(Constants.METRICS_TRANSLATE, "channel", "google", "result", "failure").increment();
  }

  @NotNull
  private TranslateResponse translateResponse(GoogleTranslateResult rs) {
    TranslateResponse response = new TranslateResponse();
    response.setTranslations(Optional.ofNullable(rs.getData())
      .map(GoogleTranslateData::getTranslations)
      .orElseGet(Collections::emptyList));
    return response;
  }

  private String model() {
    return properties.getGoogle().getModel();
  }
}
