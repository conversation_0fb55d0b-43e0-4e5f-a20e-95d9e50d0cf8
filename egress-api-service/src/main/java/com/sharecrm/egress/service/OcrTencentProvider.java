package com.sharecrm.egress.service;

import com.sharecrm.egress.config.ConditionalOnTencentOcrEnabled;
import com.sharecrm.egress.config.OcrProperties;
import com.sharecrm.egress.entity.BusinessCardKey;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.Constants;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardInfo;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRResponse;
import io.micrometer.core.annotation.Counted;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

import static com.sharecrm.egress.entity.BusinessCardKey.firstNotBlankAlias;

/**
 * 腾讯云文字识别（Optical Character Recognition，OCR ）服务，提供图片、卡证、名片、发票等常用图片转文字服务
 * <p>
 * 注意此服务默认接口请求频率限制：10次/秒
 * <p>
 * 文档参考：<a href="https://cloud.tencent.com/document/product/866">...</a>
 */
@Slf4j
@Component
@ConditionalOnTencentOcrEnabled
public class OcrTencentProvider implements OcrProvider {

  private final Supplier<OcrClient> client;
  private final OcrProperties properties;

  public OcrTencentProvider(Supplier<OcrClient> client, OcrProperties properties) {
    this.client = client;
    this.properties = properties;
  }

  private OcrProperties.TencentConfig config() {
    return properties.getTencent();
  }

  /**
   * {@inheritDoc}
   */
  @Override
  @Counted(value = Constants.METRICS_OCR, extraTags = {"channel", "tencent"})
  public EgressApiResponse<BusinessCardResponse> businessCard(BusinessCardRequest req) {
    try {
      BusinessCardResponse rs = new BusinessCardResponse();
      BusinessCardOCRRequest request = businessCardRequest(req);
      BusinessCardOCRResponse response = Objects.requireNonNull(client.get()).BusinessCardOCR(request);
      if (Objects.isNull(response) || Objects.isNull(response.getBusinessCardInfos())) {
        log.warn("tencent business card response is empty");
        return EgressApiResponse.ok(null);
      }

      Map<String, String> results = new HashMap<>();
      BusinessCardInfo[] infos = response.getBusinessCardInfos();
      for (BusinessCardInfo info : infos) {
        results.put(info.getName(), info.getValue());
      }
      //放一些规范化的k-v值，按优先级，第一个非空的数据作为标准值
      rs.setName(firstNotBlankAlias(results, BusinessCardKey.NAME));
      rs.setEmail(firstNotBlankAlias(results, BusinessCardKey.EMAIL));
      rs.setCompany(firstNotBlankAlias(results, BusinessCardKey.COMPANY));
      rs.setDepartment(firstNotBlankAlias(results, BusinessCardKey.DEPARTMENT));
      rs.setTitle(firstNotBlankAlias(results, BusinessCardKey.TITLE));
      rs.setAddress(firstNotBlankAlias(results, BusinessCardKey.ADDRESS));
      rs.setMobile(firstNotBlankAlias(results, BusinessCardKey.MOBILE));
      rs.setTelephone(firstNotBlankAlias(results, BusinessCardKey.TEL));
      rs.setQq(firstNotBlankAlias(results, BusinessCardKey.QQ));
      rs.setWechat(firstNotBlankAlias(results, BusinessCardKey.WECHAT));
      rs.setFax(firstNotBlankAlias(results, BusinessCardKey.FAX));
      rs.setUrl(firstNotBlankAlias(results, BusinessCardKey.URL));
      rs.setExtended(results);
      log.info("tencent ocr result:{}", rs);
      return EgressApiResponse.ok(rs);
    } catch (TencentCloudSDKException e) {
      log.warn("tencent business card call failed.", e);
      return new EgressApiResponse<>(HttpStatus.BAD_REQUEST.value(), "business card provider call failed");
    }
  }

  @NotNull
  private static BusinessCardOCRRequest businessCardRequest(BusinessCardRequest req) {
    BusinessCardOCRRequest request = new BusinessCardOCRRequest();
    String imageBase64 = req.getImageBase64();
    //优先用imageBase64
    if (StringUtils.isNotBlank(imageBase64)) {
      request.setImageBase64(imageBase64);
    } else {
      request.setImageBase64(Base64.encodeBase64String(req.getImageBytes()));
    }
    return request;
  }


  @Override
  public String id() {
    return config().getId();
  }

  @Override
  public int getOrder() {
    return config().getOrder();
  }
}
