package com.sharecrm.egress.dao;


import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import com.sharecrm.egress.config.ConditionalOnNotifyEnabled;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 消息推送数据访问组件，存入ClickHouse
 */
@Slf4j
@Service
@ConditionalOnNotifyEnabled
public class NotifyDao {

  private final NotifyProperties properties;

  public NotifyDao(NotifyProperties properties) {
    this.properties = properties;
  }

  public void save(PushRecord pushRecord) {
    if (properties.isRecordEnabled()) {
      BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(pushRecord));
    }
  }

}
