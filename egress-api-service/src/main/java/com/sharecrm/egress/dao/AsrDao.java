package com.sharecrm.egress.dao;


import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.AsrRecTaskEntity;
import com.sharecrm.egress.service.LockingExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
@ConditionalOnProperty(name = "sharecrm.api.asr.tencent.enabled", havingValue = "true")
public class AsrDao implements InitializingBean {

  private final Datastore datastore;
  private final LockingExecutor lockingExecutor;

  public AsrDao(@Qualifier("asrMongoDatastore") Datastore datastore, LockingExecutor lockingExecutor) {
    this.datastore = datastore;
    this.lockingExecutor = lockingExecutor;
  }

  @Override
  public void afterPropertiesSet() {
    lockingExecutor.execute(() -> {
      try {
        datastore.ensureIndexes(AsrRecTaskEntity.class, true);
      } catch (Exception e) {
        log.error("asr task mongo create index failed", e);
      }
    }, "asr-mongo-index");
  }

  public <T> void save(T entity) {
    datastore.save(entity);
  }

  public AsrRecTaskEntity queryByTaskId(String taskId) {
    Query<AsrRecTaskEntity> query = datastore.createQuery(AsrRecTaskEntity.class);
    query.filter(AsrRecTaskEntity.TASK_ID, taskId);
    return query.get();
  }

  /**
   * 查询还没有收到结果的记录，然后去服务提供商去更新结果
   */
  public List<AsrRecTaskEntity> queryWithNoReplay(String providerId) {
    Query<AsrRecTaskEntity> query = datastore.createQuery(AsrRecTaskEntity.class);
    if (StringUtils.isNotEmpty(providerId)) {
      query.filter(AsrRecTaskEntity.PROVIDER_ID, providerId);
    }
    // 按status判断
    query.field(AsrRecTaskEntity.FILED_STATUS).in(List.of(AsrQueryRecTaskResponse.STATUS_INIT,
      AsrQueryRecTaskResponse.STATUS_WAITING, AsrQueryRecTaskResponse.STATUS_DOING));
    // 只查询最近25小时的，腾讯只支持查询最近24小时的
    Date hourAgo = new Date(System.currentTimeMillis() - 60 * 60 * 1000 * 25);
    query.criteria(AsrRecTaskEntity.CREATE_TIME).greaterThanOrEq(hourAgo);
    return query.asList();
  }

}
