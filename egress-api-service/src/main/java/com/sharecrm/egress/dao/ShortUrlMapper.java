package com.sharecrm.egress.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sharecrm.egress.config.ConditionalOnShortUrlEnabled;
import com.sharecrm.egress.entity.ShortUrl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Optional;

/**
 * 短链服务PG数据库操作
 */
@Mapper
@ConditionalOnShortUrlEnabled
public interface ShortUrlMapper extends BaseMapper<ShortUrl> {

  @Select("select * from t_shorturl where code=#{code} and deleted=0")
  Optional<ShortUrl> selectByCode(@Param("code") String code);

}
