package com.sharecrm.egress.dao;


import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsMongoFields;
import com.sharecrm.egress.entity.SmsPageRequest;
import com.sharecrm.egress.entity.SmsPageResult;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.service.LockingExecutor;
import com.sharecrm.egress.utils.NumUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.mapping.Mapper;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.sharecrm.egress.sms.SmsUtils.isAllowEi;

/**
 * 短信服务数据访问组件，存入MongoDB
 */
@Slf4j
@Service
@ConditionalOnSmsEnabled
public class SmsDao implements InitializingBean {

  private final Datastore datastore;
  private final LockingExecutor lockingExecutor;

  public SmsDao(@Qualifier("smsMongoDatastore") Datastore datastore, LockingExecutor lockingExecutor) {
    this.datastore = datastore;
    this.lockingExecutor = lockingExecutor;
  }

  @Override
  public void afterPropertiesSet() {
    lockingExecutor.execute(() -> {
      try {
        datastore.ensureIndexes(SmsTemplateEntity.class, true);
        datastore.ensureIndexes(SmsMongoEntity.class, true);
      } catch (Exception e) {
        log.error("sms mongo create index failed", e);
      }
    }, "sms-mongo-index");
  }

  public <T> void save(T entity) {
    datastore.save(entity);
  }

  public SmsTemplateEntity queryTemplate(String id) {
    SmsTemplateQuery arg = new SmsTemplateQuery();
    arg.setTemplateId(id);
    return queryTemplates(arg).stream().findFirst().orElse(null);
  }

  public List<SmsTemplateEntity> queryTemplates(SmsTemplateQuery arg) {
    Query<SmsTemplateEntity> query = datastore.createQuery(SmsTemplateEntity.class);
    if (StringUtils.isNotEmpty(arg.getTemplateId())) {
      query.filter(SmsTemplateEntity.TEMPLATE_ID, arg.getTemplateId());
    }
    if (StringUtils.isNotEmpty(arg.getProviderId())) {
      query.filter(SmsTemplateEntity.PROVIDER_ID, arg.getProviderId());
    }
    if (StringUtils.isNotEmpty(arg.getTemplateType())) {
      query.filter(SmsTemplateEntity.TEMPLATE_TYPE, arg.getTemplateType());
    }

    if (Objects.nonNull(arg.getInternational())) {
      query.filter(SmsTemplateEntity.INTL, arg.getInternational());
    }

    if (StringUtils.isNotEmpty(arg.getStatus())) {
      String[] split = arg.getStatus().split(",");
      if (split.length == 1) {
        query.filter(SmsTemplateEntity.FILED_STATUS, arg.getStatus());
      } else {
        query.field(SmsTemplateEntity.FILED_STATUS).in(Arrays.stream(split).toList());
      }
    }
    List<SmsTemplateEntity> list = query.asList();
    Integer enterpriseId = arg.getEnterpriseId();
    if (Objects.isNull(enterpriseId)) {
      return list;
    }
    return list.stream()
      .filter(e -> enterpriseId.equals(e.getEnterpriseId()) || isAllowEi(e.getAllowAccounts(), enterpriseId))
      .toList();
  }

  public SmsPageResult findHistories(SmsPageRequest arg) {
    Query<SmsMongoEntity> query = smsPageQuery(arg);
    int pageSize = NumUtils.range(1, 1000, arg.getPageSize());
    int pageNum = NumUtils.range(1, Integer.MAX_VALUE, arg.getPageNum());
    query.retrievedFields(false, "id").offset((pageNum - 1) * pageSize).limit(pageSize);
    query.order("-" + Mapper.ID_KEY);
    List<SmsMongoEntity> entities = query.asList();
    SmsPageResult queryResult = new SmsPageResult();
    queryResult.setCount(entities.size());
    queryResult.setResults(entities);
    queryResult.setPageNum(pageNum);
    queryResult.setTotalCount(query.countAll());
    return queryResult;
  }

  /**
   * 查询还没有收到运营商回复状态的记录，用于更新短信记录
   */
  public List<SmsMongoEntity> querySmsWithNoReplay(String providerId) {
    Query<SmsMongoEntity> query = datastore.createQuery(SmsMongoEntity.class);
    if (StringUtils.isNotEmpty(providerId)) {
      query.filter(SmsMongoFields.PROVIDER_ID, providerId);
    }
    query.field(SmsMongoFields.REPLY_TIME).doesNotExist();
    query.field(SmsMongoFields.SERIAL_ID).exists();
    // 只查询最近3小时的，防止运营商接口有问题导致一直无限查询
    Date hourAgo = new Date(System.currentTimeMillis() - 60 * 60 * 1000 * 3);
    query.criteria(SmsMongoFields.SEND_TIME).greaterThanOrEq(hourAgo);
    return query.asList();
  }

  public SmsMongoEntity updateMonternetStatus(String providerId, String serialId, int replayStatus, String replayMessage) {
    try {
      Query<SmsMongoEntity> query = datastore.createQuery(SmsMongoEntity.class);
      query.field(SmsMongoFields.PROVIDER_ID).equal(providerId);
      query.field(SmsMongoFields.SERIAL_ID).equal(serialId);
      UpdateOperations<SmsMongoEntity> operations = datastore.createUpdateOperations(SmsMongoEntity.class);
      operations.set(SmsMongoFields.REPLY_STATUS, 0 == replayStatus);
      operations.set(SmsMongoFields.REPLY_TIME, new Date());
      operations.set(SmsMongoFields.REPLY_CODE, "" + replayStatus);
      operations.set(SmsMongoFields.REPLY_MESSAGE, Objects.requireNonNullElse(replayMessage, ""));
      return datastore.findAndModify(query, operations, false, false);
    } catch (RuntimeException e) {
      log.warn("update sms status failed", e);
      return null;
    }
  }

  public SmsMongoEntity updateSingleSmsStatus(String providerId, String serialId, String phone,
                                              boolean replayStatus, Date replayDate,
                                              String replayCode, String replayMessage) {
    try {
      Query<SmsMongoEntity> query = datastore.createQuery(SmsMongoEntity.class);
      query.field(SmsMongoFields.PROVIDER_ID).equal(providerId);
      query.field(SmsMongoFields.SERIAL_ID).equal(serialId);
      if (StringUtils.isNotEmpty(phone)) {
        query.field(SmsMongoFields.PHONE).equal(phone);
      }
      UpdateOperations<SmsMongoEntity> operations = datastore.createUpdateOperations(SmsMongoEntity.class);
      operations.set(SmsMongoFields.REPLY_STATUS, replayStatus);
      if (Objects.nonNull(replayDate)) {
        operations.set(SmsMongoFields.REPLY_TIME, replayDate);
      }
      operations.set(SmsMongoFields.REPLY_CODE, Objects.requireNonNullElse(replayCode, ""));
      operations.set(SmsMongoFields.REPLY_MESSAGE, Objects.requireNonNullElse(replayMessage, ""));
      return datastore.findAndModify(query, operations, false, false);
    } catch (RuntimeException e) {
      log.warn("update sms status failed", e);
      return null;
    }
  }

  private Query<SmsMongoEntity> smsPageQuery(SmsPageRequest arg) {
    Query<SmsMongoEntity> query = datastore.createQuery(SmsMongoEntity.class);
    if (StringUtils.isNotEmpty(arg.getPhone())) {
      query.filter(SmsMongoFields.PHONE, arg.getPhone());
    }
    if (StringUtils.isNotEmpty(arg.getProviderId())) {
      query.filter(SmsMongoFields.PROVIDER_ID, arg.getProviderId());
    }
    if (arg.getStartTime() != null) {
      query.criteria(SmsMongoFields.SEND_TIME).greaterThanOrEq(arg.getStartTime());
    }
    if (arg.getEndTime() != null) {
      query.criteria(SmsMongoFields.SEND_TIME).lessThanOrEq(arg.getEndTime());
    }
    if (StringUtils.isNotEmpty(arg.getMsgId())) {
      query.filter(SmsMongoFields.MSG_ID, arg.getMsgId());
    }
    if (StringUtils.isNotEmpty(arg.getBatchId())) {
      query.filter(SmsMongoFields.MSG_BATCH_ID, arg.getBatchId());
    }
    if (StringUtils.isNotEmpty(arg.getContent())) {
      query.criteria(SmsMongoFields.CONTENT).contains(arg.getContent());
    }
    if (StringUtils.isNotEmpty(arg.getBizName())) {
      query.filter(SmsMongoFields.BIZ_NAME, arg.getBizName());
    }
    if (StringUtils.isNotEmpty(arg.getEi())) {
      query.filter(SmsMongoFields.EI, arg.getEi());
    }
    if (arg.getStatus() != null) {
      query.filter(SmsMongoFields.STATUS, arg.getStatus());
    }
    if (arg.getReplyStatus() != null) {
      query.filter(SmsMongoFields.REPLY_STATUS, arg.getReplyStatus());
    }
    if (StringUtils.isNotEmpty(arg.getSmsType())) {
      query.filter(SmsMongoFields.SMS_TYPE, arg.getSmsType());
    }
    return query;
  }


}
