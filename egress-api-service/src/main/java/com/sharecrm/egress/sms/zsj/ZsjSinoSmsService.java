package com.sharecrm.egress.sms.zsj;

import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

import static com.sharecrm.egress.sms.SmsUtils.zsjReplaceContent;

/**
 * 对接招商局-中国外运消息中心，注意招商云只为招商局私有化部署可用，纷享本身不会用
 */
@Slf4j
@Service
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.sino.enabled", havingValue = "true")
public class ZsjSinoSmsService {

  private final SmsProperties properties;

  private final OkHttpSupport httpClient;

  private final PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

  public ZsjSinoSmsService(SmsProperties properties, @Qualifier("smsHttpSupport") OkHttpSupport httpClient) {
    this.properties = properties;
    this.httpClient = httpClient;
  }

  public SmsSendResult sendSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    SmsSendResult rs = doSmsSend(phone, wrapper, smsSendRequest(phone, request));
    rs.setRequest(request);
    return rs;
  }

  public SmsSendResult sendIntlSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    SmsSendResult rs = doSmsSend(phone, wrapper, intlSmsSendRequest(phone, request));
    rs.setRequest(request);
    return rs;
  }

  public EmailSendResponse sendEmail(EmailSendRequest request) {
    EmailSendResponse result = new EmailSendResponse();
    try {
      SinoMailRequest sinoReq = new SinoMailRequest();
      // 邮件接收人有多个时的拼接字符，注意用逗号，有的接口用的是分号
      sinoReq.setAcceptorEmail(String.join(",", request.getTo()));
      Optional.ofNullable(request.getCc())
        .filter(Predicate.not(List::isEmpty))
        .ifPresent(ccs -> sinoReq.setEmailCc(String.join(",", ccs)));
      sinoReq.setSendEmail(config().getEmailFrom());
      sinoReq.setEmailTitle(request.getSubject());
      sinoReq.setSystemCode(config().getSystemCode());
      sinoReq.setSystemName(config().getSystemName());
      sinoReq.setEmailContent(request.getContent());
      sinoReq.setEmailType(config().getEmailType());

      //根据招商云短信对接文档拼接Header和请求体
      Request req = convertOkHttpRequest(config().getEmailSendUrl(), sinoReq);
      String resp = doHttpCall(req);
      result.setSuccess(true);
      result.setMessage(resp);
      log.debug("send email success:{}", result);
    } catch (Exception e) {
      log.warn("send email failed.", e);
      result.setSuccess(false);
      result.setMessage(e.getMessage());
    }
    return result;
  }

  @NotNull
  private Request convertOkHttpRequest(String url, Object request) {
    //根据招商云对接文档拼接Header和请求体
    return WebUtils.okHttpJsonPost(url, request,
      Map.of("keyId", config().getKeyId(),
        "appCode", config().getSystemCode()));
  }

  private SmsSendResult doSmsSend(String phone, SmsRequestWrapper<SmsSendRequest> wrapper, SinoSmsRequest request) {
    try {
      //根据招商云短信对接文档拼接Header和请求体
      Request req = convertOkHttpRequest(config().getSmsSendUrl(), request);
      String resp = doHttpCall(req);
      log.info("sms resp detail:{}", resp);
      return SmsUtils.success(config(), wrapper, phone, "none");
    } catch (Exception e) {
      log.warn("send sms failed.", e);
      return SmsUtils.failed(config(), wrapper, phone, e.getMessage());
    }
  }

  private SinoSmsRequest intlSmsSendRequest(String phone, SmsSendRequest req) {
    // 国际短信请求体
    SinoSmsRequest sinoReq = new SinoSmsRequest();
    String param = req.getContent();

    Optional<String> captcha = SmsUtils.getStrictCaptcha(param);
    if (captcha.isPresent()) {
      log.info("sino captcha message, will use captcha template, phone {}.", phone);
      //如果是短信验证码的，有单独的模板，通用模板不允许发送验证码
      sinoReq.setTemplateParamList(List.of(captcha.get()));
      sinoReq.setTemplateId(config().getEnCaptchaTemplateId());
      sinoReq.setTemplateName(config().getEnCaptchaTemplateName());
    } else {
      //替换特定字符
      sinoReq.setTemplateParamList(List.of(zsjReplaceContent(param)));
      sinoReq.setTemplateId(config().getEnTemplateId());
      sinoReq.setTemplateName(config().getEnTemplateName());
    }

    sinoReq.setSystemName(config().getSystemName());
    sinoReq.setSystemCode(config().getSystemCode());
    sinoReq.setSmsType(0);
    sinoReq.setAcceptorType(1);
    sinoReq.setAcceptNo(phone);

    try {
      PhoneNumber phoneNumber = phoneUtil.parse(phone, "CH");
      //招商云国际电话区号和号码要分开
      sinoReq.setCountry(String.valueOf(phoneNumber.getCountryCode()));
      sinoReq.setAcceptNo(String.valueOf(phoneNumber.getNationalNumber()));
      //如果是国内86短信，强制转为国内模板，招商云消息中心不支持把国内86当做国际短信
      if (phoneNumber.getCountryCode() == 86) {
        sinoReq.setCountry(SmsUtils.COUNTRY_CODE_CH);
        if (captcha.isPresent()) {
          //验证码需要用单独的模板
          sinoReq.setTemplateId(config().getZhCaptchaTemplateId());
          sinoReq.setTemplateName(config().getZhCaptchaTemplateName());
        } else {
          sinoReq.setTemplateId(config().getZhTemplateId());
          sinoReq.setTemplateName(config().getZhTemplateName());
        }
      }
    } catch (NumberParseException e) {
      log.error("invalid intl phone number:{}", phone, e);
    }
    log.debug("send intl sms:{}", sinoReq);
    return sinoReq;
  }

  private SinoSmsRequest smsSendRequest(String phone, SmsSendRequest req) {
    // 国内短信请求体
    SinoSmsRequest request = new SinoSmsRequest();
    String param = req.getContent();

    Optional<String> captcha = SmsUtils.getStrictCaptcha(param);
    if (captcha.isPresent()) {
      log.info("sino captcha message, will use captcha template, phone {}.", phone);
      //如果是短信验证码的，有单独的模板，通用模板不允许发送验证码
      request.setTemplateParamList(List.of(captcha.get()));
      request.setTemplateId(config().getZhCaptchaTemplateId());
      request.setTemplateName(config().getZhCaptchaTemplateName());
    } else {
      //替换特定字符
      request.setTemplateParamList(List.of(zsjReplaceContent(param)));
      request.setTemplateId(config().getZhTemplateId());
      request.setTemplateName(config().getZhTemplateName());
    }

    request.setSystemName(config().getSystemName());
    request.setSystemCode(config().getSystemCode());
    request.setSmsType(0);
    request.setAcceptorType(0);
    request.setAcceptNo(phone);
    return request;
  }

  private String doHttpCall(Request request) {
    Object rs = httpClient.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) {
        return getSmsResult(response);
      }
    });
    log.debug("sino sms http call result: {}", rs);
    return rs.toString();
  }

  private String getSmsResult(Response response) {
    int code = response.code();
    String msg = response.message();
    try {
      ResponseBody obj = response.body();
      String result = Objects.isNull(obj) ? "" : obj.string();
      log.info("sms response code: {}, message: {}, body:{}", code, msg, result);
      return result;
    } catch (Exception e) {
      log.warn("send sms failed. response code: {}, message: {}", code, msg, e);
    }
    throw new SmsException("sms api response failed.");
  }

  private SmsProperties.SinoConfig config() {
    return properties.getSino();
  }

  @Data
  public static class SinoSmsRequest {

    /**
     * 系统五位码，申请的时候给的
     */
    private String systemCode;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 0是普通短信，1是营销短信
     */
    private int smsType = 0;

    /**
     * 0 国内短信, 1 国际和港澳台
     */
    private int acceptorType = 0;

    /**
     * 模板代码，对应创建时的模板代码
     */
    private Integer templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 接收短信手机号
     */
    private String acceptNo;

    /**
     * 模板中的参数，数量必须与模板中参数配置一一对应
     */
    private List<String> templateParamList;

    /**
     * 手机号国家编码 中国 +86
     */
    private String country = SmsUtils.COUNTRY_CODE_CH;

  }

  @Data
  public static class SinoMailRequest {

    private String systemCode;
    private String systemName;

    /**
     * 邮件类型：1、文本, 2、html、3、附件
     */
    private int emailType = 2;

    /**
     * 邮件主题名
     */
    private String emailTitle;
    /**
     * 发件箱，对应在邮件发送配置中设置的发件箱
     */
    private String sendEmail;

    /**
     * 收件箱，英文逗号分隔
     */
    private String acceptorEmail;

    /**
     * 邮件内容
     */
    private String emailContent;

    /**
     * 密送人
     */
    private String emailBcc;

    /**
     * 抄送人
     */
    private String emailCc;
  }

}
