package com.sharecrm.egress.sms.aliyun;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.service.LockingExecutor;
import com.sharecrm.egress.sms.SmsStatusTask;
import com.sharecrm.egress.sms.SmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ScheduledFuture;

/**
 * 阿里云短信和模板状态查询
 */
@Slf4j
public class AliyunStatusTask implements SmsStatusTask {

  private final SmsProperties.AliYunConfig config;
  private final AliyunSmsSender sender;
  private final TaskScheduler taskScheduler;
  private final LockingExecutor lock;
  private final ScheduledFuture<?> smsScheduledFuture;
  private final ScheduledFuture<?> templateScheduledFuture;

  public AliyunStatusTask(SmsProperties.AliYunConfig config, AliyunSmsSender sender,
                          TaskScheduler taskScheduler, LockingExecutor lock) {
    this.config = config;
    this.sender = sender;
    this.taskScheduler = taskScheduler;
    this.lock = lock;
    this.smsScheduledFuture = startSmsTaskScheduler();
    this.templateScheduledFuture = startTemplateTaskScheduler();
  }

  private ScheduledFuture<?> startTemplateTaskScheduler() {
    log.info("start aliyun template status task, id: {}, delay: {}", config.getId(), config.getTemplateTaskDelay());
    return taskScheduler.scheduleWithFixedDelay(this::updateTemplateStatus, SmsUtils.randomDelayInstant(60, 300), config.getTemplateTaskDelay());
  }

  private ScheduledFuture<?> startSmsTaskScheduler() {
    log.info("start aliyun sms status task, id: {}, delay: {}", config.getId(), config.getSmsTaskDelay());
    return taskScheduler.scheduleWithFixedDelay(this::updateSmsStatus, SmsUtils.randomDelayInstant(30, 120), config.getSmsTaskDelay());
  }

  private void updateSmsStatus() {
    try {
      lock.execute(() -> {
        log.info("run aliyun sms status task: {}", config.getId());
        sender.updateSmsStatus();
      }, "sms-task-" + config.getId(), Duration.ofSeconds(30), Duration.ofSeconds(10));
    } catch (Exception e) {
      log.warn("run aliyun sms status task failed.", e);
    }
  }

  private void updateTemplateStatus() {
    try {
      lock.execute(() -> {
        log.info("run aliyun template status task: {}", config.getId());
        sender.updateTemplateStatus();
      }, "template-task-" + config.getId(), Duration.ofSeconds(60), Duration.ofSeconds(10));
    } catch (Exception e) {
      log.warn("run aliyun template status task failed.", e);
    }
  }

  @Override
  public void destroy() {
    try {
      smsScheduledFuture.cancel(true);
      templateScheduledFuture.cancel(true);
      log.info("try stop aliyun sms task, id: {}", config.getId());
    } catch (Exception e) {
      log.warn("cancel aliyun sms status task failed.", e);
    }
  }
}