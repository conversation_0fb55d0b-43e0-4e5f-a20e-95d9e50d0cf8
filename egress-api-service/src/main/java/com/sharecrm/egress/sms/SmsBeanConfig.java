package com.sharecrm.egress.sms;

import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 短信服务配置
 */
@Configuration
@ConditionalOnSmsEnabled
public class SmsBeanConfig {

  @Value("${sharecrm.api.sms.mongodb.config:fs-sms-mongo}")
  private String smsMongoConfig = "fs-sms-mongo";
  @Value("${sharecrm.api.sms.http.config:fs-sms-http-egress}")
  private String httpConfig = "fs-sms-http-egress";
  @Value("${sharecrm.api.sms.rocket.config:fs-sms-rocketmq}")
  private String rocketConfig = "fs-sms-rocketmq";

  /**
   * 蒙牛云专用http客户端
   */
  @Value("${sharecrm.api.sms.http.config.mengniu:fs-sms-http-mengniu}")
  private String mengniuHttpConfig = "fs-sms-http-mengniu";

  @Bean("smsMongoDatastore")
  public MongoDataStoreFactoryBean smsMongoDatastore() {
    MongoDataStoreFactoryBean bean = new MongoDataStoreFactoryBean();
    bean.setConfigName(smsMongoConfig);
    return bean;
  }

  @Bean("smsRocketMQProducer")
  @ConditionalOnProperty(name = "sharecrm.api.sms.rocket.enabled", havingValue = "true")
  public AutoConfMQProducer mqProducer() {
    return new AutoConfMQProducer(rocketConfig);
  }

  @Bean("smsHttpSupport")
  public HttpSupportFactoryBean smsHttpSupportFactoryBean() {
    HttpSupportFactoryBean bean = new HttpSupportFactoryBean();
    bean.setConfigName(httpConfig);
    return bean;
  }

  @Bean("mengniuHttpSupport")
  @ConditionalOnProperty(name = "sharecrm.api.sms.mengniu.enabled", havingValue = "true")
  public HttpSupportFactoryBean mengniuHttpSupportFactoryBean() {
    HttpSupportFactoryBean bean = new HttpSupportFactoryBean();
    bean.setConfigName(mengniuHttpConfig);
    return bean;
  }

}
