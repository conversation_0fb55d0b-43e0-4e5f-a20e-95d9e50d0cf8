package com.sharecrm.egress.sms.share;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.config.SmsProperties.DahantcConfig;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import com.sharecrm.egress.sms.SmsUtils;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 大汉三通短信通道
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.dahantc.enabled", havingValue = "true", matchIfMissing = true)
public class DahantcSender implements SmsSender, SmsBeanIgnoreDestroy {

  private final DahantcConfig config;

  private final OkHttpSupport httpClient;

  public DahantcSender(SmsProperties properties, @Qualifier("smsHttpSupport") OkHttpSupport httpClient) {
    this.config = properties.getDahantc();
    this.httpClient = httpClient;
    log.info("dahantc sms enabled, config: {}", config);
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    try {
      Request req = requestBody(phone, request);
      Object rs = httpClient.syncExecute(req, new SyncCallback() {
        @Override
        public DahanSmsResult response(Response response) {
          return getSmsResult(response);
        }
      });
      DahanSmsResult resp = (DahanSmsResult) rs;
      SmsSendResult result = adapterResult(phone, wrapper, resp);
      result.setSign(getSign(request));
      return result;
    } catch (Exception e) {
      log.warn("send sms failed, phone :{}", phone, e);
      return SmsUtils.failed(config, wrapper, phone, e.getMessage());
    }
  }

  @NotNull
  private SmsSendResult adapterResult(String phone, SmsRequestWrapper<SmsSendRequest> wrapper, DahanSmsResult resp) {
    if ("0".equals(resp.getResult())) {
      return SmsUtils.success(config, wrapper, phone, "none");
    } else {
      return SmsUtils.failed(config, wrapper, phone, resp.getDesc());
    }
  }

  @NotNull
  Request requestBody(String phone, SmsSendRequest request) {
    DahanSmsBody smsBody = buildBody(phone, request);
    RequestBody body = RequestBody.create(JSON.toJSONString(smsBody), MediaType.parse("application/json; charset=utf-8"));
    return new Request.Builder().url(config.getUrl()).post(body).build();
  }

  DahanSmsBody buildBody(String phone, SmsSendRequest request) {
    String content = request.getContent();
    return DahanSmsBody.builder()
      .account(config.getUsername())
      .password(SmsUtils.getMd5(config.getPassword()).toLowerCase())
      // 需要主动传签名
      .sign(getSign(request))
      .msgid(UUID.randomUUID().toString())
      .phones(phone)
      .content(content)
      .build();
  }

  private String getSign(SmsSendRequest request) {
    //大汉三通的英文签名是中文方括号
    return SmsUtils.chooseSign(request.getLanguage(), request.getContent(), config.getZhSignName(), config.getEnSignName());
  }

  DahanSmsResult getSmsResult(Response response) {
    String body = null;
    try {
      body = Objects.requireNonNull(response.body()).string();
      log.debug("dahantc response body{}", body);
      return JSON.parseObject(body, DahanSmsResult.class);
    } catch (Exception e) {
      log.warn("send sms failed. response code: {}, message: {}, body: {}", response.code(),
        response.message(), body, e);
    }
    throw new SmsException("dahantc response failed.");
  }

  @Override
  public SmsProvider provider() {
    return config;
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_CHINESE);
  }

  @Data
  @Builder
  @Accessors(chain = true)
  static class DahanSmsBody {

    private String account;
    private String password;
    private String sign;
    private String msgid;
    private String phones;
    private String content;
    private String subcode;
    private String sendtime;
  }

  @Data
  static class DahanSmsResult {

    private String msgid;
    private String result;
    private String desc;
    private String failPhones;
  }

}
