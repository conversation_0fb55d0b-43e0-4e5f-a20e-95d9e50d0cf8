package com.sharecrm.egress.sms.byteplus;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 短信状态报告消息体: <a href="https://docs.byteplus.com/zh-CN/docs/byteplus-sms/docs-receiving-delivery-receipts">...</a>
 */
@Data
public class BytePlusStatusReport {

  private String account;

  private String mobile;

  @JsonProperty("message_id")
  @SerializedName("message_id")
  @JSONField(name = "message_id")
  private String messageId;

  /**
   * 短信状态报告枚举值，常见取值请参考表 Status常见参数，处理方法请参考状态报告错误码。
   */
  @JsonProperty("status_code")
  @SerializedName("status_code")
  @JSONField(name = "status_code")
  private String statusCode;

  private String description;

  @JsonProperty("send_time")
  @SerializedName("send_time")
  @JSONField(name = "send_time")
  private Long sendTime;
  
  @JsonProperty("recv_time")
  @SerializedName("recv_time")
  @JSONField(name = "recv_time")
  private Long recvTime;

}
