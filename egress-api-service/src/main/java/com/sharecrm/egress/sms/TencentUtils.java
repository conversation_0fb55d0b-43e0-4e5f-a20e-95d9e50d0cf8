package com.sharecrm.egress.sms;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsArgException;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsResponseData;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.NetUtils;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatus;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_NOTIFICATION;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_PROMOTION;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE;
import static com.sharecrm.egress.sms.SmsUtils.SMS_PATTERN_NO_DOLLAR;
import static com.sharecrm.egress.sms.SmsUtils.SMS_PRE_DOLLAR;
import static com.sharecrm.egress.sms.SmsUtils.SMS_PRE_NO_DOLLAR;
import static com.sharecrm.egress.utils.JsonUtil.jsonToMap;

/**
 * 腾讯云短信工具类
 */
@Slf4j
@UtilityClass
public class TencentUtils {

  /**
   * 短信发送状态成功标记
   */
  public static final String SEND_STATUS_SUCCESS = "SUCCESS";

  /**
   * 腾讯云短信类型，1表示营销短信，2表示通知短信，3表示验证码短信。
   */
  public static final Map<String, Long> types = Map.of(
    TEMPLATE_TYPE_VERIFY_CODE, 3L,
    TEMPLATE_TYPE_NOTIFICATION, 2L,
    TEMPLATE_TYPE_PROMOTION, 1L
  );


  /**
   * 模板状态转换成我们自己的标准定义。
   * 其中0表示审核通过且已生效，1表示审核中，2表示审核通过待生效，-1表示审核未通过或审核失败。
   */
  public static String tencentTemplateStatus(Long providerStatus) {
    if (Objects.isNull(providerStatus) || providerStatus.equals(1L) || providerStatus.equals(2L)) {
      return SmsSdkConstants.TEMPLATE_STATUS_APPROVING;
    }
    if (providerStatus.equals(0L)) {
      return SmsSdkConstants.TEMPLATE_STATUS_APPROVED;
    }
    if (providerStatus.equals(-1L)) {
      return SmsSdkConstants.TEMPLATE_STATUS_REJECTED;
    }
    throw new SmsException("unknown template status: " + providerStatus);
  }

  /**
   * 抽取原生Api请求中的变量，替换成腾讯云Api需要的形式，一定是需要替换，用于变量形式转换 ${code} -> {1}
   *
   * @param content 咱们标准API定义的内容，变量形式是 ${code}
   * @return 需要替换的变量，key是老变量名如 code，value是新变量名如 1
   */
  public static Map<String, String> tencentSimpleContentForReplace(String content) {
    List<String> keys = SmsUtils.extractKeys(content);
    Map<String, String> keyMap = new HashMap<>();
    for (int i = 0; i < keys.size(); i++) {
      // 腾讯的变量从1开始
      keyMap.put(keys.get(i), i + 1 + "");
    }
    return keyMap;
  }

  /**
   * 抽取腾讯短信模板需要替换的变量，转换成标准Api的形式，用于变量形式转换 {1} -> ${var1}
   *
   * @param content 腾讯模板内容，变量形式是 {1}
   * @return 需要替换的变量，key 如 var1，value 如 1
   */
  private static Map<String, String> tencentContentToSimpleReplace(String content) {
    List<String> keys = SmsUtils.extractKeys(SMS_PATTERN_NO_DOLLAR, content);
    Map<String, String> keyMap = new HashMap<>();
    for (String key : keys) {
      keyMap.put("var" + key, key);
    }
    return keyMap;
  }

  public static SmsStaticTemplate appendTemplateReplace(SmsStaticTemplate src) {
    // 已经替换过了，注意不能重复替换
    if (StringUtils.isNotBlank(src.getParamReplace())) {
      return src;
    }
    String content = src.getContent();
    Map<String, String> replace = tencentContentToSimpleReplace(content);
    src.setParamReplace(JsonUtil.mapToJsonOrNull(replace));
    // 替换内容，将 {1} 转为 ${var1} 这种形式
    src.setContent(tencentContentToSimpleContent(content, replace));
    return src;
  }

  /**
   * 将模板中的变量替换为合法的变量，再返回新的模板，比如 将 {1} 转为 ${var1}
   */
  public static String tencentContentToSimpleContent(String content, Map<String, String> keyMap) {
    if (MapUtils.isEmpty(keyMap) || StringUtils.isEmpty(content)) {
      return content;
    }
    for (Map.Entry<String, String> entry : keyMap.entrySet()) {
      content = content.replace(SMS_PRE_NO_DOLLAR + entry.getValue() + "}", SMS_PRE_DOLLAR + entry.getKey() + "}");
    }
    return content;
  }

  public static boolean isSuccess(String code) {
    return "OK".equalsIgnoreCase(code);
  }

  public static List<SmsResponseData> convertResponseData(SmsSendByTemplate request, SendSmsResponse resp, String msgId) {
    SendStatus[] statusSet = resp.getSendStatusSet();
    if (ArrayUtils.isEmpty(statusSet)) {
      log.warn("tencent sms response is empty");
      String message = "tencent response is empty";
      return SmsUtils.zipPhoneResponse(request, false, message, msgId, null);
    }

    List<String> phones = request.getPhones();

    return Arrays.stream(statusSet)
      .map(s -> {
        boolean success = isSuccess(s.getCode());
        String phone = tencentPhoneToSimple(phones, s.getPhoneNumber());
        String message = s.getMessage();
        String id = Objects.toString(msgId, NanoIdUtils.randomNanoId());
        String serialId = s.getSerialNo();
        return new SmsResponseData(success, phone, message, id, serialId);
      })
      .toList();
  }

  /**
   * 将腾讯返回的手机号转成标准请求里的手机号，比如  +8618511122233 -> 18511122233
   */
  public static String tencentPhoneToSimple(List<String> phones, String phone) {
    return phones.stream()
      .filter(s -> s.contains(nationalNumber(phone)))
      .findFirst()
      .orElse(phone);
  }

  public static String nationalNumber(String phone) {
    try {
      Phonenumber.PhoneNumber phoneNumber = PhoneNumberUtil.getInstance().parse(phone, "CH");
      return phoneNumber.getNationalNumber() + "";
    } catch (NumberParseException e) {
      return phone;
    }
  }

  /**
   * 将标准手机号转为腾讯云需要的手机号（国内手机号前缀可加可不加），比如  18511122233 -> +8618511122233
   */
  public static String[] simplePhoneToTencent(List<String> phones, boolean international) {
    return SmsUtils.simplePhoneToE164(phones, international);
  }

  /**
   * 是否国际/港澳台短信：
   * 0：表示国内短信。
   * 1：表示国际/港澳台短信。
   */
  public static Long tencentInternational(boolean international) {
    return international ? 1L : 0L;
  }

  /**
   * 腾讯云短信类型，1表示营销短信，2表示通知短信，3表示验证码短信。
   */
  public static Long tencentTemplateType(SmsTemplateRequest request) {
    String key = request.getTemplateType();
    Long rs = types.get(key);
    if (Objects.nonNull(rs)) {
      return rs;
    }
    throw new SmsArgException("unknown template type:" + key);
  }

  /**
   * 短信下发状态，是否下发成功
   */
  public static boolean isSendSuccess(String sendStatus) {
    return SEND_STATUS_SUCCESS.equalsIgnoreCase(sendStatus);
  }

  /**
   * 转成Tencent Api需要的参数，注意是有序的
   */
  public static String[] tencentTemplateParamSet(SmsSendByTemplate request, SmsTemplateEntity template) {
    Map<String, String> templateParam = request.getTemplateParam();
    //需要替换的key映射关系, key是如 var1, value是1
    Map<String, String> replaceMap = jsonToMap(template.getParamReplace());

    //注意模板可能没有任何变量，是全文固定值
    if (MapUtils.isEmpty(templateParam) || MapUtils.isEmpty(replaceMap)) {
      return new String[]{};
    }
    String[] rs = new String[replaceMap.size()];
    for (Map.Entry<String, String> entry : replaceMap.entrySet()) {
      // 腾讯index是从1开始
      int index = Integer.parseInt(entry.getValue());
      rs[index - 1] = templateParam.getOrDefault(entry.getKey(), "");
    }
    return rs;
  }

  public static SmsClient initTencentSmsClient(SmsProperties.TencentConfig config) {
    try {
      Credential cred = new Credential(config.getAppKey(), config.getAppSecret());
      return new SmsClient(cred, config.getRegion(), clientProfile(config.getHttpProxy()));
    } catch (Exception e) {
      log.error("init tencent sms client failed.", e);
      return null;
    }
  }

  @NotNull
  public static ClientProfile clientProfile(String proxy) {
    ClientProfile profile = new ClientProfile();
    if (StringUtils.isNotBlank(proxy)) {
      HttpProfile httpProfile = new HttpProfile();
      URI uri = NetUtils.uri(proxy);
      httpProfile.setProxyHost(uri.getHost());
      httpProfile.setProxyPort(uri.getPort());
      profile.setHttpProfile(httpProfile);
    }
    return profile;
  }


}
