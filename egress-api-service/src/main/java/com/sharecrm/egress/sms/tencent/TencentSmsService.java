package com.sharecrm.egress.sms.tencent;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsResponseData;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.sms.TencentUtils;
import com.sharecrm.egress.utils.JsonUtil;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.AddSmsTemplateRequest;
import com.tencentcloudapi.sms.v20210111.models.AddSmsTemplateResponse;
import com.tencentcloudapi.sms.v20210111.models.AddTemplateStatus;
import com.tencentcloudapi.sms.v20210111.models.DescribeSmsTemplateListRequest;
import com.tencentcloudapi.sms.v20210111.models.DescribeSmsTemplateListResponse;
import com.tencentcloudapi.sms.v20210111.models.DescribeTemplateListStatus;
import com.tencentcloudapi.sms.v20210111.models.PullSmsSendStatus;
import com.tencentcloudapi.sms.v20210111.models.PullSmsSendStatusByPhoneNumberRequest;
import com.tencentcloudapi.sms.v20210111.models.PullSmsSendStatusByPhoneNumberResponse;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.rocketmq.common.message.Message;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import reactor.util.function.Tuple2;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TAG_SMS_STATUS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TAG_TEMPLATE_STATUS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TOPIC_SMS_TEXT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.SMS_TYPE_TTS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_APPROVING;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_INIT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_REJECTED;
import static com.sharecrm.egress.sms.AliyunUtils.selectSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.chooseSign;
import static com.sharecrm.egress.sms.SmsUtils.filterSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.replaceTemplateContent;
import static com.sharecrm.egress.sms.SmsUtils.tencentKeyReplacer;
import static com.sharecrm.egress.sms.SmsUtils.zipPhoneResponse;
import static com.sharecrm.egress.sms.TencentUtils.simplePhoneToTencent;
import static com.sharecrm.egress.sms.TencentUtils.tencentInternational;
import static com.sharecrm.egress.sms.TencentUtils.tencentTemplateParamSet;
import static com.sharecrm.egress.sms.TencentUtils.tencentTemplateStatus;
import static com.sharecrm.egress.sms.TencentUtils.tencentTemplateType;

/**
 * 腾讯云通用短信服务
 */
@Slf4j
public class TencentSmsService {

  private final SmsProperties.TencentConfig config;
  private final SmsDao smsDao;
  private final AutoConfMQProducer smsProducer;
  private final SmsClient smsClient;

  public TencentSmsService(SmsProperties.TencentConfig config, SmsDao smsDao, @Nullable AutoConfMQProducer smsProducer,
                           @Nullable SmsClient smsClient) {
    this.config = config;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
    this.smsClient = smsClient;
    log.info("tencent init, config:{}", config);
  }

  /**
   * 发送国内短信
   */
  public SmsSendResult sendSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setInternational(wrapper.isInternational());
    Tuple2<SmsSendByTemplate, SmsTemplateEntity> result = selectSmsTemplate(phone, request, queryStaticTemplates(query));
    SmsSendResult sendResult = sendByTemplate(wrapper, result.getT1(), result.getT2());
    sendResult.setRequest(request);
    return sendResult;
  }

  /**
   * 使用模板发送短信
   */
  public SmsSendResult sendByTemplate(SmsRequestWrapper<?> wrapper, SmsSendByTemplate request, SmsTemplateEntity template) {
    List<String> phones = request.getPhones();
    if (SmsUtils.isExcludeCountry(config.getExcludeCountryCodes(), phones, wrapper.isInternational())) {
      log.info("not support country, we will try with other providers, phone:{}", phones);
      return SmsUtils.notSupportWarn(wrapper, request, config, template);
    }
    SmsSendResponse rsp = new SmsSendResponse();
    rsp.setBatchMsgId(wrapper.getBatchMsgId());
    String msgId = wrapper.getMsgId();
    String content = template.getContent();
    boolean international = template.isInternational();
    //国际短信允许没有签名，国内必须有签名，如果没有英文签名，用中文占位
    String zhSign = international ? config.getIntlZhSignName() : config.getZhSignName();
    String enSign = international ? config.getIntlEnSignName() : Objects.toString(config.getEnSignName(), zhSign);
    // 根据目标内容选择签名
    String signName = chooseSign(request.getLanguage(), content, request.getTemplateParam(), zhSign, enSign, international);
    try {
      SendSmsRequest tencentRequest = new SendSmsRequest();
      tencentRequest.setPhoneNumberSet(simplePhoneToTencent(phones, international));
      tencentRequest.setSignName(signName);
      tencentRequest.setTemplateId(template.getProviderTemplateId());
      //替换冲突部分内容，去除多余符号，替换成真实的key
      tencentRequest.setTemplateParamSet(tencentTemplateParamSet(request, template));
      tencentRequest.setSmsSdkAppId(config.getSmsSdkAppId());
      if (international && StringUtils.isNotBlank(config.getSenderId())) {
        tencentRequest.setSenderId(config.getSenderId());
      }
      SendSmsResponse resp = smsClient.SendSms(tencentRequest);
      log.info("tencent sms http call result: {}", resp);
      List<SmsResponseData> dataList = TencentUtils.convertResponseData(request, resp, msgId);
      boolean success = dataList.stream().allMatch(SmsResponseData::isSuccess);
      String message = success ? "success" : joinFailed(dataList);
      rsp.setSuccess(success);
      rsp.setMessage(message);
      rsp.setPhones(dataList);
    } catch (Exception e) {
      log.error("send sms failed.", e);
      rsp.setSuccess(false);
      rsp.setMessage(e.getMessage());
      rsp.setPhones(zipPhoneResponse(request, false, e.getMessage(), msgId, ""));
    }
    return SmsUtils.initResult(request, rsp, config, template, signName);
  }

  @NotNull
  private String joinFailed(List<SmsResponseData> dataList) {
    return dataList.stream()
      .filter(Predicate.not(SmsResponseData::isSuccess))
      .map(SmsResponseData::getMessage)
      .distinct()
      .collect(Collectors.joining(","));
  }

  /**
   * 申请创建新模板
   */
  public SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    try {
      log.info("tencent add template source request: {}", request);
      AddSmsTemplateRequest tplRequest = new AddSmsTemplateRequest();
      String content = request.getContent();
      // 实际申请时，替换变量，原Request中 ${code} -> {1}
      Map<String, String> keyReplaces = TencentUtils.tencentSimpleContentForReplace(content);
      tplRequest.setTemplateContent(replaceTemplateContent(content, keyReplaces, (s, e) -> tencentKeyReplacer(s, e.getKey(), e.getValue())));
      tplRequest.setSmsType(tencentTemplateType(request));
      tplRequest.setRemark(request.getRemark());
      tplRequest.setTemplateName(request.getName());
      tplRequest.setInternational(tencentInternational(request.isInternational()));

      log.info("tencent add template request: {}", tplRequest);
      AddSmsTemplateResponse resp = smsClient.AddSmsTemplate(tplRequest);
      //校验是否返回正常
      validateTemplateResp(resp);
      String templateId = SmsUtils.randomTemplateId();
      //to mongo entity
      saveToMongo(templateId, request, resp.getAddTemplateStatus().getTemplateId(), keyReplaces);
      return SmsUtils.initTemplateDetail(config, request, templateId);
    } catch (Exception e) {
      log.warn("tencent add template failed.", e);
      throw new SmsException("add template failed. " + e.getMessage());
    }
  }

  private void saveToMongo(String templateId, SmsTemplateRequest request, String templateCode, Map<String, String> keyReplaces) {
    SmsTemplateEntity entity = SmsUtils.initTemplateEntity(config, templateId, templateCode, request, keyReplaces);
    smsDao.save(entity);
  }

  private void validateTemplateResp(AddSmsTemplateResponse resp) {
    log.info("tencent add template response: {}", resp);
    AddTemplateStatus status = resp.getAddTemplateStatus();
    if (Objects.isNull(status)) {
      throw new SmsException("add template failed, response body is empty. " + resp.getRequestId());
    }
    if (StringUtils.isEmpty(status.getTemplateId())) {
      throw new SmsException("add template failed, empty template code. ");
    }
  }

  /**
   * 查询并更新短信状态
   */
  public void updateSmsStatus() {
    smsDao.querySmsWithNoReplay(config.getId())
      .stream()
      // TTS记录也混杂在里面，我们还不支持查TTS状态
      .filter(Predicate.not(e -> SMS_TYPE_TTS.equals(e.getSmsType())))
      .filter(e -> StringUtils.isNotEmpty(e.getSerialId()))
      //腾讯此查询接口限流20次每秒，这一批拉不到放到下次再查
      .limit(20)
      .forEach(this::queryAndUpdateSmsStatus);
  }

  private void queryAndUpdateSmsStatus(SmsMongoEntity entity) {
    try {
      PullSmsSendStatusByPhoneNumberRequest request = new PullSmsSendStatusByPhoneNumberRequest();
      request.setSmsSdkAppId(config.getSmsSdkAppId());
      long time = entity.getSendTime().getTime() / 1000;
      request.setBeginTime(time - 3);
      request.setOffset(0L);
      request.setLimit(10L);
      request.setPhoneNumber(SmsUtils.phoneToE164(entity.getPhone(), Objects.requireNonNullElse(entity.getInternational(), false)));
      PullSmsSendStatusByPhoneNumberResponse response = smsClient.PullSmsSendStatusByPhoneNumber(request);
      if (Objects.isNull(response) || Objects.isNull(response.getPullSmsSendStatusSet())) {
        log.info("query sms status, response is empty: {}", response);
        return;
      }
      for (PullSmsSendStatus sendStatus : response.getPullSmsSendStatusSet()) {
        updateSmsStatus(sendStatus, entity);
      }
    } catch (Exception e) {
      log.warn("query {} tencent sms status failed.", config.getId(), e);
    }
  }

  private void updateSmsStatus(PullSmsSendStatus sendStatus, SmsMongoEntity old) {
    try {
      SmsMongoEntity entity = smsDao.updateSingleSmsStatus(config.getId(), sendStatus.getSerialNo(), old.getPhone(),
        TencentUtils.isSendSuccess(sendStatus.getReportStatus()),
        receiveTime(sendStatus.getUserReceiveTime()),
        sendStatus.getReportStatus(),
        sendStatus.getDescription()
      );
      SmsUtils.sendSmsStatusEvent(entity, smsProducer);
    } catch (Exception e) {
      log.warn("tencent sms status update failed.", e);
    }
  }

  private Date receiveTime(Long time) {
    return Objects.isNull(time) ? null : new Date(time * 1000);
  }

  /**
   * 查询并更新模板状态
   */
  public void updateTemplateStatus() {
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setProviderId(config.getId());
    query.setStatus(String.join(",", TEMPLATE_STATUS_INIT, TEMPLATE_STATUS_APPROVING, TEMPLATE_STATUS_REJECTED));
    // 6个月前的记录
    Date sixMonthsAgo = DateUtils.addMonths(new Date(), -6);
    smsDao.queryTemplates(query)
      .stream()
      .filter(e -> e.getSendTime().after(sixMonthsAgo))
      .forEach(this::queryAndUpdateTemplateStatus);
  }

  /**
   * 查询并更新模版状态
   */
  private void queryAndUpdateTemplateStatus(SmsTemplateEntity entity) {
    try {
      log.info("try query tencent template status, entity: {}", entity);
      DescribeSmsTemplateListRequest request = new DescribeSmsTemplateListRequest();
      request.setInternational(tencentInternational(entity.isInternational()));
      String id = entity.getProviderTemplateId();
      request.setTemplateIdSet(new Long[]{Long.parseLong(id)});

      DescribeSmsTemplateListResponse response = smsClient.DescribeSmsTemplateList(request);
      log.info("tencent query template status response: {}", response);
      DescribeTemplateListStatus[] statusSet = response.getDescribeTemplateStatusSet();
      if (ArrayUtils.isEmpty(statusSet)) {
        log.warn("tencent query template status response is empty.");
        return;
      }
      for (DescribeTemplateListStatus e : statusSet) {
        entity.setReply(e.getReviewReply());
        // 状态转换成标准的定义
        entity.setStatus(tencentTemplateStatus(e.getStatusCode()));
        entity.setUpdateTime(new Date());
        smsDao.save(entity);
        if (Objects.nonNull(smsProducer)) {
          //发送MQ通知，通知有模板状态变更
          log.info("tencent send template status mq, entity: {}", entity);
          // 设置EI用于MQ跨云投递，具体参考跨云投递的实现
          SmsUtils.addTraceContext(Objects.toString(entity.getEnterpriseId(), ""));
          smsProducer.send(new Message(MQ_TOPIC_SMS_TEXT, MQ_TAG_TEMPLATE_STATUS, JsonUtil.toBytes(entity)));
        }
      }
    } catch (Exception e) {
      log.warn("tencent update template status failed.", e);
    }
  }

  public List<SmsStaticTemplate> queryStaticTemplates(SmsTemplateQuery query) {
    return config.getTemplates()
      .values()
      .stream()
      .map(e -> SmsUtils.appendStaticTemplate(config, e))
      //静态模板中的变量要转为标准的定义，并设置replaceKeys
      .map(TencentUtils::appendTemplateReplace)
      .filter(e -> filterSmsTemplate(query, e))
      .toList();
  }

}
