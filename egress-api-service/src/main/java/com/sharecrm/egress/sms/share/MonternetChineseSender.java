package com.sharecrm.egress.sms.share;


import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.config.SmsProperties.MonternetConfig;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.MonternetStatus;
import com.sharecrm.egress.entity.RPTPack;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import com.sharecrm.egress.sms.SmsUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 梦网国内短信通道
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.monternet-internal.enabled", havingValue = "true", matchIfMissing = true)
public class MonternetChineseSender implements SmsSender, SmsBeanIgnoreDestroy {

  private MonternetHttpClient monternetSMS;

  private final SmsProperties properties;
  private final SmsDao smsDao;
  private final ObjectProvider<AutoConfMQProducer> smsProducer;

  public MonternetChineseSender(SmsProperties properties, SmsDao smsDao,
                                @Qualifier("smsRocketMQProducer") ObjectProvider<AutoConfMQProducer> smsProducer) {
    this.properties = properties;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
  }

  @PostConstruct
  public void init() {
    MonternetConfig config = config();
    log.info("MonternetInternalSender init, config:{}", config);
    this.monternetSMS = new MonternetHttpClient(config.getIp(), config.getPort(), config.getAccount(),
      config.getPassword());
  }

  private MonternetConfig config() {
    return properties.getMonternetInternal();
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    try {
      //梦网把我们的英文签名禁用了，等恢复后再放开
      String sign = SmsUtils.chooseSign(request.getLanguage(), request.getContent(), config().getZhSignName(), config().getEnSignName());
      if (sign.equals(config().getEnSignName()) && !config().isEnSignEnabled()) {
        log.warn("monternet en sign temp disabled, will retry by other provider. phone:{}", phone);
        return SmsUtils.failed(config(), wrapper, phone, "monternet en sign temp disabled, will retry by other provider");
      }
      //梦网需要增加签名，签名由我们自己控制，需要是审核通过的签名
      String content = sign + request.getContent();
      String token = SmsUtils.randomToken();
      //短信息发送接口（相同内容群发，可自定义流水号）GET请求,httpClient为空，则是短连接,httpClient不为空，则是长连接。
      String code = String.valueOf(monternetSMS.sendSms(phone, content, "*", token));
      SmsSendResult result = adapterResult(phone, wrapper, code, token);
      result.setSign(sign);
      return result;
    } catch (Exception e) {
      log.error("monternet sms send failed, phone:{}, request:{}", phone, request, e);
      return SmsUtils.failed(config(), wrapper, phone, e.getMessage());
    }
  }

  @NotNull
  private SmsSendResult adapterResult(String phone, SmsRequestWrapper<SmsSendRequest> wrapper, String code, String token) {
    if (code.equals("0")) {
      return SmsUtils.success(config(), wrapper, phone, token);
    }
    String orDefault = MonternetStatus.getOrDefault(code, "状态字典中不存在：" + code);
    return SmsUtils.failed(config(), wrapper, phone, code + "->" + orDefault);
  }

  /**
   * 更新数据库中短信下发状态
   */
  public void updateStatus() {
    try {
      List<RPTPack> result = Optional.ofNullable(monternetSMS.getRpt()).orElseGet(Collections::emptyList);
      result.forEach(this::updateStatus);
      log.debug("monternet sms update success, num: {}", result.size());
    } catch (RuntimeException e) {
      log.warn("monternet sms update failed", e);
    }
  }

  private void updateStatus(RPTPack rptPack) {
    SmsMongoEntity entity = smsDao.updateMonternetStatus(config().getId(),
      rptPack.getStrUserMsgId(), rptPack.getnStatus(), rptPack.getStrErCode());
    SmsUtils.sendSmsStatusEvent(entity, smsProducer.getIfAvailable());
  }

  @Override
  public SmsProvider provider() {
    return properties.getMonternetInternal();
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_CHINESE);
  }

  @Override
  public int getOrder() {
    return DEFAULT_ORDER - 20;
  }
}
