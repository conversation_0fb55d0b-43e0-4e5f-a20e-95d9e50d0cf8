package com.sharecrm.egress.sms.tencent;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.service.LockingExecutor;
import com.sharecrm.egress.sms.SmsStatusTask;
import com.sharecrm.egress.sms.SmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ScheduledFuture;

/**
 * 腾讯云短信和模板状态更新
 */
@Slf4j
public class TencentStatusTask implements SmsStatusTask {

  private final SmsProperties.TencentConfig config;
  private final TencentSmsSender sender;
  private final TaskScheduler taskScheduler;
  private final LockingExecutor lock;
  private final ScheduledFuture<?> smsScheduledFuture;
  private final ScheduledFuture<?> templateScheduledFuture;

  public TencentStatusTask(SmsProperties.TencentConfig config, TencentSmsSender sender, TaskScheduler taskScheduler, LockingExecutor lock) {
    this.config = config;
    this.sender = sender;
    this.taskScheduler = taskScheduler;
    this.lock = lock;
    this.smsScheduledFuture = startSmsTaskScheduler();
    this.templateScheduledFuture = startTemplateTaskScheduler();
  }

  private ScheduledFuture<?> startTemplateTaskScheduler() {
    log.info("start tencent template status task, id: {}, delay: {}", config.getId(), config.getTemplateTaskDelay());
    return taskScheduler.scheduleWithFixedDelay(this::updateTemplateStatus, SmsUtils.randomDelayInstant(60, 300), config.getTemplateTaskDelay());
  }

  private ScheduledFuture<?> startSmsTaskScheduler() {
    log.info("start tencent sms status task, id: {}, delay: {}", config.getId(), config.getSmsTaskDelay());
    return taskScheduler.scheduleWithFixedDelay(this::updateSmsStatus, SmsUtils.randomDelayInstant(30, 120), config.getSmsTaskDelay());
  }

  public void updateSmsStatus() {
    try {
      lock.execute(() -> {
        log.info("run tencent sms status task: {}", config.getId());
        sender.updateSmsStatus();
      }, "sms-task-" + config.getId(), Duration.ofSeconds(30), Duration.ofSeconds(10));
    } catch (Exception e) {
      log.warn("run tencent sms status task failed.", e);
    }

  }

  private void updateTemplateStatus() {
    try {
      lock.execute(() -> {
        log.info("run tencent template status task: {}", config.getId());
        sender.updateTemplateStatus();
      }, "template-task-" + config.getId(), Duration.ofSeconds(60), Duration.ofSeconds(10));
    } catch (Exception e) {
      log.warn("run tencent template status task failed.", e);
    }
  }

  @Override
  public void destroy() {
    try {
      smsScheduledFuture.cancel(true);
      templateScheduledFuture.cancel(true);
      log.info("try stop tencent sms task, id: {}", config.getId());
    } catch (Exception e) {
      log.warn("cancel tencent sms status task failed.", e);
    }
  }
}