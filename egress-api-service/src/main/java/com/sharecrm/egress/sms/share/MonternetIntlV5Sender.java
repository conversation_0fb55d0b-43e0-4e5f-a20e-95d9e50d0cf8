package com.sharecrm.egress.sms.share;

import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.config.SmsProperties.MonternetConfig;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 梦网国际短信通道(新版V5API)
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.monternet-intl-v5.enabled", havingValue = "true")
public class MonternetIntlV5Sender implements SmsSender, SmsBeanIgnoreDestroy {

  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMddHHmmss");

  private final SmsProperties properties;
  private final OkHttpSupport httpClient;

  public MonternetIntlV5Sender(SmsProperties properties, @Qualifier("smsHttpSupport") OkHttpSupport httpClient) {
    this.properties = properties;
    this.httpClient = httpClient;
  }

  private MonternetConfig config() {
    return properties.getMonternetIntlV5();
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    try {
      SmsSendRequest request = wrapper.getRequest();
      String sign = SmsUtils.chooseSign(request.getLanguage(), request.getContent(), config().getIntlZhSignName(), config().getIntlEnSignName());
      if (sign.equals(config().getIntlEnSignName()) && !config().isEnSignEnabled()) {
        log.warn("monternet en sign temp disabled, will retry by other provider. phone:{}", phone);
        return SmsUtils.failed(config(), wrapper, phone, "monternet en sign temp disabled, will retry by other provider");
      }
      //增加签名
      String content = sign + request.getContent();
      Tuple2<String, String> accountPwd = selectAccount(request);

      MonternetIntlV5Request req = new MonternetIntlV5Request();
      String now = formatter.format(LocalDateTime.now());
      String account = accountPwd.getT1();
      req.setUserid(account);
      // 这里按照梦网要求的固定格式
      String pwd = account.toUpperCase() + "********" + accountPwd.getT2() + now;
      req.setPwd(DigestUtils.md5Hex(pwd));
      req.setContent(URLEncoder.encode(content, StandardCharsets.UTF_8));
      req.setMobile(phone);
      req.setTimestamp(now);
      req.setCustid(wrapper.getMsgId());

      MonternetIntlV5Response rsp = httpClient.parseObject(WebUtils.okHttpJsonPost(sendUrl(), req, Map.of()), new TypeReference<>() {
      });
      SmsSendResult result = intlResult(rsp, phone, wrapper);
      result.setSign(sign);
      return result;
    } catch (Exception e) {
      log.error("monternet sms send failed, phone:{}, request:{}", phone, wrapper, e);
      return SmsUtils.failed(config(), wrapper, phone, e.getMessage());
    }
  }

  private String sendUrl() {
    return config().getSendUrlSchema() + "://"
      + config().getIp() + ":" + config().getPort()
      + config().getSendSingleUrl();
  }

  @NotNull
  private SmsSendResult intlResult(MonternetIntlV5Response rsp, String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    String code = rsp.getResult();
    if ("0".equals(code)) {
      return SmsUtils.success(config(), wrapper, phone, wrapper.getMsgId());
    }
    String desc = URLDecoder.decode(Objects.requireNonNullElse(rsp.getDesc(), ""), StandardCharsets.UTF_8);
    log.warn("monternet intl response:{},desc:{}", rsp, desc);
    return SmsUtils.failed(config(), wrapper, phone, "error:" + "->" + desc);
  }

  private Tuple2<String, String> selectAccount(SmsSendRequest request) {
    //国际短信如果配置了营销短信账号，则非验证码短信使用营销账号发送短信
    String marketingAccount = config().getMarketingAccount();
    String marketingPassword = config().getMarketingPassword();
    if (StringUtils.isNotEmpty(marketingAccount) && StringUtils.isNotEmpty(marketingPassword) && !request.getContent().contains("验证码")) {
      log.info("International SMS use marketing account");
      return Tuples.of(marketingAccount, marketingPassword);
    }
    return Tuples.of(config().getAccount(), config().getPassword());
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_INTL, SUPPORT_CAPTCHA_INTL);
  }

  @Override
  public SmsProvider provider() {
    return config();
  }

  @Override
  public int getOrder() {
    return config().getOrder();
  }

}
