package com.sharecrm.egress.sms.byteplus;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.google.common.reflect.TypeToken;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sms.AliyunUtils;
import com.sharecrm.egress.sms.BytePlusUtils;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import reactor.util.function.Tuple2;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.sharecrm.egress.sms.AliyunUtils.reallyTemplateParamJson;
import static com.sharecrm.egress.sms.AliyunUtils.selectSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.chooseSign;
import static com.sharecrm.egress.sms.SmsUtils.filterSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.zipPhoneResponse;

/**
 * BytePlus短信服务
 * See: <a href="https://docs.byteplus.com/en/docs/byteplus-sms/What_is_Byteplus_Messaging">...</a>
 */
@Slf4j
public class BytePlusSmsService {

  private final SmsProperties.BytePlusConfig config;
  private final SmsDao smsDao;
  private final AutoConfMQProducer smsProducer;
  private final com.byteplus.service.sms.SmsService smsClient;

  public BytePlusSmsService(SmsProperties.BytePlusConfig config,
                            SmsDao smsDao,
                            @Nullable AutoConfMQProducer smsProducer,
                            com.byteplus.service.sms.SmsService smsClient) {
    this.config = config;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
    this.smsClient = smsClient;
    log.info("byteplus init, config:{}", config);
  }

  /**
   * 发送国内或国际短信
   */
  public SmsSendResult sendSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setInternational(wrapper.isInternational());
    Tuple2<SmsSendByTemplate, SmsTemplateEntity> result = selectSmsTemplate(phone, request, queryStaticTemplates(query));
    SmsSendResult sendResult = sendByTemplate(wrapper, result.getT1(), result.getT2());
    sendResult.setRequest(request);
    return sendResult;
  }

  /**
   * 使用模板发送短信
   */
  public SmsSendResult sendByTemplate(SmsRequestWrapper<?> wrapper, SmsSendByTemplate request, SmsTemplateEntity template) {
    List<String> phones = request.getPhones();
    if (SmsUtils.isExcludeCountry(config.getExcludeCountryCodes(), phones, wrapper.isInternational())) {
      log.info("not support country, we will try with other providers, phone:{}", phones);
      return SmsUtils.notSupportWarn(wrapper, request, config, template);
    }
    SmsSendResponse rsp = new SmsSendResponse();
    rsp.setBatchMsgId(wrapper.getBatchMsgId());
    // 此时可能为null，为null时每个手机号new一次新ID
    String msgId = wrapper.getMsgId();
    String content = template.getContent();
    boolean international = template.isInternational();
    //如果没有英文签名，用中文占位
    String zhSign = zhSignName(config, international);
    String enSign = enSignName(config, international);
    // 根据目标内容选择签名
    String signName = chooseSign(request.getLanguage(), content, request.getTemplateParam(), zhSign, enSign);
    try {
      com.byteplus.model.request.SmsSendRequest bytePlusReq = new com.byteplus.model.request.SmsSendRequest();
      bytePlusReq.setFrom(config.getSmsFrom());
      bytePlusReq.setSmsAccount(config.getMessageGroupId());
      bytePlusReq.setPhoneNumbers(AliyunUtils.aliyunPhoneNumbers(phones, international));
      bytePlusReq.setTemplateId(template.getProviderTemplateId());
      bytePlusReq.setTemplateParam(reallyTemplateParamJson(request, template));
      bytePlusReq.setSign(signName);
      com.byteplus.model.response.SmsSendResponse resp = smsClient.send(bytePlusReq);
      String code = resp.getCode();
      boolean success = BytePlusUtils.isSuccess(resp);
      String message = resp.getMessage();
      String serialId = Optional.ofNullable(resp.getResult())
        .map(com.byteplus.model.response.SmsSendResponse.ResultBean::getMessageId)
        .map(e -> e.isEmpty() ? "" : e.getFirst()).orElse("");
      log.info("byteplus sms response, phone:{}, code: {}, message: {}, serialId: {}", phones, code, message, serialId);
      rsp.setSuccess(success);
      rsp.setMessage(message);
      rsp.setPhones(zipPhoneResponse(request, success, message, msgId, serialId));
    } catch (Exception e) {
      log.error("byteplus send sms failed.", e);
      rsp.setSuccess(false);
      rsp.setMessage(e.getMessage());
      rsp.setPhones(zipPhoneResponse(request, false, e.getMessage(), msgId, ""));
    }
    return SmsUtils.initResult(request, rsp, config, template, signName);
  }

  private String zhSignName(SmsProperties.BytePlusConfig config, boolean international) {
    if (international) {
      return StringUtils.defaultIfEmpty(config.getIntlZhSignName(), config.getIntlEnSignName());
    }
    return StringUtils.defaultIfEmpty(config.getZhSignName(), config.getEnSignName());
  }

  private String enSignName(SmsProperties.BytePlusConfig config, boolean international) {
    if (international) {
      return StringUtils.defaultIfEmpty(config.getIntlEnSignName(), config.getIntlZhSignName());
    }
    return StringUtils.defaultIfEmpty(config.getEnSignName(), config.getZhSignName());
  }

  public List<SmsStaticTemplate> queryStaticTemplates(SmsTemplateQuery query) {
    return config.getTemplates()
      .values()
      .stream()
      .map(e -> SmsUtils.appendStaticTemplate(config, e))
      .filter(e -> filterSmsTemplate(query, e))
      .toList();
  }

  public void smsStatusCallback(String ext, String body) {
    log.info("byteplus sms status callback, ext:{}, body:{}", ext, body);
    try {
      List<BytePlusStatusReport> reports = JsonUtil.fromJson(body, new TypeToken<List<BytePlusStatusReport>>() {
      }.getType());
      if (CollectionUtils.isEmpty(reports)) {
        return;
      }
      for (BytePlusStatusReport report : reports) {
        String status = report.getStatusCode();
        String smsMsgId = report.getMessageId();
        if (StringUtils.isAnyBlank(status, smsMsgId)) {
          return;
        }
        SmsMongoEntity entity = smsDao.updateSingleSmsStatus(config.getId(), smsMsgId, null,
          BytePlusUtils.isSendSuccess(status),
          new Date(Objects.requireNonNullElse(report.getRecvTime(), System.currentTimeMillis())),
          status,
          report.getDescription()
        );
        SmsUtils.sendSmsStatusEvent(entity, smsProducer);
      }
    } catch (Exception e) {
      log.warn("byteplus sms status update failed.", e);
    }
  }
}
