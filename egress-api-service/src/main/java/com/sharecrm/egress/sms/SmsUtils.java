package com.sharecrm.egress.sms;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.common.Guard;
import com.fxiaoke.release.GrayRule;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.trace.TraceContext;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsPageResult;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsArgException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsResponseData;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.utils.EgressUtils;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.SingleExecutorUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.rocketmq.common.message.Message;
import org.jetbrains.annotations.NotNull;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.function.UnaryOperator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TAG_SMS_STATUS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TOPIC_SMS_TEXT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.SMS_TYPE_TTS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_APPROVED;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_INIT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_NOTIFICATION;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_PROMOTION;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE;

/**
 * 短信工具类
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@UtilityClass
public class SmsUtils {

  private final PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

  private final RandomUtils randomUtils = RandomUtils.secureStrong();

  public static final String COUNTRY_CODE_CH = "+86";

  public static final String CHINA_PHONE_REGEX = "^1[3-9]\\d{9}$";

  /**
   * 加密短信内容，这个不要随便改，因为运维可能需要解密历史数据
   */
  private static final Guard encrypt = new Guard("WeMp2us.nx-Eqm<v");

  /**
   * 判断是否包含中文
   */
  private static final Pattern ZH_PATTERN = Pattern.compile("[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】]");

  /**
   * 开通短信被人恶意利用，在密码里加广告词，如果密码包含中文，就任务是违法短信
   */
  private static final Pattern ILLEGAL_PASSWORD_PATTERN = Pattern.compile("和密码.*[\\u4e00-\\u9fa5].*登录。");

  /**
   * 默认中文短信签名，注意签名是要向运营商申请的，不是随便写的，否则发不出去
   */
  public static final String SMS_SIGN_ZH = "【纷享销客】";
  public static final String SMS_SIGN_EN = "[ShareCRM]";

  /**
   * 短信模板变量提取规则，带$符号，类似${code}
   */
  public static final Pattern SMS_PATTERN_DOLLAR = Pattern.compile("\\$\\{(.*?)}");

  /**
   * 短信模板变量提取规则，不带$符号，类似 {1}
   */
  public static final Pattern SMS_PATTERN_NO_DOLLAR = Pattern.compile("\\{(.*?)}");

  /**
   * 带${}的短信模板的前缀
   */
  public static final String SMS_PRE_DOLLAR = "${";

  /**
   * 不带$，例如 {1} 的短信模板的前缀
   */
  public static final String SMS_PRE_NO_DOLLAR = "{";

  /**
   * 所有支持的模板类型，用于模板参数校验，如果有新增类型注意维护此字段
   */
  public static final List<String> SUPPORT_TEMPLATE_TYPES = List.of(TEMPLATE_TYPE_VERIFY_CODE,
    TEMPLATE_TYPE_NOTIFICATION, TEMPLATE_TYPE_PROMOTION);

  private static final char[] codeArray = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    .toCharArray();

  /**
   * 一位数的国际电话区号
   */
  private static final List<String> ONE_NUM_COUNTRY_CODE = Arrays.asList("1", "7");
  /**
   * 二位数的国际电话区号
   */
  private static final List<String> TWO_NUM_COUNTRY_CODE = Arrays
    .asList("20", "27", "30", "31", "32", "33", "34", "36", "39", "40", "41", "43", "44", "45", "46", "47", "48",
      "49",
      "51", "52", "53", "54", "55", "56", "57", "58", "60", "61", "62", "63", "64", "65", "66", "81", "82", "84",
      "86",
      "90", "91", "92", "93", "94", "95", "98");
  /**
   * 三位数的国际电话区号
   */
  private static final List<String> THREE_NUM_COUNTRY_CODE = Arrays
    .asList("212", "213", "216", "218", "220", "221", "223", "224", "225", "226", "227", "228", "229", "230", "231",
      "232", "233", "233", "234", "235", "236", "237", "239", "241", "242", "243", "244", "247", "248", "249",
      "251",
      "252", "253", "254", "255", "256", "257", "258", "260", "261", "262", "263", "264", "265", "266", "267",
      "268",
      "327", "331", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "370", "371", "372",
      "373",
      "374", "375", "376", "377", "378", "380", "381", "386", "420", "421", "423", "501", "502", "503", "504",
      "505",
      "506", "507", "509", "591", "592", "593", "594", "595", "596", "597", "598", "599", "673", "674", "675",
      "676",
      "677", "679", "682", "684", "685", "689", "850", "852", "853", "855", "856", "880", "886", "960", "961",
      "962",
      "963", "964", "965", "966", "967", "968", "971", "972", "973", "974", "976", "977", "992", "993", "994",
      "995");

  /**
   * 模板列表
   */
  private static final String[] patternList = Arrays.stream(
    new String[]{
      "(.+)企业管理员(.+)给你发来链接",
      "(.+)您好,咱们公司为您开通的纷享销客帐号您一直未登录,请尽快登录",
      "贵公司部署了移动销售管理专家纷享销客，企业名称  (.+)。您可凭手机号",
    }).map(pattern -> pattern.replaceAll("\\s", "")).toArray(String[]::new);

  /**
   * 中国11位手机号匹配
   */
  private static final Pattern MOBILE_PATTERN = Pattern.compile("1[3-9]\\d{9}");

  /**
   * 大汉三通或移动梦网返回的结果里，如果包含下面关键字，证明号码无效。后续如果他们Api完善了能根据状态码判断更好
   */
  private static final List<String> invalidatedPhoneMessages = List.of("无可用号码", "有异常电话号码", "手机号码格式错误", "手机号:长度",
    "incorrect number format", "invalid mobile number");

  /**
   * 判断是否为验证码的关键字
   */
  private static final List<String> captchaMessages = List.of("验证码", "Verification Code");

  /**
   * 有几个英文模板可能在内容中嵌入其他语言，我们主动提高优先级
   */
  private static final List<String> prioritizeEnMessages = List.of("Your company has deployed a connected CRM",
    "Welcome to ShareCRM");

  public static boolean containIllegalPassword(String content) {
    return StringUtils.isNotBlank(content) && ILLEGAL_PASSWORD_PATTERN.matcher(content).find();
  }

  public static String chooseLanguage(String language, String content) {
    if (StringUtils.isNotEmpty(language)) {
      return language;
    }
    if (containPrioritizeEnMsg(content)) {
      return SmsSdkConstants.LANGUAGE_EN;
    }
    return containChinese(content) ? SmsSdkConstants.LANGUAGE_ZH : SmsSdkConstants.LANGUAGE_EN;
  }

  public static String chooseSign(String language, String content) {
    return chooseSign(language, content, SMS_SIGN_ZH, SMS_SIGN_EN);
  }

  public static String chooseSign(String language, String content, String zhSign, String enSign) {
    String zh = StringUtils.defaultIfEmpty(zhSign, SMS_SIGN_ZH);
    String en = StringUtils.defaultIfEmpty(enSign, SMS_SIGN_EN);
    if (StringUtils.isBlank(language)) {
      if (containPrioritizeEnMsg(content)) {
        return en;
      }
      return containChinese(content) ? zh : en;
    }
    // 不指定时，优先中文
    return SmsSdkConstants.LANGUAGE_EN.equalsIgnoreCase(language) ? en : zh;
  }

  public static String chooseSign(String language, String templateContent, Map<String, String> params, String zhSign,
                                  String enSign) {
    return chooseSign(language, templateContent, params, zhSign, enSign, false);
  }

  public static String chooseSign(String language, String templateContent, Map<String, String> params, String zhSign,
                                  String enSign, boolean allowNull) {
    // 有些云的国际短信，允许没有签名
    String zh = allowNull ? zhSign : StringUtils.defaultIfEmpty(zhSign, SMS_SIGN_ZH);
    String en = allowNull ? enSign : StringUtils.defaultIfEmpty(enSign, SMS_SIGN_EN);
    if (StringUtils.isBlank(language)) {
      String content = fakeTemplateContent(templateContent, params);
      if (containPrioritizeEnMsg(content)) {
        return en;
      }
      return containChinese(content) ? zh : en;
    }
    // 不指定时，优先中文
    return SmsSdkConstants.LANGUAGE_EN.equalsIgnoreCase(language) ? en : zh;
  }

  @NotNull
  private static String fakeTemplateContent(String template, Map<String, String> params) {
    if (MapUtils.isEmpty(params)) {
      return template;
    }
    return template + String.join("", params.values());
  }

  /**
   * 有些异常情况根据ID找不到模板，返回一个假的内容。
   */
  @NotNull
  public static String fakeTtsContent(Integer id, String[] params) {
    if (ArrayUtils.isEmpty(params)) {
      return id + "";
    }
    return id + String.join("", params);
  }

  /**
   * 此处只是简单验证手机号码的合法性，11个数字就OK
   *
   * @param phone 国内手机号码
   * @return 合格true
   */
  public static boolean isPhone(String phone) {
    return Objects.nonNull(phone) && phone.matches("\\d{11}");
  }

  public static void validateInternal(List<String> phones) throws SmsArgException {
    if (CollectionUtils.isEmpty(phones)) {
      throw new SmsArgException("phones is empty");
    }
    phones.forEach(s -> {
      if (!isPhone(s)) {
        throw new SmsArgException(s + " is not a mobile phone number");
      }
    });
  }

  public static void validateTts(List<String> phones) throws SmsArgException {
    if (CollectionUtils.isEmpty(phones)) {
      throw new SmsArgException("phones is empty");
    }
    phones.forEach(s -> {
      if (!isPhone(s) && !isIntlPhone(s)) {
        throw new SmsArgException(s + " is not a mobile phone number");
      }
    });
  }

  /**
   * 简单国际号码判定OK，必须以0开头
   *
   * @param phone 手机号
   * @return 是否是国际号码
   */
  public static boolean isIntlPhone(String phone) {
    return Objects.nonNull(phone) && phone.matches("0+\\d+");
  }

  /**
   * 校验是否全部是合法的国内手机号或全部是国外手机号
   *
   * @param phones        手机号列表
   * @param international true表示校验国际手机号
   * @throws SmsArgException 失败异常
   */
  public static void validatePhones(List<String> phones, boolean international) throws SmsArgException {
    if (international) {
      validateIntl(phones);
    } else {
      validateInternal(phones);
    }
  }

  public static void validateIntl(List<String> phones) throws SmsArgException {
    if (CollectionUtils.isEmpty(phones)) {
      throw new SmsArgException("phones is empty");
    }
    phones.forEach(s -> {
      if (!isIntlPhone(s)) {
        throw new SmsArgException(s + " is not a international phone number");
      }
    });
  }

  public static Optional<String> getStrictCaptcha(String text) {
    if (!isCaptchaSms(text)) {
      return Optional.empty();
    }
    return Optional.of(getCaptcha(text))
      .filter(Predicate.not(String::isEmpty));
  }

  /**
   * 判断是否为验证码短信，有些运营商要求验证码单独处理
   */
  public static boolean isCaptchaSms(String text) {
    return text != null && captchaMessages.stream().anyMatch(text::contains);
  }

  /**
   * 判断是否为验证码短信，有些运营商要求验证码单独处理
   */
  public static String selectSmsType(String text) {
    return isCaptchaSms(text) ? TEMPLATE_TYPE_VERIFY_CODE : TEMPLATE_TYPE_NOTIFICATION;
  }

  /**
   * 从文本内容里获取验证码
   *
   * @param text 文本内容
   * @return 验证码
   */
  public static String getCaptcha(String text) {
    int start = -1;
    int end = text.length();
    for (int i = 0; i < text.length(); i++) {
      if (start == -1 && text.charAt(i) >= '0' && text.charAt(i) <= '9') {
        start = i;
      } else if (start != -1 && (text.charAt(i) < '0' || text.charAt(i) > '9')) {
        end = i;
        break;
      }
    }
    // 目前只支持6位的验证码
    if (start == -1 || end - start != 6) {
      return "";
    }
    return text.substring(start, end);
  }

  /**
   * 修正国际号码，兼容开头0格式
   *
   * @param phone 手机号
   * @return 修正后的手机号
   */
  public static String amendIntlPhone(String phone) {
    // 国际电话结构： 00 + 国际电话区号 + 号码。
    // 国际电话区号是不能以0开头的，但部分调用方会错误地使用这种格式。为了兼容这类错误号码，对国际电话区号下的0进行移除
    if (phone.startsWith("0000")) {
      return "00" + phone.substring(4);
    } else if (phone.startsWith("000")) {
      return "00" + phone.substring(3);
    }
    return phone;
  }

  /**
   * 将我们API中的手机号转为国际 E.164标准，格式为：[+][国家/地区代码][区号][本地电话号码]
   *
   * @param phone         原始手机号
   * @param international 是否为国际手机号
   * @return 格式话以后手机号
   */
  public static String phoneToE164(String phone, boolean international) {
    try {
      if (!international) {
        // phoneUtil对国内手机号识别不准确，会识别为其他国家的，不如直接返回
        return COUNTRY_CODE_CH + phone;
      }
      phone = amendIntlPhone(phone);
      Phonenumber.PhoneNumber phoneNumber = phoneUtil.parse(phone, "CH");
      return "+" + phoneNumber.getCountryCode() + phoneNumber.getNationalNumber();
    } catch (RuntimeException | NumberParseException e) {
      log.warn("invalid phone number:{}", phone, e);
      return phone;
    }
  }

  public static int parseCountryCode(String phone, boolean international) {
    try {
      if (!international) {
        // phoneUtil对国内手机号识别不准确，会识别为其他国家的，不如直接返回
        return 86;
      }
      phone = amendIntlPhone(phone);
      Phonenumber.PhoneNumber phoneNumber = phoneUtil.parse(phone, "CH");
      return phoneNumber.getCountryCode();
    } catch (RuntimeException | NumberParseException e) {
      log.warn("invalid phone number:{}", phone, e);
      return 0;
    }
  }

  public static boolean isChinaMobile(String phone) {
    return Objects.nonNull(phone) && phone.matches(CHINA_PHONE_REGEX);
  }

  public static boolean tryGuessMobile(String phone) {
    // 一个粗略的判断是不是手机号的方法，不保证准确
    if (Objects.isNull(phone) || phone.length() < 8) {
      return false;
    }
    if (SmsUtils.isPhone(phone)) {
      return true;
    }
    try {
      // 只有座机号才有括号
      if (StringUtils.containsAny(phone, "(", ")", "（", "）")) {
        return false;
      }
      Phonenumber.PhoneNumber phoneNumber = phoneUtil.parse(phone.replace("-", ""), "CH");
      return phoneNumber.hasCountryCode();
    } catch (RuntimeException | NumberParseException e) {
      log.info("invalid phone number:{}", phone, e);
      return false;
    }
  }

  /**
   * 将我们API中的手机号转为国际 E.164标准，格式为：[+][国家/地区代码][区号][本地电话号码]，比如 18511122233 ->
   * +8618511122233
   */
  public static String[] simplePhoneToE164(List<String> phones, boolean international) {
    return phones.stream()
      .map(s -> phoneToE164(s, international))
      .toArray(String[]::new);
  }

  /**
   * 由于很多国际号码的第一位数是为0， 但这种情况下的号码发送时客户是接收不到短信的，经过测试，需要吧号码前面的0去掉后客户才能正常接收短信
   * 比如一个越南号码：008401636615754 拨打电话：越南用户给这个号码拨打电话时使用 01636615754， 国内给这个号码拨打电话时使用
   * 00841636615754 发送短信： 国内给 008401636615754 发送短信时， 用户接收不到短信， 当给 00841636615754
   * 发送短信时，客户可以正常接收到短信
   * <p>
   * 基于以上原因， 当客户手机号的第一位为0时， 去掉这个0然后重新发送一次短信。保障短信的达到率
   */
  public static String removeTheFirstZero(String phone) {
    if (phone.startsWith("00")) {
      String countryCode = "";
      if (ONE_NUM_COUNTRY_CODE.contains(phone.substring(2, 3))) {
        countryCode = phone.substring(2, 3);
      } else if (TWO_NUM_COUNTRY_CODE.contains(phone.substring(2, 4))) {
        countryCode = phone.substring(2, 4);
      } else if (THREE_NUM_COUNTRY_CODE.contains(phone.substring(2, 5))) {
        countryCode = phone.substring(2, 5);
      }
      if (!countryCode.isEmpty()) {
        int phoneStartIndex = 2 + countryCode.length();
        if ("0".equals(phone.substring(phoneStartIndex, phoneStartIndex + 1))) {
          phone = phone.substring(0, phoneStartIndex) + phone.substring(phoneStartIndex + 1);
        }
      }
    }
    return phone;
  }

  /**
   * 字符串是否包含中文
   *
   * @param text 待校验字符串
   * @return true 包含中文字符 false 不包含中文字符
   */
  public static boolean containChinese(String text) {
    Matcher m = ZH_PATTERN.matcher(Objects.toString(text, ""));
    return m.find();
  }

  /**
   * 判断是否包含特定的英文内容，包含的优先用英文
   *
   * @param text 待校验字符串
   * @return true 包含特定的英文内容 false 不包含特定的英文内容
   */
  public static boolean containPrioritizeEnMsg(String text) {
    return prioritizeEnMessages.stream()
      .anyMatch(text::contains);
  }

  /**
   * 判断短信内容是否包含无效号码标识
   *
   * @param message 运营商返回结果
   * @return true: 包含无效号码标识
   */
  public static boolean isInvalidatedPhoneByMessage(String message) {
    return Optional.ofNullable(message)
      .map(s -> invalidatedPhoneMessages.stream().anyMatch(s::contains))
      .orElse(Boolean.FALSE);
  }

  public static String replaceContext(String context) {
    if (context == null) {
      return null;
    }
    // 因梦网的过滤，把 “邀请您加入” 改为 “邀您加入”
    String txt = context.replace("邀请您加入", "邀您加入")
      // 解决双签名问题，我们或运营商会自动重新签名
      .replace(SMS_SIGN_ZH, "")
      .replace(SMS_SIGN_EN, "")
      .replace("【", "")
      .replace("】", "");

    // 替换11位手机号中间4位为****
    txt = replaceMobileNumbers(txt);
    // 再按模板处理
    for (String pattern : patternList) {
      txt = replaceContentByPattern(pattern, txt);
    }
    return txt;
  }

  /**
   * 替换文本中的11位手机号，将中间4位替换为****
   *
   * @param text 待处理的文本
   * @return 替换后的文本
   */
  private static String replaceMobileNumbers(String text) {
    // 匹配中国的11位数字的手机号（1开头，第二位是1-9，后面9位数字）
    Matcher matcher = MOBILE_PATTERN.matcher(text);
    StringBuilder result = new StringBuilder();
    while (matcher.find()) {
      String mobile = matcher.group();
      // 保留前3位和后4位，中间4位替换为****
      String maskedMobile = mobile.substring(0, 3) + "****" + mobile.substring(7);
      matcher.appendReplacement(result, maskedMobile);
    }
    matcher.appendTail(result);
    return result.toString();
  }

  private static String replaceContentByPattern(String patternStr, String content) {
    String rs = content;
    Pattern pattern = Pattern.compile(patternStr);
    Matcher matcher = pattern.matcher(rs.replaceAll("\\s", ""));
    if (matcher.find()) {
      for (int i = 1; i <= matcher.groupCount(); i++) {
        String value = matcher.group(i);
        if (value.length() > 4) {
          rs = rs.replace(value, value.substring(0, 2) + "**" + value.substring(value.length() - 2));
        }
      }
    }
    return rs;
  }

  public static String replaceSign(String content, String zhSign, String enSign) {
    // 去掉签名，内容中不需要包含前面，而且选择模板的时候无法匹配
    if (StringUtils.isNotEmpty(enSign)) {
      if (content.startsWith(enSign)) {
        content = content.replaceFirst(enSign, "");
      }
      content = content.replace("[" + enSign + "]", "");
    }
    if (StringUtils.isNotEmpty(zhSign)) {
      // 只对开头的替换，内容中可能包含签名
      if (content.startsWith(zhSign)) {
        content = content.replaceFirst(zhSign, "");
      }
      content = content.replace("【" + zhSign + "】", "");
    }
    return content;
  }

  /**
   * 获取需要替换变量的key，不需要替换的不返回
   */
  public Map<String, String> paramKeyForReplace(String content, UnaryOperator<String> replacer) {
    List<String> keys = extractKeys(content);
    Map<String, String> keyMap = new HashMap<>();
    // 替换不合法的变量
    for (String key : keys) {
      String newKey = replacer.apply(key);
      if (!key.equals(newKey)) {
        keyMap.put(key, newKey);
      }
    }
    return keyMap;
  }

  /**
   * 将模板中的变量替换为合法的变量，再返回替换后的模板内容，比如 ${Leader.name} -> ${leader_name}
   */
  public static String replaceTemplateContent(String content, Map<String, String> keyMap,
                                              BiFunction<String, Map.Entry<String, String>, String> replacer) {
    if (MapUtils.isEmpty(keyMap) || StringUtils.isEmpty(content)) {
      return content;
    }
    for (Map.Entry<String, String> entry : keyMap.entrySet()) {
      content = replacer.apply(content, entry);
    }
    return content;
  }

  /**
   * Aliyun替换模板中的变量名字，替换成阿里云合规的变量名字，比如：${Leader.name} -> ${leader_name}
   */
  public static String aliyunTemplateParamReplacer(String key) {
    String rs = key.replace(".", "_");
    return rs.toLowerCase();
  }

  public static String aliyunKeyReplacer(String content, String oldKey, String newKey) {
    return content.replace(SMS_PRE_DOLLAR + oldKey + "}", SMS_PRE_DOLLAR + newKey + "}");
  }

  /**
   * 腾讯云替换模板中的变量，比如 ${name} -> {1}
   */
  public static String tencentKeyReplacer(String content, String oldKey, String newKey) {
    return content.replace(SMS_PRE_DOLLAR + oldKey + "}", SMS_PRE_NO_DOLLAR + newKey + "}");
  }

  public static Map<String, String> replaceTemplateParamValue(SmsSendByTemplate request,
                                                              Function<Map.Entry<String, String>, String> func) {
    return request.getTemplateParam()
      .entrySet()
      .stream()
      .collect(Collectors.toMap(Map.Entry::getKey, func));
  }

  public static SmsSendRequest replaceContext(SmsSendRequest req) {
    req.setContent(replaceContext(req.getContent()));
    return req;
  }

  public static String zsjReplaceContent(String content) {
    // 招商环境短信内容单独处理，使用的模板是 “尊敬的用户：${content}，如非本人操作，请忽略本短信。”
    String replaced = content
      // 短信模板中已包含此内容，避免内容重复
      .replaceFirst("尊敬的用户：", "")
      .replaceFirst("尊敬的用户，", "")
      .replaceFirst("Dear valued user,", "")
      .replace("如非本人操作，请勿泄露。", "")
      .replace("如非本人操作，请勿泄露", "")
      .replace("如非本人操作，请忽略本短信。", "")
      .replace("如非本人操作，请忽略本短信", "");
    // 模板中已经有结尾句号，剔除结尾标点符号
    return StringUtils.stripEnd(replaced, "！!，,。.");
  }

  public static String aliyunReplaceContent(String content, String template) {
    // 只对使用默认模板的短信才处理，有些模板是完全匹配的不用处理
    if (template.contains("尊敬的用户") || template.contains("Dear valued user")) {
      // 蒙牛短信内容单独处理，目前和招商局的一样
      return zsjReplaceContent(content);
    }
    return content;
  }

  public static String mengniuReplaceContent(String content, String template) {
    // 只对使用默认模板的短信才处理，有些模板是完全匹配的不用处理
    return aliyunReplaceContent(content, template);
  }

  /**
   * MD5加密
   *
   * @param input 待加密的文本内容
   * @return MD5加密后的文本内容
   */
  public static String getMd5(String input) {
    return DigestUtils.md5Hex(input);
  }

  public static String randomToken() {
    return NanoIdUtils.randomNanoId(NanoIdUtils.DEFAULT_NUMBER_GENERATOR, codeArray, 10);
  }

  public static String randomTemplateId() {
    return "fs-sms-" + NanoIdUtils.randomNanoId(NanoIdUtils.DEFAULT_NUMBER_GENERATOR, codeArray, 12);
  }

  public static int sortSendOrder(Set<String> channels, SmsSender o1, SmsSender o2) {
    int od1 = o1.getOrder();
    int od2 = o2.getOrder();
    // 如果已经用过了，就降低优先级，数字越大优先级越低
    if (channels.contains(o1.provider().getId())) {
      od1 += SmsSender.DEFAULT_ORDER;
    }
    if (channels.contains(o2.provider().getId())) {
      od2 += SmsSender.DEFAULT_ORDER;
    }
    // 只有优先级一样才增加随机，随机最大为1，不要太大影响了优先级
    if (od1 == od2) {
      od1 += randomUtils.randomInt(0, 2);
      od2 += randomUtils.randomInt(0, 2);
    }
    return Integer.compare(od1, od2);
  }

  /**
   * 去除短信记录中的敏感信息
   *
   * @param rs 短信记录列表
   * @return 消除敏感信息后的记录
   */
  public static SmsPageResult sensitiveMasker(SmsPageResult rs) {
    rs.getResults().forEach(e -> e.setContent(null));
    return rs;
  }

  /**
   * 对短信内容脱敏
   */
  public static String sensitiveMaskerContent(String content) {
    if (StringUtils.isEmpty(content)) {
      return content;
    }
    // 替换所有连续4个数字
    content = content.replaceAll("\\d{4}", "****");
    // 使用replaceAll替换“密码”后面的3位字符
    return content.replaceAll("(密码.{4})", "密码****");
  }

  /**
   * 对短信内容加密
   */
  public static String encryptContent(String content) {
    if (StringUtils.isEmpty(content)) {
      return content;
    }
    return encrypt.encode(content);
  }

  public static boolean isAllowEi(String grayRule, Integer enterpriseId) {
    if (Objects.isNull(grayRule) || Objects.isNull(enterpriseId)) {
      return false;
    }
    return new GrayRule(grayRule).isAllow("EI." + enterpriseId);
  }

  public static String grayRuleByEi(Integer enterpriseId) {
    return "white:EI." + enterpriseId;
  }

  public static boolean isAllowAll(String grayRule) {
    return StringUtils.isNotEmpty(grayRule)
      && (grayRule.contains("*") || grayRule.contains("white:*") || grayRule.contains("white:*.*"));
  }

  /**
   * 模板状态值转换，找不到返回原值
   */
  public static String templateStatus(Map<String, String> templateStatus, String providerStatus) {
    // 模板状态值转换
    return templateStatus.entrySet()
      .stream().filter(e -> e.getValue().equals(providerStatus))
      .map(Map.Entry::getKey)
      .findFirst()
      .orElse(providerStatus);
  }

  /**
   * 提前短信模板中的key
   *
   * @param template 短信模板
   * @return keys
   */
  public static List<String> extractKeys(String template) {
    return extractKeys(SMS_PATTERN_DOLLAR, template);
  }

  /**
   * 提前短信模板中的key，不同云厂商可能有不同的前缀
   */
  public static List<String> extractKeys(Pattern pattern, String template) {
    List<String> keys = new ArrayList<>();
    Matcher matcher = pattern.matcher(template);
    while (matcher.find()) {
      keys.add(matcher.group(1));
    }
    return keys;
  }

  /**
   * 提前短信模板中的key和value
   */
  public static Map<String, String> extractValues(String template, String message) {
    return extractValues(SMS_PRE_DOLLAR, template, message);
  }

  /**
   * 提前短信模板中的key和value，不同云厂商可能有不同的前缀
   */
  public static Map<String, String> extractValues(String pre, String template, String message) {
    Map<String, String> keyValueMap = new HashMap<>();
    Pattern keyPattern = SMS_PATTERN_NO_DOLLAR;
    if (SMS_PRE_DOLLAR.equals(pre)) {
      keyPattern = SMS_PATTERN_DOLLAR;
    }
    List<String> keys = extractKeys(keyPattern, template);
    Tuple2<String, String> tuple2 = replaceForExtractValues(template, message);
    String regex = tuple2.getT1();
    for (String key : keys) {
      regex = regex.replace(pre + key + "}", "(.*)");
    }
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(tuple2.getT2());
    if (matcher.find()) {
      for (int i = 0; i < keys.size(); i++) {
        keyValueMap.put(keys.get(i), matcher.group(i + 1));
      }
    }
    return keyValueMap;
  }

  /**
   * 短信模板中可能包含特殊符号影响正则匹配，先转义字符。
   * <p>
   * 发短信的时候，内容中有时候空格，有时候又不带，导致模板无法匹配上，没法玩，所以大家都去掉空格再比对
   */
  public static Tuple2<String, String> replaceForExtractValues(String template, String message) {
    // 内容中有时候空格，有时候又不带，和正则表达式一起处理掉特殊字符
    message = message.replaceAll("\\s+", "");
    template = template.replaceAll("\\s+", "");
    // 内容中也可能包含这些字符，为减少误伤，只有模板中也包含时才替换内容中的
    List<String> notSafeStr = List.of("(", ")", "[", "]");
    for (String notSafe : notSafeStr) {
      if (template.contains(notSafe)) {
        template = template.replace(notSafe, "");
        message = message.replace(notSafe, "");
      }
    }
    return Tuples.of(template, message);
  }

  /**
   * 强制类型转换，抹除子类中的敏感信息，比如账号密码
   */
  public static SmsProvider simpleSmsProvider(SmsProvider e) {
    return new SmsProvider() {
      @Override
      public String getId() {
        return e.getId();
      }

      @Override
      public String getName() {
        return e.getName();
      }

      @Override
      public String getType() {
        return e.getType();
      }

      @Override
      public boolean isExclusive() {
        return e.isExclusive();
      }

      @Override
      public boolean isEnabled() {
        return e.isEnabled();
      }

      @Override
      public String getAllowAccounts() {
        return e.getAllowAccounts();
      }

      @Override
      public String getZhSignName() {
        return e.getZhSignName();
      }

      @Override
      public String getEnSignName() {
        return e.getEnSignName();
      }

      @Override
      public String getIntlZhSignName() {
        return e.getIntlZhSignName();
      }

      @Override
      public String getIntlEnSignName() {
        return e.getIntlEnSignName();
      }
    };
  }

  public static boolean isTemplateAvailable(String status) {
    return TEMPLATE_STATUS_APPROVED.equals(status);
  }

  /**
   * 判断模板是否符合查询条件
   */
  public static boolean filterSmsTemplate(SmsTemplateQuery query, SmsStaticTemplate e) {
    if (StringUtils.isNotBlank(query.getTemplateId())) {
      return query.getTemplateId().equals(e.getTemplateId());
    }
    if (StringUtils.isNotBlank(query.getProviderId())) {
      return query.getProviderId().equals(e.getProviderId());
    }
    if (StringUtils.isNotBlank(query.getTemplateType())) {
      return query.getTemplateType().equals(e.getTemplateType());
    }
    if (Objects.nonNull(query.getEnterpriseId())) {
      return query.getEnterpriseId().equals(e.getEnterpriseId())
        || isAllowEi(e.getAllowAccounts(), query.getEnterpriseId());
    }
    if (Objects.nonNull(query.getInternational())) {
      return query.getInternational().equals(e.isInternational());
    }
    if (StringUtils.isNotBlank(query.getStatus())) {
      return e.getStatus().equals(query.getStatus());
    }
    return true;
  }

  public static void assertLength(String txt, int min, int max, String name) {
    int length = StringUtils.length(txt);
    if (length >= min && length <= max) {
      return;
    }
    throw new SmsArgException(name + " length must between " + min + " and " + max);
  }

  public static void assertNonNull(Object obj, String name) {
    if (Objects.isNull(obj)) {
      throw new SmsArgException(name + " can not be null");
    }
  }

  /**
   * 校验模板类型是否合法，不合法抛出异常
   */
  public static void validateTemplateType(String templateType) {
    if (!SUPPORT_TEMPLATE_TYPES.contains(templateType)) {
      throw new SmsArgException("unsupported template type: " + templateType);
    }
  }

  /**
   * 校验模板参数是否合法，不合法抛出异常
   */
  public static void validateAddTemplate(SmsTemplateRequest request) {
    assertNonNull(request.getEnterpriseId(), "enterpriseId");
    assertLength(request.getName(), 1, 30, "name");
    assertLength(request.getContent(), 1, 500, "content");
    assertLength(request.getRemark(), 1, 100, "remark");
    validateTemplateType(request.getTemplateType());
  }

  /**
   * 渲染模板，将变量替换为真实的内容
   */
  public String renderTemplate(String template, Map<String, String> paramMap) {
    if (MapUtils.isEmpty(paramMap)) {
      return template;
    }
    try {
      StringSubstitutor sub = new StringSubstitutor(paramMap);
      return sub.replace(template);
    } catch (Exception e) {
      return template;
    }
  }

  public static boolean filterByEi(String ei, SmsSender s) {
    // 非专用通道，所有人都可以用。
    if (!s.provider().isExclusive()) {
      return true;
    }
    // 老接口都找不到EA，找不到时只允许使用上面的宽松通道
    if (StringUtils.isEmpty(ei)) {
      // 蒙牛这种专用通道的，但是完全在蒙牛云内使用，实际上允许所有的EA使用
      return isAllowAll(s.provider().getAllowAccounts());
    }
    // 可能是多个，用逗号分割，比如登录时一个手机号可能对应多个，全部匹配才允许使用
    return Arrays.stream(ei.split(","))
      .map(s1 -> NumberUtils.toInt(s1, -1))
      .allMatch(e -> isAllowEi(s.provider().getAllowAccounts(), e));
  }

  public static SmsSendResult zipSmsSendResult(List<SmsResponseData> phonesDetails) {
    SmsSendResult result = new SmsSendResult();
    SmsSendResponse response = new SmsSendResponse();
    response.setBatchMsgId(NanoIdUtils.randomNanoId());
    response.setPhones(phonesDetails);
    response.setSuccess(phonesDetails.stream().allMatch(SmsResponseData::isSuccess));
    result.setResponse(response);
    return result;
  }

  /**
   * 封装单条成功记录
   */
  public static SmsSendResult success(SmsProvider provider, SmsRequestWrapper<?> wrapper, String phone,
                                      String serialId) {
    SmsSendResponse response = new SmsSendResponse();
    response.setSuccess(true);
    String id = NanoIdUtils.randomNanoId();
    response.setBatchMsgId(Objects.toString(wrapper.getBatchMsgId(), id));
    SmsResponseData data = new SmsResponseData();
    data.setPhone(phone);
    data.setSuccess(true);
    data.setSerialId(serialId);
    data.setMsgId(Objects.toString(wrapper.getMsgId(), id));
    response.setPhones(List.of(data));
    SmsSendResult result = initResult(wrapper.getRequest(), response);
    result.setProvider(provider);
    return result;
  }

  public static SmsSendResult failed(SmsProvider provider, SmsRequestWrapper<?> wrapper, String phone, String message) {
    SmsSendResponse response = new SmsSendResponse();
    String id = NanoIdUtils.randomNanoId();
    response.setBatchMsgId(Objects.toString(wrapper.getBatchMsgId(), id));
    response.setSuccess(false);
    SmsResponseData data = new SmsResponseData();
    data.setPhone(phone);
    data.setSuccess(false);
    data.setMsgId(Objects.toString(wrapper.getMsgId(), id));
    data.setMessage(message);
    response.setPhones(List.of(data));
    SmsSendResult result = initResult(wrapper.getRequest(), response);
    result.setProvider(provider);
    return result;
  }

  @NotNull
  public static SmsSendResult initResult(Object request, SmsSendResponse response) {
    return initResult(request, response, null);
  }

  @NotNull
  public static SmsSendResult initResult(Object request, SmsSendResponse response, SmsProvider provider) {
    return initResult(request, response, provider, null, null);
  }

  /**
   * 判断是否都是不支持的国家。全部都不支持的时候返回 true
   */
  public static boolean isExcludeCountry(List<Integer> excludeCountryCodes, List<String> phones,
                                         boolean international) {
    if (CollectionUtils.isEmpty(excludeCountryCodes) || CollectionUtils.isEmpty(phones)) {
      return false;
    }
    return phones.stream().allMatch(s -> isExcludeCountry(excludeCountryCodes, s, international));
  }

  public static boolean isExcludeCountry(List<Integer> excludeCountryCodes, String phone, boolean international) {
    int countryCode = SmsUtils.parseCountryCode(phone, international);
    return excludeCountryCodes.contains(countryCode);
  }

  public static SmsSendResult notSupportWarn(SmsRequestWrapper<?> wrapper, SmsSendByTemplate request,
                                             SmsProvider provider, SmsTemplateEntity template) {
    SmsSendResponse rsp = new SmsSendResponse();
    rsp.setBatchMsgId(wrapper.getBatchMsgId());
    String msgId = wrapper.getMsgId();
    String msg = "you can ignore this failed, we will try with other providers, not support this country";
    rsp.setSuccess(false);
    rsp.setMessage(msg);
    rsp.setPhones(zipPhoneResponse(request, false, msg, msgId, ""));
    return SmsUtils.initResult(request, rsp, provider, template, "");
  }

  public static SmsSendResult initResult(Object request, SmsSendResponse response, SmsProvider provider,
                                         SmsTemplateEntity template, String sign) {
    SmsSendResult result = new SmsSendResult();
    if (request instanceof TTSSendRequest req) {
      result.setTtsSendRequest(req);
      result.setSmsType(SMS_TYPE_TTS);
    }
    if (request instanceof SmsSendRequest req) {
      result.setRequest(req);
      result.setSmsType(selectSmsType(req.getContent()));
    }
    if (request instanceof SmsSendByTemplate req) {
      result.setTemplateRequest(req);
      if (Objects.nonNull(template)) {
        String content = template.getContent();
        result.setContent(renderTemplate(content, req.getTemplateParam()));
        result.setSmsType(template.getTemplateType());
        result.setInternational(template.isInternational());
      }
    }
    result.setResponse(response);
    result.setProvider(provider);
    result.setSign(sign);
    return result;
  }

  public static SmsTemplateDetail initTemplateDetail(SmsProvider provider, SmsTemplateRequest request,
                                                     String templateId) {
    SmsTemplateDetail detail = new SmsTemplateDetail();
    detail.setTemplateId(templateId);
    detail.setContent(request.getContent());
    detail.setTemplateType(request.getTemplateType());
    detail.setName(request.getName());
    detail.setRemark(request.getRemark());
    detail.setEnterpriseId(request.getEnterpriseId());
    detail.setAllowAccounts(request.getAllowAccounts());
    detail.setInternational(request.isInternational());
    detail.setStatus(TEMPLATE_STATUS_INIT);
    detail.setProviderId(provider.getId());
    detail.setProviderName(provider.getName());
    return detail;
  }

  public static SmsTemplateEntity initTemplateEntity(SmsProvider provider, String templateId, String providerTemplateId,
                                                     SmsTemplateRequest request, Map<String, String> keyReplaceMap) {
    SmsTemplateEntity entity = new SmsTemplateEntity();
    entity.setProviderTemplateId(providerTemplateId);
    entity.setProviderId(provider.getId());
    entity.setProviderName(provider.getName());
    entity.setTemplateId(templateId);
    entity.setContent(request.getContent());
    entity.setParamReplace(JsonUtil.mapToJsonOrNull(keyReplaceMap));
    entity.setTemplateType(request.getTemplateType());
    entity.setName(request.getName());
    entity.setRemark(request.getRemark());
    entity.setEnterpriseId(request.getEnterpriseId());
    entity.setAllowAccounts(request.getAllowAccounts());
    entity.setInternational(request.isInternational());
    entity.setStatus(TEMPLATE_STATUS_INIT);
    Date now = new Date();
    entity.setSendTime(now);
    entity.setUpdateTime(now);
    return entity;
  }

  public static SmsStaticTemplate appendStaticTemplate(SmsProvider provider, SmsStaticTemplate src) {
    src.setTemplateId(provider.getId() + "-" + src.getProviderTemplateId());
    src.setStatus(SmsSdkConstants.TEMPLATE_STATUS_APPROVED);
    src.setProviderId(provider.getId());
    src.setProviderName(provider.getName());
    src.setAllowAccounts(provider.getAllowAccounts());
    return src;
  }

  public static List<SmsResponseData> zipPhoneResponse(SmsSendByTemplate request, boolean success, String message,
                                                       String msgId, String serialId) {
    // msgId允许为null，此时每个手机号使用独立id，使用Supplier封装下确保不同手机号使用不同ID
    Supplier<String> id = () -> Objects.toString(msgId, NanoIdUtils.randomNanoId());
    return request.getPhones()
      .stream()
      .map(s -> new SmsResponseData(success, s, message, id.get(), serialId))
      .toList();
  }

  /**
   * 使用@RefreshScope注解的Bean，会创建两个Bean，比如 一个叫 scopedTarget.hexagonTencentSmsSender
   * ，一个叫 hexagonTencentSmsSender，去重，并保留RefreshScope的代理类
   */
  public static <T> List<T> filterSmsSenderBeans(Map<String, T> beansOfType) {
    return EgressUtils.filterRefreshScopeBeans(beansOfType);
  }

  /**
   * 计算短信条数，目前只使用于国内短信
   * <p>
   * 短信字数<=70个字数，按照70个字数一条短信计算。
   * 短信字数>70个字数，即为长短信，按照每67个字数记为一条短信计算。
   */
  public static int adapterSmsSize(int length) {
    if (length <= 70) {
      return 1;
    }
    return (int) Math.ceil(length * 1.0 / 67);
  }

  public static Instant randomDelayInstant(int startInclusive, int endExclusive) {

    return Instant.now().plusSeconds(randomUtils.randomInt(startInclusive, endExclusive));
  }

  public static void sendSmsStatusEvent(SmsMongoEntity entity, AutoConfMQProducer smsProducer) {
    if (Objects.isNull(entity)) {
      return;
    }
    if (Objects.nonNull(smsProducer)) {
      log.info("sms status update, entity: {}", entity);
      addTraceContext(entity.getEnterpriseId());
      // 发送MQ通知，通知短信记录变更
      smsProducer.send(new Message(MQ_TOPIC_SMS_TEXT, MQ_TAG_SMS_STATUS, JsonUtil.toBytes(entity)));
    }
    SingleExecutorUtils.saveSmsBizLog(entity);
  }

  public static void addTraceContext(String enterpriseId) {
    if (StringUtils.isNotEmpty(enterpriseId)) {
      // 设置EI用于MQ跨云投递，具体参考跨云投递的实现，注意不要跨线程池使用
      TraceContext context = TraceContext.get();
      context.setEi(enterpriseId);
      context.setTraceId(WebUtils.traceId());
    }
  }

}
