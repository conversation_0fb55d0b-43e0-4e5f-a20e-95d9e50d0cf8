package com.sharecrm.egress.sms;

import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsSign;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import org.springframework.core.Ordered;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;

/**
 * 对接服务商短信通信接口
 */
public interface SmsSender extends Ordered {

  int DEFAULT_ORDER = 100;

  /**
   * 支持类型：国际短信
   */
  String SUPPORT_INTL = "intl";

  /**
   * 支持类型：国内短信
   */
  String SUPPORT_CHINESE = "chinese";

  /**
   * 支持类型：国际验证码
   */
  String SUPPORT_CAPTCHA_INTL = "captcha-intl";

  /**
   * 支持类型：允许申请国内短信模板
   */
  String SUPPORT_ADD_TEMPLATE_CHINESE = "add-template-chinese";

  /**
   * 支持类型：允许申请国际短信模板
   */
  String SUPPORT_ADD_TEMPLATE_INTL = "add-template-intl";

  /**
   * 支持类型：允许根据模板发送国内短信
   */
  String SUPPORT_SEND_TEMPLATE_CHINESE = "send-by-template-chinese";

  /**
   * 支持类型：允许根据模板发送国际短信
   */
  String SUPPORT_SEND_TEMPLATE_INTL = "send-by-template-intl";

  /**
   * 支持类型：文本转语音TTS服务
   */
  String SUPPORT_TTS = "tts";

  /**
   * 单条发送
   */
  SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper);

  /**
   * 发送TTS语音
   */
  default SmsSendResult sendTts(TTSSendRequest request) {
    throw new SmsException("not support send tts");
  }

  /**
   * 支持的静态模板，通过配置文件配置上去的，已经申请好可直接用的
   *
   * @param query 查询条件
   * @return 模板列表
   */
  default List<SmsStaticTemplate> staticTemplates(SmsTemplateQuery query) {
    return List.of();
  }

  default SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    throw new SmsException("not support add template");
  }

  default SmsSendResult sendByTemplate(SmsSendByTemplate request, SmsTemplateEntity template) {
    throw new SmsException("not support send by template");
  }

  default List<SmsSign> listSigns() {
    return List.of();
  }

  /**
   * 短信状态回调，更新短信状态
   */
  default void smsStatusCallback(String ext, String body, ServerWebExchange exchange) {
  }

  SmsProvider provider();

  /**
   * 支持哪些类型的服务
   *
   * @return 支持能力集合
   */
  default List<String> supports() {
    return List.of();
  }

  /**
   * 通道优先级顺序，数字越大优先级越低
   *
   * @return 优先级，数字越大优先级越低
   */
  default int getOrder() {
    return DEFAULT_ORDER;
  }

}
