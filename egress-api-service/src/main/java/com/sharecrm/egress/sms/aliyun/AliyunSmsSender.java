package com.sharecrm.egress.sms.aliyun;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties.AliYunConfig;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsSign;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.sms.SmsSender;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.sharecrm.egress.sms.AliyunUtils.initAliyunSmsClient;
import static com.sharecrm.egress.sms.AliyunUtils.initAliyunTtsClient;

/**
 * 阿里云短信通道
 */
@Slf4j
public class AliyunSmsSender implements SmsSender {

  private final AliYunConfig config;
  private final SmsDao smsDao;
  private final AliyunSmsService aliyunSmsService;

  public AliyunSmsSender(AliYunConfig config, SmsDao smsDao, AutoConfMQProducer smsProducer) {
    this.config = config;
    this.smsDao = smsDao;
    this.aliyunSmsService = initAliyunSmsService(config, smsProducer);
  }

  private AliyunSmsService initAliyunSmsService(AliYunConfig config, AutoConfMQProducer smsProducer) {
    log.info("aliyun sms sender init, config:{}", config);
    return new AliyunSmsService(config, smsDao, smsProducer, initAliyunTtsClient(config), initAliyunSmsClient(config));
  }

  @Override
  public SmsSendResult sendTts(TTSSendRequest request) {
    return aliyunSmsService.sendTts(request);
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    return aliyunSmsService.sendSms(phone, wrapper);
  }

  @Override
  public List<SmsStaticTemplate> staticTemplates(SmsTemplateQuery query) {
    return aliyunSmsService.queryStaticTemplates(query);
  }

  @Override
  public SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    return aliyunSmsService.addTemplate(request);
  }

  @Override
  public SmsSendResult sendByTemplate(SmsSendByTemplate request, SmsTemplateEntity template) {
    // Wrapper中的msgId置为null，以便为每个手机号生成一个独立的ID
    return aliyunSmsService.sendByTemplate(new SmsRequestWrapper<>(request, NanoIdUtils.randomNanoId(), null), request, template);
  }

  public void updateSmsStatus() {
    aliyunSmsService.updateSmsStatus();
  }

  public void updateTemplateStatus() {
    aliyunSmsService.updateTemplateStatus();
  }


  @Override
  public List<SmsSign> listSigns() {
    return aliyunSmsService.listSigns();
  }

  @Override
  public SmsProvider provider() {
    return config;
  }

  @Override
  public List<String> supports() {
    return config.getSupports();
  }

  @Override
  public int getOrder() {
    return config.getOrder();
  }
}
