package com.sharecrm.egress.sms.zsj;

import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 招商-外运环境国内短信通道
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.sino.enabled", havingValue = "true")
public class ZsjSinoSender implements SmsSender, SmsBeanIgnoreDestroy {

  private final ZsjSinoSmsService zsjSmsService;
  private final SmsProperties properties;

  public ZsjSinoSender(ZsjSinoSmsService zsjSmsService, SmsProperties properties) {
    this.zsjSmsService = zsjSmsService;
    this.properties = properties;
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    return zsjSmsService.sendSms(phone, wrapper);
  }


  @Override
  public SmsProvider provider() {
    return properties.getSino();
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_CHINESE);
  }

  @Override
  public int getOrder() {
    return DEFAULT_ORDER - 10;
  }

}
