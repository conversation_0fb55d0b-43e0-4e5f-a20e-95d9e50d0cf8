package com.sharecrm.egress.sms.aliyun;

import com.aliyun.dysmsapi20170525.models.CreateSmsTemplateRequest;
import com.aliyun.dysmsapi20170525.models.CreateSmsTemplateResponse;
import com.aliyun.dysmsapi20170525.models.CreateSmsTemplateResponseBody;
import com.aliyun.dysmsapi20170525.models.GetSmsTemplateRequest;
import com.aliyun.dysmsapi20170525.models.GetSmsTemplateResponse;
import com.aliyun.dysmsapi20170525.models.GetSmsTemplateResponseBody;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsRequest;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponse;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponseBody;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.dysmsapi20170525.models.SendSmsResponseBody;
import com.aliyun.dyvmsapi20170525.Client;
import com.aliyun.dyvmsapi20170525.models.SingleCallByTtsRequest;
import com.aliyun.dyvmsapi20170525.models.SingleCallByTtsResponse;
import com.aliyun.dyvmsapi20170525.models.SingleCallByTtsResponseBody;
import com.aliyun.tea.TeaException;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.entity.TtsTemplate;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsResponseData;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateVariableAttr;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.sms.AliyunUtils;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.rocketmq.common.message.Message;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import reactor.util.function.Tuple2;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TAG_TEMPLATE_STATUS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TOPIC_SMS_TEXT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.SMS_TYPE_TTS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_APPROVING;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_INIT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_REJECTED;
import static com.sharecrm.egress.sms.AliyunUtils.aliyunTemplateType;
import static com.sharecrm.egress.sms.AliyunUtils.reallyTemplateParamJson;
import static com.sharecrm.egress.sms.AliyunUtils.selectSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.aliyunKeyReplacer;
import static com.sharecrm.egress.sms.SmsUtils.chooseSign;
import static com.sharecrm.egress.sms.SmsUtils.filterSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.replaceTemplateContent;
import static com.sharecrm.egress.sms.SmsUtils.zipPhoneResponse;

/**
 * 阿里云短信服务
 */
@Slf4j
public class AliyunSmsService {

  private final SmsProperties.AliYunConfig config;
  private final SmsDao smsDao;
  private final AutoConfMQProducer smsProducer;
  private final Client ttsClient;
  private final com.aliyun.dysmsapi20170525.Client smsClient;

  public AliyunSmsService(SmsProperties.AliYunConfig config, SmsDao smsDao,
                          @Nullable AutoConfMQProducer smsProducer,
                          @Nullable Client ttsClient,
                          @Nullable com.aliyun.dysmsapi20170525.Client smsClient) {
    this.config = config;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
    this.ttsClient = ttsClient;
    this.smsClient = smsClient;
    log.info("aliyun init, config:{}", config);
  }

  /**
   * 发送TTS语音
   */
  public SmsSendResult sendTts(TTSSendRequest request) {
    try {
      log.debug("aliyun tts send source request:{}", request);
      TtsTemplate ttsTemplate = AliyunUtils.selectTtsTemplate(request.getTemplateId(), config.getTtsTemplates().values());
      String code = ttsTemplate.getProviderTemplateId();
      Map<String, String> param = AliyunUtils.buildTtsParams(ttsTemplate, request);
      List<SmsResponseData> list = request.getPhones()
        .stream()
        .map(phone -> singleCallByTts(phone, code, param))
        .toList();

      SmsSendResult result = SmsUtils.zipSmsSendResult(list);
      result.setContent(SmsUtils.renderTemplate(ttsTemplate.getContent(), param));
      result.setProvider(config);
      result.setTtsSendRequest(request);
      result.setSmsType(SMS_TYPE_TTS);
      return result;
    } catch (Exception e) {
      log.warn("aliyun tts send failed:{}", request, e);
      return SmsUtils.failed(config, new SmsRequestWrapper<>(request), String.join(",", request.getPhones()), e.getMessage());
    }
  }

  /**
   * 文本转语音外呼
   */
  private SmsResponseData singleCallByTts(String phone, String ttsCode, Map<String, String> param) {
    SmsResponseData result = new SmsResponseData();
    result.setSuccess(true);
    result.setMsgId(NanoIdUtils.randomNanoId());
    result.setPhone(phone);
    try {
      //组装请求对象-具体描述见控制台-文档部分内容
      SingleCallByTtsRequest request = singleCallByTtsRequest(phone, ttsCode, param);
      log.info("aliyun tts request:{}", request);
      SingleCallByTtsResponse response = ttsClient.singleCallByTts(request);
      SingleCallByTtsResponseBody body = response.getBody();
      if (Objects.nonNull(body)) {
        result.setMessage(body.getMessage());
        result.setSuccess(AliyunUtils.isSuccess(body.getCode()));
      }
      return result;
    } catch (Exception e) {
      log.error("aliyun tts send failed:{}", phone, e);
      result.setSuccess(false);
      result.setMessage(e.getMessage());
      return result;
    }
  }

  @NotNull
  SingleCallByTtsRequest singleCallByTtsRequest(String phone, String ttsCode, Map<String, String> param) {
    SingleCallByTtsRequest request = new SingleCallByTtsRequest();
    //被叫显号,可在语音控制台中找到所购买的显号。当使用 阿里云公共号池 时，保持为空
    if (StringUtils.isNotBlank(config.getShowNumber())) {
      request.setCalledShowNumber(config.getShowNumber());
    }
    //必填-被叫号码
    request.setCalledNumber(phone);
    //必填-Tts模板ID
    request.setTtsCode(ttsCode);
    //可选-当模板中存在变量时需要设置此值
    request.setTtsParam(JsonUtil.mapToJsonOrNull(param));
    return request;
  }

  /**
   * 发送国内或国际短信
   */
  public SmsSendResult sendSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setInternational(wrapper.isInternational());
    Tuple2<SmsSendByTemplate, SmsTemplateEntity> result = selectSmsTemplate(phone, request, queryStaticTemplates(query));
    SmsSendResult sendResult = sendByTemplate(wrapper, result.getT1(), result.getT2());
    sendResult.setRequest(request);
    return sendResult;
  }

  /**
   * 使用模板发送短信
   */
  public SmsSendResult sendByTemplate(SmsRequestWrapper<?> wrapper, SmsSendByTemplate request, SmsTemplateEntity template) {
    List<String> phones = request.getPhones();
    if (SmsUtils.isExcludeCountry(config.getExcludeCountryCodes(), phones, wrapper.isInternational())) {
      log.info("not support country, we will try with other providers, phone:{}", phones);
      return SmsUtils.notSupportWarn(wrapper, request, config, template);
    }
    SmsSendResponse rsp = new SmsSendResponse();
    rsp.setBatchMsgId(wrapper.getBatchMsgId());
    // 此时可能为null，为null时每个手机号new一次新ID
    String msgId = wrapper.getMsgId();
    String content = template.getContent();
    boolean international = template.isInternational();
    //如果没有英文签名，用中文占位
    String zhSign = zhSignName(config, international);
    String enSign = enSignName(config, international);
    // 根据目标内容选择签名
    String signName = chooseSign(request.getLanguage(), content, request.getTemplateParam(), zhSign, enSign);
    try {
      SendSmsRequest aliRequest = new SendSmsRequest();
      //转成阿里云需要的格式
      aliRequest.setPhoneNumbers(AliyunUtils.aliyunPhoneNumbers(phones, international));
      aliRequest.setSignName(signName);
      aliRequest.setTemplateCode(template.getProviderTemplateId());
      //替换冲突部分内容，去除多余符号，替换成真实的key
      aliRequest.setTemplateParam(reallyTemplateParamJson(request, template));
      SendSmsResponse resp = smsClient.sendSms(aliRequest);
      SendSmsResponseBody body = resp.getBody();
      String code = body.getCode();
      boolean success = AliyunUtils.isSuccess(code);
      String message = body.getMessage();
      String serialId = body.getBizId();
      log.info("aliyun sms response, phone:{}, code: {}, message: {}, serialId: {}", phones, code, message, serialId);
      rsp.setSuccess(success);
      rsp.setMessage(message);
      rsp.setPhones(zipPhoneResponse(request, success, message, msgId, serialId));
    } catch (Exception e) {
      log.error("send sms failed.", e);
      rsp.setSuccess(false);
      rsp.setMessage(e.getMessage());
      rsp.setPhones(zipPhoneResponse(request, false, e.getMessage(), msgId, ""));
    }
    return SmsUtils.initResult(request, rsp, config, template, signName);
  }

  private String zhSignName(SmsProperties.AliYunConfig config, boolean international) {
    if (international) {
      return StringUtils.defaultIfEmpty(config.getIntlZhSignName(), config.getIntlEnSignName());
    }
    return StringUtils.defaultIfEmpty(config.getZhSignName(), config.getEnSignName());
  }

  private String enSignName(SmsProperties.AliYunConfig config, boolean international) {
    if (international) {
      return StringUtils.defaultIfEmpty(config.getIntlEnSignName(), config.getIntlZhSignName());
    }
    return StringUtils.defaultIfEmpty(config.getEnSignName(), config.getZhSignName());
  }

  /**
   * 申请创建新模板
   */
  public SmsTemplateDetail createSmsTemplate(SmsTemplateRequest request) {
    //阿里云当前最新接口
    try {
      log.info("aliyun create template source request: {}", request);
      CreateSmsTemplateRequest aliRequest = new CreateSmsTemplateRequest();
      String content = request.getContent();
      //实际申请时，替换掉不合法的变量
      Map<String, String> keyReplaces = SmsUtils.paramKeyForReplace(content, SmsUtils::aliyunTemplateParamReplacer);
      aliRequest.setTemplateContent(replaceTemplateContent(content, keyReplaces, (s, e) -> aliyunKeyReplacer(s, e.getKey(), e.getValue())));
      aliRequest.setTemplateType(aliyunTemplateType(request));
      aliRequest.setRemark(request.getRemark());
      aliRequest.setTemplateName(request.getName());
      // 创建模板时，必须关联签名，从而关联到签名对应的资质信息，提升审核效率。
      aliRequest.setRelatedSignName(AliyunUtils.relatedSignName(request, config));
      //设置每个变量的类型
      aliRequest.setTemplateRule(convertTemplateAttributeRules(request, keyReplaces));
      if (request.isInternational()) {
        //国际/港澳台模板类型
        aliRequest.setIntlType(AliyunUtils.aliyunIntlType(request));
      }
      log.info("aliyun create template request: {}", aliRequest.toMap());
      CreateSmsTemplateResponse resp = smsClient.createSmsTemplate(aliRequest);
      //校验是否返回正常
      validateCreateSmsTemplateResponse(resp);
      String templateId = SmsUtils.randomTemplateId();
      //to mongo entity
      saveToMongo(templateId, request, resp.getBody().getTemplateCode(), keyReplaces);
      return SmsUtils.initTemplateDetail(config, request, templateId);
    } catch (Exception e) {
      log.warn("aliyun create template failed.", e);
      throw new SmsException("create template failed. " + e.getMessage());
    }
  }

  private String convertTemplateAttributeRules(SmsTemplateRequest request, Map<String, String> keyReplaces) {
    List<SmsTemplateVariableAttr> variableAttributes = request.getVariableAttributes();
    if (CollectionUtils.isNotEmpty(variableAttributes)) {
      // 模板变量规则。{"code":"characterWithNumber"}，我们对变量还做了转换呢
      // See https://help.aliyun.com/zh/sms/templaterule-template-variable-parameter-filling-example?spm=a2c4g.11186623.0.0.aba27f57ikbE0F
      Map<String, String> rules = variableAttributes.stream()
        .collect(Collectors.toMap(
          attr -> keyReplaces.getOrDefault(attr.getName(), attr.getName()),
          SmsTemplateVariableAttr::getType,
          (o1, o2) -> o1
        ));
      return JsonUtil.toJson(rules);
    }
    return null;
  }

  private void validateCreateSmsTemplateResponse(CreateSmsTemplateResponse resp) {
    log.info("aliyun create template response: {}", resp.toMap());
    CreateSmsTemplateResponseBody body = resp.getBody();
    if (Objects.isNull(body)) {
      throw new SmsException("add template failed. " + resp.getStatusCode());
    }
    if (!AliyunUtils.isSuccess(body.getCode())) {
      throw new SmsException("add template failed. " + body.getMessage());
    }
    if (StringUtils.isEmpty(body.getTemplateCode())) {
      throw new SmsException("add template failed, empty template code. " + body.getMessage());
    }
  }

  public SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    return createSmsTemplate(request);
  }

  private void saveToMongo(String templateId, SmsTemplateRequest request, String templateCode, Map<String, String> keyReplaces) {
    SmsTemplateEntity entity = SmsUtils.initTemplateEntity(config, templateId, templateCode, request, keyReplaces);
    smsDao.save(entity);
  }

  /**
   * 查询并更新短信状态
   */
  public void updateSmsStatus() {
    smsDao.querySmsWithNoReplay(config.getId())
      .stream()
      // TTS记录也混杂在里面，我们还不支持查TTS状态
      .filter(Predicate.not(e -> SMS_TYPE_TTS.equals(e.getSmsType())))
      .filter(e -> StringUtils.isNotEmpty(e.getSerialId()))
      //防止接口限流，这一批拉不到放到下次再查
      .limit(100)
      .forEach(this::queryAndUpdateSmsStatus);
  }

  private void queryAndUpdateSmsStatus(SmsMongoEntity entity) {
    try {
      QuerySendDetailsRequest request = new QuerySendDetailsRequest();
      request.setBizId(entity.getSerialId());
      request.setPhoneNumber(entity.getPhone());
      request.setSendDate(DateFormatUtils.format(entity.getSendTime(), "yyyyMMdd"));
      request.setPageSize(10L);
      request.setCurrentPage(1L);
      QuerySendDetailsResponse response = smsClient.querySendDetails(request);
      QuerySendDetailsResponseBody body = response.getBody();
      if (Objects.isNull(body) || Objects.isNull(body.getSmsSendDetailDTOs())) {
        log.info("query sms status, response is empty: {}", response);
        return;
      }
      body.getSmsSendDetailDTOs()
        .getSmsSendDetailDTO()
        .forEach(e -> updateSmsStatus(e, entity));
    } catch (Exception e) {
      log.warn("query sms status failed.", e);
    }
  }

  private void updateSmsStatus(QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO dto, SmsMongoEntity old) {
    try {
      Long status = dto.getSendStatus();
      String errCode = dto.getErrCode();
      log.info("aliyun sms status:{},code:{},phone:{}", status, errCode, dto.getPhoneNum());
      if (Objects.isNull(status)) {
        return;
      }
      SmsMongoEntity entity = null;

      //处理等待回执的状态(如果是等待回执但是还没有超时，不能更新数据库，下次轮训查到状态再更新)
      if (AliyunUtils.isWaiting(status)) {
        if (AliyunUtils.isWaitingTimeout(old.getSendTime())) {
          entity = smsDao.updateSingleSmsStatus(config.getId(), old.getSerialId(), old.getPhone(),
            false,
            new Date(),
            "Timeout", "Timeout waiting for user terminal response status"
          );
        }
        //等待回执并且未超时，不更新数据库，下次轮训
      } else {
        entity = smsDao.updateSingleSmsStatus(config.getId(), old.getSerialId(), old.getPhone(),
          AliyunUtils.isSendSuccess(status),
          AliyunUtils.safeStrToDate(dto.getReceiveDate()),
          errCode, errCode
        );
      }

      SmsUtils.sendSmsStatusEvent(entity, smsProducer);

    } catch (Exception e) {
      log.warn("aliyun sms status update failed.", e);
    }
  }

  /**
   * 查询并更新模板状态
   */
  public void updateTemplateStatus() {
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setProviderId(config.getId());
    query.setStatus(String.join(",", TEMPLATE_STATUS_INIT, TEMPLATE_STATUS_APPROVING, TEMPLATE_STATUS_REJECTED));
    // 6个月前的记录
    Date sixMonthsAgo = DateUtils.addMonths(new Date(), -6);
    smsDao.queryTemplates(query)
      .stream()
      .filter(e -> e.getSendTime().after(sixMonthsAgo))
      .forEach(this::queryAndUpdateTemplateStatus);
  }

  /**
   * 查询并更新模版状态
   */
  private void queryAndUpdateTemplateStatus(SmsTemplateEntity entity) {
    try {
      log.debug("try get template status, entity: {}", entity);
      GetSmsTemplateRequest request = new GetSmsTemplateRequest();
      request.setTemplateCode(entity.getProviderTemplateId());
      GetSmsTemplateResponse response = smsClient.getSmsTemplate(request);
      log.info("aliyun query template status response: {}", response);
      GetSmsTemplateResponseBody body = response.getBody();
      if (Objects.isNull(body)) {
        log.warn("aliyun query template status failed. {}", response);
        return;
      }
      String status = body.getTemplateStatus();
      // 状态转换成标准的定义
      entity.setStatus(SmsUtils.templateStatus(config.getTemplateStatus(), status));
      GetSmsTemplateResponseBody.GetSmsTemplateResponseBodyAuditInfo audit = body.getAuditInfo();
      if (Objects.nonNull(audit)) {
        entity.setReply(audit.getRejectInfo());
      }
      entity.setUpdateTime(new Date());
      smsDao.save(entity);
      sendTemplateEvent(entity);
    } catch (Exception e) {
      log.warn("aliyun update template status failed.", e);
      updateDeletedStatus(entity, e);
    }
  }

  private void updateDeletedStatus(SmsTemplateEntity entity, Exception e) {
    if (e instanceof TeaException tea && "404".equals(tea.getCode()) && tea.getMessage().contains("The template does not exist")) {
      log.warn("aliyun template not exist, will set status to deleted, entity: {}", entity);
      entity.setStatus(SmsSdkConstants.TEMPLATE_STATUS_DELETED);
      entity.setUpdateTime(new Date());
      smsDao.save(entity);
      sendTemplateEvent(entity);
    }
  }

  private void sendTemplateEvent(SmsTemplateEntity entity) {
    if (Objects.nonNull(smsProducer)) {
      //发送MQ通知，通知有模板状态变更
      log.info("aliyun send template status mq, entity: {}", entity);
      smsProducer.send(new Message(MQ_TOPIC_SMS_TEXT, MQ_TAG_TEMPLATE_STATUS, JsonUtil.toBytes(entity)));
    }
  }

  public List<SmsStaticTemplate> queryStaticTemplates(SmsTemplateQuery query) {
    return config.getTemplates()
      .values()
      .stream()
      .map(e -> SmsUtils.appendStaticTemplate(config, e))
      .filter(e -> filterSmsTemplate(query, e))
      .toList();
  }

}
