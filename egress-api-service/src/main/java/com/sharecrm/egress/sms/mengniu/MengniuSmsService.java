package com.sharecrm.egress.sms.mengniu;

import com.alibaba.fastjson.TypeReference;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sms.AliyunUtils;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.JsonUtil;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.rocketmq.common.message.Message;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TAG_TEMPLATE_STATUS;
import static com.sharecrm.egress.sdk.SmsSdkConstants.MQ_TOPIC_SMS_TEXT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_APPROVING;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_INIT;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_STATUS_REJECTED;
import static com.sharecrm.egress.sms.AliyunUtils.reallyTemplateParamJson;
import static com.sharecrm.egress.sms.AliyunUtils.selectSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.aliyunKeyReplacer;
import static com.sharecrm.egress.sms.SmsUtils.chooseSign;
import static com.sharecrm.egress.sms.SmsUtils.filterSmsTemplate;
import static com.sharecrm.egress.sms.SmsUtils.replaceTemplateContent;
import static com.sharecrm.egress.sms.SmsUtils.zipPhoneResponse;

/**
 * 对接蒙牛消息中心，注意只在蒙牛云可用
 */
@Slf4j
@Service
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.mengniu.enabled", havingValue = "true")
public class MengniuSmsService {

  private final SmsProperties properties;
  private final OkHttpSupport httpClient;
  private final SmsDao smsDao;
  private final ObjectProvider<AutoConfMQProducer> smsProducer;

  public MengniuSmsService(SmsProperties properties, @Qualifier("mengniuHttpSupport") OkHttpSupport httpClient, SmsDao smsDao,
                           @Qualifier("smsRocketMQProducer") ObjectProvider<AutoConfMQProducer> smsProducer) {
    this.properties = properties;
    this.httpClient = httpClient;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
    log.info("mengniu sms enabled, config: {}", config());
  }

  /**
   * 发送国内或国际短信
   */
  public SmsSendResult sendSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setInternational(wrapper.isInternational());

    SmsSendRequest request = wrapper.getRequest();
    //去掉签名，内容中不需要包含签名，而且选择模板的时候无法匹配
    request.setContent(SmsUtils.replaceSign(request.getContent(), config().getZhSignName(), config().getEnSignName()));

    Tuple2<SmsSendByTemplate, SmsTemplateEntity> result = selectSmsTemplate(phone, request, queryStaticTemplates(query));
    SmsSendResult sendResult = sendByTemplate(wrapper, result.getT1(), result.getT2());
    sendResult.setRequest(request);
    return sendResult;
  }

  /**
   * 使用模板发送短信
   */
  public SmsSendResult sendByTemplate(SmsRequestWrapper<?> wrapper, SmsSendByTemplate request, SmsTemplateEntity template) {
    SmsSendResponse rsp = new SmsSendResponse();
    rsp.setBatchMsgId(Objects.toString(wrapper.getBatchMsgId(), NanoIdUtils.randomNanoId()));
    // 此时可能为null，为null时每个手机号new一次新ID
    String msgId = wrapper.getMsgId();
    MengniuRequest<MengniuSmsRequest> mengniuReq = smsSendRequestWrapper(request, template);
    try {
      Request req = convertOkHttpRequest(apiUrl(config().getSmsSendPath()), mengniuReq);
      MengniuResponse<MengniuSmsResponse> resp = httpClient.parseObject(req, new TypeReference<>() {
      });
      log.info("mengniu sms http call, phone: {}, result: {}", request.getPhones(), resp);
      String serialId = serialId(resp);
      //有时候参数不合法，返回是也是success，但是实际上失败，所以又加了serialId判断
      boolean success = resp.isSuccess() && StringUtils.isNotEmpty(serialId);
      String message = respMessage(resp);
      rsp.setSuccess(success);
      rsp.setMessage(message);
      rsp.setPhones(zipPhoneResponse(request, success, message, msgId, serialId));
    } catch (Exception e) {
      log.error("send sms failed.", e);
      rsp.setSuccess(false);
      rsp.setMessage(e.getMessage());
      rsp.setPhones(zipPhoneResponse(request, false, e.getMessage(), msgId, ""));
    }
    SmsSendResult sendResult = new SmsSendResult();
    sendResult.setProvider(config());
    sendResult.setTemplateRequest(request);
    sendResult.setResponse(rsp);
    sendResult.setContent(SmsUtils.renderTemplate(template.getContent(), request.getTemplateParam()));
    sendResult.setSign(mengniuReq.getDatas().getSignName());
    sendResult.setSmsType(template.getTemplateType());
    return sendResult;
  }

  private String serialId(MengniuResponse<MengniuSmsResponse> resp) {
    if (resp.isSuccess() && Objects.nonNull(resp.getData()) && Objects.nonNull(resp.getData().getDatas())) {
      return resp.getData().getDatas().getBizId();
    }
    return null;
  }

  /**
   * 申请创建新模板
   */
  public SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    try {
      log.info("mengniu add template source request: {}", request);
      Tuple2<MengniuRequest<MengniuTemplateRequest>, Map<String, String>> rs = convertTemplateRequest(request);

      Request req = convertOkHttpRequest(apiUrl(config().getAddTemplatePath()), rs.getT1());
      MengniuResponse<MengniuTemplateResp> resp = httpClient.parseObject(req, new TypeReference<>() {
      });
      //校验是否返回正常
      validateTemplateResp(resp);
      String templateId = SmsUtils.randomTemplateId();
      //to mongo entity
      saveToMongo(templateId, request, resp.getData(), rs.getT2());
      return SmsUtils.initTemplateDetail(config(), request, templateId);
    } catch (Exception e) {
      log.warn("mengniu add template failed.", e);
      throw new SmsException("add template failed. " + e.getMessage());
    }
  }


  Tuple2<MengniuRequest<MengniuTemplateRequest>, Map<String, String>> convertTemplateRequest(SmsTemplateRequest request) {
    MengniuTemplateRequest body = new MengniuTemplateRequest();
    String content = request.getContent();
    Map<String, String> keyReplaces = SmsUtils.paramKeyForReplace(content, SmsUtils::aliyunTemplateParamReplacer);
    //实际申请时，替换掉不合法的变量
    body.setTemplateContent(replaceTemplateContent(content, keyReplaces, (s, e) -> aliyunKeyReplacer(s, e.getKey(), e.getValue())));
    body.setTemplateType(mengniuTemplateType(request.getTemplateType()));
    body.setTemplateName(request.getName());
    body.setRemark(request.getRemark());
    //封装到data字段里
    MengniuRequest<MengniuTemplateRequest> requestForData = requestForData(body);
    log.info("mengniu add template request: {}", requestForData);
    return Tuples.of(requestForData, keyReplaces);
  }

  public List<SmsStaticTemplate> queryStaticTemplates(SmsTemplateQuery query) {
    return config().getTemplates()
      .values()
      .stream()
      .map(e -> SmsUtils.appendStaticTemplate(config(), e))
      .filter(e -> filterSmsTemplate(query, e))
      .toList();
  }

  /**
   * 查询并更新模板状态
   */
  public void updateTemplateStatus() {
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setProviderId(config().getId());
    String status = String.join(",", TEMPLATE_STATUS_INIT, TEMPLATE_STATUS_APPROVING, TEMPLATE_STATUS_REJECTED);
    query.setStatus(status);
    Date sixMonthsAgo = DateUtils.addMonths(new Date(), -6);
    smsDao.queryTemplates(query)
      .stream()
      .filter(e -> e.getSendTime().after(sixMonthsAgo))
      .forEach(this::queryAndUpdateTemplateStatus);
  }

  /**
   * 从蒙牛查询并更新模版状态
   */
  private void queryAndUpdateTemplateStatus(SmsTemplateEntity entity) {
    try {
      Map<String, String> params = Map.of("action", "QuerySmsTemplate", "templateCode", entity.getProviderTemplateId());
      Request req = convertOkHttpRequest(apiUrl(config().getQueryTemplatePath()), requestForData(params));
      MengniuResponse<MengniuTemplateStatusResp> resp = httpClient.parseObject(req, new TypeReference<>() {
      });
      log.info("mengniu query template status response. {}", resp);
      if (!resp.isSuccess() || Objects.isNull(resp.getData()) || !resp.getData().isSuccess()) {
        log.warn("mengniu query template status failed. {}", resp);
        return;
      }
      String status = resp.getData().getDatas().getStatus();
      // 状态转换成标准的定义
      entity.setStatus(SmsUtils.templateStatus(config().getTemplateStatus(), status));
      entity.setReply(resp.getData().getDatas().getFailReason());
      entity.setUpdateTime(new Date());
      smsDao.save(entity);
      //发送MQ通知，通知有模板状态变更
      smsProducer.ifAvailable(s -> {
        log.info("mengniu send template status mq, entity: {}", entity);
        s.send(new Message(MQ_TOPIC_SMS_TEXT, MQ_TAG_TEMPLATE_STATUS, JsonUtil.toBytes(entity)));
      });
    } catch (Exception e) {
      log.warn("mengniu update template status failed.", e);
    }
  }

  private void validateTemplateResp(MengniuResponse<MengniuTemplateResp> resp) {
    log.info("mengniu sms http call result: {}", resp);
    if (!resp.isSuccess()) {
      throw new SmsException("add template failed. " + resp.getMsg());
    }
    MengniuTemplateResp data = resp.getData();
    if (Objects.isNull(data)) {
      throw new SmsException("add template failed. " + resp.getMsg());
    }
    if (!data.isSuccess() || StringUtils.isBlank(data.getDatas())) {
      throw new SmsException("add template failed. " + data.getErrMsg());
    }
  }

  private void saveToMongo(String templateId, SmsTemplateRequest request, MengniuTemplateResp respData, Map<String, String> keyReplaceMap) {
    SmsTemplateEntity entity = SmsUtils.initTemplateEntity(config(), templateId, respData.getDatas(), request, keyReplaceMap);
    smsDao.save(entity);
  }

  private String mengniuTemplateType(String src) {
    //转成蒙牛API需要的类型
    return config().getTemplateTypes().getOrDefault(src, src);
  }

  @NotNull
  private Request convertOkHttpRequest(String url, Object request) {
    log.info("mengniu okhttp request: {}", request);
    //根据对接文档拼接Header和请求体
    long timestamp = System.currentTimeMillis();
    String clientId = config().getClientId();
    return WebUtils.okHttpJsonPost(url, request,
      Map.of("clientID", clientId,
        "sign", sign(clientId, config().getClientSecret(), timestamp),
        "t", timestamp + ""));
  }

  private String apiUrl(String path) {
    return config().getApiGateway() + path;
  }

  private String respMessage(MengniuResponse<MengniuSmsResponse> resp) {
    MengniuSmsResponse data = resp.getData();
    if (data != null && data.getDatas() != null) {
      return data.getDatas().getMessage();
    }
    if (data != null) {
      return data.getErrMsg();
    }
    return resp.getMsg();
  }

  MengniuRequest<MengniuSmsRequest> smsSendRequestWrapper(SmsSendByTemplate sendRequest, SmsTemplateEntity template) {
    // 国内短信请求体
    MengniuSmsRequest request = new MengniuSmsRequest();
    String phones = String.join(",", sendRequest.getPhones());
    request.setPhoneNumbers(phones);

    //当前还没有准备英文签名，用中文占位
    String zhSign = config().getZhSignName();
    String enSign = Objects.toString(config().getEnSignName(), zhSign);
    // 根据目标内容选择签名
    request.setSignName(chooseSign(sendRequest.getLanguage(), template.getContent(), sendRequest.getTemplateParam(), zhSign, enSign));
    request.setTemplateCode(template.getProviderTemplateId());
    //替换冲突部分内容，去除多余符号，替换成真实的key
    request.setTemplateParam(reallyTemplateParamJson(sendRequest, template));
    return requestWithDatas(request);
  }

  private <T> MengniuRequest<T> requestWithDatas(T request) {
    //将数据放到datas字段里
    MengniuRequest<T> rs = new MengniuRequest<>();
    rs.setAccountId(config().getAccountId());
    rs.setDatas(request);
    return rs;
  }

  private <T> MengniuRequest<T> requestForData(T request) {
    //将数据放到data字段里，注意和datas区别开
    MengniuRequest<T> rs = new MengniuRequest<>();
    rs.setAccountId(config().getAccountId());
    rs.setData(request);
    return rs;
  }

  private String sign(String clientId, String clientSecret, long timestamp) {
    return SmsUtils.getMd5(clientId + clientSecret + timestamp).toUpperCase();
  }

  private SmsProperties.MengniuConfig config() {
    return properties.getMengniu();
  }

  /**
   * 蒙牛短信状态回调
   */
  public void smsStatusCallback(String body, ServerWebExchange exchange) {
    try {
      checkCallback(body, exchange);
      JsonUtil.jsonToListMap(body).forEach(this::updateSmsStatus);
    } catch (Exception e) {
      log.warn("mengniu sms status callback failed.", e);
    }
  }

  void checkCallback(String body, ServerWebExchange exchange) {
    if (StringUtils.isBlank(body)) {
      log.warn("mengniu sms status callback failed. missing body.");
      throw new SmsException("mengniu sms status callback failed. missing body.");
    }

    HttpHeaders headers = exchange.getRequest().getHeaders();
    String ak = headers.getFirst("ak");
    String timestamp = headers.getFirst("timestamp");
    String token = headers.getFirst("token");

    // 校验是否包含ak、timestamp、token签名
    if (StringUtils.isBlank(ak) || StringUtils.isBlank(timestamp) || StringUtils.isBlank(token)) {
      log.warn("mengniu sms status callback failed. missing headers: {}", headers);
      throw new SmsException("mengniu sms status callback failed. missing headers.");
    }
    if (!ak.equals(config().getCallbackId())) {
      log.warn("mengniu sms status callback failed. invalid ak: {}", ak);
      throw new SmsException("mengniu sms status callback failed. invalid ak.");
    }

    //校验签名，回调的签名算法，注意和调用API时的签名顺序是不同的
    String reallyToken = SmsUtils.getMd5(timestamp + config().getCallbackId() + config().getCallbackSecret());
    if (!token.equalsIgnoreCase(reallyToken)) {
      log.warn("mengniu sms status callback failed. invalid token. token: {}", token);
      throw new SmsException("mengniu sms status callback failed. invalid token.");
    }
    log.info("mengniu sms status callback success. body: {}", body.replaceAll("[\r\n]", ""));
  }

  private void updateSmsStatus(Map<String, String> map) {
    try {
      SmsMongoEntity entity = smsDao.updateSingleSmsStatus(config().getId(), map.get("biz_id"), map.get("phone_number"),
        Boolean.parseBoolean(map.get("success")),
        AliyunUtils.safeStrToDate(map.get("report_time")),
        map.get("err_code"),
        map.get("err_msg")
      );
      SmsUtils.sendSmsStatusEvent(entity, smsProducer.getIfAvailable());
    } catch (Exception e) {
      log.warn("monternet sms status update failed.", e);
    }
  }

}
