package com.sharecrm.egress.sms.share;

import lombok.Data;

/**
 * 梦网国际短信通道(新版V5API)请求消息体
 */
@Data
public class MonternetIntlV5Request {

  /**
   * 用户账号
   */
  private String userid;
  
  private String pwd;
  private String mobile;

  /**
   * 短信内容：最大支持 3000 个字。使用 UrlEncode 编码 UTF-8 的消息内容。
   */
  private String content;

  /**
   * MD5 加密方式必填该参数,时间戳：24 小时制,格式：MMDDHHMMSS
   */
  private String timestamp;

  /**
   * 用户自定义流水号
   */
  private String custid;

}
