package com.sharecrm.egress.sms.dingqiao;

import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 鼎桥通信短信和模板状态查询
 */
@Slf4j
@Component
@ConditionalOnBean(DingqiaoAliyunSmsSender.class)
public class DingqiaoAliyunStatusTask implements SmsBeanIgnoreDestroy {

  private final DingqiaoAliyunSmsSender sender;

  public DingqiaoAliyunStatusTask(DingqiaoAliyunSmsSender sender) {
    this.sender = sender;
  }

  @ConditionalOnProperty(name = "sharecrm.api.sms.dingqiao-ali.sms.task.enabled", havingValue = "true", matchIfMissing = true)
  @Scheduled(initialDelay = 60, fixedDelayString = "${sharecrm.api.sms.dingqiao-ali.sms.delay:60}", timeUnit = TimeUnit.SECONDS)
  @SchedulerLock(name = "DingqiaoAliyunSmsStatus", lockAtLeastFor = "30s", lockAtMostFor = "10m")
  public void updateSmsStatus() {
    log.info("run dingqiao aliyun sms status task");
    sender.updateSmsStatus();
  }

  @ConditionalOnProperty(name = "sharecrm.api.sms.dingqiao-ali.template.task.enabled", havingValue = "true", matchIfMissing = true)
  @Scheduled(initialDelay = 300, fixedDelayString = "${sharecrm.api.sms.dingqiao-ali.template.delay:600}", timeUnit = TimeUnit.SECONDS)
  @SchedulerLock(name = "DingqiaoAliyunTplStatus", lockAtLeastFor = "300s", lockAtMostFor = "20m")
  public void updateTemplateStatus() {
    log.info("run dingqiao aliyun template status task");
    sender.updateTemplateStatus();
  }

}