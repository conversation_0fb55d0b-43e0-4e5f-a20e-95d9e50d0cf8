package com.sharecrm.egress.sms.zsj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.config.SmsProperties.ZhaoShangConfig;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.WebUtils;
import io.micrometer.core.annotation.Counted;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Credentials;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

import static com.sharecrm.egress.sms.SmsUtils.zsjReplaceContent;

/**
 * 对接招商云集团的消息中心（由PaaS平台提供），注意招商云只为招商局私有化部署可用，纷享本身不会用
 */
@Slf4j
@Service
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.zhaoshang.enabled", havingValue = "true")
public class ZsjCmhkSmsService {

  private final SmsProperties properties;

  private final OkHttpSupport httpClient;

  private final PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

  private final LoadingCache<String, String> tokenCache = Caffeine.newBuilder()
    // 注意过期时间要小于招商云默认的过期时间
    .expireAfterWrite(3000, TimeUnit.SECONDS)
    .build(key -> createAccessToken());

  public ZsjCmhkSmsService(SmsProperties properties, @Qualifier("smsHttpSupport") OkHttpSupport httpClient) {
    this.properties = properties;
    this.httpClient = httpClient;
  }


  @Counted(value = Constants.METRICS_SMS, extraTags = {"channel", "zsj"})
  public SmsSendResult sendSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    SmsSendResult rs = doSmsSend(phone, wrapper, smsSendRequest(phone, request));
    rs.setRequest(request);
    return rs;
  }

  @Counted(value = Constants.METRICS_SMS, extraTags = {"channel", "zsj"})
  public SmsSendResult sendIntlSms(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = wrapper.getRequest();
    SmsSendResult rs = doSmsSend(phone, wrapper, intlSmsSendRequest(phone, request));
    rs.setRequest(request);
    return rs;
  }

  @Counted(value = Constants.METRICS_EMAIL, extraTags = {"channel", "zsj"})
  public EmailSendResponse sendEmail(EmailSendRequest request) {
    EmailSendResponse result = new EmailSendResponse();
    try {
      ZsjMailRequest mailRequest = new ZsjMailRequest();
      mailRequest.setTo(String.join(";", request.getTo()));
      Optional.ofNullable(request.getCc())
        .filter(Predicate.not(List::isEmpty))
        .ifPresent(ccs -> mailRequest.setCc(String.join(";", ccs)));
      mailRequest.setSubject(request.getSubject());
      mailRequest.setBody(request.getContent());
      mailRequest.setFrom(config().getEmailFrom());

      //根据招商云短信对接文档拼接Header和请求体
      Request req = convertOkHttpRequest(config().getEmailSendUrl(), mailRequest);
      ZsjSmsResponse resp = doHttpCall(req);
      result.setSuccess(isSuccess(resp));
      result.setMessage(resp.getMessage());
      log.debug("send email success:{}", result);
    } catch (Exception e) {
      log.warn("send email failed.", e);
      result.setSuccess(false);
      result.setMessage(e.getMessage());
    }
    return result;
  }

  @NotNull
  private Request convertOkHttpRequest(String url, Object request) {
    //根据招商云对接文档拼接Header和请求体
    return WebUtils.okHttpJsonPost(url, request,
      Map.of("Authorization", bearToken(),
        "appId", config().getAppId(),
        "nonce", UUID.randomUUID().toString().replace("-", ""),
        "timestamp", String.valueOf(System.currentTimeMillis() / 1000)));
  }

  private SmsSendResult doSmsSend(String phone, SmsRequestWrapper<SmsSendRequest> wrapper, ZsjSmsRequest request) {
    try {
      //根据招商云短信对接文档拼接Header和请求体
      Request req = convertOkHttpRequest(config().getSmsSendUrl(), request);
      ZsjSmsResponse resp = doHttpCall(req);
      boolean success = isSuccess(resp);
      if (success) {
        return SmsUtils.success(config(), wrapper, phone, "none");
      } else {
        return SmsUtils.failed(config(), wrapper, phone, resp.getMessage());
      }
    } catch (Exception e) {
      log.warn("send sms failed.", e);
      return SmsUtils.failed(config(), wrapper, phone, e.getMessage());
    }
  }

  private ZsjSmsRequest intlSmsSendRequest(String phone, SmsSendRequest req) {
    ZsjSmsRequest request = new ZsjSmsRequest();
    String param = req.getContent();
    //替换特定字符
    request.setParams(new String[]{zsjReplaceContent(param)});
    request.setSignId(config().getEnSignId());
    request.setTemplateId(config().getEnTemplateId());

    try {
      PhoneNumber phoneNumber = phoneUtil.parse(phone, "CH");
      //招商云国际电话区号和号码要分开
      request.setCountryCode(String.valueOf(phoneNumber.getCountryCode()));
      request.setMobile(String.valueOf(phoneNumber.getNationalNumber()));
      //如果是国内86短信，强制转为国内模板，招商云消息中心不支持把国内86当做国际短信
      if (phoneNumber.getCountryCode() == 86) {
        request.setSignId(config().getZhSignId());
        request.setTemplateId(config().getZhTemplateId());
      }

    } catch (NumberParseException e) {
      log.error("invalid intl phone number:{}", phone, e);
      //使用原始值尝试
      request.setMobile(phone);
    }
    log.debug("send intl sms:{}", request);
    return request;
  }

  private ZsjSmsRequest smsSendRequest(String phone, SmsSendRequest req) {
    ZsjSmsRequest request = new ZsjSmsRequest();
    request.setMobile(phone);
    String param = req.getContent();
    //替换特定字符
    request.setParams(new String[]{zsjReplaceContent(param)});
    request.setSignId(config().getZhSignId());
    request.setTemplateId(config().getZhTemplateId());
    return request;
  }

  private boolean isSuccess(ZsjSmsResponse resp) {
    return HttpStatus.OK.value() == resp.getCode();
  }

  private ZsjSmsResponse doHttpCall(Request request) {
    Object rs = httpClient.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        return getSmsResult(response);
      }
    });
    log.debug("sms http call result: {}", rs);
    return (ZsjSmsResponse) rs;
  }

  private ZsjSmsResponse getSmsResult(Response response) {
    String body = null;
    try {
      log.info("sms response code: {}, message: {}", response.code(), response.message());
      body = Objects.requireNonNull(response.body()).string();
      return JSON.parseObject(body, ZsjSmsResponse.class);
    } catch (Exception e) {
      log.warn("send sms failed. response code: {}, message: {}, body: {}", response.code(),
        response.message(), body, e);
    }
    throw new SmsException("sms api response failed.");
  }

  private String bearToken() {
    return "Bearer " + tokenCache.get("token");
  }

  private String createAccessToken() {
    Request request = WebUtils.okHttpJsonPost(config().getAccessTokenUrl(), tokenRequest(),
      Map.of("appId", config().getAppId(), "Authorization", basicAuth()));

    TokenResponse rs = httpClient.parseObject(request, new TypeReference<>() {
    });

    if (StringUtils.isBlank(rs.getAccessToken())) {
      log.warn("get access token failed. response: {}", rs);
      rs = httpClient.parseObject(request, new TypeReference<>() {
      });
    }
    log.debug("token response:{}", rs);
    return rs.getAccessToken();
  }

  private TokenRequest tokenRequest() {
    TokenRequest body = new TokenRequest();
    body.setUsername(config().getClientId());
    body.setPassword(config().getClientSecret());
    return body;
  }

  private String basicAuth() {
    return Credentials.basic(config().getClientId(), config().getClientSecret());
  }

  private ZhaoShangConfig config() {
    return properties.getZhaoshang();
  }

  @Data
  public static class ZsjSmsRequest {

    /**
     * 短信国际码，不填默认为国内86
     */
    private String countryCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 短信模板参数
     */
    private String[] params;

    /**
     * 模板编码
     */
    private String templateId;

    /**
     * 签名标识
     */
    private String signId;

  }

  @Data
  public static class ZsjMailRequest {

    private String to;
    private String cc;
    private String subject;
    private String body;
    private String from;
  }

  @Data
  public static class ZsjSmsResponse {

    /**
     * 200、400、500
     */
    private int code;
    private String message;
    private String data;

  }

  @Data
  public static class TokenRequest {

    @JSONField(name = "grant_type")
    private String grantType = "password";
    private String username;
    private String password;
  }

  @Data
  public static class TokenResponse {

    @JSONField(name = "access_token")
    private String accessToken;
    /**
     * token 有效期，单位秒
     */
    @JSONField(name = "expires_in")
    private Integer expiresIn;

  }

}
