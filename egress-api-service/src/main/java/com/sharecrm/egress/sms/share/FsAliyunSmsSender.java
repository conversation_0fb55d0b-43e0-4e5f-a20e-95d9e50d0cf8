package com.sharecrm.egress.sms.share;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.config.SmsProperties.AliYunConfig;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import com.sharecrm.egress.sms.aliyun.AliyunSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sharecrm.egress.sms.AliyunUtils.initAliyunSmsClient;
import static com.sharecrm.egress.sms.AliyunUtils.initAliyunTtsClient;

/**
 * 纷享默认自带的阿里云短信服务
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.ali.enabled", havingValue = "true", matchIfMissing = true)
public class FsAliyunSmsSender implements SmsSender, SmsBeanIgnoreDestroy {

  private final SmsProperties properties;
  private final SmsDao smsDao;
  private final AliyunSmsService aliyunSmsService;

  public FsAliyunSmsSender(SmsProperties properties, SmsDao smsDao,
                           @Qualifier("smsRocketMQProducer") ObjectProvider<AutoConfMQProducer> smsProducer) {
    this.properties = properties;
    this.smsDao = smsDao;
    this.aliyunSmsService = initAliyunSmsService(properties, smsProducer);
    log.info("AliYunTTS init, config:{}", properties.getAli());
  }

  private AliyunSmsService initAliyunSmsService(SmsProperties properties, ObjectProvider<AutoConfMQProducer> smsProducer) {
    AliYunConfig config = properties.getAli();
    return new AliyunSmsService(config, smsDao, smsProducer.getIfAvailable(), initAliyunTtsClient(config), initAliyunSmsClient(config));
  }

  @Override
  public SmsSendResult sendTts(TTSSendRequest request) {
    return aliyunSmsService.sendTts(request);
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    return aliyunSmsService.sendSms(phone, wrapper);
  }

  @Override
  public List<SmsStaticTemplate> staticTemplates(SmsTemplateQuery query) {
    return aliyunSmsService.queryStaticTemplates(query);
  }

  @Override
  public SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    return aliyunSmsService.addTemplate(request);
  }

  @Override
  public SmsSendResult sendByTemplate(SmsSendByTemplate request, SmsTemplateEntity template) {
    // Wrapper中的msgId置为null，以便为每个手机号生成一个独立的ID
    return aliyunSmsService.sendByTemplate(new SmsRequestWrapper<>(request, NanoIdUtils.randomNanoId(), null), request, template);
  }

  public void updateSmsStatus() {
    aliyunSmsService.updateSmsStatus();
  }

  public void updateTemplateStatus() {
    aliyunSmsService.updateTemplateStatus();
  }

  @Override
  public SmsProvider provider() {
    return properties.getAli();
  }

  @Override
  public List<String> supports() {
    return properties.getAli().getSupports();
  }

  @Override
  public int getOrder() {
    return properties.getAli().getOrder();
  }
}
