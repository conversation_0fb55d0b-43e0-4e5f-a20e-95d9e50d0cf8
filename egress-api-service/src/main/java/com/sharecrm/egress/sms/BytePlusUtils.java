package com.sharecrm.egress.sms;

import com.byteplus.error.SdkError;
import com.byteplus.http.ClientConfiguration;
import com.byteplus.http.HttpClientFactory;
import com.byteplus.model.response.ResponseMetadata;
import com.byteplus.model.response.SmsSendResponse;
import com.byteplus.service.sms.impl.SmsServiceImpl;
import com.sharecrm.egress.config.SmsProperties;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;

import java.util.Optional;

@Slf4j
@UtilityClass
public class BytePlusUtils {

  public static boolean isSuccess(SmsSendResponse resp) {
    // 即使成功了 code 也是空    
    return (SdkError.SUCCESS.getNumber() + "").equals(resp.getCode()) ||
      (StringUtils.isEmpty(resp.getCode()) &&
        Optional.ofNullable(resp.getResponseMetadata())
          .map(ResponseMetadata::getError)
          .isEmpty());
  }

  public static boolean isSendSuccess(String status) {
    return (SdkError.SUCCESS.getNumber() + "").equals(status);
  }

  public static com.byteplus.service.sms.SmsService initBytePlusSmsClient(SmsProperties.BytePlusConfig config) {
    try {
      com.byteplus.service.sms.SmsService smsService = SmsServiceImpl.getInstance(config.getRegion());
      smsService.setAccessKey(config.getAppKey());
      smsService.setSecretKey(config.getAppSecret());
      if (StringUtils.isNotBlank(config.getHttpProxy())) {
        HttpHost proxy = HttpHost.create(config.getHttpProxy());
        HttpClientFactory.ClientInstance clientInstance = HttpClientFactory.create(new ClientConfiguration(), proxy);
        smsService.setHttpClient(clientInstance.getHttpClient());
      }
      return smsService;
    } catch (Exception e) {
      log.error("init byteplus sms client failed.", e);
      return null;
    }
  }

}
