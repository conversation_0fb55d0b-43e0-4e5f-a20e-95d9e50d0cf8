package com.sharecrm.egress.sms.mengniu;

import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 蒙牛状态收集任务
 */
@Slf4j
@Component
@ConditionalOnBean(MengniuSmsService.class)
public class MengniuStatusTask {

  private final MengniuSmsService mengniuSmsService;

  public MengniuStatusTask(MengniuSmsService mengniuSmsService) {
    this.mengniuSmsService = mengniuSmsService;
  }

  /**
   * 更新模板状态，模板审核没那么快，状态更新不要太频繁
   */
  @Scheduled(initialDelay = 200, fixedDelayString = "${sharecrm.api.sms.mengniu.template.delay:600}", timeUnit = TimeUnit.SECONDS)
  @SchedulerLock(name = "MengniuTplStatus", lockAtLeastFor = "300s", lockAtMostFor = "20m")
  public void updateTemplateStatus() {
    log.info("run update mengniu template status task.");
    mengniuSmsService.updateTemplateStatus();
  }

}