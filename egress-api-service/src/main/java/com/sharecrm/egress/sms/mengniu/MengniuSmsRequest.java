package com.sharecrm.egress.sms.mengniu;

import lombok.Data;

/**
 * 发送文本短信的请求体
 */
@Data
public class MengniuSmsRequest {

  private String action = "SendSms";

  /**
   * 手机号, 手机号码之间以半角逗号（,）分隔。上限为1000个手机号码
   */
  private String phoneNumbers;

  /**
   * 签名
   */
  private String signName;

  /**
   * 短信模板
   */
  private String templateCode;

  /**
   * 短信模板中的参数, 短信模板变量对应的实际值，JSON格式。支持传入多个参数，示例：{"name":"张三","number":"1503876"}。
   */
  private String templateParam;

  /**
   * 外部流水号
   */
  private String outId;

}
