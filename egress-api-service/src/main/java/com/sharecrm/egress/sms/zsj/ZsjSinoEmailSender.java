package com.sharecrm.egress.sms.zsj;

import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.email.EmailSender;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 招商-外运环境，邮件服务
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.sino.enabled", havingValue = "true")
public class ZsjSinoEmailSender implements EmailSender {

  private final ZsjSinoSmsService zsjSmsService;

  public ZsjSinoEmailSender(ZsjSinoSmsService zsjSmsService) {
    this.zsjSmsService = zsjSmsService;
  }

  @Override
  public EmailSendResponse send(EmailSendRequest req) {
    return zsjSmsService.sendEmail(req);
  }

}
