package com.sharecrm.egress.sms.tencent;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sms.SmsSender;

import java.util.List;

import static com.sharecrm.egress.sms.TencentUtils.initTencentSmsClient;

/**
 * 腾讯云短信渠道
 */
public class TencentSmsSender implements SmsSender {

  private final SmsProperties.TencentConfig config;
  private final SmsDao smsDao;
  private final TencentSmsService tencentSmsService;

  public TencentSmsSender(SmsProperties.TencentConfig config, SmsDao smsDao, AutoConfMQProducer smsProducer) {
    this.config = config;
    this.smsDao = smsDao;
    this.tencentSmsService = initTencentSmsService(smsProducer);
  }

  private TencentSmsService initTencentSmsService(AutoConfMQProducer smsProducer) {
    return new TencentSmsService(config, smsDao, smsProducer, initTencentSmsClient(config));
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    return tencentSmsService.sendSms(phone, wrapper);
  }

  @Override
  public List<SmsStaticTemplate> staticTemplates(SmsTemplateQuery query) {
    return tencentSmsService.queryStaticTemplates(query);
  }

  @Override
  public SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    return tencentSmsService.addTemplate(request);
  }

  @Override
  public SmsSendResult sendByTemplate(SmsSendByTemplate request, SmsTemplateEntity template) {
    // Wrapper中的msgId置为null，以便为每个手机号生成一个独立的ID
    return tencentSmsService.sendByTemplate(new SmsRequestWrapper<>(request, NanoIdUtils.randomNanoId(), null), request, template);
  }

  public void updateSmsStatus() {
    tencentSmsService.updateSmsStatus();
  }

  public void updateTemplateStatus() {
    tencentSmsService.updateTemplateStatus();
  }

  @Override
  public SmsProvider provider() {
    return config;
  }

  @Override
  public List<String> supports() {
    return config.getSupports();
  }

  @Override
  public int getOrder() {
    return config.getOrder();
  }

  @Override
  public int getWeight() {
    return config.getWeight();
  }
  
}
