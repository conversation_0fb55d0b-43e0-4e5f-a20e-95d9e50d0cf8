package com.sharecrm.egress.sms;

import com.sharecrm.egress.entity.SmsPageRequest;
import com.sharecrm.egress.entity.SmsPageResult;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsSign;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 短信服务总调用入口
 */
public interface SmsService {

  /**
   * 发送国内短信
   *
   * @param request  短信内容
   * @param exchange exchange
   * @return 发送结果
   */
  Mono<SmsSendResponse> sendChineseSms(SmsSendRequest request, ServerWebExchange exchange);

  /**
   * 发送国际和港澳台短信
   *
   * @param request  国际短信内容
   * @param exchange exchange
   * @return 发送结果
   */
  Mono<SmsSendResponse> sendIntlSms(SmsSendRequest request, ServerWebExchange exchange);

  /**
   * 语音短信验证码，使用验证码模板发送语音
   *
   * @param request  request
   * @param exchange exchange
   * @return 发送结果
   */
  Mono<SmsSendResponse> sendCodeCall(SmsSendRequest request, ServerWebExchange exchange);

  /**
   * 语音服务
   *
   * @param request  request
   * @param exchange exchange
   * @return 发送结果
   */
  Mono<SmsSendResponse> sendTts(TTSSendRequest request, ServerWebExchange exchange);

  /**
   * 按指定模板发送短信
   *
   * @param request  request
   * @param exchange exchange
   * @return 发送结果
   */
  default Mono<SmsSendResponse> sendByTemplate(SmsSendByTemplate request, ServerWebExchange exchange) {
    return Mono.empty();
  }

  /**
   * 新增短信模板
   *
   * @param request  request
   * @param exchange exchange
   * @return 回复结果
   */
  default Mono<EgressApiResponse<SmsTemplateDetail>> addTemplate(SmsTemplateRequest request, ServerWebExchange exchange) {
    return Mono.empty();
  }

  /**
   * 查询短信模板列表
   *
   * @param request  request
   * @param exchange exchange
   * @return 回复结果
   */
  default Mono<List<SmsTemplateDetail>> queryTemplates(SmsTemplateQuery request, ServerWebExchange exchange) {
    return Mono.empty();
  }

  /**
   * 查询企业定制的短信服务商，只返回定制的，默认的通道不返回
   *
   * @param enterpriseId EI
   * @param exchange     exchange
   * @return 回复结果
   */
  default Mono<List<SmsProvider>> queryExclusiveProvider(Integer enterpriseId, ServerWebExchange exchange) {
    return Mono.just(List.of());
  }

  /**
   * 查询短信模板详情，比如审核状态
   *
   * @param id       id
   * @param exchange exchange
   * @return 回复结果
   */
  default Mono<SmsTemplateDetail> queryTemplate(String id, ServerWebExchange exchange) {
    return Mono.empty();
  }

  /**
   * 查询签名列表
   *
   * @return 签名列表
   */
  default Mono<List<SmsSign>> listSigns(String providerId) {
    return Mono.just(List.of());
  }

  /**
   * 查询短信发送记录
   *
   * @param request request
   * @return 短信历史记录
   */
  Mono<SmsPageResult> queryHistories(SmsPageRequest request);

  /**
   * 查询短信发送记录，隐藏敏感信息
   *
   * @param request request
   * @return 短信历史记录
   */
  Mono<SmsPageResult> querySmsStatus(SmsPageRequest request);

}
