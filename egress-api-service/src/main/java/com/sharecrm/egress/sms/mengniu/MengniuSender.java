package com.sharecrm.egress.sms.mengniu;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;

/**
 * 蒙牛国内短信通道
 */
@Slf4j
@Service
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.mengniu.enabled", havingValue = "true")
public class MengniuSender implements SmsSender, SmsBeanIgnoreDestroy {

  private final MengniuSmsService mengniuSmsService;
  private final SmsProperties properties;

  public MengniuSender(MengniuSmsService mengniuSmsService, SmsProperties properties) {
    this.mengniuSmsService = mengniuSmsService;
    this.properties = properties;
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    return mengniuSmsService.sendSms(phone, wrapper);
  }

  @Override
  public SmsTemplateDetail addTemplate(SmsTemplateRequest request) {
    return mengniuSmsService.addTemplate(request);
  }

  @Override
  public SmsSendResult sendByTemplate(SmsSendByTemplate request, SmsTemplateEntity template) {
    // Wrapper中的msgId置为null，以便为每个手机号生成一个独立的ID
    return mengniuSmsService.sendByTemplate(new SmsRequestWrapper<>(request, NanoIdUtils.randomNanoId(), null), request, template);
  }

  @Override
  public void smsStatusCallback(String ext, String body, ServerWebExchange exchange) {
    mengniuSmsService.smsStatusCallback(body, exchange);
  }

  @Override
  public List<SmsStaticTemplate> staticTemplates(SmsTemplateQuery query) {
    return mengniuSmsService.queryStaticTemplates(query);
  }

  @Override
  public SmsProvider provider() {
    return properties.getMengniu();
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_CHINESE, SUPPORT_ADD_TEMPLATE_CHINESE, SUPPORT_SEND_TEMPLATE_CHINESE);
  }

  @Override
  public int getOrder() {
    return properties.getMengniu().getOrder();
  }
}
