package com.sharecrm.egress.sms;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.service.LockingExecutor;
import com.sharecrm.egress.sms.aliyun.AliyunSmsSender;
import com.sharecrm.egress.sms.aliyun.AliyunStatusTask;
import com.sharecrm.egress.sms.byteplus.BytePlusSmsSender;
import com.sharecrm.egress.sms.tencent.TencentSmsSender;
import com.sharecrm.egress.sms.tencent.TencentStatusTask;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 动态短信服务运营商相关的Bean
 */
@Slf4j
@Component
@ConditionalOnSmsEnabled
public class SmsProviderRefresher implements InitializingBean {

  private final AtomicLong counter = new AtomicLong();

  private final SmsProperties properties;

  private final ConfigurableApplicationContext applicationContext;
  private final SmsDao smsDao;
  private final AutoConfMQProducer smsProducer;
  private final TaskScheduler taskScheduler;
  private final LockingExecutor lock;

  public SmsProviderRefresher(SmsProperties properties,
                              ConfigurableApplicationContext applicationContext,
                              SmsDao smsDao,
                              @Autowired(required = false) @Qualifier("smsRocketMQProducer") AutoConfMQProducer smsProducer,
                              @Qualifier("taskScheduler") TaskScheduler taskScheduler,
                              LockingExecutor lock) {
    this.properties = properties;
    this.applicationContext = applicationContext;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
    this.taskScheduler = taskScheduler;
    this.lock = lock;
  }

  @Override
  public void afterPropertiesSet() {
    registerAndDestroyBeans();
  }

  /**
   * 配置有变更时刷新bean，动态卸载和更新
   */
  @EventListener
  public void handleRefreshedEvent(RefreshScopeRefreshedEvent event) {
    log.info("config refreshed, refresh sms bean: {}", event);
    registerAndDestroyBeans();
  }

  private synchronized void registerAndDestroyBeans() {
    // 已经存在的旧bean
    Map<String, SmsSender> existSenders = applicationContext.getBeansOfType(SmsSender.class);
    Map<String, SmsStatusTask> existTasks = applicationContext.getBeansOfType(SmsStatusTask.class);
    DefaultListableBeanFactory beanFactory = beanFactory();
    // 注册Bean
    properties.getAliyuns().forEach(this::initAliBean);
    properties.getTencents().forEach(this::initTencentBean);
    properties.getByteplus().forEach(this::initBytePlusBean);

    //销毁旧Bean，注意此处可能涉及enabled的切换，所以全都销毁，先销毁task再销毁sender
    existTasks.forEach((k, v) -> {
      if (!(v instanceof SmsBeanIgnoreDestroy)) {
        log.info("sms config refresh, destroy task: {}", k);
        // Spring registerSingleton 的bean不会执行DisposableBean
        v.destroy();
        beanFactory.destroySingleton(k);
      }
    });

    existSenders.forEach((k, v) -> {
      if (!(v instanceof SmsBeanIgnoreDestroy)) {
        log.info("sms config refresh, destroy sender: {}", k);
        //用destroySingleton，不要用destroyBean，改了记得验证
        beanFactory.destroySingleton(k);
      }
    });

    log.info("sms bean refresh success");
  }

  private void initBytePlusBean(String key, SmsProperties.BytePlusConfig config) {
    if (!config.isEnabled()) {
      log.info("byteplus sms provider {} disabled", key);
      return;
    }
    log.info("init byteplus sms bean: {}", config);
    try {
      BytePlusSmsSender sender = new BytePlusSmsSender(config, smsDao, smsProducer);
      registerSingleton("byteplus.sender.bean", sender);
    } catch (Exception e) {
      log.error("byteplus sms bean init error", e);
    }
  }

  private void initTencentBean(String key, SmsProperties.TencentConfig config) {
    if (!config.isEnabled()) {
      log.info("tencent sms provider {} disabled", key);
      return;
    }
    log.info("init tencent sms bean: {}", config);

    TencentSmsSender sender = new TencentSmsSender(config, smsDao, smsProducer);
    registerSingleton("tencent.sender.bean", sender);
    if (config.isStatusTaskEnabled()) {
      TencentStatusTask task = new TencentStatusTask(config, sender, taskScheduler, lock);
      registerSingleton("tencent.task.bean", task);
    }
  }

  private void initAliBean(String key, SmsProperties.AliYunConfig config) {
    if (!config.isEnabled()) {
      log.info("aliyun sms provider {} disabled", key);
      return;
    }
    log.info("init aliyun sms bean: {}", config);

    AliyunSmsSender sender = new AliyunSmsSender(config, smsDao, smsProducer);
    registerSingleton("aliyun.sender.bean", sender);
    if (config.isStatusTaskEnabled()) {
      AliyunStatusTask task = new AliyunStatusTask(config, sender, taskScheduler, lock);
      registerSingleton("aliyun.task.bean", task);
    }

  }

  @NotNull
  private DefaultListableBeanFactory beanFactory() {
    // 只有 DefaultListableBeanFactory 才能有效 destroyBean，ConfigurableListableBeanFactory destroyBean失败
    return (DefaultListableBeanFactory) applicationContext.getBeanFactory();
  }

  void registerSingleton(String prefix, Object singletonObject) {
    // 注意prefix符合Spring bean name规范，不要随便改
    beanFactory().registerSingleton(prefix + counter.getAndIncrement(), singletonObject);
  }

}
