package com.sharecrm.egress.sms.share;

import com.sharecrm.egress.entity.RPTPack;
import com.sharecrm.egress.utils.HTTPUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 梦网客户端实现类
 */
@Slf4j
public class MonternetHttpClient {

  //参数错误(格式，范围等不符合要求
  private static final int PARAM_ERROR = -1002;

  private final String account;
  private final String password;
  private final String ip;
  private final int port;


  public MonternetHttpClient(String ip, int port, String account, String password) {
    this.ip = ip;
    this.port = port;
    this.account = account;
    this.password = password;
  }

  public int sendSms(String strMobiles, String strMessage, String strSubPort, String strUserMsgId) throws Exception {
    int returnInt = -200;
    String result;
    Params p = new Params();

    //验证账号
    if (!validateUserId(account)) {
      return PARAM_ERROR;
    }
    p.setUserId(account);

    //验证密码
    if (!validatePwd(this.password)) {
      return PARAM_ERROR;
    }
    p.setPassword(this.password);

    p.setPszMobis(strMobiles);

    //验证短信内容，不合法就返回
    if (!validateMessage(strMessage)) {
      return PARAM_ERROR;
    }
    p.setPszMsg(strMessage);

    p.setIMobiCount(String.valueOf(strMobiles.split(",").length));

    //验证扩展子号是否合法，不合法就返回
    if (!validateSubPort(strSubPort)) {
      return PARAM_ERROR;
    }
    p.setPszSubPort(strSubPort);

    p.setMsgId(strUserMsgId);

    result = sendSubmitForGet(p);
    if (result != null && result.length() > 10) {
      returnInt = 0;
    } else if (result == null || result.isEmpty()) {
      log.info("result is null or empty");
    } else {
      returnInt = Integer.parseInt(result);
    }
    return returnInt;
  }

  private String host() {
    return "http://" + ip + ":" + port;
  }

  /**
   * 短信息发送接口
   * <pre>
   *
   * 返回值：错误描述对应说明
   * 发送成功：信息编号，如：-8485643440204283743或1485643440204283743
   * 错误返回：-1	参数为空。信息、电话号码等有空指针，登陆失败
   * -2	电话号码个数超过100
   * -10	申请缓存空间失败
   * -11	电话号码中有非数字字符
   * -12	有异常电话号码
   * -13	电话号码个数与实际个数不相等
   * -14	实际号码个数超过100
   * -101	发送消息等待超时
   * -102	发送或接收消息失败
   * -103	接收消息超时
   * -200	其他错误
   * -999	web服务器内部错误
   *
   * </pre>
   */
  private String sendSubmitForGet(Params params) throws Exception {
    String result = null;
    String msg = executeGet(params, host() + "/MWGate/wmgw.asmx/MongateSendSubmit");
    try {
      if (!msg.isEmpty()) {
        Document doc = DocumentHelper.parseText(msg);
        Element el = doc.getRootElement();
        result = el.getText();
      }
    } catch (Exception e) {
      log.error("monternet xml document exception,msg:{}", msg);
      return "200";
    }
    return result;
  }


  public List<RPTPack> getRpt() {
    List<RPTPack> rptPackList = new ArrayList<>();
    try {
      String[] result;

      Params p = new Params();
      p.setUserId(account);
      p.setPassword(this.password);
      //2是状态报告
      p.setIReqType("2");

      result = getDeliverForGet(p);
      if (result != null) {
        for (String s : result) {
          String[] resultArr = s.split(",");
          RPTPack rptPack = new RPTPack();
          rptPack.setStrMoTime(resultArr[1]);
          rptPack.setStrPtMsgId(resultArr[2]);
          rptPack.setStrSpNumber(resultArr[3]);
          rptPack.setStrMobile(resultArr[4]);
          rptPack.setStrUserMsgId(resultArr[5]);
          rptPack.setStrReserve(resultArr[6]);
          rptPack.setnStatus(Integer.parseInt(resultArr[7]));
          rptPack.setStrErCode(resultArr[8]);
          rptPackList.add(rptPack);
        }
      }
    } catch (Exception e) {
      log.error("获取状态报告失败", e);
    }
    return rptPackList;
  }

  public String[] getDeliverForGet(Params params) {
    List<String> lists = new ArrayList<>();
    try {
      String msg = executeGet(params, host() + "/MWGate/wmgw.asmx/MongateGetDeliver");
      if (msg.contains("<string>")) {
        Document doc = DocumentHelper.parseText(msg);
        Element el = doc.getRootElement();
        var it = el.elementIterator();
        while (it.hasNext()) {
          Element elm = it.next();
          lists.add(elm.getText());
        }
        return lists.toArray(new String[0]);
      } else {
        log.debug("monternet xml document,msg:{}", msg);
        return null;
      }
    } catch (Exception e) {
      log.warn("monternet get sms status failed.", e);
      throw new RuntimeException(e);
    }
  }

  private String executeGet(Params obj, String httpUrl) throws Exception {
    httpUrl += obj.string();
    try {
      return new String(HTTPUtils.get(httpUrl), StandardCharsets.UTF_8);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }

  }

  /**
   * 验证账号是否合法
   *
   * @param strUserId
   * @return
   */
  public static boolean validateUserId(String strUserId) {
    int length = StringUtils.length(strUserId);
    return length == 6;
  }

  /**
   * 验证密码是否合法
   */
  public static boolean validatePwd(String strPwd) {
    int length = StringUtils.length(strPwd);
    return length > 0 && length <= 32;
  }

  /**
   * 验证信息内容是否合法
   */
  public static boolean validateMessage(String strMessage) {
    int length = StringUtils.length(strMessage);
    return length > 0 && length <= 350;
  }

  /**
   * 验证扩展子号是否合法
   */
  public static boolean validateSubPort(String strSubPort) {
    int length = StringUtils.length(strSubPort);
    return length > 0 && length < 7 && ("*".equals(strSubPort) || isUnSignDigit(strSubPort));
  }

  /**
   * 无符号数字
   */
  public static boolean isUnSignDigit(String str) {
    char[] num = str.toCharArray();
    for (char c : num) {
      if (!Character.isDigit(c)) {
        return false;
      }
    }
    return true;
  }


  @Data
  public static class Params {
    private String userId;  //用户账号
    private String password;  //用户密码
    private String pszMobis;   //目标号码，用英文逗号(,)分隔，最大100个号码。一次提交的号码类型不受限制，但手机会做验证，若有不合法的手机号将会被退回。号码段类型分为：移动、联通、电信手机 注意：请不要使用中文的逗号
    private String pszMsg;     //短信内容， 内容长度不大于350个汉字
    private String iMobiCount;  //号码个数（最大100个手机）
    private String pszSubPort;  //子端口号码，不带请填星号{*} 长度由账号类型定4-6位，通道号总长度不能超过20位。如：10657****主通道号，3321绑定的扩展端口，主+扩展+子端口总长度不能超过20位。
    private String MsgId;  //一个8字节64位的大整型（INT64），格式化成的字符串。因此该字段必须为纯数字，且范围不能超过INT64的取值范围（-(2^63）……2^63-1）
    //    即-9223372036854775807……9223372036854775808。
    // 格式化成字符串后最大长度不超过20个字节。

    private String iReqType;  //请求类型(0: 上行&状态报告 1:上行 2: 状态报告)
    private String Sa;  // 扩展号
    private String multixmt;//批量短信请求包。该字段中包含N个短信包结构体。每个结构体间用固定的分隔符隔开。

    public String string() {
      StringBuilder sbd = new StringBuilder(256);
      add(sbd, "userId", this.userId);
      add(sbd, "password", this.password);
      add(sbd, "pszMobis", this.pszMobis);
      add(sbd, "pszMsg", this.pszMsg);
      add(sbd, "iMobiCount", this.iMobiCount);
      add(sbd, "pszSubPort", this.pszSubPort);
      add(sbd, "MsgId", this.MsgId);
      add(sbd, "iReqType", this.iReqType);
      add(sbd, "Sa", this.Sa);
      add(sbd, "multixmt", this.multixmt);
      sbd.setCharAt(0, '?');
      return sbd.toString();
    }

    private void add(StringBuilder sbd, String key, String val) {
      if (val != null) {
        sbd.append('&').append(key).append('=').append(URLEncoder.encode(val, StandardCharsets.UTF_8));
      }
    }
  }

}
