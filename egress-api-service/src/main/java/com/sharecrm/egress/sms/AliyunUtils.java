package com.sharecrm.egress.sms;

import com.aliyun.teaopenapi.models.Config;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.entity.TtsTemplate;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_NOTIFICATION;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_PROMOTION;
import static com.sharecrm.egress.sdk.SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE;
import static com.sharecrm.egress.utils.JsonUtil.jsonToMap;
import static com.sharecrm.egress.utils.JsonUtil.mapToJsonOrNull;

@Slf4j
@UtilityClass
public class AliyunUtils {

  /**
   * 验证码
   */
  public static final Integer ALIYUN_TEMPLATE_TYPE_VERIFY_CODE = 0;
  /**
   * 短信通知
   */
  public static final Integer ALIYUN_TEMPLATE_TYPE_NOTIFICATION = 1;
  /**
   * 推广短信
   */
  public static final Integer ALIYUN_TEMPLATE_TYPE_PROMOTION = 2;
  /**
   * 国际/港澳台消息
   */
  public static final Integer ALIYUN_TEMPLATE_TYPE_INTERNATIONAL = 3;

  /**
   * 短信发送状态：
   * 1：等待回执。
   * 2：发送失败。
   * 3：发送成功。
   */
  public static final Long SEND_STATUS_WAITING = 1L;
  public static final Long SEND_STATUS_SUCCESS = 3L;

  private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

  private final PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

  public static final Map<String, Integer> types = Map.of(
    TEMPLATE_TYPE_VERIFY_CODE, ALIYUN_TEMPLATE_TYPE_VERIFY_CODE,
    TEMPLATE_TYPE_NOTIFICATION, ALIYUN_TEMPLATE_TYPE_NOTIFICATION,
    TEMPLATE_TYPE_PROMOTION, ALIYUN_TEMPLATE_TYPE_PROMOTION
  );

  /**
   * TTS请求参数数组转Map
   */
  public static Map<String, String> buildTtsParams(TtsTemplate template, TTSSendRequest request) {
    List<String> paramKeys = SmsUtils.extractKeys(template.getContent());
    Map<String, String> params = new HashMap<>();
    String[] paramArray = request.getTemplateParams();
    for (int i = 0; i < paramKeys.size(); i++) {
      params.put(paramKeys.get(i), paramArray[i]);
    }
    return params;
  }

  /**
   * 替换模板中的key，我们自定义模板的时候key可能是非法的，我们存了一个映射关系，转成合法的key。
   * 替换value，有些内容是敏感的，运营商不允许发送，我们替换掉
   */
  public static String reallyTemplateParamJson(SmsSendByTemplate sendRequest, SmsTemplateEntity template) {
    Map<String, String> srcParamMap = sendRequest.getTemplateParam();
    if (MapUtils.isEmpty(srcParamMap)) {
      return null;
    }
    //需要替换的key映射关系
    Map<String, String> replaceMap = jsonToMap(template.getParamReplace());
    Map<String, String> map = srcParamMap.entrySet()
      .stream()
      // 替换key，而value不变
      .map(src -> replaceEntityKey(src, replaceMap))
      //替换value中的敏感内容
      .map(e -> Map.entry(e.getKey(), SmsUtils.aliyunReplaceContent(e.getValue(), template.getContent())))
      .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    return mapToJsonOrNull(map);
  }

  public static Date safeStrToDate(String date) {
    try {
      if (StringUtils.isBlank(date)) {
        return new Date();
      }
      return Date.from(LocalDateTime.parse(date, FORMATTER).atZone(ZoneId.systemDefault()).toInstant());
    } catch (Exception e) {
      return new Date();
    }
  }

  @NotNull
  private Map.Entry<String, String> replaceEntityKey(Map.Entry<String, String> src, Map<String, String> replaceMap) {
    return replaceMap.containsKey(src.getKey()) ? Map.entry(replaceMap.get(src.getKey()), src.getValue()) : src;
  }

  public static boolean isSuccess(String code) {
    return "OK".equalsIgnoreCase(code);
  }

  public static boolean isSendSuccess(Long sendStatus) {
    return SEND_STATUS_SUCCESS.equals(sendStatus);
  }

  public static boolean isWaiting(Long sendStatus) {
    return SEND_STATUS_WAITING.equals(sendStatus);
  }

  public static boolean isWaitingTimeout(Date sendTime) {
    Date hoursAgo = DateUtils.addHours(new Date(), -2);
    return sendTime.before(hoursAgo);
  }

  public static String relatedSignName(SmsTemplateRequest request, SmsProperties.AliYunConfig config) {
    String relatedSignName = request.getRelatedSignName();
    if (request.isInternational()) {
      return StringUtils.defaultIfBlank(relatedSignName, config.getIntlEnSignName());
    }
    return StringUtils.defaultIfBlank(relatedSignName, config.getZhSignName());
  }

  /**
   * 判断短信模板中是否包含的有变量
   */
  public static boolean hasTemplateKeys(String template) {
    return CollectionUtils.isNotEmpty(SmsUtils.extractKeys(template));
  }

  public static Integer aliyunTemplateType(SmsTemplateRequest request) {
    if (request.isInternational()) {
      return ALIYUN_TEMPLATE_TYPE_INTERNATIONAL;
    }
    Integer rs = types.get(request.getTemplateType());
    if (Objects.nonNull(rs)) {
      return rs;
    }
    throw new SmsException("unknown template type:" + request.getTemplateType());
  }

  public static Integer aliyunIntlType(SmsTemplateRequest request) {
    Integer rs = types.get(request.getTemplateType());
    if (Objects.nonNull(rs)) {
      return rs;
    }
    throw new SmsException("unknown template type:" + request.getTemplateType());
  }

  /**
   * 转成阿里云要求的手机号格式：
   * <p>
   * 国内短信：+/+86/0086/86 或无任何前缀的 11 位手机号码，例如 1390000****。
   * 国际/港澳台消息：国际区号+号码，例如 852000012****。
   *
   * @return 阿里API手机号格式
   */
  public static String aliyunPhoneNumbers(List<String> phones, boolean international) {
    return phones.stream()
      .map(s -> aliyunPhoneNumber(s, international))
      .collect(Collectors.joining(","));
  }

  public static String aliyunPhoneNumber(String phone, boolean international) {
    try {
      if (!international) {
        return phone;
      }
      phone = SmsUtils.amendIntlPhone(phone);
      Phonenumber.PhoneNumber phoneNumber = phoneUtil.parse(phone, "CH");
      return "" + phoneNumber.getCountryCode() + phoneNumber.getNationalNumber();
    } catch (RuntimeException | NumberParseException e) {
      log.warn("invalid phone number:{}", phone, e);
      return phone;
    }

  }

  public static TtsTemplate selectTtsTemplate(int templateId, Collection<TtsTemplate> templates) {
    return templates
      .stream()
      .filter(t -> t.getTemplateId() == templateId)
      .findFirst()
      .orElseThrow(() -> new SmsException("not found tts template:" + templateId));
  }

  @NotNull
  public static Tuple2<SmsSendByTemplate, SmsTemplateEntity> selectSmsTemplate(String phone, SmsSendRequest request, List<SmsStaticTemplate> staticTemplates) {
    //根据内容和模板匹配，如果匹配上，解析出模板ID和key-value参数
    SmsSendByTemplate templateRequest = new SmsSendByTemplate();
    // 单个发送，不要多发
    templateRequest.setPhones(List.of(phone));
    templateRequest.setEnterpriseId(request.getEnterpriseId());
    templateRequest.setBizName(request.getBizName());
    templateRequest.setLanguage(request.getLanguage());

    //有些模板变量范围比较大，可能匹配多个，按优先级匹配，数字越小优先级越高
    List<SmsStaticTemplate> templates = staticTemplates.stream()
      .sorted(Comparator.comparingInt(SmsStaticTemplate::getOrder)
        // 模板内容越长，优先级越高，尽可能精确匹配，以免变量中匹配到无关内容
        .thenComparing((o1, o2) -> o2.getContent().length() - o1.getContent().length()))
      .toList();

    for (SmsStaticTemplate e : templates) {
      //模板可能无变量，全文相等
      if (e.getContent().equals(request.getContent())) {
        templateRequest.setTemplateId(e.getTemplateId());
        break;
      }
      Map<String, String> map = SmsUtils.extractValues(e.getContent(), request.getContent());
      // map非空，表示匹配     
      if (!map.isEmpty()) {
        templateRequest.setTemplateParam(map);
        templateRequest.setTemplateId(e.getTemplateId());
        break;
      }
    }

    //如果支持多个语言，按语言选择
    String language = SmsUtils.chooseLanguage(request.getLanguage(), request.getContent());

    //如果没有匹配上，看看有没有可用的验证码模板，优先走验证码模板
    String captcha = SmsUtils.getCaptcha(request.getContent());
    if (Objects.isNull(templateRequest.getTemplateId()) && StringUtils.isNotBlank(captcha)) {
      templates.stream()
        .filter(SmsStaticTemplate::getAsDefaultCaptcha)
        .filter(e -> e.getLanguages().contains(language))
        .findFirst()
        .ifPresent(e -> {
          log.info("captcha template found, will use default template: {}, request: {}", e.getContent(), request);
          //验证码模板参数，只支持一个参数
          List<String> keys = SmsUtils.extractKeys(e.getContent());
          templateRequest.setTemplateParam(Map.of(keys.getFirst(), captcha));
          templateRequest.setTemplateId(e.getTemplateId());
        });
    }

    //如果没有匹配上，使用默认的宽泛模板，将全部内容当做参数发送过去
    if (Objects.isNull(templateRequest.getTemplateId())) {
      log.warn("no static template match, will try default template, content: {}", request.getContent());
      SmsStaticTemplate defaultTemplate = templates.stream()
        .filter(SmsStaticTemplate::getAsDefaultContent)
        .filter(e -> e.getLanguages().contains(language))
        .findFirst()
        .orElseThrow(() -> new SmsException("not found default template"));

      Map<String, String> param = new HashMap<>();
      for (String key : SmsUtils.extractKeys(defaultTemplate.getContent())) {
        param.put(key, request.getContent());
      }
      templateRequest.setTemplateParam(param);
      templateRequest.setTemplateId(defaultTemplate.getTemplateId());
    }
    SmsStaticTemplate template = templates.stream()
      .filter(e -> e.getTemplateId().equals(templateRequest.getTemplateId()))
      .findFirst()
      .orElseThrow(() -> new SmsException("not found default template"));
    return Tuples.of(templateRequest, template);
  }

  public static com.aliyun.dyvmsapi20170525.Client initAliyunTtsClient(SmsProperties.AliYunConfig ali) {
    try {
      Config config = initConfig(ali, "dyvmsapi.aliyuncs.com");
      return new com.aliyun.dyvmsapi20170525.Client(config);
    } catch (Exception e) {
      log.error("init aliyun tts client failed.", e);
      return null;
    }
  }

  public static com.aliyun.dysmsapi20170525.Client initAliyunSmsClient(SmsProperties.AliYunConfig ali) {
    try {
      Config config = initConfig(ali, "dysmsapi.aliyuncs.com");
      return new com.aliyun.dysmsapi20170525.Client(config);
    } catch (Exception e) {
      log.error("init aliyun sms client failed.", e);
      return null;
    }
  }

  private static Config initConfig(SmsProperties.AliYunConfig ali, String endpoint) {
    Config config = new Config()
      .setRegionId(ali.getRegion())
      // Endpoint 请参考 https://api.aliyun.com/product/Dyvmsapi
      .setEndpoint(endpoint)
      .setConnectTimeout(ali.getDefaultConnectTimeout())
      .setReadTimeout(ali.getDefaultReadTimeout())
      .setAccessKeyId(ali.getAppKey())
      .setAccessKeySecret(ali.getAppSecret());
    String httpProxy = ali.getHttpProxy();
    if (Strings.isNotEmpty(httpProxy)) {
      config.setHttpProxy(httpProxy);
      config.setHttpsProxy(httpProxy);
    }
    return config;
  }


}
