package com.sharecrm.egress.sms;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsPageRequest;
import com.sharecrm.egress.entity.SmsPageResult;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsSign;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsArgException;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sdk.entity.SmsResponseData;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.service.DirtyWordService;
import com.sharecrm.egress.service.RedisService;
import com.sharecrm.egress.utils.Counters;
import com.sharecrm.egress.utils.EgressUtils;
import com.sharecrm.egress.utils.SchedulerUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sharecrm.egress.sms.SmsSender.SUPPORT_ADD_TEMPLATE_CHINESE;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_ADD_TEMPLATE_INTL;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_SEND_TEMPLATE_CHINESE;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_SEND_TEMPLATE_INTL;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_TTS;
import static com.sharecrm.egress.sms.SmsUtils.SMS_SIGN_ZH;
import static com.sharecrm.egress.sms.SmsUtils.isTemplateAvailable;
import static com.sharecrm.egress.sms.SmsUtils.replaceContext;
import static com.sharecrm.egress.utils.PatternUtils.simpleAnyMatch;
import static org.apache.commons.lang3.StringUtils.defaultIfEmpty;

/**
 * 短信服务，本地真正发短信的代码
 */
@Slf4j
@Service
@ConditionalOnSmsEnabled
public class SmsReallyService implements SmsService {
  private final RedisService redisService;
  private final DirtyWordService dirtyWordService;
  private final SmsProperties smsProperties;
  private final ApplicationContext applicationContext;
  private final SmsDao smsDao;
  private final ObjectProvider<AutoConfMQProducer> smsProducer;

  private final AsyncTaskExecutor executor;

  public SmsReallyService(RedisService redisService, DirtyWordService dirtyWordService,
                          SmsProperties smsProperties, ApplicationContext applicationContext, SmsDao smsDao,
                          @Qualifier("smsRocketMQProducer") ObjectProvider<AutoConfMQProducer> smsProducer,
                          @Qualifier("taskScheduler") AsyncTaskExecutor executor) {
    this.redisService = redisService;
    this.dirtyWordService = dirtyWordService;
    this.smsProperties = smsProperties;
    this.applicationContext = applicationContext;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
    this.executor = executor;
  }

  /**
   * {@inheritDoc}
   */
  @Override
  public Mono<SmsSendResponse> sendChineseSms(SmsSendRequest request, ServerWebExchange exchange) {
    return validateChinese(request, exchange)
      .map(SmsUtils::replaceContext)
      .map(this::sendChineseByProvider)
      .onErrorResume(e -> Mono.just(onErrorResumeAndSave(request, e)))
      .map(SmsSendResult::getResponse);
  }

  private SmsSendResult sendChineseByProvider(SmsSendRequest req) {
    // 发送国内短信
    String batchMsgId = NanoIdUtils.randomNanoId();
    //转成单条发送，最后汇总结果
    List<SmsResponseData> phonesDetails = req.getPhones()
      .stream()
      //异步发送，保持ID不变
      .map(phone -> singleChineseSend(req, phone, batchMsgId))
      .toList();
    SmsSendResult result = SmsUtils.zipSmsSendResult(phonesDetails);
    //重置批次ID
    result.getResponse().setBatchMsgId(batchMsgId);
    return result;
  }

  @NotNull
  private SmsResponseData singleChineseSend(SmsSendRequest req, String phone, String batchMsgId) {
    Tuple3<Boolean, String, String> v = validateIgnoreOrRateLimit(phone, req.getBizName(), RedisService.KEY_SMS);
    if (!v.getT1()) {
      log.warn("sms not send phone:{}, reason:{}", phone, v.getT3());
      SmsResponseData data = new SmsResponseData(false, phone, v.getT3(), NanoIdUtils.randomNanoId());
      trySaveErrorDetail(false, req, batchMsgId, data);
      return data;
    }
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(req, batchMsgId);
    //转异步发送，目前有些业务调用量没有控制批次，同步可能超时失败
    executor.execute(() -> {
      // 从redis中获取该手机号已经使用过的通道
      Map<Integer, List<SmsSender>> senders = sortedSenders(req, phone, SmsSender.SUPPORT_CHINESE);
      //正常不会为空，防止谁配置错误，加个error日志
      if (senders.isEmpty()) {
        log.error("No available sms provider for phone:{}, batchMsgId:{}", phone, batchMsgId);
        return;
      }
      // 保持优先级，在优先级的基础上进行加权轮训
      for (Map.Entry<Integer, List<SmsSender>> e : senders.entrySet()) {
        // 某个通道发送失败，使用其他通道重试
        if (trySenderInSubList(e.getValue(), phone, wrapper, Counters.get("sms-chinese"))) {
          break;
        }
      }
    });
    return new SmsResponseData(true, phone, "accepted, async send", wrapper.getMsgId());
  }

  private boolean trySenderInSubList(List<SmsSender> all, String phone, SmsRequestWrapper<SmsSendRequest> wrapper, AtomicInteger counter) {
    int size = all.size();
    for (int i = 0; i < size; i++) {
      SmsSender sender = EgressUtils.roundRobin(all.subList(i, size), counter);
      if (trySend(wrapper, phone, sender)) {
        return true;
      }
    }
    return false;
  }

  private void trySaveErrorDetail(boolean international, SmsSendRequest req, String batchMsgId, SmsResponseData data) {
    //模拟一个假的结果用于记录日志
    SmsSendResult rs = new SmsSendResult();
    rs.setRequest(req);
    rs.setInternational(international);
    SmsSendResponse response = new SmsSendResponse();
    response.setSuccess(false);
    response.setBatchMsgId(batchMsgId);
    response.setMessage(data.getMessage());
    response.setPhones(List.of(data));
    rs.setResponse(response);
    saveDetail(rs);
  }

  private boolean trySend(SmsRequestWrapper<SmsSendRequest> wrapper, String phone, SmsSender sender) {
    try {
      SmsSendResult rs = sender.send(phone, wrapper);
      rs.setInternational(wrapper.isInternational());
      saveDetail(rs);
      if (cacheChannelAndBreak(phone, sender, rs)) {
        return true;
      }
    } catch (Exception e) {
      log.warn("{} send to phone {} exception", sender.provider().getId(), phone, e);
    }
    return false;
  }

  private boolean cacheChannelAndBreak(String phone, SmsSender sender, SmsSendResult rs) {
    SmsSendResponse response = rs.getResponse();
    if (Objects.isNull(response) || Objects.isNull(response.getPhones())) {
      return false;
    }
    //发送成功或已经知道号码不合法，就不要再发了
    if (response.isSuccess() || SmsUtils.isInvalidatedPhoneByMessage(response.getMessage())) {
      //cache phone channel
      redisService.saveChannel(phone, sender.provider().getId());
      return true;
    }
    return false;
  }

  /**
   * {@inheritDoc}
   */
  @Override
  public Mono<SmsSendResponse> sendIntlSms(SmsSendRequest request, ServerWebExchange exchange) {
    return validateIntl(request, exchange)
      .map(SmsUtils::replaceContext)
      .map(this::sendIntlByProvider)
      .onErrorResume(e -> Mono.just(onErrorResumeAndSave(request, e)))
      .map(SmsSendResult::getResponse);
  }

  private SmsSendResult sendIntlByProvider(SmsSendRequest req) {
    //发送国际短信
    String batchMsgId = NanoIdUtils.randomNanoId();
    //转成单条发送，最后汇总结果
    List<SmsResponseData> phonesDetails = req.getPhones()
      .stream()
      //异步发送，保持ID不变
      .map(phone -> singleIntlSend(req, phone, batchMsgId))
      .toList();
    SmsSendResult result = SmsUtils.zipSmsSendResult(phonesDetails);
    //重置批次ID
    result.getResponse().setBatchMsgId(batchMsgId);
    return result;
  }

  private SmsResponseData singleIntlSend(SmsSendRequest req, String phone, String batchMsgId) {
    //校验是否限流或者在忽略名单里
    Tuple3<Boolean, String, String> v = validateIgnoreOrRateLimit(phone, req.getBizName(), RedisService.KEY_SMS);
    if (!v.getT1()) {
      log.warn("intl sms not send phone:{}, reason:{}", phone, v.getT3());
      SmsResponseData data = new SmsResponseData(false, phone, v.getT3(), NanoIdUtils.randomNanoId());
      trySaveErrorDetail(true, req, batchMsgId, data);
      return data;
    }
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(req, batchMsgId);
    //标记是国际短信，便于后面选择渠道，记录日志
    wrapper.setInternational(true);
    //转异步发送，目前有些业务调用量没有控制批次，同步可能超时失败
    executor.execute(() -> {
      //先修正开头0号码发送一次
      String phoneNumber = SmsUtils.amendIntlPhone(phone);
      sendIntlSmsByPhone(wrapper, phoneNumber);
      //去掉号码位的0然后重新发送一次,保障到达率，详细规则参考下面这个方法里的注释
      String removedZeroPhone = SmsUtils.removeTheFirstZero(phone);
      if (!phone.equals(removedZeroPhone)) {
        log.debug("do send intl sms by removed zero phone: {}", removedZeroPhone);
        sendIntlSmsByPhone(wrapper, removedZeroPhone);
      }
    });
    return new SmsResponseData(true, phone, "accepted, async send", wrapper.getMsgId());
  }

  private void sendIntlSmsByPhone(SmsRequestWrapper<SmsSendRequest> wrapper, String phone) {
    // 为了提高国际短信的到达率，使用双短信通道在固定时间内自动切换
    String support = selectIntlFilterType(wrapper.getRequest());
    Map<Integer, List<SmsSender>> senders = sortedSenders(wrapper.getRequest(), phone, support);
    //正常不会为空，防止谁配置错误，加个error日志
    if (senders.isEmpty()) {
      log.error("No available intl sms provider for phone:{}, batchMsgId:{}", phone, wrapper.getBatchMsgId());
      return;
    }
    for (Map.Entry<Integer, List<SmsSender>> e : senders.entrySet()) {
      // 某个通道发送失败，使用其他通道重试
      if (trySenderInSubList(e.getValue(), phone, wrapper, Counters.get("sms-intl"))) {
        break;
      }
    }
  }

  @NotNull
  private Map<Integer, List<SmsSender>> sortedSenders(SmsSendRequest req, String phone, String support) {
    Set<String> channels = redisService.getChannel(phone);
    return smsSenderBeans().stream()
      .filter(s -> s.provider().isEnabled())
      .filter(s -> s.supports().contains(support))
      .filter(s -> SmsUtils.filterByEi(req.getEnterpriseId(), s))
      // 同一号码在一定时间内使用不同的通道发送，提高可用性
      .sorted((o1, o2) -> SmsUtils.sortSendOrder(channels, o1, o2))
      .collect(Collectors.groupingBy(
        SmsSender::getOrder,
        // 使用 TreeMap 保证按key优先级排序
        TreeMap::new,
        Collectors.toList()
      ));
  }

  String selectIntlFilterType(SmsSendRequest req) {
    if (StringUtils.isNotBlank(SmsUtils.getCaptcha(req.getContent()))) {
      //验证码短信
      return SmsSender.SUPPORT_CAPTCHA_INTL;
    }
    return SmsSender.SUPPORT_INTL;
  }

  /**
   * {@inheritDoc}
   */
  @Override
  public Mono<SmsSendResponse> sendCodeCall(SmsSendRequest request, ServerWebExchange exchange) {
    TTSSendRequest tts = new TTSSendRequest();
    tts.setPhones(request.getPhones());
    tts.setTemplateId(SmsSdkConstants.TTS_ID_VERIFY_CODE);
    tts.setTemplateParams(new String[]{request.getContent()});
    return sendTts(tts, exchange);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  public Mono<SmsSendResponse> sendTts(TTSSendRequest request, ServerWebExchange exchange) {
    return validateTts(request, exchange)
      .map(this::sendTtsByProvider)
      .onErrorResume(e -> Mono.just(onErrorResumeResult(request, e)))
      .doOnNext(this::saveDetail)
      .map(SmsSendResult::getResponse);
  }

  @NotNull
  private SmsSendResult onErrorResumeAndSave(Object request, Throwable e) {
    SmsSendResult rs = onErrorResumeResult(request, e);
    saveDetail(rs);
    return rs;
  }

  SmsSendResult onErrorResumeResult(Object request, Throwable e) {
    //模拟一个假的发送结果，用于记录数据库日志
    String message = e.getMessage();
    List<String> phones = requestPhones(request);
    SmsSendResponse response = new SmsSendResponse();
    response.setSuccess(false);
    response.setMessage(message);
    response.setBatchMsgId(NanoIdUtils.randomNanoId());
    List<SmsResponseData> dataList = phones.stream()
      .map(s -> new SmsResponseData(false, s, message, NanoIdUtils.randomNanoId()))
      .toList();
    response.setPhones(dataList);
    return SmsUtils.initResult(request, response);
  }

  private List<String> requestPhones(Object request) {
    if (request instanceof TTSSendRequest req) {
      return req.getPhones();
    }
    if (request instanceof SmsSendRequest req) {
      return req.getPhones();
    }
    if (request instanceof SmsSendByTemplate req) {
      return req.getPhones();
    }
    log.warn("unknown request type for get phone list: {}", request);
    return new ArrayList<>();
  }

  private SmsSendResult sendTtsByProvider(TTSSendRequest req) {
    return smsSenderBeans().stream()
      .filter(s -> s.supports().contains(SUPPORT_TTS))
      .findAny()
      .orElseThrow(() -> new SmsException("no available tts sender"))
      .sendTts(req);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  public Mono<SmsPageResult> queryHistories(SmsPageRequest request) {
    return Mono.fromCallable(() -> smsDao.findHistories(request))
      .subscribeOn(SchedulerUtils.SMS_SCHEDULER);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  public Mono<SmsPageResult> querySmsStatus(SmsPageRequest request) {
    return Mono.fromCallable(() -> smsDao.findHistories(request))
      //抹除敏感信息
      .map(SmsUtils::sensitiveMasker)
      .subscribeOn(SchedulerUtils.SMS_SCHEDULER);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  public Mono<List<SmsProvider>> queryExclusiveProvider(Integer enterpriseId, ServerWebExchange exchange) {
    return Mono.just(queryExclusiveProvider(enterpriseId));
  }

  private List<SmsProvider> queryExclusiveProvider(Integer enterpriseId) {
    return smsSenderBeans().stream()
      .map(SmsSender::provider)
      .filter(SmsProvider::isEnabled)
      .filter(SmsProvider::isExclusive)
      .filter(e -> StringUtils.isNotBlank(e.getAllowAccounts()))
      .filter(e -> SmsUtils.isAllowEi(e.getAllowAccounts(), enterpriseId))
      // 抹除敏感信息
      .map(SmsUtils::simpleSmsProvider)
      .toList();
  }

  @Override
  public Mono<SmsSendResponse> sendByTemplate(SmsSendByTemplate request, ServerWebExchange exchange) {
    return Mono.just(sendSmsByTemplate(request, exchange));
  }

  @Override
  public Mono<EgressApiResponse<SmsTemplateDetail>> addTemplate(SmsTemplateRequest request, ServerWebExchange exchange) {
    return Mono.fromCallable(() -> addTemplate(request))
      .subscribeOn(SchedulerUtils.SMS_SCHEDULER);
  }

  private EgressApiResponse<SmsTemplateDetail> addTemplate(SmsTemplateRequest request) {
    try {
      //模板参数校验
      SmsUtils.validateAddTemplate(request);
      //根据当前EA找到支持创建模板的供应商
      Integer ei = request.getEnterpriseId();
      //设置允许的EI，如果未指定，只允许当前EI
      request.setAllowAccounts(defaultIfEmpty(request.getAllowAccounts(), SmsUtils.grayRuleByEi(ei)));

      String providerId = request.getProviderId();
      boolean intl = request.isInternational();
      Predicate<SmsSender> addPredicate = s -> s.supports().contains(intl ? SUPPORT_ADD_TEMPLATE_INTL : SUPPORT_ADD_TEMPLATE_CHINESE);
      SmsTemplateDetail detail = smsSenderBeans().stream()
        .filter(s -> filterByProvider(providerId, s))
        .filter(addPredicate)
        .filter(s -> SmsUtils.isAllowEi(s.provider().getAllowAccounts(), ei))
        .findFirst()
        .orElseThrow(() -> new SmsException("no available sms template provider"))
        .addTemplate(request);
      return EgressApiResponse.ok(detail);
    } catch (SmsArgException | SmsException e) {
      log.warn("add template failed", e);
      return new EgressApiResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }
  }

  public SmsSendResponse sendSmsByTemplate(SmsSendByTemplate request, ServerWebExchange exchange) {
    // 手机号限流，为了简单，如果是单个才限流    
    SmsSendResult result;
    String smsType = "";
    try {
      //尝试从Header中补充EA信息
      appendEi(request, exchange);
      SmsTemplateEntity template = validateSendByTemplate(request);
      smsType = template.getTemplateType();

      //过滤掉需要限流和需要忽略的手机号
      Map<Boolean, List<Tuple3<Boolean, String, String>>> maps = request.getPhones()
        .stream()
        .map(phone -> validateIgnoreOrRateLimit(phone, request.getBizName(), RedisService.KEY_SMS))
        .collect(Collectors.groupingBy(Tuple3::getT1));

      //校验通过的手机号
      List<String> success = maps.getOrDefault(Boolean.TRUE, List.of())
        .stream().map(Tuple2::getT2).toList();

      result = doTemplateSend(request, success, template);

      zipWithFailedSendResult(result, maps);
    } catch (RuntimeException e) {
      log.warn("sms send failed", e);
      result = onErrorResumeResult(request, e);
    }
    //补充异常情况下的数据
    result.setTemplateRequest(request);
    result.setSmsType(smsType);

    //记录日志详情
    saveDetail(result);
    return result.getResponse();
  }

  Tuple3<Boolean, String, String> validateIgnoreOrRateLimit(String phone, String bizName, String redisKey) {
    if (simpleAnyMatch(smsProperties.getIgnorePhones(), phone)) {
      return Tuples.of(false, phone, phone + " is ignored");
    }
    if (isRateLimit(phone, bizName, redisKey)) {
      return Tuples.of(false, phone, phone + " is rate limited");
    }
    return Tuples.of(true, phone, "OK");
  }

  private static void zipWithFailedSendResult(SmsSendResult result, Map<Boolean, List<Tuple3<Boolean, String, String>>> maps) {
    //汇总成功和失败的结果
    SmsSendResponse rsp = result.getResponse();
    List<SmsResponseData> phones = rsp.getPhones();
    Stream<SmsResponseData> failed = maps.getOrDefault(Boolean.FALSE, List.of())
      .stream()
      .map(t -> new SmsResponseData(false, t.getT2(), t.getT3(), NanoIdUtils.randomNanoId()));
    List<SmsResponseData> allResult = Stream.concat(phones.stream(), failed).toList();
    rsp.setPhones(allResult);
    rsp.setSuccess(allResult.stream().allMatch(SmsResponseData::isSuccess));
  }

  private SmsSendResult doTemplateSend(SmsSendByTemplate request, List<String> success, SmsTemplateEntity template) {
    if (success.isEmpty()) {
      throw new SmsException("all template request phone is ignored or rate limited");
    }
    //只对校验通过的手机号发送短信
    request.setPhones(success);
    //替换内容中的敏感信息
    replaceTemplateContext(request);
    String support = template.isInternational() ? SUPPORT_SEND_TEMPLATE_INTL : SUPPORT_SEND_TEMPLATE_CHINESE;
    return smsSenderBeans()
      .stream()
      // 模板决定了使用哪个通道
      .filter(s -> filterByProvider(template.getProviderId(), s))
      // 校验是否允许
      .filter(s -> s.supports().contains(support))
      //校验EI是否有权限
      .filter(s -> SmsUtils.filterByEi(request.getEnterpriseId(), s))
      .findFirst()
      .orElseThrow(() -> new SmsException("no available sms template provider"))
      .sendByTemplate(request, template);
  }

  private void replaceTemplateContext(SmsSendByTemplate request) {
    request.setTemplateParam(SmsUtils.replaceTemplateParamValue(request, e -> replaceContext(e.getValue())));
  }

  private SmsTemplateEntity validateSendByTemplate(SmsSendByTemplate request) {
    String templateId = request.getTemplateId();
    if (StringUtils.isBlank(templateId)) {
      throw new SmsException("templateId is required");
    }
    SmsTemplateEntity template = queryTemplateByCache(templateId);
    if (Objects.isNull(template)) {
      throw new SmsException("template not found: " + templateId);
    }
    if (!isTemplateAvailable(template.getStatus())) {
      throw new SmsException("template status is not approved yet");
    }
    //号码格式校验，注意和国内短信手机号格式不同
    SmsUtils.validatePhones(request.getPhones(), template.isInternational());
    // 脏词检测
    request.getTemplateParam().forEach((s, v) -> validateWords(v));
    return template;
  }

  private SmsTemplateEntity queryTemplateByCache(String templateId) {
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setTemplateId(templateId);
    Optional<SmsStaticTemplate> staticTemplate = smsSenderBeans()
      .stream()
      .flatMap(s -> s.staticTemplates(query).stream())
      .findFirst();
    if (staticTemplate.isPresent()) {
      return staticTemplate.get();
    }
    SmsTemplateEntity cache = redisService.querySmsTemplate(templateId);
    if (Objects.nonNull(cache)) {
      return cache;
    }
    SmsTemplateEntity detail = smsDao.queryTemplate(templateId);
    if (Objects.nonNull(detail)) {
      redisService.saveSmsTemplate(detail);
    }
    return detail;
  }

  private boolean filterByProvider(String providerId, SmsSender smsSender) {
    return StringUtils.isBlank(providerId) || providerId.equals(smsSender.provider().getId());
  }

  @Override
  public Mono<List<SmsTemplateDetail>> queryTemplates(SmsTemplateQuery request, ServerWebExchange exchange) {
    return Mono.fromCallable(() -> concatSmsTemplateDetails(request))
      .subscribeOn(SchedulerUtils.SMS_SCHEDULER);
  }

  @NotNull
  private List<SmsTemplateDetail> concatSmsTemplateDetails(SmsTemplateQuery request) {
    Stream<SmsTemplateDetail> dbTemplates = smsDao.queryTemplates(request).stream()
      .map(this::convertTemplateDetail);
    Stream<SmsTemplateDetail> staticTemplates = smsSenderBeans()
      .stream()
      .flatMap(s -> s.staticTemplates(request).stream())
      .map(this::convertTemplateDetail);
    //这里可能包含重复的，但是云厂商允许完全相同的内容模板，允许重复。
    return Stream.concat(staticTemplates, dbTemplates).toList();
  }

  @NotNull
  private List<SmsSender> smsSenderBeans() {
    return SmsUtils.filterSmsSenderBeans(applicationContext.getBeansOfType(SmsSender.class));
  }

  @NotNull
  private SmsTemplateDetail convertTemplateDetail(SmsTemplateEntity e) {
    SmsTemplateDetail detail = new SmsTemplateDetail();
    BeanUtils.copyProperties(e, detail);
    return detail;
  }

  @Override
  public Mono<SmsTemplateDetail> queryTemplate(String id, ServerWebExchange exchange) {
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setTemplateId(id);
    return Mono.fromCallable(() -> concatSmsTemplateDetails(query))
      .flatMap(s -> Mono.justOrEmpty(s.stream().findFirst()))
      .subscribeOn(SchedulerUtils.SMS_SCHEDULER);
  }

  /**
   * 判断是否需要限流，true时限流，false不限流
   */
  private boolean isRateLimit(String phone, String bizName, String limitKey) {
    //特殊短信不限流，不计入限流次数，不如首次邀请开通的短信
    if (simpleAnyMatch(smsProperties.getIgnoreLimitBizNames(), bizName)) {
      return false;
    }
    return !redisService.smsSetIfAbsent(phone, limitKey);
  }

  private Mono<SmsSendRequest> validateIntl(SmsSendRequest request, ServerWebExchange exchange) {
    return Mono.just(request)
      // 先补充EI，便于记录日志
      .doOnNext(req -> appendEi(req, exchange))
      .map(s -> {
        //号码格式校验，注意和国内短信手机号格式不同
        SmsUtils.validatePhones(request.getPhones(), true);
        validateWords(s.getContent());
        return s;
      });
  }


  private void appendEi(Object request, ServerWebExchange exchange) {
    String ea = WebUtils.getEaFromHeader(exchange);
    String ei = WebUtils.getEiFromHeader(exchange);
    log.info("sms request ei in header, ea:{}, ei:{}", ea, ei);

    if (request instanceof TTSSendRequest req) {
      req.setEnterpriseId(defaultIfEmpty(req.getEnterpriseId(), ei));
      log.debug("sms request ei in body, ei:{}", req.getEnterpriseId());
    }
    if (request instanceof SmsSendRequest req) {
      req.setEnterpriseId(defaultIfEmpty(req.getEnterpriseId(), ei));
      log.debug("sms request ei in body, ei:{}", req.getEnterpriseId());
    }
    if (request instanceof SmsSendByTemplate req) {
      req.setEnterpriseId(defaultIfEmpty(req.getEnterpriseId(), ei));
      log.debug("sms request ei in body, ei:{}", req.getEnterpriseId());
    }

  }

  private Mono<SmsSendRequest> validateChinese(SmsSendRequest request, ServerWebExchange exchange) {
    return Mono.just(request)
      // 先补充EI，便于记录日志
      .doOnNext(req -> appendEi(req, exchange))
      .map(s -> {
        //号码格式校验
        SmsUtils.validatePhones(s.getPhones(), false);
        validateWords(s.getContent());
        return s;
      });
  }

  private Mono<TTSSendRequest> validateTts(TTSSendRequest request, ServerWebExchange exchange) {
    return Mono.just(request)
      .doOnNext(req -> appendEi(req, exchange))
      .map(s -> {
        //号码格式校验
        SmsUtils.validateTts(s.getPhones());
        validateWords(s);
        validateTtsIgnore(request);
        return s;
      });
  }

  private void validateTtsIgnore(TTSSendRequest request) {
    //虽然接口是列表，但是实际上只有一个手机号
    if (request.getPhones().size() == 1) {
      String phone = request.getPhones().getFirst();
      Tuple3<Boolean, String, String> v = validateIgnoreOrRateLimit(phone, request.getBizName(), RedisService.KEY_TTS);
      if (!v.getT1()) {
        throw new SmsArgException("phone is ignored or rate limited");
      }
    }
  }

  private void validateWords(TTSSendRequest s) {
    Stream.ofNullable(s.getTemplateParams())
      .flatMap(Stream::of)
      .forEach(this::validateWords);
  }

  private void validateWords(String text) {
    //总有恶人在密码里加广告
    if (SmsUtils.containIllegalPassword(text)) {
      log.info("sms content has illegal password: {}", text);
      throw new SmsArgException("sms content has illegal password");
    }
    // 敏感词检测
    if (dirtyWordService.smsHasDirtyWords(text)) {
      log.info("sms content has dirty words: {}", text);
      throw new SmsArgException("sms content has dirty words");
    }
  }

  private void saveDetail(SmsSendResult rs) {
    executor.execute(() -> {
      SmsSendResponse response = rs.getResponse();
      if (Objects.isNull(response) || Objects.isNull(response.getPhones())) {
        log.warn("sms send result is empty");
        return;
      }
      log.info("sms send result: {}", response);
      response.getPhones().forEach(data -> {
        try {
          SmsMongoEntity entity = singleMongoEntity(rs, data);
          saveMongo(entity);
          //发送MQ通知，通知有记录变更
          SmsUtils.sendSmsStatusEvent(entity, smsProducer.getIfAvailable());
        } catch (Exception e) {
          log.warn("save sms record failed.", e);
        }
      });
    });
  }

  private void saveMongo(SmsMongoEntity entity) {
    try {
      //mongo异常时保证其他事件能正常发送
      smsDao.save(entity);
    } catch (Exception e) {
      log.warn("save sms record failed.", e);
    }
  }

  SmsMongoEntity singleMongoEntity(SmsSendResult rs, SmsResponseData data) {
    SmsSendResponse response = rs.getResponse();
    SmsMongoEntity entity = new SmsMongoEntity();
    entity.setSmsType(rs.getSmsType());
    entity.setStatus(data.isSuccess());
    entity.setPhone(data.getPhone());
    entity.setBatchMsgId(response.getBatchMsgId());
    entity.setSerialId(data.getSerialId());
    entity.setMsgId(data.getMsgId());
    entity.setMessage(data.getMessage());
    entity.setInternational(rs.isInternational());
    Date now = new Date();
    entity.setSendTime(now);
    // 如果原始发送就失败了，标记Replay相关状态为失败，就不要去运营商查状态了
    if (!data.isSuccess() && StringUtils.isEmpty(data.getSerialId())) {
      entity.setReplyStatus(false);
      entity.setReplyMessage(data.getMessage());
      entity.setReplyTime(now);
    }

    SmsSendRequest req = rs.getRequest();
    if (Objects.nonNull(req)) {
      entity.setContent(req.getContent());
      entity.setBizName(req.getBizName());
      entity.setEnterpriseId(req.getEnterpriseId());
    }
    SmsSendByTemplate template = rs.getTemplateRequest();
    if (Objects.nonNull(template)) {
      entity.setBizName(template.getBizName());
      entity.setTemplateId(template.getTemplateId());
      entity.setEnterpriseId(template.getEnterpriseId());
    }
    TTSSendRequest ttsSendRequest = rs.getTtsSendRequest();
    if (Objects.nonNull(ttsSendRequest)) {
      Integer templateId = ttsSendRequest.getTemplateId();
      entity.setTemplateId(templateId + "");
      entity.setBizName(ttsSendRequest.getBizName());
      entity.setEnterpriseId(ttsSendRequest.getEnterpriseId());
      entity.setContent(SmsUtils.fakeTtsContent(templateId, ttsSendRequest.getTemplateParams()));
      //咱不支持此状态
      entity.setReplyStatus(true);
      entity.setReplyMessage("none");
      entity.setReplyTime(now);
    }
    if (Objects.nonNull(rs.getContent())) {
      entity.setContent(rs.getContent());
    }

    entity.setSign(rs.getSign());

    SmsProvider provider = rs.getProvider();
    if (Objects.nonNull(provider)) {
      entity.setProviderId(provider.getId());
      entity.setProviderName(provider.getName());
    } else {
      entity.setProviderId("EMPTY");
      entity.setProviderName("EMPTY");
    }

    entity.setSmsType(StringUtils.defaultIfEmpty(entity.getSmsType(), SmsSdkConstants.TEMPLATE_TYPE_NOTIFICATION));
    entity.setBizName(StringUtils.defaultIfEmpty(entity.getBizName(), "EMPTY"));

    //一个手机号可能属于很多个企业，我们存日志的时候只记录前10个
    entity.setEnterpriseId(StringUtils.abbreviate(entity.getEnterpriseId(), 60));
    String sign = Objects.toString(entity.getSign(), SMS_SIGN_ZH);
    int signLength = sign.length();
    //中括号也算钱
    if (!StringUtils.containsAny(sign, "[", "【")) {
      signLength += 2;
    }
    int length = Objects.toString(entity.getContent(), "").length() + signLength;
    entity.setSmsLength(length);
    entity.setSmsSize(SmsUtils.adapterSmsSize(length));
    return entity;
  }

  public Mono<List<SmsSign>> listSigns(String providerId) {
    return Mono.justOrEmpty(smsSenderBeans().stream()
        .filter(s -> s.provider().getId().equals(providerId)).findFirst())
      .map(SmsSender::listSigns);
  }

}
