package com.sharecrm.egress.sms.share;

import com.sharecrm.egress.config.SmsProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 大汉三通短信下发状态收集任务，注意线下和线上环境用的同一个账户的话，线下不开启任务，否则会把线上的数据拉到线下
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnBean(DahantcSender.class)
@ConditionalOnProperty(name = "sharecrm.api.sms.dahantc.status-task-enabled", havingValue = "true")
public class DahanStatusTask {

  private final DahantcSender sender;
  private final SmsProperties properties;

  /**
   * 每N秒定时执行，延迟等服务启动完成
   */
  @Scheduled(initialDelay = 175, fixedDelayString = "${sharecrm.api.sms.dahantc.sms-task-delay:30}", timeUnit = TimeUnit.SECONDS)
  @SchedulerLock(name = "DahanStatusTask", lockAtLeastFor = "10s", lockAtMostFor = "1m")
  public void run() {
    if (properties.isEnabled() && properties.isStatusTaskEnabled()) {
      log.debug("run dahantc sms collect status task.");
      sender.updateStatus();
    }
  }

}