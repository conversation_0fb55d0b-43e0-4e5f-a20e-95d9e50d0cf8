package com.sharecrm.egress.sms.byteplus;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sms.SmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;

import static com.sharecrm.egress.sms.BytePlusUtils.initBytePlusSmsClient;

/**
 * BytePlus短信服务
 */
@Slf4j
public class BytePlusSmsSender implements SmsSender {

  private final SmsProperties.BytePlusConfig config;
  private final BytePlusSmsService smsService;

  public BytePlusSmsSender(SmsProperties.BytePlusConfig config, SmsDao smsDao,
                           AutoConfMQProducer smsProducer) {
    this.config = config;
    this.smsService = initSmsService(config, smsDao, smsProducer);
  }

  private BytePlusSmsService initSmsService(SmsProperties.BytePlusConfig config, SmsDao smsDao, AutoConfMQProducer smsProducer) {
    log.info("byteplus sms sender init, config:{}", config);
    return new BytePlusSmsService(config, smsDao, smsProducer, initBytePlusSmsClient(config));
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    return smsService.sendSms(phone, wrapper);
  }

  @Override
  public List<SmsStaticTemplate> staticTemplates(SmsTemplateQuery query) {
    return smsService.queryStaticTemplates(query);
  }

  @Override
  public SmsSendResult sendByTemplate(SmsSendByTemplate request, SmsTemplateEntity template) {
    // Wrapper中的msgId置为null，以便为每个手机号生成一个独立的ID
    return smsService.sendByTemplate(new SmsRequestWrapper<>(request, NanoIdUtils.randomNanoId(), null), request, template);
  }

  @Override
  public SmsProvider provider() {
    return config;
  }

  @Override
  public void smsStatusCallback(String ext, String body, ServerWebExchange exchange) {
    smsService.smsStatusCallback(ext, body);
  }

  @Override
  public List<String> supports() {
    return config.getSupports();
  }

  @Override
  public int getOrder() {
    return config.getOrder();
  }

  @Override
  public int getWeight() {
    return config.getWeight();
  }
}
