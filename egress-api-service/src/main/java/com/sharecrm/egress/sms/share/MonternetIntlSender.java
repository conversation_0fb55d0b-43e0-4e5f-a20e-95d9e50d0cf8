package com.sharecrm.egress.sms.share;

import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.config.SmsProperties.MonternetConfig;
import com.sharecrm.egress.entity.MonternetStatus;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import com.sharecrm.egress.sms.SmsUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 梦网国际短信通道(旧版本)
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.monternet-intl.enabled", havingValue = "true", matchIfMissing = true)
public class MonternetIntlSender implements SmsSender, SmsBeanIgnoreDestroy {

  private MonternetHttpClient monternetSMS;
  private MonternetHttpClient monternetMarketingSMS;

  private final SmsProperties properties;

  public MonternetIntlSender(SmsProperties properties) {
    this.properties = properties;
  }

  @PostConstruct
  public void init() {
    MonternetConfig config = config();
    log.info("MonternetIntlSender init, config:{}", config);
    this.monternetSMS = new MonternetHttpClient(config.getIp(), config.getPort(), config.getAccount(), config.getPassword());
    String marketingAccount = config.getMarketingAccount();
    String marketingPassword = config.getMarketingPassword();
    if (marketingAccount != null && marketingPassword != null) {
      this.monternetMarketingSMS = new MonternetHttpClient(config.getIp(), config.getPort(),
        marketingAccount,
        marketingPassword);
    }
  }

  private MonternetConfig config() {
    return properties.getMonternetIntl();
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    try {
      SmsSendRequest request = wrapper.getRequest();
      //梦网把我们的英文签名禁用了，等恢复后再放开
      String sign = SmsUtils.chooseSign(request.getLanguage(), request.getContent(), config().getIntlZhSignName(), config().getIntlEnSignName());
      if (sign.equals(config().getIntlEnSignName()) && !config().isEnSignEnabled()) {
        log.warn("monternet en sign temp disabled, will retry by other provider. phone:{}", phone);
        return SmsUtils.failed(config(), wrapper, phone, "monternet en sign temp disabled, will retry by other provider");
      }
      //增加签名
      String content = sign + request.getContent();
      MonternetHttpClient client = selectClient(request);
      String code = String.valueOf(client.sendSms(phone, content, "*", ""));
      SmsSendResult result = intlResult(phone, wrapper, code);
      result.setSign(sign);
      return result;
    } catch (Exception e) {
      log.error("monternet sms send failed, phone:{}, request:{}", phone, wrapper, e);
      return SmsUtils.failed(config(), wrapper, phone, e.getMessage());
    }
  }

  @NotNull
  private SmsSendResult intlResult(String phone, SmsRequestWrapper<SmsSendRequest> wrapper, String code) {
    if (code.equals("0")) {
      return SmsUtils.success(config(), wrapper, phone, "none");
    } else {
      String orDefault = MonternetStatus.getOrDefault(code, "状态字典中不存在：" + code);
      return SmsUtils.failed(config(), wrapper, phone, code + "->" + orDefault);
    }
  }

  private MonternetHttpClient selectClient(SmsSendRequest request) {
    MonternetHttpClient client = this.monternetSMS;
    //国际短信如果配置了营销短信账号，则非验证码短信使用营销账号发送短信
    if (this.monternetMarketingSMS != null && !request.getContent().contains("验证码")) {
      log.info("International SMS use marketing account");
      client = monternetMarketingSMS;
    }
    return client;
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_INTL, SUPPORT_CAPTCHA_INTL);
  }

  @Override
  public SmsProvider provider() {
    return config();
  }

  @Override
  public int getOrder() {
    return config().getOrder();
  }

}
