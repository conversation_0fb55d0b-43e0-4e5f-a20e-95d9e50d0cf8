package com.sharecrm.egress.sms.zsj;

import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 招商云国内短信通道，对接招商云PaaS平台"统一消息服务" 2.0版本
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
@ConditionalOnProperty(name = "sharecrm.api.sms.zhaoshang.enabled", havingValue = "true")
public class ZsjCmhkSender implements SmsSender, SmsBeanIgnoreDestroy {

  private final ZsjCmhkSmsService zsjSmsService;
  private final SmsProperties properties;

  public ZsjCmhkSender(ZsjCmhkSmsService zsjSmsService, SmsProperties properties) {
    this.zsjSmsService = zsjSmsService;
    this.properties = properties;
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    return zsjSmsService.sendSms(phone, wrapper);
  }

  @Override
  public SmsProvider provider() {
    return properties.getZhaoshang();
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_CHINESE);
  }

  @Override
  public int getOrder() {
    return DEFAULT_ORDER;
  }


}
