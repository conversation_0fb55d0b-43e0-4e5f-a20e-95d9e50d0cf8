package com.sharecrm.egress.task;

import com.sharecrm.egress.service.AsrTencentProvider;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 腾讯录音转文件轮训查询结果
 */
@Slf4j
@Component
@ConditionalOnBean(AsrTencentProvider.class)
@ConditionalOnProperty(name = "sharecrm.api.asr.tencent.task.enabled", havingValue = "true", matchIfMissing = true)
public class AsrTencentTask {

  private final AsrTencentProvider asrTencentProvider;

  public AsrTencentTask(AsrTencentProvider asrTencentProvider) {
    this.asrTencentProvider = asrTencentProvider;
  }

  /**
   * 大多数情况下，1小时的音频1-3分钟即可完成识别，我们每3分钟跑一次任务
   */
  @Scheduled(cron = "${sharecrm.api.asr.tencent.task.cron:0 0/3 * * * ?}")
  @SchedulerLock(name = "AsrTencentTask", lockAtLeastFor = "30s", lockAtMostFor = "10m")
  public void doTask() {
    log.info("asr tencent rec task start");
    try {
      asrTencentProvider.updateRecTaskStatus();
    } catch (Exception e) {
      log.warn("asr tencent rec task failed.", e);
    }
  }

}
