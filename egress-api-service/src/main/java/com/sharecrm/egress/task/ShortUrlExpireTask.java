package com.sharecrm.egress.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sharecrm.egress.config.ConditionalOnShortUrlEnabled;
import com.sharecrm.egress.config.ShortUrlProperties;
import com.sharecrm.egress.dao.ShortUrlHistoryMapper;
import com.sharecrm.egress.dao.ShortUrlMapper;
import com.sharecrm.egress.entity.ShortUrl;
import com.sharecrm.egress.entity.ShortUrlHistory;
import com.sharecrm.egress.utils.ShortUrlUtils;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 删除过期的短链，默认先不开启任务有些链接真的用了很多年还要用
 */
@Slf4j
@Component
@ConditionalOnShortUrlEnabled
@ConditionalOnProperty(name = "sharecrm.api.short.url.expire-task-enabled", havingValue = "true")
public class ShortUrlExpireTask {

  private final ShortUrlProperties properties;
  private final ShortUrlMapper shortUrlMapper;
  private final ShortUrlHistoryMapper shortUrlHistoryMapper;

  public ShortUrlExpireTask(ShortUrlProperties properties, ShortUrlMapper shortUrlMapper, ShortUrlHistoryMapper shortUrlHistoryMapper) {
    this.properties = properties;
    this.shortUrlMapper = shortUrlMapper;
    this.shortUrlHistoryMapper = shortUrlHistoryMapper;
  }

  @Scheduled(cron = "${sharecrm.api.short.url.expire.cron:0 0 2 * * ?}")
  @SchedulerLock(name = "ShortUrlExpireTask", lockAtLeastFor = "10m", lockAtMostFor = "60m")
  public void doExpire() {
    // 标记为删除，复制到历史表备份
    log.info("expire short url task start");
    try {
      int pageNo = 1;
      while (true) {
        Page<ShortUrl> page = new Page<>(pageNo, 100);
        Page<ShortUrl> shortUrls = shortUrlMapper.selectPage(page, Wrappers.<ShortUrl>lambdaQuery().eq(ShortUrl::getDeleted, 0));
        //没有记录了，退出
        List<ShortUrl> records = shortUrls.getRecords();
        if (CollectionUtils.isEmpty(records)) {
          log.info("expire short url is empty.");
          break;
        }
        log.info("expire short url, pageNo:{}, total:{}", pageNo, records.size());
        expireUrls(records);
        pageNo++;
      }
    } catch (Exception e) {
      log.warn("expire short url task failed.", e);
    }
    log.info("expire short url task end");
  }

  @Scheduled(cron = "${sharecrm.api.short.url.expire.delete.cron:0 0 3 * * ?}")
  @SchedulerLock(name = "ShortUrlExpireTask", lockAtLeastFor = "10m", lockAtMostFor = "60m")
  public void doExpireDelete() {
    //执行真正的删除
    log.info("expire short url delete task start");
    try {
      int pageNo = 1;
      while (true) {
        Page<ShortUrl> page = new Page<>(pageNo, 100);
        //找出已经标记为可删除的
        Page<ShortUrl> shortUrls = shortUrlMapper.selectPage(page, Wrappers.<ShortUrl>lambdaQuery().gt(ShortUrl::getDeleted, 1));
        //没有记录了，退出
        List<ShortUrl> records = shortUrls.getRecords();
        if (CollectionUtils.isEmpty(records)) {
          log.info("delete expire short url is empty.");
          break;
        }
        log.info("delete expire short url, pageNo:{}, total:{}", pageNo, records.size());
        deleteExpireUrls(records);
        pageNo++;
      }
    } catch (Exception e) {
      log.warn("delete expire short url task failed.", e);
    }
    log.info("expire short url delete task end");
  }

  private void deleteExpireUrls(List<ShortUrl> records) {
    records.forEach(shortUrl -> {
      if (ShortUrlUtils.isDeleteExpire(shortUrl, properties.getDefaultDeleteDay())) {
        log.info("delete short url, {}", shortUrl);
        shortUrlMapper.deleteById(shortUrl);
      }
    });
  }

  private void expireUrls(List<ShortUrl> shortUrls) {
    shortUrls.forEach(this::expireShortUrl);
  }

  private void expireShortUrl(ShortUrl shortUrl) {
    if (ShortUrlUtils.isExpire(shortUrl, properties.getExpireRule(), properties.getDefaultExpireDay())) {
      log.debug("expire url:{}", shortUrl);
      shortUrl.setDeleted(System.currentTimeMillis());
      ShortUrlHistory history = new ShortUrlHistory();
      BeanUtils.copyProperties(shortUrl, history);
      history.setId(null);
      //存入历史表
      shortUrlHistoryMapper.insert(history);
      //在主表保留一段时间，由另外一个程序删除
      shortUrlMapper.updateById(shortUrl);
    }
  }
}
