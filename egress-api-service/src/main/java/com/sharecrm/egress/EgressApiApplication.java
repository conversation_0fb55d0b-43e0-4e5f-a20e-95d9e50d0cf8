package com.sharecrm.egress;

import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.mongo.MongoMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Indexed;

/**
 * 访问第三方服务代理，提供地址位置转换、手机号归属地查询、短链转换、发送短信等服务
 *
 * <AUTHOR>
 */
@Indexed
@EnableScheduling
@EnableAspectJAutoProxy
@EnableSchedulerLock(defaultLockAtMostFor = "10s")
@SpringBootApplication(exclude = {MongoAutoConfiguration.class, MongoDataAutoConfiguration.class,
  MongoMetricsAutoConfiguration.class})
public class EgressApiApplication {
  public static void main(String[] args) {
    SpringApplication.run(EgressApiApplication.class, args);
  }

}
