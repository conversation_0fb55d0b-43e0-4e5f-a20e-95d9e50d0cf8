package com.sharecrm.egress.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局通用配置文件映射
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.short.url")
public class ShortUrlProperties {

  /**
   * 代理到foneshare的egress-api-service服务地址，只有在专属云才需要配置
   */
  private String proxyEgressUrl;

  /**
   * 是否开启双写，默认关闭
   */
  private boolean doubleWriteEnabled;

  private boolean doubleReadEnabled;

  /**
   * 默认的短链域名
   */
  private String domain = "https://fs80.cn";

  /**
   * 默认的短链过期时间，单位天
   */
  private int defaultExpireDay = 180;

  /**
   * 过期后保留N天，过了N天后真的执行删除
   */
  private int defaultDeleteDay = 180;

  /**
   * 短链长度，注意这个不要随便改
   */
  private int codeLength = 6;

  /**
   * 创建短链时最大重试次数
   */
  private int maxRetry = 20;

  /**
   * 短链过期规则，key:url，value:过期时间，单位天
   */
  private final Map<String, Integer> expireRule = new HashMap<>();

  /**
   * 在查询短链的时候，是否启用过期策略，为安全起见做个灰度，稳定后应该删除
   */
  private boolean timeoutEnabled;

  private boolean rateLimiterEnabled = true;

  /**
   * 结合rateLimiterCount实现在N时间窗口内最多调用N次的接口
   */
  private Duration rateLimiterDuration = Duration.ofSeconds(10);

  private int rateLimiterCount = 10;

  private Duration syncSingleSleep = Duration.ofMillis(5);

  private Duration syncSetSleep = Duration.ofSeconds(1);

}
