package com.sharecrm.egress.config;

import com.github.trace.TraceContext;
import com.sharecrm.egress.utils.WebUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 把Trace信息放入WebFlux上下文，注意部分业务依赖EA、EI、UID做灰度判断，不要随意丢弃
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TraceWebFilter implements WebFilter {

  /**
   * Spring cloud gateway 代理地图服务，前端传的ID当做trace id
   */
  private static final String GATEWAY_PARAM_TRACE_ID = "csid";

  private final List<String> names = List.of("X-fs-Trace-Id", "X-fs-RPC-Id", "X-fs-Trace-Color",
    "x-peer-name", "X-fs-User-Info", "X-fs-Employee-Id",
    "X-fs-Enterprise-Id", "X-fs-Enterprise-Account",
    "x-fs-ea", "x-fs-ei");

  @Override
  public @NonNull Mono<Void> filter(ServerWebExchange exchange, @NonNull WebFilterChain chain) {
    var headers = exchange.getRequest().getHeaders();
    var params = exchange.getRequest().getQueryParams();
    var tracing = processTracing(headers, params);
    var traceId = MDC.get("traceId");
    if (traceId != null) {
      exchange.getResponse().getHeaders().addIfAbsent("X-Trace-Id", traceId);
    }
    return chain.filter(exchange)
      //注意这里要return putAll以后的content，否则下游获取不到put后的值
      .contextWrite(ctx -> ctx.putAllMap(tracing))
      .doFinally(s -> doClear());
  }

  private void doClear() {
    TraceContext.remove();
    MDC.clear();
  }

  /**
   * 解析trace相关参数，并透传到下游服务，注意部分业务依赖EA、EI、UID做灰度判断，不要随意丢弃
   */
  private Map<String, String> processTracing(HttpHeaders headers, MultiValueMap<String, String> params) {
    Map<String, String> values = new HashMap<>();
    for (String name : names) {
      String value = headers.getFirst(name);
      if (value != null && !value.isEmpty()) {
        values.put(name, value);
        MDC.put(name, value);
      }
    }
    // 兼容header和param获取traceId
    var traceId = Optional.ofNullable(values.get("X-fs-Trace-Id"))
      .or(() -> Optional.ofNullable(params.getFirst("traceId")))
      .or(() -> Optional.ofNullable(params.getFirst(GATEWAY_PARAM_TRACE_ID)))
      .filter(x -> !x.isEmpty())
      .orElseGet(WebUtils::traceId);

    values.putIfAbsent("X-fs-Trace-Id", traceId);
    MDC.put("X-fs-Trace-Id", traceId);
    MDC.put("traceId", traceId);
    // 兼容header和param获取color
    var color = Optional.ofNullable(values.get("X-fs-Trace-Color")).orElseGet(() -> params.getFirst("_color"));
    if (color != null && !color.isEmpty()) {
      values.putIfAbsent("X-fs-Trace-Color", color);
    }
    // 设置userId方便打印日志
    var uid = values.get("X-fs-User-Info");
    if (uid != null) {
      if (uid.startsWith("E.")) {
        uid = uid.substring(2);
      }
      values.putIfAbsent("userId", uid);
      MDC.put("userId", uid);
    }
    return values;
  }

}
