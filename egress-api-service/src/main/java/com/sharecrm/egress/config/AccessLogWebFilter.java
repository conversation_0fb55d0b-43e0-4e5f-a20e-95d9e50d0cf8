package com.sharecrm.egress.config;

import com.google.common.base.CharMatcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.function.UnaryOperator;

@Component
@Slf4j(topic = "access-log")
public class AccessLogWebFilter implements WebFilter {

  private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MMM/yyyy:HH:mm:ss Z");

  @NonNull
  @Override
  public Mono<Void> filter(@NonNull ServerWebExchange exchange, @NonNull WebFilterChain chain) {
    long startTime = System.currentTimeMillis();
    BytesCounter decorator = new BytesCounter(exchange);
    // %{X-fs-ei}i %{X-fs-Enterprise-Account}i %{X-fs-Employee-Id}i &quot;%{x-peer-name}i&quot; &quot;%{X-FORWARDED-FOR}i&quot; &quot;%{x-real-ip}i&quot; %{X-fs-Trace-Id}i %h %t &quot;%r&quot; %s %b %D
    return chain.filter(decorator).doFinally(s -> {
      ServerHttpRequest req = exchange.getRequest();
      ServerHttpResponse res = exchange.getResponse();
      HttpHeaders headers = req.getHeaders();
      UnaryOperator<String> supplier = x -> Optional.ofNullable(headers.getFirst(x)).orElse("-");
      String ei = supplier.apply("X-fs-ei");
      String ea = supplier.apply("X-fs-Enterprise-Account");
      String userId = supplier.apply("X-fs-Employee-Id");
      String peerName = supplier.apply("x-peer-name");
      String xForwardedFor = supplier.apply("X-Forwarded-For");
      String xRealIp = supplier.apply("x-real-ip");
      String traceId = supplier.apply("X-fs-Trace-Id");
      int status = Optional.ofNullable(res.getStatusCode()).orElse(HttpStatus.NOT_ACCEPTABLE).value();
      String method = req.getMethod().name();
      String host = abbreviate(Optional.ofNullable(req.getRemoteAddress())
        // 真有意思，这个getAddress也能为null
        .map(InetSocketAddress::getAddress)
        .map(InetAddress::getHostAddress)
        .orElse(null));
      String path = abbreviate(req.getPath().value());
      long reqLength = Math.max(headers.getContentLength(), decorator.getRequestSize());
      long resLength = Math.max(res.getHeaders().getContentLength(), decorator.getResponseSize());

      long elapsedTime = System.currentTimeMillis() - startTime;

      StringBuilder builder = new StringBuilder(512);
      builder
        .append(ei)
        .append(" ")
        .append(ea)
        .append(" ")
        .append(userId)
        .append(" ")
        .append("\"").append(peerName).append("\"")
        .append(" ")
        .append("\"").append(xForwardedFor).append("\"")
        .append(" ")
        .append("\"").append(xRealIp).append("\"")
        .append(" ")
        .append(traceId)
        .append(" ")
        .append(host)
        .append(" ")
        .append("[")
        .append(ZonedDateTime.now().format(DATE_TIME_FORMATTER))
        .append("]")
        .append(" ")
        .append("\"")
        .append(method)
        .append(" ")
        .append(path)
        .append(" ")
        .append("HTTP/1.1")
        .append("\"")
        .append(" ")
        .append(status)
        .append(" ")
        .append(resLength)
        .append(" ")
        .append(elapsedTime)
        .append(" ")
        .append(reqLength);
      // access log 使用 debug 级别，避免被metrics组件默认收集到app log里收集两份      
      log.debug("{}", builder);
    });
  }

  private String abbreviate(String val) {
    if (val == null || val.isEmpty()) {
      return "-";
    }

    return StringUtils.abbreviate(CharMatcher.anyOf("\r\n\t ").removeFrom(val), "...", 128);
  }
}
