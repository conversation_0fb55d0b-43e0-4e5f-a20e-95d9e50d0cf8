package com.sharecrm.egress.config;

import com.sharecrm.egress.service.ActiveSessionApi;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

/**
 * 注入Bean：com.fxiaoke.enterpriserelation2.service.AuthService
 */
@Configuration
@ConditionalOnGatewayMapEnabled
@ImportResource({"classpath:enterpriserelation2/enterpriserelation.xml"})
public class GatewayAuthBeanImportConfig {

  @Bean
  @RefreshScope
  ActiveSessionApi activeSessionApi(EgressProperties properties) {
    WebClient webClient = WebClient.builder()
      .baseUrl(properties.getActiveSessionBaseUrl())
      .build();
    HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
      .builderFor(WebClientAdapter.create(webClient))
      .build();
    return httpServiceProxyFactory.createClient(ActiveSessionApi.class);
  }

}
