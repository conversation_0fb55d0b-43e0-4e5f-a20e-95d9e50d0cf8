package com.sharecrm.egress.config;

import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 短链公开地址进行限流，防止恶意攻击
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@ConditionalOnProperty(name = "sharecrm.api.short.url.rate-limiter-filter-enabled", havingValue = "true")
public class ShortUrlRateLimiterFilter implements WebFilter {

  private final ReactiveStringRedisTemplate redisTemplate;
  private final ShortUrlProperties properties;

  public ShortUrlRateLimiterFilter(@Autowired(required = false) ReactiveStringRedisTemplate redisTemplate, ShortUrlProperties properties) {
    this.redisTemplate = redisTemplate;
    this.properties = properties;
  }

  @Override
  public @NonNull Mono<Void> filter(@NonNull ServerWebExchange exchange, @NonNull WebFilterChain chain) {
    if (Objects.isNull(redisTemplate) || !properties.isRateLimiterEnabled()) {
      log.debug("short url rate limiter disabled");
      return chain.filter(exchange);
    }
    ServerHttpRequest request = exchange.getRequest();
    String path = request.getPath().value();
    if (!path.contains("/public-short-urls/")) {
      return chain.filter(exchange);
    }
    HttpHeaders headers = request.getHeaders();
    Optional<String> ip = reallyIp(headers);
    if (ip.isEmpty()) {
      log.info("can not get really ip, skipped short url limiter, path:{}", path);
      return chain.filter(exchange);
    }
    String agent = userAgent(headers);
    return isAllowAccess(ip.get(), agent)
      .flatMap(x -> {
        log.info("url:{}, ip: {},agent:{}, allow:{}", path, ip, agent, x);
        return x ? chain.filter(exchange) : tooManyRequests(exchange);
      });
  }

  private Mono<Void> tooManyRequests(@NonNull ServerWebExchange exchange) {
    exchange.getResponse().setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
    return exchange.getResponse().setComplete();
  }

  private Mono<Boolean> isAllowAccess(String ip, String userAgent) {
    // userAgent太长了，占内存，用hashCode代替原值
    String key = String.join(":", "egress-u-l", ip, "" + Objects.hashCode(userAgent));
    return redisTemplate.opsForValue().increment(key, 1)
      .flatMap(count -> {
        // 如果是第一次请求，重新设置过期时间
        if (count == 1) {
          return redisTemplate.expire(key, properties.getRateLimiterDuration())
            .map(v -> true);
        }
        return Mono.just(count <= properties.getRateLimiterCount());
      })
      .onErrorResume(e -> {
        log.warn("short url rate limiter redis error", e);
        return Mono.just(true);
      });
  }

  private Optional<String> reallyIp(HttpHeaders headers) {
    return WebUtils.firstNotBlankValue(List.of("X-Forwarded-For", "x-real-ip"), headers)
      .map(s -> s.split(",")[0]);
  }

  private String userAgent(HttpHeaders headers) {
    return WebUtils.firstNotBlankValue(List.of(HttpHeaders.USER_AGENT), headers)
      .orElse("none");
  }

}
