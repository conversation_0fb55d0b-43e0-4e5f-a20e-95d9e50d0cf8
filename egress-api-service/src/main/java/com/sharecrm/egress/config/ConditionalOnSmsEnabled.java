package com.sharecrm.egress.config;

import com.sharecrm.egress.config.ConditionalOnSmsEnabled.OnSmsEnabledCondition;
import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 短信服务开关，在某些专属云场景网络无白名单，配置关闭短信服务
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({OnSmsEnabledCondition.class})
public @interface ConditionalOnSmsEnabled {

  class OnSmsEnabledCondition extends AllNestedConditions {

    OnSmsEnabledCondition() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.sms.enabled", havingValue = "true", matchIfMissing = true)
    static class FoundProperty {

    }
  }

  class OnSmsNotEnabledCondition extends AllNestedConditions {

    OnSmsNotEnabledCondition() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.sms.enabled", havingValue = "false")
    static class FoundProperty {

    }
  }
}
