package com.sharecrm.egress.config;

import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.ocr.v1.region.OcrRegion;
import com.sharecrm.egress.sms.TencentUtils;
import com.sharecrm.egress.utils.NetUtils;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;
import java.util.function.Supplier;

/**
 * 文字识别（Optical Character Recognition，OCR ）相关配置
 */
@Configuration
public class OcrBeanConfig {

  private final OcrProperties properties;

  public OcrBeanConfig(OcrProperties properties) {
    this.properties = properties;
  }

  /**
   * 此时Spring Boot并不支持ConditionalOnTencentOcrEnabled，所以我们用Supplier套一层
   */
  @Bean
  @RefreshScope
  Supplier<OcrClient> tencentOcrClient() {
    OcrProperties.TencentConfig tencent = properties.getTencent();
    if (!tencent.isEnabled()) {
      return () -> null;
    }
    Credential credential = new Credential(tencent.getSecretId(), tencent.getSecretKey());
    ClientProfile profile = TencentUtils.clientProfile(tencent.getProxy());
    return () -> new OcrClient(credential, tencent.getRegion(), profile);
  }

  @Bean
  @RefreshScope
  Supplier<com.huaweicloud.sdk.ocr.v1.OcrClient> huaweiOcrClient() {
    OcrProperties.HuaweiConfig huawei = properties.getHuawei();
    if (!huawei.isEnabled()) {
      return () -> null;
    }
    ICredential auth = new BasicCredentials()
      .withAk(huawei.getSecretId())
      .withSk(huawei.getSecretKey())
      .withProjectId(huawei.getProjectId());
    HttpConfig config = HttpConfig.getDefaultHttpConfig();
    config.withIgnoreSSLVerification(true);
    config.setConnectionTimeout((int) huawei.getConnectionTimeout().toSeconds());

    String proxy = huawei.getProxy();
    if (StringUtils.isNotBlank(proxy)) {
      URI uri = NetUtils.uri(proxy);
      config.withProxyHost(uri.getHost())
        .withProxyPort(uri.getPort());
    }
    return () -> com.huaweicloud.sdk.ocr.v1.OcrClient.newBuilder()
      .withHttpConfig(config)
      .withCredential(auth)
      .withEndpoints(huawei.getEndpoints())
      .withRegion(OcrRegion.valueOf(huawei.getRegion()))
      .build();
  }

}
