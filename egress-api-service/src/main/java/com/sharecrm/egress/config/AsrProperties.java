package com.sharecrm.egress.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.asr")
public class AsrProperties {

  private final TencentConfig tencent = new TencentConfig();

  @Data
  @ToString(exclude = {"secretKey"})
  public static class TencentConfig {

    private boolean enabled;
    private String secretId;
    private String secretKey;
    private String endpoint = "asr.tencentcloudapi.com";
    private String proxy;
    private String region = "ap-beijing";

    /**
     * 长录音转文字默认引擎
     */
    private String defaultRecEngine = "16k_zh";
    
  }
}
