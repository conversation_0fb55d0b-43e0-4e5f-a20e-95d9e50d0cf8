package com.sharecrm.egress.config;

import lombok.Data;

import java.time.Duration;

@Data
public class ConnectionProperties {
  /**
   * 是否启用日志记录，记录请求和响应的hexdump内容
   */
  private boolean loggingEnabled = false;

  /**
   * 是否启用keepAlive，为null时根据是否开启代理赋予不同的默认值
   */
  private Boolean keepAlive;

  /**
   * 完整的proxy地址，包含域名和端口
   */
  private String proxy;

  private String nonProxyHosts;

  private Duration readTimeout = Duration.ofSeconds(15);
  private Duration writeTimeout = Duration.ofSeconds(15);
  private Duration connectTimeout = Duration.ofSeconds(3);
  private Duration proxyConnectTimeout = Duration.ofSeconds(3);
}
