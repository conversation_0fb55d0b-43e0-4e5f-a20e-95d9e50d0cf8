package com.sharecrm.egress.config;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import com.sharecrm.egress.utils.NetUtils;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Configuration
public class AsrBeanConfig {

  @Value("${sharecrm.api.asr.mongodb.config:fs-asr-mongo}")
  private String asrMongoConfig = "fs-asr-mongo";

  @Value("${sharecrm.api.asr.rocket.config:fs-asr-rocketmq}")
  private String rocketConfig = "fs-asr-rocketmq";

  private final AsrProperties asrProperties;

  public AsrBeanConfig(AsrProperties asrProperties) {
    this.asrProperties = asrProperties;
  }

  @RefreshScope
  @Bean("asrMongoDatastore")
  @ConditionalOnProperty(name = "sharecrm.api.asr.tencent.enabled", havingValue = "true")
  public MongoDataStoreFactoryBean asrMongoDatastore() {
    MongoDataStoreFactoryBean bean = new MongoDataStoreFactoryBean();
    bean.setConfigName(asrMongoConfig);
    return bean;
  }

  @RefreshScope
  @Bean("asrRocketMQProducer")
  @ConditionalOnProperty(name = "sharecrm.api.asr.tencent.enabled", havingValue = "true")
  public AutoConfMQProducer asrMqProducer() {
    return new AutoConfMQProducer(rocketConfig);
  }

  @Bean
  @RefreshScope
//  @ConditionalOnTencentAsrEnabled
  @ConditionalOnProperty(name = "sharecrm.api.asr.tencent.enabled", havingValue = "true")
  AsrClient tencentAsrClient() {
    AsrProperties.TencentConfig config = asrProperties.getTencent();
    Credential cred = new Credential(config.getSecretId(), config.getSecretKey());
    ClientProfile clientProfile = new ClientProfile();
    String proxy = config.getProxy();
    if (StringUtils.isNotBlank(proxy)) {
      HttpProfile httpProfile = new HttpProfile();
      URI uri = NetUtils.uri(proxy);
      httpProfile.setEndpoint(config.getEndpoint());
      httpProfile.setProxyHost(uri.getHost());
      httpProfile.setProxyPort(uri.getPort());
      clientProfile.setHttpProfile(httpProfile);
    }
    return new AsrClient(cred, config.getRegion(), clientProfile);
  }
}
