package com.sharecrm.egress.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * 翻译接口配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.translate")
public class TranslateProperties {

  private final GoogleConfig google = new GoogleConfig();

  @Data
  @ToString(exclude = {"apiKey"})
  public static class GoogleConfig {
    private boolean enabled = true;
    /**
     * 默认一个空值，部分环境不支持不要报空指针异常
     */
    private String apiKey = "";

    /**
     * full url
     */
    private String url = "https://translation.googleapis.com/language/translate/v2";

    private Duration readTimeout = Duration.ofSeconds(10);

    /**
     * http proxy
     */
    private String proxy;

    /**
     * The translation model. Cloud Translation - Basic offers only the nmt Neural Machine Translation (NMT) model.
     * <p>
     * If the model is base, the request is translated by using the NMT model.
     */
    private String model = "nmt";
  }

}
