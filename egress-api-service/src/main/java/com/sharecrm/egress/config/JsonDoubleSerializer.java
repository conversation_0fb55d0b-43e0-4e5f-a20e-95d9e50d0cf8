package com.sharecrm.egress.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.DecimalFormat;

public class JsonDoubleSerializer extends JsonSerializer<Double> {
  private final DecimalFormat format = new DecimalFormat("#.######");

  @Override
  public void serialize(Double value, JsonGenerator gen, SerializerProvider provider) throws IOException {
    if (null == value) {
      gen.writeNull();
    } else {
      gen.writeNumber(format.format(value));
    }
  }
}
