package com.sharecrm.egress.config;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * Open API config
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@OpenAPIDefinition(info = @Info(title = "EgressApiService", version = "1.0.0",
  description = "访问第三方服务代理，提供地址位置转换、手机号归属地查询、短链转换、发送短信等服务。"),
  tags = {
    @Tag(name = "sms", description = "短信服务"),
    @Tag(name = "email", description = "邮件服务"),
    @Tag(name = "short-url", description = "短链接转换服务"),
    @Tag(name = "dirty-word", description = "脏词检测服务"),
    @Tag(name = "location", description = "IP地址解析"),
    @Tag(name = "geocode", description = "地址转换经纬度"),
    @Tag(name = "reverse-geocode", description = "逆地址解析，经纬度查询地址"),
    @Tag(name = "driving", description = "驾车路线规划"),
    @Tag(name = "distance", description = "两地距离或者驾车距离计算")},
  externalDocs = @ExternalDocumentation(description = "参考文档", url = "https://www.lddgo.net/convert/coordinate")
)
public class OpenApiConfig {

}
