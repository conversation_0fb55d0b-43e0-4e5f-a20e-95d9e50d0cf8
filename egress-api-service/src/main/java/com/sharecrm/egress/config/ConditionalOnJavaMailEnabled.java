package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.condition.AnyNestedCondition;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({ConditionalOnJavaMailEnabled.OnJavaMailEnabled.class})
public @interface ConditionalOnJavaMailEnabled {

  class OnJavaMailEnabled extends AnyNestedCondition {
    OnJavaMailEnabled() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(prefix = "spring.mail", name = {"jndi-name"})
    static class JndiNameProperty {
      JndiNameProperty() {
      }
    }

    @ConditionalOnProperty(prefix = "spring.mail", name = {"host"})
    static class HostProperty {
      HostProperty() {
      }
    }
  }

}
