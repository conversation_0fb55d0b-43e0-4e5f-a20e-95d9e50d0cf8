package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.codec.CodecProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * 有些服务比如OCR数据比较大，超过默认256K限制，增大到10MB
 */
@Configuration
public class WebConfig implements WebFluxConfigurer {

  private final CodecProperties codecProperties;

  public WebConfig(CodecProperties codecProperties) {
    this.codecProperties = codecProperties;
  }

  @Override
  public void configureHttpMessageCodecs(ServerCodecConfigurer configurer) {
    configurer.defaultCodecs().maxInMemorySize((int) codecProperties.getMaxInMemorySize().toBytes());
  }
}
