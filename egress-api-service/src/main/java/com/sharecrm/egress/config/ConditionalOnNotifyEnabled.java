package com.sharecrm.egress.config;

import com.sharecrm.egress.config.ConditionalOnNotifyEnabled.OnNotifyEnabledCondition;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

/**
 * 消息推送服务开关，在某些专属云场景，不需要消息推送服务，可以通过配置关闭
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({OnNotifyEnabledCondition.class})
public @interface ConditionalOnNotifyEnabled {

  class OnNotifyEnabledCondition extends AllNestedConditions {

    OnNotifyEnabledCondition() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.notify.push.enabled", havingValue = "true")
    static class FoundProperty {

    }
  }
}
