package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 短链服务开关，在某些专属云场景，短链服务依赖的PG不提供，且短链需要规划单独的域名，可以配置不启用
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({ConditionalOnShortUrlEnabled.OnShortUrlEnabledCondition.class})
public @interface ConditionalOnShortUrlEnabled {

  class OnShortUrlEnabledCondition extends AllNestedConditions {

    OnShortUrlEnabledCondition() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.short.url.enabled", havingValue = "true", matchIfMissing = true)
    static class FoundProperty {

    }
  }

  class OnShortUrlNotEnabledCondition extends AllNestedConditions {

    OnShortUrlNotEnabledCondition() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.short.url.enabled", havingValue = "false")
    static class FoundProperty {

    }
  }
}
