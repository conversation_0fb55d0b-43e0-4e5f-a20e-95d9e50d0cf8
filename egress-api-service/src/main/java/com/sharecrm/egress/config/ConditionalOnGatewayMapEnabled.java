package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 使用Spring cloud gateway 代理地图服务开关
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({ConditionalOnGatewayMapEnabled.OnGatewayMapEnabled.class})
public @interface ConditionalOnGatewayMapEnabled {

  class OnGatewayMapEnabled extends AllNestedConditions {

    OnGatewayMapEnabled() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = {"spring.cloud.gateway.enabled"}, matchIfMissing = true)
    static class GatewayEnabledProperty {
    }

    @ConditionalOnProperty(name = {"sharecrm.api.map.gateway.proxy.enabled"}, matchIfMissing = true)
    static class FoundEnabledProperty {
    }
  }
}
