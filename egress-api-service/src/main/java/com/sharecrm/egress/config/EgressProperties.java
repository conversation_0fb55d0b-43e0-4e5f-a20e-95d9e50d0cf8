package com.sharecrm.egress.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * egress properties
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.egress.global")
public class EgressProperties {

  /**
   * Cookie校验服务地址
   */
  private String activeSessionBaseUrl = "http://fs-active-session-manage";

  /**
   * 互联用户Cookie校验服务地址
   */
  private String enterpriseRelationBaseUrl = "http://fs-enterprise-relation-biz-login/fs-er-http";

  private Integer sessionMaximumSize = 10 * 10000;

  private Duration sessionTimeout = Duration.ofMinutes(30);

  /**
   * 是否跳过 cookie 认证，用于在服务有问题时临时跳过，避免大面积故障
   */
  private boolean skipCookieAuth = false;

  /**
   * FSAuthXC|FSAuthX
   */
  private List<String> fsAuthCookies = List.of("FSAuthXC", "FSAuthX");

  /**
   * 互联账号 Cookie ERInfo CRInfo
   */
  private List<String> enterpriseAuthCookies = List.of("ERInfo", "CRInfo");

  /**
   * 远程多语言转换服务的 url
   */
  private String i18nUrl;

  /**
   * 城市简称和全称映射，k 是简称，v 是全称.
   * <p>
   * 仙桃是省直辖县级行政区划，是县级市，不是市，修正一下解决与国家真实区域无法匹配的问题。
   */
  private Map<String, String> cityFullNames = Map.of("巴州", "巴音郭楞蒙古自治州", "仙桃", "省直辖县级行政区划");


}
