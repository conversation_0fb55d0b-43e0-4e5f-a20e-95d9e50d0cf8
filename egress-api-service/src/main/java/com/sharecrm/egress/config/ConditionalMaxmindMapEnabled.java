package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * maxmind IP地址查询服务开关控制
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({ConditionalMaxmindMapEnabled.OnIpLocationEnabledCondition.class})
public @interface ConditionalMaxmindMapEnabled {

  class OnIpLocationEnabledCondition extends AllNestedConditions {

    OnIpLocationEnabledCondition() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.map.maxmind.enabled", havingValue = "true", matchIfMissing = true)
    static class FoundProperty {

    }
  }
}
