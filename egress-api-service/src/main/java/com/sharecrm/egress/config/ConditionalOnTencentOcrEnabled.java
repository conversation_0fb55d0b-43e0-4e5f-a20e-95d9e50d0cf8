package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Tencent OCR服务开关
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({ConditionalOnTencentOcrEnabled.OnTencentOcrEnabled.class})
public @interface ConditionalOnTencentOcrEnabled {

  class OnTencentOcrEnabled extends AllNestedConditions {

    OnTencentOcrEnabled() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.ocr.tencent.enabled", havingValue = "true")
    static class FoundEnabledProperty {
    }
  }
}
