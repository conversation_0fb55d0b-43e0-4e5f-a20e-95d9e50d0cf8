package com.sharecrm.egress.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.sharecrm.egress.api.GoogleTranslateApi;
import com.sharecrm.egress.api.HttpExchangeUtils;
import com.sharecrm.egress.api.I18nApi;
import com.sharecrm.egress.utils.Constants;
import com.sharecrm.egress.utils.NetUtils;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.reactor.circuitbreaker.operator.CircuitBreakerOperator;
import io.github.resilience4j.reactor.ratelimiter.operator.RateLimiterOperator;
import io.github.resilience4j.timelimiter.TimeLimiterConfig;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.circuitbreaker.resilience4j.ReactiveResilience4JCircuitBreakerFactory;
import org.springframework.cloud.circuitbreaker.resilience4j.Resilience4JConfigBuilder;
import org.springframework.cloud.client.circuitbreaker.Customizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.codec.ClientCodecConfigurer;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebExceptionHandler;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.net.URI;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Configuration
@Slf4j(topic = "egress-remote-log")
class HttpInterfaceConfig {
  private static final List<String> NAMES = List.of("X-fs-Trace-Id", "X-fs-RPC-Id", "X-fs-Trace-Color", "X-fs-User-Info", "X-fs-Employee-Id",
    "X-fs-Enterprise-Id", "X-fs-Enterprise-Account");

  private final TaskExecutor executor;

  HttpInterfaceConfig(@Qualifier("taskExecutor") TaskExecutor executor) {
    this.executor = executor;
  }

  /**
   * 修改默认的json mapper，因为WebClient自带的JsonEncoder不使用Spring Boot的全局参数。
   * <p>
   * 我们有些服务必须忽略json null值否则请求失败，比如i18n服务，如果序列化请求参数里包含 `xxx:null` 就会失败。
   *
   * @param objectMapper 全局Mapper
   * @return ExchangeStrategies
   */
  @Bean
  ExchangeStrategies jackson2JsonCodecs(ObjectMapper objectMapper) {
    return ExchangeStrategies.builder().codecs(configurer -> {
      ClientCodecConfigurer.ClientDefaultCodecs codecs = configurer.defaultCodecs();
      codecs.jackson2JsonEncoder(new Jackson2JsonEncoder(objectMapper, MediaType.APPLICATION_JSON));
      codecs.jackson2JsonDecoder(new Jackson2JsonDecoder(objectMapper, MediaType.APPLICATION_JSON));
    }).build();
  }

  @NotNull
  private HttpServiceProxyFactory httpServiceProxyFactory(Duration readTimeout, WebClient client) {
    return HttpExchangeUtils.httpServiceProxyFactory(readTimeout, client);
  }

  @Bean
  @ConditionalOnProperty(name = "sharecrm.api.translate.google.enabled", havingValue = "true", matchIfMissing = true)
  GoogleTranslateApi buildGoogleTranslateApi(ExchangeStrategies strategies, TranslateProperties config) {
    Map<String, String> params = Map.of("key", config.getGoogle().getApiKey());
    WebClient client = WebClient.builder()
      .baseUrl(config.getGoogle().getUrl())
      .exchangeStrategies(strategies)
      .defaultUriVariables(params)
      .defaultHeader("Content-Type", "application/json")
      .clientConnector(googleTranslateConnector(config))
      .build();
    HttpServiceProxyFactory factory = httpServiceProxyFactory(config.getGoogle().getReadTimeout(), client);
    return factory.createClient(GoogleTranslateApi.class);
  }

  @SneakyThrows
  private ClientHttpConnector googleTranslateConnector(TranslateProperties config) {
    ConnectionProperties adapter = new ConnectionProperties();
    adapter.setLoggingEnabled(true);
    adapter.setReadTimeout(config.getGoogle().getReadTimeout());
    adapter.setWriteTimeout(Duration.ofSeconds(5));
    adapter.setProxy(config.getGoogle().getProxy());
    adapter.setProxyConnectTimeout(Duration.ofSeconds(5));
    return HttpExchangeUtils.connector(adapter);
  }

  @Bean
  I18nApi buildI18nApi(ReactiveResilience4JCircuitBreakerFactory breakerFactory,
                       RateLimiterRegistry limiterRegistry,
                       ExchangeStrategies strategies,
                       EgressProperties config) {
    // 传递traceId等参数
    Consumer<WebClient.RequestHeadersSpec<?>> addTracingHeaders = spec -> NAMES.forEach(name -> {
      String value = MDC.get(name);
      if (value != null) {
        spec.header(name, value);
      }
    });
    ConnectionProperties adapter = new ConnectionProperties();
    adapter.setLoggingEnabled(true);
    WebClient client = WebClient
      .builder()
      .baseUrl(config.getI18nUrl())
      .exchangeStrategies(strategies)
      .defaultRequest(addTracingHeaders)
      // add caller for trace
      .defaultHeader("x-peer-name", Constants.APP_NAME)
      .defaultHeader("Content-Type", "application/json")
      .clientConnector(HttpExchangeUtils.connector(adapter))
      .filter(retryFilter(breakerFactory, limiterRegistry, "paas-static"))
      .build();
    HttpServiceProxyFactory factory = httpServiceProxyFactory(Duration.ofSeconds(10), client);
    return factory.createClient(I18nApi.class);
  }

  @Bean
  Customizer<ReactiveResilience4JCircuitBreakerFactory> defaultCustomizer() {
    return factory -> factory.configureDefault(id -> new Resilience4JConfigBuilder(id)
      .circuitBreakerConfig(CircuitBreakerConfig.ofDefaults())
      .timeLimiterConfig(TimeLimiterConfig.ofDefaults())
      .build());
  }

  /**
   * 设置熔断器、重试规则等
   *
   * @param breakerFactory  熔断器工厂
   * @param limiterRegistry 限流器注册中心
   * @return 请求过滤器
   */
  private ExchangeFilterFunction retryFilter(ReactiveResilience4JCircuitBreakerFactory breakerFactory,
                                             RateLimiterRegistry limiterRegistry,
                                             String breakerName) {
    var circuitBreaker = breakerFactory.getCircuitBreakerRegistry().circuitBreaker(breakerName);
    var limiter = limiterRegistry.rateLimiter(breakerName);
    long startTime = System.currentTimeMillis();
    return (req, next) -> {
      URI url = req.url();
      return next
        .exchange(req)
        .transformDeferred(RateLimiterOperator.of(limiter))
        .transform(CircuitBreakerOperator.of(circuitBreaker))
        .elapsed()
        .map(tuple -> {
          ClientResponse response = tuple.getT2();
          sendBizLog(tuple, response, url, startTime);
          return response;
        });
    };
  }

  private void sendBizLog(Tuple2<Long, ClientResponse> tuple, ClientResponse response, URI url, long startTime) {
    long cost = tuple.getT1();
    HttpStatusCode status = response.statusCode();
    long length = response.headers().asHttpHeaders().getContentLength();
    log.debug("cost: {}ms, status: {}, length: {}, URL: {}", cost, status, length, url);
    String host = url.getHost();
    AuditLogDTO dto = AuditLogDTO
      .builder()
      .appName(Constants.APP_NAME)
      .serverIp(Constants.IP)
      .profile(Constants.PROFILE)
      .createTime(startTime)
      .module("egress")
      .caller(host)
      .action(url.getPath())
      .parameters(NetUtils.maskQuery(url.getQuery()))
      .extra("responseLength=" + length)
      .cost(cost)
      .status(status.toString())
      .build();
    executor.execute(() -> BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray()));
  }

  @Bean
  WebExceptionHandler exceptionHandler() {
    return (ServerWebExchange exchange, Throwable ex) -> {
      log.error("请求异常, request: {}", exchange.getRequest().getURI(), ex);
      if (ex instanceof ResponseStatusException rse && rse.getStatusCode() == HttpStatus.NOT_FOUND) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.NOT_FOUND);
        return response.setComplete();
      }
      return Mono.error(ex);
    };
  }


}
