package com.sharecrm.egress.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;

/**
 * 文字识别（Optical Character Recognition，OCR ）配置文件
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.ocr")
public class OcrProperties {

  /**
   * OCR请求服务提供商允许的并发数，线程池控制
   */
  private int parallelism = 32;

  /**
   * 腾讯云OCR服务
   */
  private final TencentConfig tencent = new TencentConfig();

  /**
   * 华为云OCR服务
   */
  private final HuaweiConfig huawei = new HuaweiConfig();

  @Data
  @ToString(exclude = {"secretKey"})
  public static class HuaweiConfig {

    private String id = "huawei";

    /**
     * 优先级，数字越大优先级越低
     */
    private int order = 200;

    /**
     * 启停开关
     */
    private boolean enabled;

    private String projectId;

    /**
     * AK
     */
    private String secretId;
    /**
     * SK
     */
    private String secretKey;

    /**
     * 你账户开通所在的区域，具体参考官方文档，注意只有部分区域支持
     */
    private String region = "cn-north-4";

    private List<String> endpoints = List.of("https://ocr.cn-north-4.myhuaweicloud.com");

    /**
     * http proxy
     */
    private String proxy;

    /**
     * 服务调用者的总超时时间是10s，所以这里的时间不宜太长
     */
    private Duration connectionTimeout = Duration.ofSeconds(5);

  }

  @Data
  @ToString(exclude = {"secretKey"})
  public static class TencentConfig {

    private String id = "tencent";

    /**
     * 优先级，数字越大优先级越低
     */
    private int order = 100;

    /**
     * 启停开关
     */
    private boolean enabled;
    private String secretId;
    private String secretKey;
    /**
     * 你账户开通所在的区域，具体参考腾讯官方文档，截止目前仅支持以下几个: ap-beijing, ap-guangzhou, ap-hongkong, ap-shanghai, na-toronto 。
     */
    private String region = "ap-beijing";

    /**
     * http proxy
     */
    private String proxy;

  }

}
