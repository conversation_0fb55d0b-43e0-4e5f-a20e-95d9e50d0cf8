package com.sharecrm.egress.config;

import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Objects;

import static com.sharecrm.egress.utils.EgressUtils.nextDayStart;

/**
 * 租户级别对服务调用额度进行限制，防止被薅羊毛，防止某个租户调用太多导致全局服务不可用.
 * 租户 + 服务分类 + 接口级别， 按天进行额度控制，每天凌晨清零
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 10)
@ConditionalOnProperty(name = "sharecrm.api.quota.enabled", havingValue = "true")
@ConditionalOnBean(ReactiveStringRedisTemplate.class)
public class QuotaLimiterFilter implements WebFilter {

  private final ReactiveStringRedisTemplate redisTemplate;
  private final QuotaLimiterProperties properties;

  /**
   * -1 不限制； 0是不允许使用，无配额
   */
  private static final long UNLIMITED = -1L;

  private static final long NONE_QUOTA = 0L;


  public QuotaLimiterFilter(@Autowired(required = false) ReactiveStringRedisTemplate redisTemplate, QuotaLimiterProperties properties) {
    this.redisTemplate = redisTemplate;
    this.properties = properties;
  }

  @Override
  public @NonNull Mono<Void> filter(@NonNull ServerWebExchange exchange, @NonNull WebFilterChain chain) {
    if (Objects.isNull(redisTemplate) || !properties.isEnabled()) {
      log.debug("quota limiter disabled");
      return chain.filter(exchange);
    }
    ServerHttpRequest request = exchange.getRequest();
    long ei = WebUtils.getEiNumFromHeader(exchange);
    if (ei <= 0L) {
      log.debug("quota limiter ignore, ei is empty");
      return chain.filter(exchange);
    }
    // 有些VIP、超级VIP没有限额，也不要走redis耗费时间
    if (isIgnoreEi(ei)) {
      log.debug("quota limiter ignore ei:{}", ei);
      return chain.filter(exchange);
    }
    // 按path拆分接口分类
    String path = request.getPath().value();
    String catalog = serviceCatalog(path, request.getMethod().name());
    if (StringUtils.isEmpty(catalog)) {
      log.debug("quota limiter ignore url:{}", path);
      return chain.filter(exchange);
    }
    return isAllowAccess(catalog, ei)
      .flatMap(x -> {
        log.debug("url:{}, ei: {}, allow:{}", path, ei, x);
        return x ? chain.filter(exchange) : quotaExceededResponse(exchange);
      });
  }

  private String serviceCatalog(String path, String method) {
    return properties.getPathCatalogs()
      .entrySet()
      .stream()
      .filter(e -> path.contains(e.getValue().getPath())
        && method.equalsIgnoreCase(e.getValue().getMethod()))
      .map(Map.Entry::getKey)
      .findFirst()
      .orElse(null);
  }

  private boolean isIgnoreEi(long ei) {
    return properties.getIgnoreEis().contains(ei);
  }

  private Mono<Void> quotaExceededResponse(@NonNull ServerWebExchange exchange) {
    ServerHttpResponse response = exchange.getResponse();
    response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
    return response.setComplete();
  }

  private Mono<Boolean> isAllowAccess(String catalog, long ei) {
    long quota = quotaForCatalogWithEi(catalog, ei);
    if (quota == UNLIMITED) {
      log.debug("quota no limit for ei:{}, catalog:{}", ei, catalog);
      // -1 不限制； 0是不允许使用，无配额
      return Mono.just(true);
    }
    if (quota == NONE_QUOTA) {
      log.debug("none quota for ei:{}, catalog:{}", ei, catalog);
      return Mono.just(false);
    }
    // 实际调用这个服务的租户不多，但也不少，至少按每天1W租户考虑, 将租户ID分片到桶中，减少Key数量
    String redisBucketKey = "egress:q:" + ei % 10000;
    String redisHashKey = catalog + ":" + ei;
    return redisTemplate.opsForHash().increment(redisBucketKey, redisHashKey, 1)
      .flatMap(count -> {
        // 重新设置过期时间，其实这里可能会重复设置，但是我们是 expireAt，每天0点清理，所以不影响逻辑
        if (count == 1) {
          return redisTemplate.expireAt(redisBucketKey, nextDayStart())
            .map(v -> true);
        }
        return Mono.just(count <= quota);
      })
      .onErrorResume(e -> {
        log.warn("quota limiter redis error", e);
        return Mono.just(true);
      });
  }

  private long quotaForCatalogWithEi(String catalog, long ei) {
    return properties.getTenantQuotas()
      .getOrDefault(ei, properties.getGlobalQuotas())
      .getOrDefault(catalog, UNLIMITED);
  }
  
}
