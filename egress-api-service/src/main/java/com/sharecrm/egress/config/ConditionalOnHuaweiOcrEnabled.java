package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <PERSON>aw<PERSON> OCR服务开关
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.CONSTRUCTOR})
@Conditional({ConditionalOnHuaweiOcrEnabled.OnHuaweiOcrEnabled.class})
public @interface ConditionalOnHuaweiOcrEnabled {

  class OnHuaweiOcrEnabled extends AllNestedConditions {

    OnHuaweiOcrEnabled() {
      super(ConfigurationPhase.PARSE_CONFIGURATION);
    }

    @ConditionalOnProperty(name = "sharecrm.api.ocr.huawei.enabled", havingValue = "true")
    static class FoundEnabledProperty {
    }
  }
}
