package com.sharecrm.egress.config;

import org.checkerframework.checker.nullness.qual.NonNull;
import org.reactivestreams.Publisher;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.ServerWebExchangeDecorator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.concurrent.atomic.LongAdder;

public class BytesCounter extends ServerWebExchangeDecorator {
  private final ServerWebExchange exchange;

  private final LongAdder requestSize = new LongAdder();
  private final LongAdder responseSize = new LongAdder();

  public BytesCounter(ServerWebExchange exchange) {
    super(exchange);
    this.exchange = exchange;
    this.requestLineAndHeaderSize(exchange.getRequest());
  }

  private void requestLineAndHeaderSize(ServerHttpRequest req) {
    // 计算 requestLineSize + headersSize
    requestSize.add(req.getMethod().name().length() + 1L);
    requestSize.add(req.getURI().toASCIIString().length());
    req.getHeaders().forEach((name, values) -> {
      requestSize.add(name.length() + 1L);
      values.forEach(v -> requestSize.add(v.length()));
    });
  }

  @NonNull
  @Override
  public ServerHttpRequest getRequest() {
    return new ServerHttpRequestDecorator(exchange.getRequest()) {
      @NonNull
      @Override
      public Flux<DataBuffer> getBody() {
        return super.getBody().doOnNext(buffer -> requestSize.add(buffer.readableByteCount()));
      }
    };
  }

  @NonNull
  @Override
  public ServerHttpResponse getResponse() {
    return new ServerHttpResponseDecorator(exchange.getResponse()) {
      @Override
      @NonNull
      public Mono<Void> writeWith(@NonNull Publisher<? extends DataBuffer> body) {
        return super.writeWith(Flux.from(body).doOnNext(buffer -> responseSize.add(buffer.readableByteCount())));
      }
    };
  }

  /**
   * 请求的大小是一个接近的数值，不是精确的数值
   */
  public long getRequestSize() {
    return requestSize.longValue();
  }

  /**
   * 响应包体的大小
   */
  public long getResponseSize() {
    return responseSize.longValue();
  }
}
