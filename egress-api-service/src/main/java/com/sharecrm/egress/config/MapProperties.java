package com.sharecrm.egress.config;

import com.sharecrm.egress.entity.MobileLocation;
import com.sharecrm.egress.geo.GeoAdapter;
import com.sharecrm.egress.geo.GeoProvider;
import com.sharecrm.egress.utils.Constants;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_COORDINATE_CONVERT;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_DISTANCE;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_DRIVING;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_GEO_ADDRESS;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_IP_LOCATION;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_LOCATION_POINT;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_POI_AROUND;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_REVERSE_GEO;

/**
 * 高德、百度等地图服务相关的配置文件映射
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Data
@Configuration
@ConfigurationProperties(prefix = MapProperties.KEY_PREFIX)
public class MapProperties {

  public static final String KEY_PREFIX = "sharecrm.api.map";
  /**
   * 通用本地缓存的超时时间
   */
  private Duration cacheTimeout = Duration.ofMinutes(15);

  /**
   * 本地缓存，可长时间缓存的超时时间
   */
  private Duration longCacheTimeout = Duration.ofHours(24);

  /**
   * 如果是空数据，缓存的时间，时间比较短
   */
  private Duration emptyTimeout = Duration.ofSeconds(10);

  /**
   * 本地缓存的最大个数
   */
  private int cacheMaximumSize = 10000;

  /**
   * 在 x 米范围内的 radius 数据才加入 ES cache，否则误差太大
   */
  private int maxCacheRadius = 100;

  /**
   * 高德地图服务提供商，可能有多个账号
   */
  private Map<String, AMapConfig> amap = Map.of();

  private Map<String, BaiduConfig> baidu = Map.of();

  private Map<String, TencentConfig> tencent = Map.of();

  private Map<String, HuaweiConfig> huawei = Map.of();

  private Map<String, GoogleConfig> google = Map.of();

  private MaxmindConfig maxmind = new MaxmindConfig();

  private ElasticsearchConfig elasticsearch = new ElasticsearchConfig();

  @Data
  public static class AMapConfig implements GeoProvider {
    private String id = "amap";
    private String name = "高德地图";
    private String type = Constants.MAP_PROVIDER_AMAP;
    private boolean enabled = true;
    private int order = 0;

    /**
     * 权重值，数字越大权重越大分配的流量越多，建议使用小数字不要超过10
     */
    private int weight = 3;

    private List<String> supports = List.of(SUPPORT_COORDINATE_CONVERT, SUPPORT_DISTANCE, SUPPORT_DRIVING, SUPPORT_LOCATION_POINT, SUPPORT_GEO_ADDRESS, SUPPORT_REVERSE_GEO, SUPPORT_POI_AROUND, SUPPORT_IP_LOCATION);

    /**
     * 缓存地理位置允许的误差范围，单位米，在X米内如果有缓存，直接用缓存值，不指定时需要系统全局默认值
     */
    private int radius = 0;

    private String apiUrl = "https://restapi.amap.com";

    /**
     * 认证用高德Key
     */
    private String key;

    /**
     * 认证用数字签名 sk
     */
    private String sig;

    private ConnectionProperties connection = new ConnectionProperties();

    /**
     * 临时控制是否出入country，为海外地图做准备
     */
    private boolean countryEnabled = false;

  }

  @Data
  public static class HuaweiConfig implements GeoProvider {
    private String id = "huawei";
    private String name = "华为地图";
    private String type = Constants.MAP_PROVIDER_HUAWEI;
    private boolean enabled = true;
    private int order = 0;

    /**
     * 权重值，数字越大权重越大分配的流量越多，建议使用小数字不要超过10
     */
    private int weight = 1;

    private List<String> supports = List.of(GeoAdapter.SUPPORT_GATEWAY_REST, GeoAdapter.SUPPORT_GATEWAY_WEB_API);

    /**
     * 认证用Key
     */
    private String key;

    /**
     * 认证用数字签名 sk
     */
    private String sig;

    private ConnectionProperties connection = new ConnectionProperties();
  }

  @Data
  public static class BaiduConfig implements GeoProvider {
    private String id = "baidu";
    private String name = "百度地图";
    private String type = Constants.MAP_PROVIDER_BAIDU;
    private boolean enabled = true;
    private int order = 100;
    /**
     * 权重值，数字越大权重越大分配的流量越多，建议使用小数字不要超过10
     */
    private int weight = 1;

    private List<String> supports = List.of(SUPPORT_LOCATION_POINT, SUPPORT_REVERSE_GEO, SUPPORT_IP_LOCATION);
    private int radius = 0;
    /**
     * 认证用 ak
     */
    private String key;

    /**
     * 认证用数字签名 sk
     */
    private String sig;

    private String apiUrl = "https://api.map.baidu.com";

    private ConnectionProperties connection = new ConnectionProperties();
  }

  @Data
  public static class TencentConfig implements GeoProvider {
    private String id = "tencent";
    private String name = "腾讯地图";
    private String type = Constants.MAP_PROVIDER_TENCENT;
    private boolean enabled = true;
    private int order = 200;
    /**
     * 权重值，数字越大权重越大分配的流量越多，建议使用小数字不要超过10
     */
    private int weight = 2;

    private List<String> supports = List.of(SUPPORT_DISTANCE, SUPPORT_DRIVING, SUPPORT_LOCATION_POINT, SUPPORT_GEO_ADDRESS, SUPPORT_REVERSE_GEO, SUPPORT_POI_AROUND, SUPPORT_IP_LOCATION);
    private int radius = 0;
    /**
     * 认证用Key
     */
    private String key;

    /**
     * 认证用数字签名 sk
     */
    private String sig;

    private String apiUrl = "https://apis.map.qq.com";

    private ConnectionProperties connection = new ConnectionProperties();
  }

  @Data
  public static class GoogleConfig implements GeoProvider {
    private String id = "google";
    private String name = "Google地图";
    private String type = Constants.MAP_PROVIDER_GOOGLE;
    private boolean enabled = true;
    private int order = 300;
    /**
     * 权重值，数字越大权重越大分配的流量越多，建议使用小数字不要超过10
     */
    private int weight = 1;
    private List<String> supports = List.of(SUPPORT_DRIVING, SUPPORT_LOCATION_POINT, SUPPORT_GEO_ADDRESS, SUPPORT_REVERSE_GEO, SUPPORT_POI_AROUND);
    private int radius = 0;

    /**
     * 认证用Key
     */
    private String key;

    private String mapApiUrl = "https://maps.googleapis.com";
    private String routeApiUrl = "https://routes.googleapis.com";
    private String fieldMask = "routes.duration,routes.distanceMeters";

    private ConnectionProperties connection = new ConnectionProperties();
  }


  @Data
  public static class MaxmindConfig implements GeoProvider {
    private String id = "maxmind";
    private String name = "Maxmind";
    private String type = Constants.MAP_PROVIDER_MAXMIND;
    private boolean enabled = true;
    private int order = 400;
    /**
     * 权重值，数字越大权重越大分配的流量越多，建议使用小数字不要超过10
     */
    private int weight = 1;
    /**
     * 从阿里云oss存储下载2M以上的配置文件（不适合放在代码中）
     */
    private String aliOssEndpoint;
    private String aliOssAccessKeyId;
    private String aliOssSecretAccessKey;
    private String aliOssBucketName;

    /**
     * 完整的proxy地址，包含域名和端口
     */
    private String proxy;

    /**
     * 自定义一些静态配置，有时候数据库里没有，从此读取配置
     */
    private Map<Integer, MobileLocation> ext = Map.of();

  }

  @Data
  public static class ElasticsearchConfig implements GeoProvider {
    private String id = "elasticsearch";
    private String name = "elasticsearch";
    private String type = "elasticsearch";
    private boolean enabled = true;
    private int order = 1000;
    
    /**
     * GEO地址索引
     */
    private String index = "egress_geo_address";

    /**
     * ES存储过期时间
     */
    private Duration cacheTimeout = Duration.ofDays(7);

    private int slices = 3;

    /**
     * 查询GEO数据时一页最大值，我们有重复数据，基于重复数据主动去重，所以这个值不要太小
     */
    private int pageSize = 2000;

    private Float requestsPerSecond = 1.0F;
    private long scrollSize = 10000L;

    /**
     * IP地址缓存index
     */
    private String ipIndex = "egress_ip_location";

    /**
     * 是否允许从 ES cache 查询
     */
    private boolean ipCacheReadEnabled = false;

    private boolean ipCacheWriteEnabled = true;

    /**
     * IP地址 ES存储过期时间
     */
    private Duration ipCacheTimeout = Duration.ofDays(180);

    /**
     * 必须有多少个返回值才认为缓存有效
     */
    private int minAddressHits = 3;

    /**
     * ES地址查询根据距离计算最少返回多少个数据的模数
     */
    private int minAddressModulo = 100;

  }

}
