package com.sharecrm.egress.config;

import com.alibaba.csp.sentinel.adapter.spring.webflux.SentinelWebFluxFilter;
import com.alibaba.csp.sentinel.adapter.spring.webflux.callback.DefaultBlockRequestHandler;
import com.alibaba.csp.sentinel.adapter.spring.webflux.callback.WebFluxCallbackManager;
import com.alibaba.csp.sentinel.adapter.spring.webflux.exception.SentinelBlockExceptionHandler;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.NetUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.reactive.result.view.ViewResolver;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * WebFlux Sentinel Bean
 */
@Configuration
@ConditionalOnProperty(name = "sharecrm.api.sentinel.webflux.enabled")
public class SentinelWebFluxConfig {

  private final List<ViewResolver> viewResolvers;
  private final ServerCodecConfigurer serverCodecConfigurer;

  public SentinelWebFluxConfig(ObjectProvider<List<ViewResolver>> viewResolversProvider,
                               ServerCodecConfigurer serverCodecConfigurer) {
    this.viewResolvers = viewResolversProvider.getIfAvailable(Collections::emptyList);
    this.serverCodecConfigurer = serverCodecConfigurer;
    this.initBlockConfig();
  }

  private void initBlockConfig() {
    WebFluxCallbackManager.setUrlCleaner((serverWebExchange, url) -> NetUtils.cleanUrl(url));
    WebFluxCallbackManager.setBlockHandler(new EgressBlockRequestHandler());
    WebFluxCallbackManager.setRequestOriginParser(exchange -> Optional.ofNullable(exchange.getRequest()
      .getHeaders().getFirst("x-peer-name")).orElse("-"));
  }

  @Bean
  @Order(-1)
  public SentinelBlockExceptionHandler sentinelBlockExceptionHandler() {
    // Register the block exception handler for Spring WebFlux.
    return new SentinelBlockExceptionHandler(viewResolvers, serverCodecConfigurer);
  }

  @Bean
  @Order(-1)
  public SentinelWebFluxFilter sentinelWebFluxFilter() {
    // Register the Sentinel WebFlux filter.
    return new SentinelWebFluxFilter();
  }

  static class EgressBlockRequestHandler extends DefaultBlockRequestHandler {
    @Override
    public Mono<ServerResponse> handleRequest(ServerWebExchange exchange, Throwable ex) {
      return ServerResponse.status(HttpStatus.TOO_MANY_REQUESTS).contentType(MediaType.APPLICATION_JSON)
        .bodyValue(new EgressApiResponse<>(HttpStatus.TOO_MANY_REQUESTS.value(), HttpStatus.TOO_MANY_REQUESTS.getReasonPhrase()));
    }
  }

}
