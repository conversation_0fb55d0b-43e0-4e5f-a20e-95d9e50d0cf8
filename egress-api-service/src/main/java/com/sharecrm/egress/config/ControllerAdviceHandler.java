package com.sharecrm.egress.config;


import com.sharecrm.egress.exception.SmsArgException;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.support.WebExchangeBindException;
import org.springframework.web.reactive.resource.NoResourceFoundException;
import org.springframework.web.server.MethodNotAllowedException;
import org.springframework.web.server.MissingRequestValueException;
import org.springframework.web.server.NotAcceptableStatusException;
import org.springframework.web.server.ServerWebInputException;
import reactor.core.publisher.Mono;

@Slf4j
@ControllerAdvice
public class ControllerAdviceHandler {

  @ExceptionHandler(value = SmsArgException.class)
  public Mono<ResponseEntity<EgressApiResponse<SmsSendResponse>>> smsArgException(SmsArgException ex) {
    //短信格式不合法
    log.warn("bad request ", ex);
    SmsSendResponse resp = new SmsSendResponse();
    resp.setSuccess(false);
    resp.setMessage(ex.getMessage());
    return Mono.just(ResponseEntity.badRequest().body(new EgressApiResponse<>(400, ex.getMessage(), resp)));
  }

  @ExceptionHandler(value = NotAcceptableStatusException.class)
  public Mono<ResponseEntity<EgressApiResponse<String>>> notAcceptableStatusException(NotAcceptableStatusException ex) {
    //一般是调用者写错了context-type
    log.warn("bad request ", ex);
    HttpStatus status = HttpStatus.NOT_ACCEPTABLE;
    return Mono.just(ResponseEntity.status(status).body(new EgressApiResponse<>(status.value(), status.getReasonPhrase())));
  }

  @ExceptionHandler(value = WebExchangeBindException.class)
  public Mono<ResponseEntity<EgressApiResponse<FieldError>>> methodArgumentNotValidException(WebExchangeBindException ex) {
    log.warn("bad request ", ex);
    return Mono.just(ResponseEntity.badRequest().body(new EgressApiResponse<>(400, ex.getMessage(), ex.getFieldError())));
  }

  @ExceptionHandler(value = ServerWebInputException.class)
  public Mono<ResponseEntity<EgressApiResponse<String>>> serverWebInputException(ServerWebInputException ex) {
    log.warn("bad request ", ex);
    return Mono.just(ResponseEntity.badRequest().body(new EgressApiResponse<>(400, ex.getMessage(), ex.getReason())));
  }

  @ExceptionHandler(value = NoResourceFoundException.class)
  public Mono<ResponseEntity<EgressApiResponse<String>>> noResourceFoundException(NoResourceFoundException ex) {
    String message = ex.getMessage();
    //短链服务放在公网的，有些短链不合规会重定向回来进入这个异常，避免打印这样的无用信息
    if (StringUtils.containsAny(message, "public-short-urls", "metrics")) {
      log.debug("bad request:{}", message);
    } else {
      log.warn("bad request ", ex);
    }
    return Mono.just(ResponseEntity.notFound().build());
  }

  @ExceptionHandler(value = MissingRequestValueException.class)
  public Mono<ResponseEntity<EgressApiResponse<String>>> missingRequestValueException(MissingRequestValueException ex) {
    log.warn("bad request ", ex);
    return Mono.just(ResponseEntity.badRequest().body(new EgressApiResponse<>(400, ex.getMessage(), ex.getReason())));
  }

  @ExceptionHandler(value = MethodNotAllowedException.class)
  public Mono<ResponseEntity<EgressApiResponse<String>>> methodNotAllowedException(MethodNotAllowedException ex) {
    log.warn("bad request: {}", ex.getMessage());
    return Mono.just(ResponseEntity.badRequest().body(new EgressApiResponse<>(400, ex.getMessage(), ex.getReason())));
  }

  @ExceptionHandler(value = NumberFormatException.class)
  public Mono<ResponseEntity<EgressApiResponse<String>>> poiNumberFormatException(NumberFormatException ex) {
    log.warn("bad number format", ex);
    return Mono.just(ResponseEntity.badRequest().body(new EgressApiResponse<>(400, "bad number", "bad number")));
  }

  @ExceptionHandler(value = Exception.class)
  public Mono<ResponseEntity<EgressApiResponse<String>>> handleException(Exception ex) {
    log.error("cannot handle request ", ex);
    return Mono.just(ResponseEntity.internalServerError().body(new EgressApiResponse<>(500, "Internal Server Error")));
  }
}
