package com.sharecrm.egress.config;

import com.sharecrm.egress.entity.JwtProperties;
import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.List;

/**
 * 消息推送服务的配置文件映射
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.notify.push")
public class NotifyProperties {

  private boolean skipAndroid = false;
  private boolean skipApple = false;

  /**
   * 是否开启推送记录，将推送结果记录到数据库，部分专属云可能没有数据库，只推送不记录
   */
  private boolean recordEnabled = true;

  /**
   * 删除无效Token的服务地址，完整URL
   */
  private String tokenUrl;

  private FcmConfig fcm = new FcmConfig();
  private GeTuiPushConfig getui = new GeTuiPushConfig();
  private XiaoMiPushConfig xiaomi = new XiaoMiPushConfig();

  private OppoPushConfig oppo = new OppoPushConfig();

  private VivoPushConfig vivo = new VivoPushConfig();

  private ApplePushConfig appleEnt = new ApplePushConfig();
  private ApplePushConfig appleProd = new ApplePushConfig();

  /**
   * Apple海外测试包
   */
  private ApplePushConfig appleEnEnt = new ApplePushConfig();
  
  /**
   * Apple 海外正式包
   */
  private ApplePushConfig appleEnProd = new ApplePushConfig();

  private HuaWeiPushConfig huaweiEnt = new HuaWeiPushConfig();
  private HuaWeiPushConfig huaweiProd = new HuaWeiPushConfig();
  /**
   * 华为 google play
   */
  private HuaWeiPushConfig huaweiPlay = new HuaWeiPushConfig();

  /**
   * 华为鸿蒙消息推送
   */
  private HarmonyPushConfig harmony = new HarmonyPushConfig();

  /**
   * 荣耀消息推送
   */
  private HonorPushConfig honor = new HonorPushConfig();

  /**
   * 华为推送 hms-push 的配置信息类
   */
  @Data
  @ToString(exclude = {"appKey"})
  public static class HuaWeiPushConfig {
    private String appId;
    private String appKey;
    private String tokenUrl = "https://oauth-login.cloud.huawei.com/oauth2/v2/token";
    private String pushUrl;
  }

  /**
   * 华为鸿蒙消息推送
   */
  @Data
  public static class HarmonyPushConfig {


    @NestedConfigurationProperty
    private JwtProperties jwt = new JwtProperties();

    /**
     * 消息类型，取值如下： 0：Alert消息（通知消息、授权订阅消息） 1：卡片刷新消息 2：通知扩展消息 6：后台消息 7：实况窗消息 10：VoIP呼叫消息
     */
    private String pushType = "0";

    private String pushUrl = "https://push-api.cloud.huawei.com/v3/388421841222428747/messages:send";
  }

  /**
   * 荣耀消息推送
   */
  @Data
  public static class HonorPushConfig {

    private Duration tokenTimeout = Duration.ofSeconds(3500);

    private String clientId;
    private String clientSecret;
    private String tokenUrl = "https://iam.developer.honor.com/auth/token";
    private String badgeClass = "com.facishare.fs.MainTabActivity";

    /**
     * 推送消息URL，900778358是荣耀的AppId
     */
    private String pushUrl = "https://push-api.cloud.honor.com/api/v1/900778358/sendMessage";
  }


  @Data
  public static class ApplePushConfig {

    private String apnsTokenPath = "token/apns/AuthKey_production.p8";
    private String teamId;
    private String keyId;
    private Integer cacheLength = 10000;
    private Integer maxConnections = 20;
    private Integer connectTimeout = 5 * 1000;
    private Integer idlePingInterval = 30 * 1000;
    private Integer readTimeout = 60 * 1000;
    private boolean sandbox = false;
    private String topic;
    private Integer eventLoopGroup;
    private String proxy;
  }

  @Data
  @ToString(exclude = {"appKey", "masterSecret"})
  public static class GeTuiPushConfig {

    private String appId;
    private String appKey;
    private String masterSecret;
    private String host = "mmp.nz.igexin.com";
    private int hostPort = 5266;

    /**
     * http proxy
     */
    private String proxy;
  }

  @Data
  public static class FcmConfig {
    private String projectId;
    private String authFile = "token/fcm/firebase-adminsdk.json";
    private String clickAction = "com.fxiaoke.host.FCMNotifyView";
    private String packageName = "com.facishare.fsplay";
    private String proxy;
  }

  @Data
  @ToString(exclude = {"appKey", "appSecret"})
  public static class XiaoMiPushConfig {

    private String appId;
    private String appKey;
    private String appSecret;
    private List<String> packageNames = List.of("com.facishare.fs", "com.facishare.fsneice", "com.facishare.fsplay");
    private String proxy;
  }

  @Data
  @ToString(exclude = {"appKey", "appSecret", "masterSecret"})
  public static class OppoPushConfig {
    private String appId;
    private String appKey;
    private String appSecret;
    private String masterSecret;
    private String proxy;
  }

  @Data
  @ToString(exclude = {"appKey", "appSecret"})
  public static class VivoPushConfig {
    private Integer appId;
    private String appKey;
    private String appSecret;
    private String apiUrl = "https://api-push.vivo.com.cn";
  }


}
