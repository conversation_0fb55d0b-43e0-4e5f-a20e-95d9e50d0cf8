package com.sharecrm.egress.config;

import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.TtsTemplate;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sms.SmsUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.sharecrm.egress.sms.SmsSender.DEFAULT_ORDER;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_CAPTCHA_INTL;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_TTS;

/**
 * 短信服务配置文件
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.sms")
public class SmsProperties {

  private boolean enabled = true;

  /**
   * 短信忽略的号码，不实际发短信，用于一些测试的假手机号
   */
  private List<String> ignorePhones = new ArrayList<>();

  /**
   * 对一些特殊bizName的请求，不计入限流次数，比如邀请短信，刚邀请完立马就去获取验证码，为获取验证码成功，邀请短信就不计数
   */
  private List<String> ignoreLimitBizNames = List.of("SEND_FREE_AUDIT_INVITE_MESSAGE", "SEND_CTEATE_EMPLOYEE_MESSAGE", "SEND_CREATE_EMPLOYEE_MESSAGE");

  /**
   * 梦网国际短信通道(老版本，逐渐废弃)
   */
  private final MonternetConfig monternetIntl = new MonternetConfig();

  /**
   * 梦网国际短信通道V5版本API（新版本）
   */
  private final MonternetConfig monternetIntlV5 = new MonternetConfig();

  /**
   * 梦网国内短信通道
   */
  private final MonternetConfig monternetInternal = new MonternetConfig();

  /**
   * 大汉三通短信通道
   */
  private final DahantcConfig dahantc = new DahantcConfig();

  /**
   * 纷享阿里云短信通道
   */
  private final AliYunConfig ali = new AliYunConfig();

  /**
   * VIP客户鼎桥通信，阿里云短信通道
   */
  private final DingqiaoAliConfig dingqiaoAli = new DingqiaoAliConfig();

  /**
   * 招商局-集团环境PaaS平台短信通道。仅用于招商云内部私有化部署，foneshare用不到
   */
  private final ZhaoShangConfig zhaoshang = new ZhaoShangConfig();

  /**
   * 招商局-中国外运短信通道。仅用于招商云内部私有化部署，foneshare用不到
   */
  private final SinoConfig sino = new SinoConfig();

  /**
   * 超级VIP客户蒙牛的短信通道，只给蒙牛客户使用
   */
  private final MengniuConfig mengniu = new MengniuConfig();

  /**
   * 各个阿里云专用配置
   */
  private Map<String, AliYunConfig> aliyuns = Map.of();

  /**
   * 各个腾讯云专用配置
   */
  private Map<String, TencentConfig> tencents = Map.of();

  private Map<String, BytePlusConfig> byteplus = Map.of();

  @Data
  @ToString(exclude = {"password", "marketingPassword"})
  public static class MonternetConfig implements SmsProvider {

    /**
     * 短信服务通道唯一ID，我们自己定义不可随便改
     */
    private String id = "monternet";

    /**
     * 注意名字是客户在页面上可看到的，要唯一且易读
     */
    private String name = "移动梦网";

    private String type = SmsSdkConstants.SMS_PROVIDER_TYPE_MONTERNET;

    /**
     * true表示专属定制，只能给特定的云或EA使用，比如蒙牛云短信通道只能给蒙牛云用
     */
    private boolean exclusive = false;

    /**
     * 优先级，数字越大优先级越低
     */
    private int order = DEFAULT_ORDER;

    /**
     * true表示启用
     */
    private boolean enabled;

    /**
     * 是否允许用英文签名，梦网英文签名老是出问题，所以做个开关
     */
    private boolean enSignEnabled;

    /**
     * 国内短信：中文签名，注意签名要带括号
     */
    private String zhSignName = SmsUtils.SMS_SIGN_ZH;

    /**
     * 国内短信：英文签名，注意签名要带括号，而且英文签名括号是中文括号，因为电信运营商限制，不允许用英文括号
     */
    private String enSignName = "【ShareCRM】";

    /**
     * 国际短信：中文签名，允许没有
     */
    private String intlZhSignName = SmsUtils.SMS_SIGN_ZH;

    /**
     * 国际短信：英文签名，允许没有
     */
    private String intlEnSignName = SmsUtils.SMS_SIGN_EN;

    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    private String ip;
    private int port;
    private String account;
    private String password;
    /**
     * 营销专用账号
     */
    private String marketingAccount;
    private String marketingPassword;

    /**
     * 是否开启短信上报任务，在定时任务里使用
     */
    private boolean reportCollectEnabled;

    private String sendUrlSchema = "http";

    /**
     * V5国际短信发送单条短信的URL
     */
    private String sendSingleUrl = "/sms/v2/std/send_single";

  }

  @Data
  @ToString(exclude = {"username", "password"})
  public static class DahantcConfig implements SmsProvider {
    private String id = "dahantc";
    private String name = "大汉三通";
    private String type = "dahantc";
    private boolean exclusive = false;

    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    private String url;
    private String username;
    private String password;
    private boolean enabled;

    /**
     * 国内短信：中文签名，注意签名要带括号
     */
    private String zhSignName = SmsUtils.SMS_SIGN_ZH;

    /**
     * 国内短信：英文签名，注意签名要带括号，而且大汉三通的英文签名括号是中文括号，因为电信运营商限制，不允许用英文括号
     */
    private String enSignName = "【ShareCRM】";

    /**
     * 国际短信：中文签名，大汉三通不支持国际短信
     */
    private String intlZhSignName;

    /**
     * 国际短信：英文签名，大汉三通不支持国际短信
     */
    private String intlEnSignName;

  }

  @Data
  @ToString(exclude = {"appKey", "appSecret"})
  public static class TencentConfig implements SmsProvider {
    private String id = "fs-tencent";
    private String name = "腾讯云";
    private String type = SmsSdkConstants.SMS_PROVIDER_TYPE_TENCENT;
    private boolean exclusive = false;

    /**
     * 是否启用
     */
    private boolean enabled = false;

    /**
     * 是否启用短信和模板的状态查询任务
     */
    private boolean statusTaskEnabled = true;

    /**
     * 优先级，数字越大优先级越低
     */
    private int order = DEFAULT_ORDER - 50;

    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    /**
     * 排除的国家，有些国家不支持需要排除掉
     * 比如：美国(+1)、加拿大(+1)、新加坡(+65)
     */
    private List<Integer> excludeCountryCodes = List.of();

    /**
     * 配置一些已经提前申请好的短信模板，用于发送普通短信
     */
    private Map<String, SmsStaticTemplate> templates = Map.of();

    /**
     * 配置一些已经提前申请好的TTS语音模板
     */
    private Map<String, TtsTemplate> ttsTemplates = Map.of();

    /**
     * 腾讯云短信服务的能力集
     */
    private List<String> supports = List.of();

    /**
     * 国内短信：中文签名
     */
    private String zhSignName = "纷享销客";

    /**
     * 国内短信：英文签名，如果没有，用中文占位
     */
    private String enSignName = "ShareCRM";

    /**
     * 国际短信：中文签名，允许没有
     */
    private String intlZhSignName;

    /**
     * 国际短信：英文签名，允许没有
     */
    private String intlEnSignName;

    /**
     * 短信 SdkAppId，在 短信控制台 添加应用后生成的实际 SdkAppId
     */
    private String smsSdkAppId;

    /**
     * 语音 SdkAppId，在语音控制台添加应用后生成的实际SdkAppId
     */
    private String voiceSdkAppId;

    /**
     * 国内短信无需填写该项；国际/港澳台短信已申请独立 SenderId 需要填写该字段，默认使用公共 SenderId，无需填写该字段。
     * <p>
     * 国际/港澳台短信通常使用Sender ID（发件人）来向收件人标识自己品牌，而非短信签名。对于不需要注册sender ID的国家/地区将使用指定Sender ID发送，但部分国家地区必须需要注册才能保证短信发送成功率，比如美国等。具体查看腾讯说明。
     */
    private String senderId;

    private String region = "ap-beijing";

    private String appKey;
    private String appSecret;
    private String httpProxy;

    /**
     * 查询短信状态的定时任务
     */
    private Duration smsTaskDelay = Duration.ofSeconds(60);

    /**
     * 查询模板状态的定时任务
     */
    private Duration templateTaskDelay = Duration.ofSeconds(600);

  }

  @Data
  @ToString(exclude = {"appKey", "appSecret"})
  public static class AliYunConfig implements SmsProvider {
    private String id = "fs-aliyun";
    private String name = "阿里云";
    private String type = SmsSdkConstants.SMS_PROVIDER_TYPE_ALIYUN;
    private boolean exclusive = false;

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 优先级，数字越大优先级越低
     */
    private int order = DEFAULT_ORDER + 50;

    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    /**
     * 排除的国家，有些国家不支持需要排除掉
     * 比如：美国(+1)、加拿大(+1)、新加坡(+65)
     */
    private List<Integer> excludeCountryCodes = List.of();

    /**
     * 配置一些已经提前申请好的短信模板，用于发送普通短信
     */
    private Map<String, SmsStaticTemplate> templates = Map.of();

    /**
     * 配置一些已经提前申请好的TTS语音模板
     */
    private Map<String, TtsTemplate> ttsTemplates = Map.of();

    /**
     * 模板状态映射，key是我们自定义的标准key，value是供应商返回的
     */
    private Map<String, String> templateStatus = Map.of(
      SmsSdkConstants.TEMPLATE_STATUS_APPROVING, "0",
      SmsSdkConstants.TEMPLATE_STATUS_APPROVED, "1",
      SmsSdkConstants.TEMPLATE_STATUS_REJECTED, "2",
      SmsSdkConstants.TEMPLATE_STATUS_CANCELED, "10"
    );

    /**
     * 阿里云短信服务的能力集
     */
    private List<String> supports = List.of(SUPPORT_TTS);

    /**
     * 中文签名
     */
    private String zhSignName = "纷享销客";

    /**
     * 英文签名，用中文占位，从2025年起阿里云政策变更，不支持纯英文，只好用中文签名代替
     */
    private String enSignName = "纷享销客";

    /**
     * 国际短信：中文签名
     */
    private String intlZhSignName = "纷享销客";

    /**
     * 国际短信：英文签名
     */
    private String intlEnSignName = "ShareCRM";

    private String region = "cn-hangzhou";
    private String appKey;
    private String appSecret;
    private String showNumber;
    private int defaultConnectTimeout = 10000;
    private int defaultReadTimeout = 10000;
    private String httpProxy;

    /**
     * 是否启用短信和模板的状态查询任务
     */
    private boolean statusTaskEnabled = true;

    /**
     * 查询短信状态的定时任务
     */
    private Duration smsTaskDelay = Duration.ofSeconds(60);

    /**
     * 查询模板状态的定时任务
     */
    private Duration templateTaskDelay = Duration.ofSeconds(600);

  }

  @Data
  @ToString(exclude = {"appKey", "appSecret"})
  public static class BytePlusConfig implements SmsProvider {
    private String id = "fs-byte-plus";
    private String name = "BytePlus";
    private String type = "byteplus";
    private boolean exclusive = false;

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 优先级，数字越大优先级越低
     */
    private int order = DEFAULT_ORDER + 50;

    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    /**
     * 排除的国家，我们使用的BytePlus混线发送，有些国家不支持需要排除掉
     * <p>
     * 目前不支持：美国(+1)、加拿大(+1)、新加坡(+65)
     */
    private List<Integer> excludeCountryCodes = List.of(1, 65);

    /**
     * 配置一些已经提前申请好的短信模板，用于发送普通短信
     */
    private Map<String, SmsStaticTemplate> templates = Map.of();

    /**
     * 服务的能力集
     */
    private List<String> supports = List.of(SUPPORT_CAPTCHA_INTL);

    /**
     * 中文签名
     */
    private String zhSignName = "纷享销客";

    /**
     * 英文签名，用中文占位
     */
    private String enSignName = "ShareCRM";

    /**
     * 国际短信：中文签名
     */
    private String intlZhSignName = "纷享销客";

    /**
     * 国际短信：英文签名
     */
    private String intlEnSignName = "ShareCRM";

    /**
     * region不是随便设置的，开通在哪就设置哪个
     */
    private String region = "ap-singapore-1";
    private String appKey;
    private String appSecret;
    
    /**
     * The message group ID.
     */
    private String messageGroupId;

    /**
     * 品牌名，必填
     */
    private String smsFrom;

    private String httpProxy;

  }

  @Data
  @ToString(callSuper = true)
  @EqualsAndHashCode(callSuper = true)
  public static class DingqiaoAliConfig extends AliYunConfig {
    /**
     * 深研短信平台依赖ID，会存入数据库标记，不能变
     */
    private String id = "dingqiao-aliyun";
    /**
     * 又写一遍改变默认值，避免纷享云配置错误
     */
    private boolean enabled = false;
  }

  /**
   * 超级VIP客户蒙牛的短信通道，只给蒙牛客户使用
   */
  @Data
  @ToString(exclude = {"clientSecret"})
  public static class MengniuConfig implements SmsProvider {

    private String id = "mengniu";
    private String name = "蒙牛短信平台";
    private String type = SmsSdkConstants.SMS_PROVIDER_TYPE_MENGNIU;
    private boolean exclusive = true;
    private int order = DEFAULT_ORDER - 100;
    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    /**
     * 模板类型映射，key是我们自定义的标准类型，value是供应商需要的类型
     */
    private Map<String, String> templateTypes = Map.of(
      SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE, "VERIFY_CODE",
      SmsSdkConstants.TEMPLATE_TYPE_NOTIFICATION, "SMS_NOTIFICATION",
      SmsSdkConstants.TEMPLATE_TYPE_PROMOTION, "PROMOTION_SMS"
    );

    /**
     * 模板状态映射，key是我们自定义的标准key，value是供应商返回的
     */
    private Map<String, String> templateStatus = Map.of(
      SmsSdkConstants.TEMPLATE_STATUS_APPROVING, "APPROVING",
      SmsSdkConstants.TEMPLATE_STATUS_APPROVED, "APPROVED",
      SmsSdkConstants.TEMPLATE_STATUS_REJECTED, "REJECTED",
      SmsSdkConstants.TEMPLATE_STATUS_CANCELED, "CANCELED"
    );

    /**
     * 配置一些已经提前申请好的短信模板，用于发送普通短信
     */
    private Map<String, SmsStaticTemplate> templates = Map.of();

    private boolean enabled;

    /**
     * accountId 代表和阿里交互最终所使用的资费账户
     */
    private String accountId;

    /**
     * client ID 和 secret 两个参数用于我们请求蒙牛的openAPI时鉴权
     */
    private String clientId;

    private String clientSecret;

    /**
     * callback client ID 和 secret 是蒙牛回调我们服务的时候用，用于短信状态回调
     */
    private String callbackId;

    private String callbackSecret;

    /**
     * 中文签名
     */
    private String zhSignName;

    /**
     * 英文签名，如果没有，用中文占位
     */
    private String enSignName;

    /**
     * 国际短信：中文签名，不支持国际短信
     */
    private String intlZhSignName;

    /**
     * 国际短信：英文签名，不支持国际短信
     */
    private String intlEnSignName;

    /**
     * 短信通过Api网关发送，测试、生产等不同环境有不同的网关域名
     */
    private String apiGateway = "https://openapi-gateway.mengniu.cn";

    private String smsSendPath = "/public-common/smg/api/short-message/send/text/single";
    private String smsStatusPath = "/public-common/smg/api/short-message/send/text/status/list";
    private String addTemplatePath = "/public-common/smg/api/short-message/templates/text/add";
    private String queryTemplatePath = "/public-common/smg/api/short-message/templates/audit/query";
  }

  /**
   * 招商云短信通道，对接招商云PaaS平台"统一消息服务" 2.0版本，用于集团测试和生产环境
   */
  @Data
  @ToString(exclude = {"clientSecret"})
  public static class ZhaoShangConfig implements SmsProvider {

    private String id = "cmhk";
    private String name = "招商云PaaS平台";
    private String type = "cmhk";
    private boolean exclusive = true;
    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    private boolean enabled;

    /**
     * 租户编码_项目编码
     */
    private String appId;

    private String clientId;
    private String clientSecret;

    /**
     * 签名标识，是那个数字ID
     */
    private String zhSignId;

    /**
     * 模板编码，是那个英文的编码ID
     */
    private String zhTemplateId;

    /**
     * 国际短信签名标识
     */
    private String enSignId;

    /**
     * 国际短信模板编码
     */
    private String enTemplateId;

    /**
     * 默认的邮件发送人
     */
    private String emailFrom;

    /**
     * 邮件和短信通过Api网关发送，开发、测试、生产等不同环境有不同的网关域名，我们是所有环境都直接调用生产的网关。
     */
    private String smsSendUrl = "https://openapi.cmft.com/gateway/msgService/2.0.0/api/sms/send";
    private String emailSendUrl = "https://openapi.cmft.com/gateway/msgService/2.0.0/api/mail/send";
    private String accessTokenUrl = "https://openapi.cmft.com/gateway/open/1.0/getAccessToken";

    /**
     * 国内短信：中文签名
     */
    private String zhSignName;

    /**
     * 国内短信：英文签名
     */
    private String enSignName;

    /**
     * 国际短信：中文签名，参数用不到
     */
    private String intlZhSignName;

    /**
     * 国际短信：英文签名，参数用不到
     */
    private String intlEnSignName;

  }

  /**
   * 招商局-中国外运消息服务配置(推送中心)，用于中外运测试和生产环境
   */
  @Data
  public static class SinoConfig implements SmsProvider {
    private String id = "sino";
    private String name = "中国外运消息平台";
    private String type = "sino";
    private boolean exclusive = true;
    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    private boolean enabled;

    /**
     * 从API网关申请的Key id
     */
    private String keyId;

    /**
     * 系统五位码，申请的时候给的，systemCode和systemName都是申请的时候给的，测试和生产使用的一样
     */
    private String systemCode = "55276";

    /**
     * 系统名称
     */
    private String systemName = "客户经营管理平台";

    /**
     * 国内模板编码
     */
    private Integer zhTemplateId;

    /**
     * 国内模板完整名字
     */
    private String zhTemplateName;

    /**
     * 国内验证码模板编码
     */
    private Integer zhCaptchaTemplateId;

    /**
     * 国内验证码模板完整名字
     */
    private String zhCaptchaTemplateName;

    /**
     * 国际短信模板编码
     */
    private Integer enTemplateId;

    /**
     * 国际模板完整名字
     */
    private String enTemplateName;

    /**
     * 国际验证码短信模板编码
     */
    private Integer enCaptchaTemplateId;

    /**
     * 国际验证码模板完整名字
     */
    private String enCaptchaTemplateName;

    /**
     * 邮件类型：1、文本, 2、html、3、附件
     */
    private int emailType = 2;

    /**
     * 默认的邮件发送人，这个需要单独申请并在对方的平台上配置好
     */
    private String emailFrom = "<EMAIL>";

    /**
     * 不同环境有不同的URL前缀，并注意开通网络防火墙.
     * 测试环境：<a href="https://apitest.i.sinotrans.com/tech-notify">...</a>
     * 生产环境：<a href="https://api.sinotrans.com/tech-notify/">...</a>
     */
    private String smsSendUrl = "https://api.sinotrans.com/tech-notify/message-sms/v3/templateSend";

    /**
     * 发送邮件接口地址
     */
    private String emailSendUrl = "https://api.sinotrans.com/tech-notify/v1/message/email/send";

    /**
     * 国内短信：中文签名，参数用不到
     */
    private String zhSignName;

    /**
     * 国内短信：英文签名，参数用不到
     */
    private String enSignName;

    /**
     * 国际短信：中文签名，参数用不到
     */
    private String intlZhSignName;

    /**
     * 国际短信：英文签名，参数用不到
     */
    private String intlEnSignName;

  }


}
