package com.sharecrm.egress.config;

import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.gexin.rp.sdk.http.IGtPush;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.oppo.push.server.Sender;
import com.sharecrm.egress.push.NotifyPushConsumerListener;
import com.sharecrm.egress.utils.NetUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;

import java.net.URI;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 消息推送服务Bean初始化
 */
@Slf4j
@Configuration
@Lazy(value = false)
@ConditionalOnNotifyEnabled
public class NotifyBeanConfig implements InitializingBean {

  @Value("${sharecrm.api.notify.push.mq.config:fs-notify-push-mq}")
  private String rocketConfig = "fs-notify-push-mq";

  @Value("${sharecrm.api.notify.push.lock.seconds:10}")
  private Integer lockSeconds = 10;

  private final NotifyProperties properties;

  /**
   * 失败锁，锁定几秒，消息推送并发量很大，避免网络故障时把代理打死
   */
  private final Cache<Object, Object> cache = Caffeine.newBuilder()
    .expireAfterWrite(lockSeconds, TimeUnit.SECONDS)
    .maximumSize(100)
    .build();

  /**
   * 缓存客户端尽量使用单例bean
   */
  private Sender oppoSender;
  private com.xiaomi.xmpush.server.Sender xiaomiSender;
  private IGtPush gtPush;

  public NotifyBeanConfig(NotifyProperties properties) {
    this.properties = properties;
  }

  @Bean(initMethod = "start", destroyMethod = "shutdown")
  public AutoConfMQPushConsumer applePushMessageConsumer(NotifyPushConsumerListener listener) {
    return new AutoConfMQPushConsumer(rocketConfig, "ios", listener);
  }

  @Bean(initMethod = "start", destroyMethod = "shutdown")
  public AutoConfMQPushConsumer androidPushMessageConsumer(NotifyPushConsumerListener listener) {
    return new AutoConfMQPushConsumer(rocketConfig, "android", listener);
  }

  @Bean("vivoHttpSupport")
  public HttpSupportFactoryBean vivoHttpSupportFactoryBean(@Value("${sharecrm.api.notify.push.vivo.http.config:fs-oms-thirdpush-http-vivo}")
                                                           String vivoConfig) {
    HttpSupportFactoryBean bean = new HttpSupportFactoryBean();
    bean.setConfigName(vivoConfig);
    return bean;
  }
  
  @Bean("huaweiHttpSupport")
  public HttpSupportFactoryBean huaweiHttpSupportFactoryBean(@Value("${sharecrm.api.notify.push.huawei.http.config:fs-oms-thirdpush-http-huawei}")
                                                             String huaweiConfig) {
    HttpSupportFactoryBean bean = new HttpSupportFactoryBean();
    bean.setConfigName(huaweiConfig);
    return bean;
  }

  @Bean("honorHttpSupport")
  public HttpSupportFactoryBean honorHttpSupportFactoryBean(@Value("${sharecrm.api.notify.push.honor.http.config:fs-oms-thirdpush-http-honor}")
  String honorConfig) {
    HttpSupportFactoryBean bean = new HttpSupportFactoryBean();
    bean.setConfigName(honorConfig);
    return bean;
  }

  @Bean("okHttpNotify")
  public HttpSupportFactoryBean okHttpNotifySupportFactoryBean() {
    return new HttpSupportFactoryBean();
  }

  @Bean("httpSupportForFCM")
  public HttpSupportFactoryBean httpSupportForFCM(@Value("${sharecrm.api.notify.push.fcm.http.config:fs-oms-thirdpush-http-fcm}")
                                                  String fcmConfig) {
    HttpSupportFactoryBean bean = new HttpSupportFactoryBean();
    bean.setConfigName(fcmConfig);
    return bean;
  }

  /**
   * 初始化 oppo 消息推送客户端端，使用Supplier封装的目的是为了可返回null bean。在网络代理出问题的情况下，client初始化会失败，允许失败后服务正常启动，否则这个项目里的其他核心服务都无法使用
   */
  @Bean
  public Supplier<Sender> oppoNotifySender() {
    return () -> {
      try {
        if (Objects.nonNull(oppoSender)) {
          return oppoSender;
        }
        //失败锁定一段时间，否则并发太大频繁去连接直接把网络代理打死
        if (isLocked("oppo")) {
          log.warn("oppo sender fail locked, will return null");
          return null;
        }
        initOppoSender();
        return oppoSender;
      } catch (Exception e) {
        lock("oppo");
        log.error("oppo sender init failed", e);
        //允许失败，否则网络异常的时候其他功能也都不可能用
        return null;
      }
    };
  }

  private void initOppoSender() throws Exception {
    NotifyProperties.OppoPushConfig oppo = properties.getOppo();
    log.info("begin init oppo notify sender:{}", oppo);
    if (StringUtils.isNotBlank(oppo.getProxy())) {
      System.setProperty("oppo.http.proxy", oppo.getProxy());
    }
    oppoSender = new Sender(oppo.getAppKey(), oppo.getMasterSecret());
  }

  @Bean
  public Supplier<com.xiaomi.xmpush.server.Sender> xiaomiNotifySender() {
    return () -> {
      try {
        if (Objects.nonNull(xiaomiSender)) {
          return xiaomiSender;
        }
        if (isLocked("xiaomi")) {
          log.warn("xiaomi sender fail locked, will return null");
          return null;
        }
        initXiaomiSender();
        return xiaomiSender;
      } catch (Exception e) {
        lock("xiaomi");
        log.error("xiaomi sender init failed", e);
        return null;
      }
    };
  }

  private void initXiaomiSender() {
    com.xiaomi.xmpush.server.Constants.useOfficial();
    NotifyProperties.XiaoMiPushConfig config = properties.getXiaomi();
    log.info("begin init xiaomi notify sender:{}", config);
    String proxy = config.getProxy();
    if (StringUtils.isNotBlank(proxy)) {
      log.info("set proxy: {}", proxy);
      URI uri = NetUtils.uri(proxy);
      com.xiaomi.xmpush.server.HttpBase.setProxy(uri.getHost(), uri.getPort());
    }
    xiaomiSender = new com.xiaomi.xmpush.server.Sender(config.getAppSecret());
  }

  @Bean
  public Supplier<IGtPush> geTuiNotifySender() {
    return () -> {
      try {
        if (Objects.nonNull(gtPush)) {
          return gtPush;
        }
        if (isLocked("getui")) {
          log.warn("getui sender fail locked, will return null");
          return null;
        }
        initGeTuiSender();
        return gtPush;
      } catch (Exception e) {
        lock("getui");
        log.error("getui sender init failed", e);
        return null;
      }
    };
  }

  private void initGeTuiSender() {
    NotifyProperties.GeTuiPushConfig config = properties.getGetui();
    log.info("begin init getui notify sender:{}", config);
    String proxy = config.getProxy();
    if (StringUtils.isNotBlank(proxy)) {
      URI uri = NetUtils.uri(proxy);
      System.setProperty("gexin_http_proxy_ip", uri.getHost());
      System.setProperty("gexin_http_proxy_port", String.valueOf(uri.getPort()));
    }
    gtPush = new IGtPush(config.getAppKey(), config.getMasterSecret());
  }

  @EventListener(RefreshScopeRefreshedEvent.class)
  public void refreshNotifyClient(RefreshScopeRefreshedEvent event) {
    log.info("config refreshed, reset notify push clients: {}", event);
    // 重置为null重新生成，也不知道为啥这些类都不提供close方法
    xiaomiSender = null;
    oppoSender = null;
    gtPush = null;
    this.tryInitSenderClient();
  }

  @Override
  public void afterPropertiesSet() {
    //首次启动提前初始化
    this.tryInitSenderClient();
  }

  private boolean isLocked(String key) {
    return Objects.nonNull(cache.getIfPresent(key));
  }

  private void lock(String key) {
    cache.put(key, "1");
  }

  private void tryInitSenderClient() {
    //调用一次get方法触发初始化
    try {
      initXiaomiSender();
      initGeTuiSender();
      initOppoSender();
    } catch (Exception e) {
      log.error("init sender client failed.", e);
    }
  }
}
