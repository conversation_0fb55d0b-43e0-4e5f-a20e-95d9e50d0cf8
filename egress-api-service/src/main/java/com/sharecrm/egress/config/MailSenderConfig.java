package com.sharecrm.egress.config;

import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Map;
import java.util.Properties;

/**
 * 参考org.springframework.boot.autoconfigure.mail.MailSenderPropertiesConfiguration实现配置文件变更后Bean动态刷新
 */
@Configuration
@EnableConfigurationProperties({MailProperties.class})
public class MailSenderConfig {

  @Bean
  @RefreshScope
  @ConditionalOnJavaMailEnabled
  JavaMailSenderImpl mailSender(MailProperties properties) {
    JavaMailSenderImpl sender = new JavaMailSenderImpl();
    applyProperties(properties, sender);
    return sender;
  }

  private void applyProperties(MailProperties properties, JavaMailSenderImpl sender) {
    sender.setHost(properties.getHost());
    if (properties.getPort() != null) {
      sender.setPort(properties.getPort());
    }

    sender.setUsername(properties.getUsername());
    sender.setPassword(properties.getPassword());
    sender.setProtocol(properties.getProtocol());
    if (properties.getDefaultEncoding() != null) {
      sender.setDefaultEncoding(properties.getDefaultEncoding().name());
    }

    if (!properties.getProperties().isEmpty()) {
      sender.setJavaMailProperties(this.asProperties(properties.getProperties()));
    }

  }

  private Properties asProperties(Map<String, String> source) {
    Properties properties = new Properties();
    properties.putAll(source);
    return properties;
  }

}
