package com.sharecrm.egress.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Set;

/**
 * 额度限流配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.quota")
public class QuotaLimiterProperties {

  /**
   * 全局开关
   */
  private boolean enabled;

  /**
   * key：服务分类的名字
   * value：映射的http path和method
   */
  private Map<String, CatalogPair> pathCatalogs = Map.of();

  /**
   * 全局默认配额
   * key: 服务分类，catalog
   * value：限额值，-1不限额，0无配额
   */
  private Map<String, Long> globalQuotas = Map.of();

  /**
   * 租户配额，租户不配置的用全局的。
   * key：租户ID，EI
   * value：配额
   */
  private Map<Long, Map<String, Long>> tenantQuotas = Map.of();

  /**
   * 不限额度的EI列表，一般都是VIP，或者临时禁用配额
   */
  private Set<Long> ignoreEis = Set.of();

  @Data
  public static class CatalogPair {
    private String method;
    private String path;
  }
}
