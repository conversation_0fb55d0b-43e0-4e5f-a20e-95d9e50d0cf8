package com.sharecrm.egress.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.scheduler")
public class SchedulerProperties {

  private final SchedulerProperties.SchedulerConfig url = new SchedulerProperties.SchedulerConfig();
  private final SchedulerProperties.SchedulerConfig sms = new SchedulerProperties.SchedulerConfig();
  private final SchedulerProperties.SchedulerConfig asr = new SchedulerProperties.SchedulerConfig();
  private final SchedulerProperties.SchedulerConfig ip = new SchedulerProperties.SchedulerConfig();

  @Data
  public static class SchedulerConfig {
    private int maxThreads = 32;
    private int maxTaskQueuedPerThread = 100;
    private Duration ttl = Duration.ofSeconds(600);
  }

}
