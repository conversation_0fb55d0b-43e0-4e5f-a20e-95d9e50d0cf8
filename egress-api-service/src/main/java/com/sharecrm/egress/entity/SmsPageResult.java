package com.sharecrm.egress.entity;

import java.util.List;
import lombok.Data;

/**
 * 短信记录分页查询结果，因为是内部接口，为了兼容老程序未使用统一的消息体，其他服务不要用。
 */
@Data
public class SmsPageResult {

  private List<SmsMongoEntity> results;
  private int count;
  private int pageNum = 0;
  private int pageSize = 10;
  private int totalPage = 0;
  private long totalCount = 0;

  public void setTotalCount(long totalCount) {
    this.totalCount = totalCount;
    if (totalCount != 0) {
      long temp = totalCount / pageSize;
      this.totalPage = (int) (temp * pageSize < totalCount ? temp + 1 : temp);
    }
  }
}
