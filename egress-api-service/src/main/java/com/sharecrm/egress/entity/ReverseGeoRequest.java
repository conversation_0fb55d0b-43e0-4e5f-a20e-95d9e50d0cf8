package com.sharecrm.egress.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 逆地址解析请求体，将经纬度信息转换为地址信息
 */
@Data
public class ReverseGeoRequest {

  /**
   * 经度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double longitude;

  /**
   * 纬度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double latitude;

  /**
   * 是否允许使用cache，默认是true，优先从cache查询，无特殊情况请启用cache
   */
  private boolean cache = true;

  /**
   * 是否在请求的response结果里返回详细的POI列表，默认是true，端上主动关闭用来减少流量传输
   */
  private boolean returnPois = true;
  
  /**
   * 是否期望返回城市编码，如果为true我们会尽量返回，但不保证所有都能返回，取决于运营商的结果。 默认是false
   */
  private boolean requireCityCode = false;

  /**
   * 是否为海外请求，如果为true则必须使用海外供应商
   */
  private boolean overseas = false;

  /**
   * POI TYPE CODE，非必填，按照高德的Code码类型传值，目前只高德支持。
   */
  private List<String> poiTypes;

  /**
   * 缓存地理位置允许的误差范围，单位米，在X米内如果有缓存，直接用缓存值，不指定时需要系统全局默认值0
   */
  private Integer radius;

  /**
   * 用户ID，用于灰度规则判断，不指定默认通过cep Header获取，类似 ea.employeeId
   */
  private String userId;

  /**
   * 优先选择的运营商，可以不指定，不指定时后台自动根据可用的运营商轮换选择，
   * 支持：amap: 高德, baidu: 百度, tencent: 腾讯, google: 谷歌。
   * <p>
   * 1. 国内运营商，如果这个运营商查不到结果，回退到其他运营商，可以不指定随机选择。
   * 2. 如果是指定Google，只能用Google，不会回退。
   */
  private String firstProvider;

  /**
   * 语言，用于多语选择，只支持简体中文zh-CN和英文 en. 默认简体中文，非中文的都使用英文
   */
  private String language;

  /**
   * 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   */
  private String bizName;

  /**
   * 终端平台，如 Android、IOS、小程序，用于统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   */
  private String platform;

}
