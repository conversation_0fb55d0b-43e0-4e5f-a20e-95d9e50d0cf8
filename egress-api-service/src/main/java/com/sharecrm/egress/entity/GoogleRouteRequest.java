package com.sharecrm.egress.entity;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class GoogleRouteRequest {
  private GoogleWayPoint origin;
  private GoogleWayPoint destination;
  private List<GoogleWayPoint> intermediates;
  @Builder.Default
  private String travelMode = "DRIVE";
  @Builder.Default
  private String routingPreference = "TRAFFIC_UNAWARE";
  @Builder.Default
  private boolean computeAlternativeRoutes = false;
  @Builder.Default
  private String languageCode = "zh-CN";
  @Builder.Default
  private String units = "METRIC";
}
