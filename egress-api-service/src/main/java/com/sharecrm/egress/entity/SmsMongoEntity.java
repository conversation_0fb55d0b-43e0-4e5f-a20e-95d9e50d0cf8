package com.sharecrm.egress.entity;


import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;

/**
 * 短信发送记录，mongo实体类
 */
@Entity(value = "sms_history", noClassnameStored = true)
@Indexes({
  @Index(fields = {@Field(SmsMongoFields.MSG_ID)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.MSG_BATCH_ID)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.SERIAL_ID)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.PHONE)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.BIZ_NAME)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.SMS_TYPE)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.STATUS)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.EI)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.REPLY_STATUS)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsMongoFields.SEND_TIME)}, options = @IndexOptions(background = true, expireAfterSeconds = 60 * 60 * 24 * 455)),
})
@Data
public class SmsMongoEntity {

  @Id
  private ObjectId id;

  /**
   * 此次发送记录的消息ID，即发送流水号，由我们自己生成，可根据此ID查询此条短信的回执状态
   */
  @Property(SmsMongoFields.MSG_ID)
  private String msgId;

  /**
   * 批次ID，由我们自己生成，每次N条是同一个ID
   */
  @Property(SmsMongoFields.MSG_BATCH_ID)
  private String batchMsgId;

  /**
   * 各个短信供应商返回的消息ID，由供应商定义的流水号，部分供应商可能不支持
   */
  @Property(SmsMongoFields.SERIAL_ID)
  private String serialId;

  /**
   * 短信发送者的租户ID
   */
  @Property(SmsMongoFields.EI)
  private String enterpriseId;

  @Property(SmsMongoFields.PHONE)
  private String phone;

  /**
   * 短信内容
   */
  @Property(SmsMongoFields.CONTENT)
  private String content;

  /**
   * 短信签名
   */
  @Property(SmsMongoFields.SIGN)
  private String sign;

  /**
   * 短信供应商唯一标识，比如梦网、华为云、阿里云，代替channel
   */
  @Property(SmsMongoFields.PROVIDER_ID)
  private String providerId;

  /**
   * 短信供应商名字，便于前台展示和记录日志
   */
  @Property(SmsMongoFields.PROVIDER_NAME)
  private String providerName;

  /**
   * 短信模板ID
   */
  @Property(SmsMongoFields.TEMPLATE_ID)
  private String templateId;

  @Property(SmsMongoFields.SEND_TIME)
  private Date sendTime;

  /**
   * 短信调用者的业务名字
   */
  @Property(SmsMongoFields.BIZ_NAME)
  private String bizName;

  /**
   * 发送状态，标记我们发送给供应商是否成功
   */
  @Property(SmsMongoFields.STATUS)
  private Boolean status;

  /**
   * 发送状态回应的消息，与status对应
   */
  @Property(SmsMongoFields.MESSAGE)
  private String message;

  /**
   * 供应商回复的状态，标记是否接收成功
   */
  @Property(SmsMongoFields.REPLY_STATUS)
  private Boolean replyStatus;

  /**
   * 供应商回复的发送时间
   */
  @Property(SmsMongoFields.REPLY_TIME)
  private Date replyTime;

  /**
   * 供应商回复的具体状态码
   */
  @Property(SmsMongoFields.REPLY_CODE)
  private String replyCode;

  /**
   * 供应商回复的消息，可能成功可能失败
   */
  @Property(SmsMongoFields.REPLY_MESSAGE)
  private String replyMessage;

  /**
   * 短信原始长度，有些运营商可能无法返回，仅作参考
   */
  @Property(SmsMongoFields.SMS_LENGTH)
  private Integer smsLength;

  /**
   * 短信计费条数，有些运营商可能无法返回，仅作参考
   */
  @Property(SmsMongoFields.SMS_SIZE)
  private Integer smsSize;

  /**
   * 短信的类型，比如验证码、通知、营销等
   */
  @Property(SmsMongoFields.SMS_TYPE)
  private String smsType;

  /**
   * 是否国际/港澳台短信,true为国际短信，false为国内短信
   */
  @Property(SmsMongoFields.INTERNATIONAL)
  private Boolean international;

}
