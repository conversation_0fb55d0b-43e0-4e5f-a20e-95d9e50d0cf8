package com.sharecrm.egress.entity;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地址解析，给一个地址，转成规范化好的地址和经纬度
 */
@Data
@NoArgsConstructor
public class GeoEncodeRequest {
  /**
   * 原始请求地址，必填
   */
  @NotBlank
  private String address;

  /**
   * 城市，可选，不指定时全国范围内搜索
   */
  private String city;

  /**
   * 国家编码，以高德code码为标准，不指定时默认是中国。
   * <p>
   * 海外地图必须指定。
   */
  private String country;

  /**
   * 指定供应商，比如必须用Google。
   */
  private String provider;

  /**
   * 是否使用缓存数据，默认true
   */
  private boolean cache = true;

  /**
   * 语言，只支持中文和英文
   */
  private String language;

  /**
   * 调用方业务名字，用于按照业务统计、限流等，由业务方自行定义。全英文，必须填写，最长64个字符
   */
  private String bizName;

  public GeoEncodeRequest(String address) {
    this.address = address;
  }

  public GeoEncodeRequest(String address, String city) {
    this.address = address;
    this.city = city;
  }

}
