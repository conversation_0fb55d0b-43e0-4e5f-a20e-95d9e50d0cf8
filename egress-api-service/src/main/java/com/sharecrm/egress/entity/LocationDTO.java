package com.sharecrm.egress.entity;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 为了节省内存，便于存储，把常用的字段都用 int 类型表示
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationDTO {
  @Tag(1)
  private int province;
  @Tag(2)
  private int city;
  @Tag(3)
  private int carrier;
  @Tag(4)
  private int code;
}
