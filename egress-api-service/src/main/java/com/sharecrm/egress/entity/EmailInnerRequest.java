package com.sharecrm.egress.entity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;


/**
 * 发送邮件请求体（内部使用，不对外公开）
 */
@Data
public class EmailInnerRequest {

  /**
   * 接收人，一次最多允许200个,逗号分割
   */
  @NotBlank
  private String to;

  /**
   * 抄送人，一次最多允许200个
   */
  private String cc;

  /**
   * 邮件主题，最大长度100
   */
  @NotBlank
  @Size(max = 100)
  private String subject;

  /**
   * 邮件详细内容
   */
  @NotBlank
  private String content;

}
