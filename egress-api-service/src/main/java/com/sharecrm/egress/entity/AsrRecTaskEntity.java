package com.sharecrm.egress.entity;

import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;

/**
 * 录音文件记录
 */
@Entity(value = "asr_rec_task", noClassnameStored = true)
@Indexes({
  @Index(fields = {@Field(AsrRecTaskEntity.TASK_ID)}, options = @IndexOptions(background = true, unique = true)),
  @Index(fields = {@Field(AsrRecTaskEntity.FILED_STATUS)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(AsrRecTaskEntity.PROVIDER_ID)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(AsrRecTaskEntity.CREATE_TIME)}, options = @IndexOptions(background = true, expireAfterSeconds = 60 * 60 * 24 * 390)),
})
@Data
public class AsrRecTaskEntity {

  public static final String TASK_ID = "tid";
  public static final String FILED_STATUS = "status";
  public static final String PROVIDER_TASK_ID = "ptid";
  public static final String FILED_MESSAGE = "msg";
  public static final String FILED_TEXT = "text";
  public static final String FILED_URL = "url";
  public static final String AUDIO_DURATION = "adt";
  public static final String PROVIDER_DB_ID = "pdid";
  public static final String PROVIDER_ID = "provider";
  public static final String PROVIDER_NAME = "providerName";
  public static final String CREATE_TIME = "cte";
  public static final String UPDATE_TIME = "ute";

  @Id
  private ObjectId id;

  /**
   * 任务ID
   */
  @Property(TASK_ID)
  private String taskId;

  /**
   * 供应商（比如腾讯云）返回的任务ID，API调用者不要用此ID，此ID可能重复
   */
  @Property(PROVIDER_TASK_ID)
  private String providerTaskId;

  /**
   * 模板对应的 provider ID
   */
  @Property(PROVIDER_ID)
  private String providerId;

  /**
   * 模板对应的 provider ID
   */
  @Property(PROVIDER_NAME)
  private String providerName;

  /**
   * 当前状态
   */
  @Property(FILED_STATUS)
  private String status;

  /**
   * 提示信息，如错误原因
   */
  @Property(FILED_MESSAGE)
  private String message;

  /**
   * 转换后的文本信息，只有转换成功后才有
   */
  @Property(FILED_TEXT)
  private String text;

  /**
   * 原始请求中的URL
   */
  @Property(FILED_URL)
  private String url;

  /**
   * 音频时长(毫秒)，此字段可能返回 null，表示取不到有效值。
   */
  @Property(AUDIO_DURATION)
  private Long audioDuration;

  @Property(CREATE_TIME)
  private Date createTime = new Date();

  @Property(UPDATE_TIME)
  private Date updateTime;

}
