package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class GoogleGeoLocation {
  /**
   * 包含此位置直观易懂的地址。此地址通常相当于邮政地址.
   */
  @JsonProperty("formatted_address")
  private String formattedAddress;
  /**
   * 唯一标识符，可以与其他 Google API 搭配使用。例如，您可以在 Places API 请求中使用 place_id
   * 来获取本地商家的详细信息，例如电话号码、营业时间、用户评价等。
   */
  @JsonProperty("place_id")
  private String id;

  /**
   * 表示地理编码器无法返回与原始请求完全匹配的结果，尽管它能够匹配所请求地址的一部分内容。建议您检查一下原始请求中是否存在拼写错误和/或地址不完整的情况。
   */
  @JsonProperty("partial_match")
  private boolean partialMatch;

  /**
   * 经纬度坐标信息
   */
  private GoogleGeometry geometry;

  /**
   * 国家省市区信息
   */
  @JsonProperty("address_components")
  private List<GoogleAddressComponent> addressComponents;

}
