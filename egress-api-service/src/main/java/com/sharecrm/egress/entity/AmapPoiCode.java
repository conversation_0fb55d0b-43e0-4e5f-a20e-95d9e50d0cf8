package com.sharecrm.egress.entity;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class AmapPoiCode {
  private String code;
  private String zhBig;
  private String zhMid;
  private String zhSub;
  private String enBig;
  private String enMid;
  private String enSub;

  public String zhFullName() {
    return StringUtils.join(List.of(zhBig, zhMid, zhSub), ";");
  }

  public String enFullName() {
    return StringUtils.join(List.of(enBig, enMid, enSub), ";");
  }

}
