package com.sharecrm.egress.entity;

import com.sharecrm.egress.sdk.SmsSdkConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 用于配置文件里静态配置的短信模板
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SmsStaticTemplate extends SmsTemplateEntity {

  /**
   * true表示默认宽泛模板，找不到模板时都套用这个，把所有内容都放这个模板里
   */
  private Boolean asDefaultContent = false;

  /**
   * 作为默认的验证码模板，如果是验证码短信，即便内容不完全匹配，也走这个模板，为了仅可能保证验证码可用
   */
  private Boolean asDefaultCaptcha = false;

  private int order;

  /**
   * 支持的语言，默认支持中英文，有单独开通的再独立配置
   */
  private List<String> languages = List.of(SmsSdkConstants.LANGUAGE_EN, SmsSdkConstants.LANGUAGE_ZH);

}
