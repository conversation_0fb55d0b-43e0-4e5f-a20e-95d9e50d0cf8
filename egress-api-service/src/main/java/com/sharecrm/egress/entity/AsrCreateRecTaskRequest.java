package com.sharecrm.egress.entity;

import lombok.Data;

/**
 * 创建录音文件识别请求体，创建Task异步返回
 * <a href="https://cloud.tencent.com/document/product/1093/37823">...</a>
 */
@Data
public class AsrCreateRecTaskRequest {

  /**
   * 识别声道数, 1：单声道（16k音频仅支持单声道，请勿设置为双声道）；
   */
  public static final Integer CHANNEL_NUM_ONE = 1;

  /**
   * 识别声道数, 2：双声道（仅支持8k电话音频，且双声道应分别为通话双方）
   */
  public static final Integer CHANNEL_NUM_TWO = 2;

  /**
   * 引擎类型，注意有些单独收费需要单独开通
   */
  private String engineModelType;

  /**
   * 识别声道数
   * 1：单声道（16k音频仅支持单声道，请勿设置为双声道）；
   * 2：双声道（仅支持8k电话音频，且双声道应分别为通话双方）
   * <p>
   * 腾讯云注意：
   * • 16k音频：仅支持单声道识别，需设置ChannelNum=1；
   * • 8k电话音频：支持单声道、双声道识别，建议设置ChannelNum=2，即双声道。双声道能够物理区分说话人、避免说话双方重叠产生的识别错误，能达到最好的说话人分离效果和识别效果。设置双声道后，将自动区分说话人，因此无需再开启说话人分离功能，相关参数（SpeakerDiarization、SpeakerNumber）使用默认值即可
   */
  private Integer channelNum;

  /**
   * 是否开启说话人分离
   * 0：不开启；
   * 1：开启（仅支持以下引擎：8k_zh/16k_zh/16k_ms/16k_en/16k_id/16k_zh_large/16k_zh_dialect，且ChannelNum=1时可用）；
   * 默认值为 0
   * <p>
   * 注意：
   * 8k双声道电话音频请按 ChannelNum 识别声道数 的参数描述使用默认值
   */
  private Integer speakerDiarization = 0;

  /**
   * 说话人分离人数, 需配合开启说话人分离使用，不开启无效，取值范围：0-10
   * 0：自动分离（最多分离出20个人）；
   * 1-10：指定人数分离；
   * 默认值为 0
   * 示例值：0
   */
  private Integer speakerNumber = 0;
  /**
   * 识别结果返回样式
   * 0：基础识别结果（仅包含有效人声时间戳，无词粒度的详细识别结果）；
   * 1：基础识别结果之上，增加词粒度的详细识别结果（包含词级别时间戳、语速值，不含标点）；
   * 2：基础识别结果之上，增加词粒度的详细识别结果（包含词级别时间戳、语速值和标点）；
   * 3：基础识别结果之上，增加词粒度的详细识别结果（包含词级别时间戳、语速值和标点），且识别结果按标点符号分段，适用字幕场景；
   */
  private Integer resTextFormat = 0;

  /**
   * 音频URL的地址（需要公网环境浏览器可下载）
   * <p>
   * 注意：
   * 1. 请确保录音文件时长在5个小时（含）之内，否则可能识别失败；
   * 2. 请保证文件的下载速度，否则可能下载失败
   */
  private String url;

  /**
   * 音频数据bytes，不能超过5M
   */
  private byte[] data;

}
