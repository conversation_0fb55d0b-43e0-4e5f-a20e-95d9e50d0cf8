package com.sharecrm.egress.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(doNotUseGetters = true)
public class GeoAddressEsCache extends GeoAddress {

  private String id;

  private String sourceName;

  /**
   * 别名
   */
  private String alias;

  /**
   * 标签信息，主要出现在美食类POI中，代表特色菜
   */
  private String tag;

  private String telephone;

  /**
   * 照片相关信息
   */
  private String photoUrl;

  /**
   * poi type，名字拼接，如：交通设施服务;地铁站;地铁站
   */
  private String poiType;
  
  /**
   * poi type code 码
   */
  private String poiCode;

  private Long createTime;

  private Long updateTime;

}
