package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import lombok.Data;

import java.util.List;

import static com.sharecrm.egress.api.AMapApi.convertEmptyToNull;

@Data
public class AmapPoi {
  /**
   * 唯一ID
   */
  private String id;

  /**
   * 名称
   */
  private String name;

  /**
   * 经纬度，格式：Lng,Lat
   */
  private String location;

  /**
   * 地址，比如：东四环中路189号百盛北门
   */
  private String address;

  /**
   * 类型，顺序为大类、中类、小类。例如：餐饮服务;中餐厅;特色/地方风味餐厅
   */
  private String type;

  /**
   * 电话
   */
  @JsonProperty("tel")
  private String telephone;

  /**
   * 离中心点的距离，单位为米
   */
  private String distance;

  /**
   * 省份，若是直辖市的时候，此处直接显示市名，例如北京市
   */
  @JsonProperty("pname")
  private String province;

  /**
   * 城市
   */
  @JsonProperty("cityname")
  private String city;

  /**
   * 区县级别名称，例如朝阳区
   */
  @JsonProperty("adname")
  private String district;

  /**
   * 省份编码，extensions=all时返回
   */
  @JsonProperty("pcode")
  private String provinceCode;

  /**
   * 区域编码，extensions=all时返回
   */
  private String adcode;

  /**
   * 类型编码
   */
  @JsonProperty("typecode")
  private String typeCode;

  /**
   * 别名
   */
  private String alias;

  /**
   * 标签信息，主要出现在美食类POI中，代表特色菜，extensions=all时返回
   */
  private String tag;

  /**
   * 所属商圈，extensions=all时返回
   */
  @JsonProperty("business_area")
  @JsonAlias(value = {"business_area", "businessarea"})
  private String businessArea;

  /**
   * 照片相关信息，extensions=all时返回
   */
  private List<AmapPoiPhoto> photos;

  @Override
  public String toString() {
    return MoreObjects
      .toStringHelper(this)
      .omitNullValues()
      .add("id", id)
      .add("name", name)
      .add("location", location)
      .add("address", convertEmptyToNull(address))
      .add("type", convertEmptyToNull(type))
      .add("telephone", convertEmptyToNull(telephone))
      .add("distance", distance)
      .add("province", province)
      .add("city", convertEmptyToNull(city))
      .add("district", convertEmptyToNull(district))
      .add("provinceCode", convertEmptyToNull(provinceCode))
      .add("adcode", convertEmptyToNull(adcode))
      .add("typeCode", convertEmptyToNull(typeCode))
      .add("alias", convertEmptyToNull(alias))
      .add("tag", convertEmptyToNull(tag))
      .add("businessArea", convertEmptyToNull(businessArea))
      .add("photos", photos)
      .toString();
  }
}
