package com.sharecrm.egress.entity;

import io.protostuff.LinkedBuffer;
import io.protostuff.ProtostuffIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import org.ehcache.spi.serialization.Serializer;
import org.ehcache.spi.serialization.SerializerException;

import java.nio.ByteBuffer;

public class LocationSerializer implements Serializer<LocationDTO> {
  private static final Schema<LocationDTO> schema;

  static {
    schema = RuntimeSchema.getSchema(LocationDTO.class);
  }

  /**
   * 构造函数，传入 ClassLoader 参数，用于反序列化时使用
   */
  public LocationSerializer(ClassLoader classLoader) {
    // do something with classLoader if needed
  }


  @Override
  public ByteBuffer serialize(LocationDTO object) throws SerializerException {
    return ByteBuffer.wrap(ProtostuffIOUtil.toByteArray(object, schema, LinkedBuffer.allocate()));
  }

  @Override
  public LocationDTO read(ByteBuffer binary) throws SerializerException {
    LocationDTO dto = schema.newMessage();
    ProtostuffIOUtil.mergeFrom(binary.array(), binary.position(), binary.remaining(), dto, schema);
    return dto;
  }

  @Override
  public boolean equals(LocationDTO object, ByteBuffer binary) throws SerializerException {
    return object.equals(read(binary));
  }
}
