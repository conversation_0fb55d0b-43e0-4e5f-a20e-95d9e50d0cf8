package com.sharecrm.egress.entity;

/**
 * 短信服务通道类型定义，比如移动梦网、阿里云等
 */
public interface SmsProvider {

  /**
   * 短信服务通道唯一ID，我们自己定义不可随便改，API使用ID交互
   */
  String getId();

  /**
   * 注意名字是客户在页面上可看到的，要唯一且易读
   */
  String getName();

  /**
   * 服务商类型，根据类型在页面上可能有不同的动作，比如：华为云、阿里云
   */
  String getType();

  /**
   * true表示专属定制，只能给特定的云或EA使用，比如蒙牛云短信通道只能给蒙牛云用
   */
  boolean isExclusive();

  /**
   * true表示启用
   */
  boolean isEnabled();

  /**
   * 支持哪些EI使用，使用灰度规则判断，类似 "white:EI.777421|EI.********-********"
   */
  String getAllowAccounts();

  /**
   * 中文签名
   */
  String getZhSignName();

  /**
   * 英文签名，可能为空，如果没有可能用英文签名占位
   */
  String getEnSignName();

  /**
   * 国际短信：中文签名，可能为空
   */
  String getIntlZhSignName();

  /**
   * 国际短信：英文签名，可能为空
   */
  String getIntlEnSignName();

}
