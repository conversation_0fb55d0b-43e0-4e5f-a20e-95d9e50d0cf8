package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import lombok.Data;

@Data
public class BaiduPoiPoint {

  /**
   * 经度
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  @JsonProperty("x")
  private double longitude;

  /**
   * 纬度
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  @JsonProperty("y")
  private double latitude;

  public BaiduPoiPoint(double longitude, double latitude) {
    this.longitude = longitude;
    this.latitude = latitude;
  }

  @JsonIgnore
  public LocationPoint toLocationPoint() {
    return new LocationPoint(longitude, latitude);
  }

}
