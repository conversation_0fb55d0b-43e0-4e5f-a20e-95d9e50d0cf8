package com.sharecrm.egress.entity;

import com.google.common.base.MoreObjects;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@EqualsAndHashCode
public class DrivingRouteRequest {
  private LocationPoint from;
  private LocationPoint to;
  private List<LocationPoint> waypoints;
  private String language;

  public String toString() {
    String points = waypoints == null ? null : waypoints.stream().map(LocationPoint::toLatitudeLongitude).collect(Collectors.joining(";"));
    return MoreObjects
      .toStringHelper(this)
      .omitNullValues()
      .add("from", this.getFrom().toLatitudeLongitude())
      .add("to", this.getTo().toLatitudeLongitude())
      .add("waypoints", points)
      .toString();
  }
}
