package com.sharecrm.egress.entity;

import lombok.Data;

import java.util.Date;

/**
 * 短信历史分页查询请求体
 */
@Data
public class SmsPageRequest {
  private String msgId;
  /**
   * 批次ID
   */
  private String batchId;
  private String providerId;
  private String ei;
  private String smsType;
  private String phone;
  private String bizName;
  private Boolean status;
  private Boolean replyStatus;
  private Date startTime;
  private Date endTime;
  private String content;
  private int pageNum = 1;
  /**
   * 每页的数据量，最大1000条，否则服务可能 OOM
   */
  private int pageSize = 10;
}