package com.sharecrm.egress.entity;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 名片识别功能k-v标准化，抹平各个厂商的差异，定义一套我们自己的key
 */
@Getter
public enum BusinessCardKey {

  /**
   * 用户的名字
   */
  NAME("name", List.of("name", "姓名", "英文姓名", "(.*)(姓名)")),
  FAX("fax", List.of("FAX", "传真", "英文传真", "(.*)(传真)")),
  TITLE("title", List.of("TITLE", "职位", "英文职位", "(.*)(职位)")),
  DEPARTMENT("department", List.of("DEPARTMENT", "部门", "英文部门", "(.*)(部门)")),
  COMPANY("company", List.of("COMPANY", "公司", "英文公司", "(.*)(公司)")),
  EMAIL("email", List.of("email", "mail", "邮箱", "英文邮箱", "(.*)(邮箱)")),
  MOBILE("mobile", List.of("MOBILE", "手机", "英文手机", "(.*)(手机)")),
  TEL("telephone", List.of("TEL", "telephone", "电话", "英文电话", "(.*)(电话)")),
  QQ("qq", List.of("QQ", "qq")),
  WECHAT("wechat", List.of("wechat", "微信")),
  URL("url", List.of("URL", "网址", "英文网址", "(.*)(网址)")),
  ADDRESS("address", List.of("ADDR", "地址", "英文地址", "(.*)(地址)"));

  /**
   * 我们定义的标准化key
   */
  private final String standard;

  /**
   * 别名，不同厂商或语言有不同的别名
   */
  private final List<String> alias;

  BusinessCardKey(String standard, List<String> alias) {
    this.standard = standard;
    this.alias = alias;
  }

  @Nullable
  public static String firstNotBlankAlias(Map<String, String> map, BusinessCardKey key) {
    Set<Map.Entry<String, String>> entries = map.entrySet();
    //注意这两个循环，alias在外层，需要按key的优先级获取值，不要按返回value的优先级
    for (String alias : key.alias) {
      for (Map.Entry<String, String> entry : entries) {
        if (entry.getKey().matches(alias) && StringUtils.isNotBlank(entry.getValue())) {
          return entry.getValue();
        }
      }
    }
    return null;
  }

}
