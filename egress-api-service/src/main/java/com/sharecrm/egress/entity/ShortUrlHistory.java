package com.sharecrm.egress.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import static com.baomidou.mybatisplus.annotation.IdType.ASSIGN_UUID;

/**
 * 已删除的短链，备份表，数据库表实体
 */
@Data
@TableName("t_shorturl_history")
public class ShortUrlHistory {

  @TableId(type = ASSIGN_UUID)
  private String id;
  private String code;
  private String url;
  @TableField("createtime")
  private Long createTime;

  /**
   * 企业ID，EI
   */
  @TableField("tenant_id")
  private String tenantId;

  /**
   * 过期时长，单位秒，根据创建时间判断是否过期
   */
  private Long timeout;

  /**
   * 0:未删除，大于0:已删除。code+deleted是唯一索引
   */
  private Long deleted = 0L;
}
