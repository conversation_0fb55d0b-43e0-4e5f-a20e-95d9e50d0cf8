package com.sharecrm.egress.entity;

import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;

/**
 * 短信模板MongoDB实体
 */
@Entity(value = "sms_template", noClassnameStored = true)
@Indexes({
  @Index(fields = {@Field(SmsTemplateEntity.TEMPLATE_ID)}, options = @IndexOptions(background = true, unique = true)),
  @Index(fields = {@Field(SmsTemplateEntity.PROVIDER_ID)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsTemplateEntity.TEMPLATE_NAME)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsTemplateEntity.INTL)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsTemplateEntity.EI)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsTemplateEntity.FILED_STATUS)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsTemplateEntity.PROVIDER_TEMPLATE_ID)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsTemplateEntity.TEMPLATE_TYPE)}, options = @IndexOptions(background = true)),
  @Index(fields = {@Field(SmsTemplateEntity.SEND_TIME)}, options = @IndexOptions(background = true)),
})
@Data
public class SmsTemplateEntity {

  public static final String TEMPLATE_NAME = "name";
  public static final String TEMPLATE_ID = "tid";
  public static final String EI = "ei";
  public static final String ALLOW_ACCOUNTS = "allows";
  public static final String FILED_STATUS = "status";
  public static final String TEMPLATE_TYPE = "tpl";
  public static final String PROVIDER_TEMPLATE_ID = "ptid";
  public static final String PROVIDER_DB_ID = "pdid";
  public static final String PROVIDER_ID = "provider";
  public static final String PROVIDER_NAME = "providerName";
  public static final String SEND_TIME = "ste";
  public static final String PARAM_REPLACE = "replace";
  public static final String UPDATE_TIME = "ute";

  /**
   * 是否为国际短信
   */
  public static final String INTL = "intl";

  @Id
  private ObjectId id;

  /**
   * 业务真正使用的模板ID，以此ID发送短信
   */
  @Property(TEMPLATE_ID)
  private String templateId;

  /**
   * 由供应商返回的模板ID，真正发短信的时候用的
   */
  @Property(PROVIDER_TEMPLATE_ID)
  private String providerTemplateId;

  /**
   * 由供应商返回的模板数据库ID，比如华为云就需要拿这个ID去查询模板状态
   */
  @Property(PROVIDER_DB_ID)
  private String providerTemplateDbId;

  /**
   * 模板对应的 provider ID
   */
  @Property(PROVIDER_ID)
  private String providerId;

  /**
   * 模板对应的 provider ID
   */
  @Property(PROVIDER_NAME)
  private String providerName;

  @Property(TEMPLATE_NAME)
  private String name;

  /**
   * 模板完整内容
   */
  @Property("content")
  private String content;

  /**
   * 模板申请说明
   */
  @Property("remark")
  private String remark;

  /**
   * 对模板中的变量进行替换，比如阿里云用 ${code} 表示变量，腾讯云 {1} 表示变量，我们在API上统一成一个格式，后台自动替换
   */
  @Property(PARAM_REPLACE)
  private String paramReplace;

  /**
   * 短信类型：验证码,短信通知,推广短信。
   */
  @Property(TEMPLATE_TYPE)
  private String templateType;

  /**
   * 指定专属EI，根据EI选择定制渠道，不指定时使用纷享默认的渠道
   */
  @Property(EI)
  private Integer enterpriseId;

  /**
   * 可以指定允许哪些EI访问，比如上游租户为下游租户创建的模板
   */
  @Property(ALLOW_ACCOUNTS)
  private String allowAccounts;

  /**
   * 是否国际/港澳台短信,true为国际短信，false为国内短信
   */
  @Property(INTL)
  private boolean international;

  /**
   * 审核状态
   */
  @Property(FILED_STATUS)
  private String status;

  /**
   * 审核意见，如果失败包含失败原因
   */
  @Property("reply")
  private String reply;

  /**
   * 发送时间，也就是创建时间
   */
  @Property(SEND_TIME)
  private Date sendTime;

  @Property(UPDATE_TIME)
  private Date updateTime;

}
