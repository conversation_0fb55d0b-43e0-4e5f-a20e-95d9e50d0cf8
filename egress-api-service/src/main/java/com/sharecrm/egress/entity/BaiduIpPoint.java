package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import lombok.Data;

@Data
public class BaiduIpPoint {
  /**
   * 经度
   */
  @JsonProperty("x")
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private double longitude;
  /**
   * 纬度
   */
  @JsonProperty("y")
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private double latitude;
}
