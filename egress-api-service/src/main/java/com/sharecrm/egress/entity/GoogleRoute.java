package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class GoogleRoute {
  @JsonProperty("distanceMeters")
  private Integer distance;
  /**
   * 所需的路线导航时长。如果您将 routingPreference 设置为 TRAFFIC_UNAWARE，则此值与 staticDuration 相同。如果您将 routingPreference 设置为 TRAFFIC_AWARE 或 TRAFFIC_AWARE_OPTIMAL，则系统会在计算此值时考虑到路况条件。
   * 此时间以秒为单位，最多包含九个小数位，并以“s”结尾。示例："3.5s"。
   */
  private String duration;

  public int toDurationInSeconds() {
    return duration == null ? 0 : Integer.parseInt(duration.substring(0, duration.length() - 1));
  }
}
