package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class GooglePoiLocation {
  @JsonProperty("place_id")
  private String id;
  private String name;
  @JsonProperty("vicinity")
  private String area;
  private GoogleGeometry geometry;
  @JsonProperty("business_status")
  private String businessStatus;
  /**
   * 分类信息
   */
  private List<String> types;
}
