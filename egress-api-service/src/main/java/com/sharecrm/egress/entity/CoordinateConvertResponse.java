package com.sharecrm.egress.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 坐标转换请求返回体
 */
@Data
public class CoordinateConvertResponse {

  /**
   * 经度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double longitude;

  /**
   * 纬度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double latitude;

}
