package com.sharecrm.egress.entity;

import java.util.Map;

/**
 * Created by wuzh on 2016/5/17.
 */
public class PayloadDTO {
  private int isRing;
  private String soundName;
  private int isVibrate;
  private int unreadNumber;
  private String enterpriseAccount;
  private Integer employeeId;
  private String title;
  private String summary;
  private String separator;
  private int type;
  private Map data;

  private boolean reportEnable = false;
  private String pushMessageId;

  //add in 2017-4-18
  private String regionId;
  private String regionTitle;
  private int regionUnreadNumber;

  /**
   * add in 2018-04-10
   */
  private String bizPath;

  public int getIsRing() {
    return isRing;
  }

  public void setIsRing(int isRing) {
    this.isRing = isRing;
  }

  public int getIsVibrate() {
    return isVibrate;
  }

  public void setIsVibrate(int isVibrate) {
    this.isVibrate = isVibrate;
  }

  public int getUnreadNumber() {
    return unreadNumber;
  }

  public void setUnreadNumber(int unreadNumber) {
    this.unreadNumber = unreadNumber;
  }

  public String getEnterpriseAccount() {
    return enterpriseAccount;
  }

  public void setEnterpriseAccount(String enterpriseAccount) {
    this.enterpriseAccount = enterpriseAccount;
  }

  public Integer getEmployeeId() {
    return employeeId;
  }

  public void setEmployeeId(Integer employeeId) {
    this.employeeId = employeeId;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public Map getData() {
    return data;
  }

  public void setData(Map data) {
    this.data = data;
  }

  public boolean isReportEnable() {
    return reportEnable;
  }

  public void setReportEnable(boolean reportEnable) {
    this.reportEnable = reportEnable;
  }

  public String getPushMessageId() {
    return pushMessageId;
  }

  public void setPushMessageId(String pushMessageId) {
    this.pushMessageId = pushMessageId;
  }

  public String getSoundName() {
    return soundName;
  }

  public void setSoundName(String soundName) {
    this.soundName = soundName;
  }

  public String getSeparator() {
    return separator;
  }

  public void setSeparator(String separator) {
    this.separator = separator;
  }

  public String getRegionId() {
    return regionId;
  }

  public void setRegionId(String regionId) {
    this.regionId = regionId;
  }

  public String getRegionTitle() {
    return regionTitle;
  }

  public void setRegionTitle(String regionTitle) {
    this.regionTitle = regionTitle;
  }

  public int getRegionUnreadNumber() {
    return regionUnreadNumber;
  }

  public void setRegionUnreadNumber(int regionUnreadNumber) {
    this.regionUnreadNumber = regionUnreadNumber;
  }

  public String getBizPath() {
    return bizPath;
  }

  public void setBizPath(String bizPath) {
    this.bizPath = bizPath;
  }

  public static final class Convert {
    public static PayloadDTO from(PushMessageDTO dto) {
      return from(dto, false);
    }

    public static PayloadDTO from(PushMessageDTO dto, boolean reportEnable) {
      PayloadDTO ret = new PayloadDTO();
      ret.setIsRing(dto.isWithSound() ? 1 : 0);
      ret.setSoundName(dto.getSoundName());
      ret.setIsVibrate(dto.isVibrate() ? 1 : 0);
      ret.setUnreadNumber(dto.getUnreadNumber());
      ret.setEnterpriseAccount(dto.getEnterpriseAccount());
      ret.setEmployeeId(dto.getEmployeeId());
      ret.setTitle(dto.getTitle());
      ret.setSummary(dto.getSummary());
      ret.setType(dto.getMessageType());
      ret.setData(dto.getBizData());
      ret.setBizPath(dto.getBizPath());
      ret.setSeparator(dto.getSeparator());
      //埋点上报
      ret.setReportEnable(reportEnable);
      ret.setPushMessageId(dto.getId());

      //聚合功能新增字段
      ret.setRegionId(dto.getRegionId());
      ret.setRegionTitle(dto.getRegionTitle());
      ret.setRegionUnreadNumber(dto.getRegionUnreadNumber());
      return ret;
    }
  }

}
