package com.sharecrm.egress.entity;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 短信发送记录，用于日志分析和告警，存入ClickHouse通用表generic-biz-log里，注意Tag的序列参考biz-log说明
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmsBizLog {

  /**
   * 日志类型是generic-biz-log的要求，必须全局唯一，不能随便变
   */
  @Tag(1)
  private String logType = "egress-sms";
  @Tag(2)
  private long stamp;
  @Tag(3)
  private String app;
  @Tag(4)
  private String serverIp;
  @Tag(5)
  private String profile;
  @Tag(6)
  private String ea;
  @Tag(7)
  private String tenantId;
  @Tag(8)
  private String uid;
  @Tag(9)
  private String traceId;
  @Tag(10)
  private String smsType;
  @Tag(11)
  private String phone;
  /**
   * 短信内容，脱敏以后的
   */
  @Tag(14)
  private String content;

  /**
   * 注意Tag的序列参考biz-log说明，51-70是string类型的扩展字段
   */
  @Tag(51)
  private String providerId;
  @Tag(52)
  private String providerName;
  @Tag(53)
  private String bizName;

  /**
   * 发送状态，标记我们发送给供应商是否成功
   */
  @Tag(54)
  private String status;

  @Tag(55)
  private String message;

  /**
   * 是否国际/港澳台短信,true为国际短信，false为国内短信
   */
  @Tag(56)
  private String international;

  /**
   * 此次发送记录的消息ID，即发送流水号，由我们自己生成，可根据此ID查询此条短信的回执状态
   */
  @Tag(57)
  private String msgId;

  /**
   * 各个短信供应商返回的消息ID，由供应商定义的流水号，部分供应商可能不支持
   */
  @Tag(58)
  private String serialId;

  /**
   * 短信内容，加密以后的
   */
  @Tag(59)
  private String encrypted;

  /**
   * 供应商回复的状态，标记是否接收成功
   */
  @Tag(60)
  private String replyStatus;

  /**
   * 供应商回复的具体状态码
   */
  @Tag(61)
  private String replyCode;

  /**
   * 供应商回复的消息，可能成功可能失败
   */
  @Tag(62)
  private String replyMessage;

  /**
   * 短信原始长度，有些运营商可能无法返回，仅作参考
   */
  @Tag(151)
  private Integer smsLength;

  /**
   * 短信计费条数，有些运营商可能无法返回，仅作参考
   */
  @Tag(152)
  private Integer smsSize;

}

