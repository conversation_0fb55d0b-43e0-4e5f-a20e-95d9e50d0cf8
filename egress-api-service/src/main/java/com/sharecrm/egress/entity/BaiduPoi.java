package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BaiduPoi {
  /**
   * poi的唯一标识，可用于详情检索
   */
  @JsonProperty("uid")
  private String id;

  /**
   * 名称
   */
  private String name;

  /**
   * 经纬度，格式：Lng,Lat
   */
  private LocationPoint location;

  /**
   * 地址，比如：东四环中路189号百盛北门
   */
  @JsonAlias(value = {"address", "addr"})
  private String address;

  /**
   * 电话
   */
  @JsonAlias(value = {"telephone", "tel"})
  private String telephone;

  /**
   * 离中心点的距离，单位为米。 日，啥类型都有，数字全占了
   */
  private String distance;

  /**
   * poi类型，如’美食;中餐厅’。tag与poiType字段均为poi类型，建议使用tag字段，信息更详细。
   */
  private String tag;

  /**
   * poi坐标{x,y}
   */
  private BaiduPoiPoint point;

  /**
   * 省份，若是直辖市的时候，此处直接显示市名，例如北京市
   */
  private String province;

  /**
   * 城市
   */
  private String city;

  /**
   * 区县级别名称，例如朝阳区
   */
  @JsonProperty("area")
  private String district;

  /**
   * 区域编码
   */
  private String adcode;

  @JsonProperty("detail_info")
  private BaiduPoiDetail detail;
}
