package com.sharecrm.egress.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString(doNotUseGetters = true)
@Schema(title = "IP地址信息")
public class IpLocation {
  @Schema(title = "IP地址", example = "***********", description = "IP地址")
  private String ip;
  @Schema(title = "国家", example = "中国", description = "国家")
  private String country;
  @Schema(title = "两位的 ISO 3166-1 国家码", example = "CN", description = "两位的国家码，注意目前只有GeoLite2服务商支持")
  private String isoCode;
  @Schema(title = "省份", example = "北京市", description = "省份")
  private String province;
  @Schema(title = "城市", example = "北京市", description = "城市")
  private String city;
  @Schema(title = "区县(运营商已不支持)", example = "朝阳区", description = "区县，运营商已不支持，无法获取,请不要再使用")
  private String district;

  /**
   * 数据来源，比如高德、百度等，仅为部分服务做标记用，无业务含义
   */
  private String provider;

  /**
   * 返回语言
   */
  private String language;

  public IpLocation(String ip) {
    this.ip = ip;
  }

}
