package com.sharecrm.egress.entity;

import lombok.Data;

import java.util.List;

@Data
public class BaiduPoiResult {
  /**
   * 本次API访问状态，如果成功返回0，如果失败返回其他数字。
   */
  private int status;
  /**
   * 对API访问状态值的英文说明，如果成功返回ok，并返回结果字段，如果失败返回错误说明。
   */
  private String message;
  /**
   * POI检索总数，开发者请求中设置了page_num字段才会出现total字段。出于数据保护目的，单次请求total最多为150。
   */
  private int total;
  private List<BaiduPoi> results;
}
