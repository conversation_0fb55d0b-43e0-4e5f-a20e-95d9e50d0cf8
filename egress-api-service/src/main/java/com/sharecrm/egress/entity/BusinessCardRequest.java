package com.sharecrm.egress.entity;

import lombok.Data;

/**
 * OCR 名片识别请求体
 */
@Data
public class BusinessCardRequest {

  /**
   * 图片的 Base64 值。
   * 支持的图片格式：PNG、JPG、JPEG，暂不支持 GIF 格式。
   * 支持的图片大小：所下载图片经Base64编码后不超过 7M
   */
  private String imageBase64;

  /**
   * 图片未经base64的原始bytes值，和imageBase64选择其中一个，如果都有优先用imageBase64
   */
  private byte[] imageBytes;

  /**
   * 用户ID，用于灰度规则判断，不指定默认通过cep Header获取，类似 ea.employeeId
   */
  private String userId;

}
