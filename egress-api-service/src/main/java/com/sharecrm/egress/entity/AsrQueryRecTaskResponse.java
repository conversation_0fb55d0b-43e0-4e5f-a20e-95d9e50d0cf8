package com.sharecrm.egress.entity;

import lombok.Data;

/**
 * 查询录音文件转换结果返回体
 */
@Data
public class AsrQueryRecTaskResponse {

  public static final String STATUS_INIT = "init";
  public static final String STATUS_WAITING = "waiting";
  public static final String STATUS_DOING = "doing";
  public static final String STATUS_SUCCESS = "success";
  public static final String STATUS_FAILED = "failed";

  /**
   * 任务ID
   */
  private String taskId;

  /**
   * 当前状态，只有状态是 success 时才有转换文字结果
   * <p>
   * init：提交到运营商成功，首次初始化
   * waiting：等待转换
   * doing：正在转换
   * success：转换成功
   * failed：转换失败
   */
  private String status;

  /**
   * 转换后的文本信息，只有转换成功后才有
   */
  private String text;

  /**
   * 提示信息，如错误原因，与status对应
   */
  private String message;

  /**
   * 音频时长(毫秒)，此字段可能返回 null，表示取不到有效值。
   */
  private Long audioDuration;

}
