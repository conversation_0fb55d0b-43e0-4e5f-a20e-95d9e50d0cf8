package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TencentGeoLocation {
  /**
   * 最终用于坐标解析的地址或地点名称
   */
  @JsonProperty("title")
  private String formattedAddress;
  /**
   * 地址或地点的经纬度坐标（GCJ02坐标系）
   */
  private LocationPoint location;
  /**
   * 行政区划编码
   */
  @JsonProperty("ad_info")
  private TencentAdInfo adInfo;
  /**
   * 解析后的地址信息
   */
  @JsonProperty("address_components")
  private TencentAddressDetail detail;
  /**
   * 可信度参考：值范围 [1, 10]. <br/>
   * 我们根据用户输入地址的准确程度，在解析过程中，将解析结果的可信度(质量)，由低到高，分为1 - 10级，该值>=7时，解析结果较为准确，<7时，会存各类不可靠因素，开发者可根据自己的实际使用场景，对于解析质量的实际要求，进行参考。
   */
  private int reliability;
  /**
   * 解析精度级别，分为11个级别，一般>=9即可采用（定位到点，精度较高） 也可根据实际业务需求自行调整，完整取值表见下文。<br/>
   * <pre>
   * 值	解析级别
   * 1	城市
   * 2	区、县
   * 3	乡镇、街道
   * 4	村、社区
   * 5	开发区
   * 6	热点区域、商圈
   * 7	道路
   * 8	道路附属点：交叉口、收费站、出入口等
   * 9	门址
   * 10	小区、大厦
   * 11	POI点
   * </pre>
   */
  private int level;
}
