package com.sharecrm.egress.entity;

import lombok.Data;

/**
 * 录音文件转换结果，Rocket MQ 消息体
 */
@Data
public class AsrRecTaskDto {

  public static final String MQ_TOPIC_ASR_TASK = "fs-egress-asr-task";
  
  public static final String MQ_TAG_ASR_TASK_TENCENT = "tencent";

  /**
   * 任务ID
   */
  private String taskId;

  /**
   * 当前状态，只有状态是 success 时才有转换文字结果
   */
  private String status;

  /**
   * 提示信息，如错误原因，与status对应
   */
  private String message;

  /**
   * 音频时长(毫秒)，此字段可能返回 null，表示取不到有效值。
   */
  private Long audioDuration;

}
