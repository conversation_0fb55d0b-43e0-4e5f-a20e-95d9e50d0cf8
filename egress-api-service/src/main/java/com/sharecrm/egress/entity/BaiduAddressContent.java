package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BaiduAddressContent {
  
  private LocationPoint location;
  
  /**
   * 标准化地址
   */
  @JsonProperty("formatted_address")
  private String formattedAddress;

  /**
   * 商圈信息
   */
  private String business;
  
  /**
   * 详细地址信息
   */
  @JsonProperty("addressComponent")
  private BaiduAddressDetail addressComponent;

  /**
   * 周边poi数组
   */
  @JsonProperty("pois")
  private List<BaiduPoi> pois;

}
