package com.sharecrm.egress.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Schema(title = "附近POI信息")
public class MapPoi {
  
  /**
   * 哪个服务商，比如高德、百度、腾讯、谷歌等
   */
  @Schema(title = "服务商", example = "amap", description = "amap: 高德, baidu: 百度, tencent: 腾讯, google: 谷歌")
  private String provider;

  /**
   * 唯一ID
   */
  @Schema(title = "唯一ID", example = "B0FFG8Q2QI", description = "服务商的的POI唯一ID，可以用来查找详情")
  private String id;

  /**
   * 名称
   */
  @Schema(title = "名称", example = "百盛购物中心", description = "POI名称")
  private String name;

  /**
   * 经纬度
   */
  @Schema(title = "经纬度", example = "39.914714,116.476904", description = "地理位置, 格式为：lat(纬度),lng(经度)")
  private LocationPoint location;

  /**
   * 地址，比如：东四环中路189号百盛北门
   */
  @Schema(title = "地址", example = "东四环中路189号百盛北门", description = "POI地址")
  private String address;

  /**
   * 类型
   */
  @Schema(title = "类型", example = "购物服务;商场;购物中心", description = "POI类型")
  private String type;

  /**
   * 电话
   */
  @Schema(title = "电话", example = "010-51283388", description = "POI电话")
  private String telephone;

  /**
   * 离中心点的距离，单位为米
   */
  @Schema(title = "距离", example = "0", description = "距离中心点的距离，单位为米")
  @Builder.Default
  private Integer distance = 0;

  /**
   * 省份，若是直辖市的时候，此处直接显示市名，例如北京市
   */
  @Schema(title = "省份", example = "北京市", description = "省份")
  private String province;

  /**
   * 城市
   */
  @Schema(title = "城市", example = "北京市", description = "城市")
  private String city;

  /**
   * 区县级别名称，例如朝阳区
   */
  @Schema(title = "区县", example = "朝阳区", description = "区县")
  private String district;

  /**
   * 区域编码
   */
  @Schema(title = "区域编码", example = "110105", description = "区域编码")
  private String adcode;

  /**
   * 别名
   */
  @Schema(title = "别名", example = "百盛购物中心", description = "POI别名")
  private String alias;

  /**
   * 标签信息，主要出现在美食类POI中，代表特色菜
   */
  @Schema(title = "标签", example = "川菜;火锅", description = "标签信息，主要出现在美食类POI中，代表特色菜")
  private String tag;

  /**
   * 照片相关信息
   */
  @Schema(title = "照片", description = "照片相关信息")
  private String photoUrl;

}
