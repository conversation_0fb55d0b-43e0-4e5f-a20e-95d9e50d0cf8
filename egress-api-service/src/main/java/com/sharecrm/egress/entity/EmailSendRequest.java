package com.sharecrm.egress.entity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;


/**
 * 发送邮件请求体
 */
@Data
public class EmailSendRequest {

  /**
   * 接收人，一次最多允许200个
   */
  @Size(min = 1, max = 200)
  private List<String> to;

  /**
   * 抄送人，一次最多允许200个
   */
  @Size(max = 200)
  private List<String> cc;

  /**
   * 邮件主题，最大长度100
   */
  @NotBlank
  @Size(max = 100)
  private String subject;

  /**
   * 邮件详细内容
   */
  @NotBlank
  private String content;

  /**
   * 邮件发送人，已废弃。
   * <p>
   * 我们目前在用的腾讯企业邮箱要求发送人和登录人一样，否则出现 501 mail from address must be same as authorization user
   */
  private String from;

}
