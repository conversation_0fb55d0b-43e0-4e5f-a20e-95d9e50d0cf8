package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TencentGeoAddress {
  private LocationPoint location;
  private String address;
  @JsonProperty("formatted_addresses")
  private TencentFormattedAddress formattedAddress;
  @JsonProperty("address_component")
  private TencentAddressDetail addressComponent;
  @JsonProperty("ad_info")
  private TencentAdInfo adInfo;
  @JsonProperty("address_reference")
  private TencentAddressReference addressReference;

  /**
   * 周边poi，仅在传入参数get_poi=1时返回
   */
  @JsonProperty("pois")
  private List<TencentPoi> pois;
}
