package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.base.MoreObjects;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import lombok.Data;

@Data
public class GoogleGeoCode {
  /**
   * 经度（以度为单位）。它必须在 [-180.0, +180.0] 范围内。
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  @JsonProperty("longitude")
  private double longitude;
  /**
   * 纬度（以度为单位）。它必须在 [-90.0, +90.0] 范围内。
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  @JsonProperty("latitude")
  private double latitude;

  public GoogleGeoCode(double[] lngLat) {
    this.longitude = lngLat[0];
    this.latitude = lngLat[1];
  }

  public GoogleGeoCode(double longitude, double latitude) {
    this.longitude = longitude;
    this.latitude = latitude;
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this).add("longitude", String.format("%.6f", longitude)).add("latitude", String.format("%.6f", latitude)).toString();
  }
}
