package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AMapReverseGeoCode {
  /**
   * 标准化地址
   */
  @JsonProperty("formatted_address")
  private String formattedAddress;

  @JsonProperty("addressComponent")
  private AMapGeoAddress component;

  @JsonProperty("pois")
  private List<AmapPoi> pois;

  @JsonProperty("aois")
  private List<AmapPoi> aois;

}
