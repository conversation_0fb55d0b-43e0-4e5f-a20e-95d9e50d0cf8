package com.sharecrm.egress.entity;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地图服务调用记录，用于日志分析和告警，存入ClickHouse通用表generic-biz-log里，注意Tag的序列参考biz-log说明
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeoBizLog {

  /**
   * 日志类型是generic-biz-log的要求，必须全局唯一，不能随便变
   */
  @Tag(1)
  @Builder.Default
  private String logType = "egress-geo";
  @Tag(2)
  private long stamp;
  @Tag(3)
  private String app;
  @Tag(4)
  private String serverIp;
  @Tag(5)
  private String profile;
  @Tag(6)
  private String ea;
  @Tag(7)
  private String tenantId;
  @Tag(8)
  private String uid;
  @Tag(9)
  private String traceId;
  @Tag(10)
  private String objectApiName;
  @Tag(11)
  private String objectId;
  @Tag(14)
  private String message;
  /**
   * 注意Tag的序列参考biz-log说明，51-70是string类型的扩展字段
   */
  @Tag(51)
  private String bizName;
  @Tag(52)
  private String platform;
  /**
   * 状态
   */
  @Tag(54)
  private String status;

  /**
   * 运营商类型，如amap、tencent
   */
  @Tag(55)
  private String providerType;

}

