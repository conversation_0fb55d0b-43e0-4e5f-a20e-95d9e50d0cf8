package com.sharecrm.egress.entity;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 梦网返回状态吗
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@UtilityClass
public class MonternetStatus {

  // 常见错误消息常量 - 按语义分组
  private static final String SUCCESS = "成功";

  // 设备状态类
  private static final String POWER_OFF = "关机";
  private static final String SHUTDOWN = "停机";
  private static final String POWER_OFF_OR_SHUTDOWN = "关机或停机";
  private static final String EXPIRED_OR_SHUTDOWN = "过期或停机";
  private static final String SUSPENDED = "暂停服务";
  private static final String UNREACHABLE = "无法接通";
  private static final String INBOX_FULL = "收件箱已满";

  // 号码状态类
  private static final String EMPTY_NUMBER = "空号";
  private static final String EMPTY_OR_SHUTDOWN = "空号或停机";
  private static final String NUMBER_NOT_EXIST = "空号或号码不存在";
  private static final String POWER_OFF_SHUTDOWN_EMPTY = "关机、停机或空号";

  // 拦截类
  private static final String BLACKLIST = "黑名单";
  private static final String KEYWORD_BLOCKED = "关键字拦截";
  private static final String OPERATOR_BLOCKED = "运营商拦截";
  private static final String REGIONAL_BLOCKED = "地区拦截";
  private static final String FREQUENCY_BLOCKED = "频次拦截";

  // 流控类
  private static final String OVER_SPEED = "超流速";
  private static final String OVER_LIMIT = "超限";

  // 系统错误类
  private static final String NO_CHANNEL = "无通道";
  private static final String SMSC_ERROR = "SMSC系统错误";
  private static final String CONNECTION_ERROR = "连接错误";
  private static final String TIMEOUT = "超时";
  private static final String MESSAGE_REJECTED = "消息被拒绝";
  private static final String QUEUE_FULL = "队列已满";

  // 业务错误类
  private static final String PORTABILITY_FAILED = "携号转网失败";
  private static final String MESSAGE_CONCAT_FAILED = "短信拼接失败";
  private static final String SIGNATURE_ROUTE_ERROR = "签名路由配置错误";
  private static final String BILLING_ERROR = "计费错误";

  private static final Map<String, String> returnStatusDict = Map.ofEntries(
    Map.entry("-1", "参数为空。信息、电话号码等有空指针,登陆失败"),
    Map.entry("0", "发送成功"),
    Map.entry("-12", "有异常电话号码"),
    Map.entry("-14", "实际号码个数超过100"),
    Map.entry("-200", "连接梦网短信服务超时"),
    Map.entry("-999", "服务器内部错误"),
    Map.entry("-10001", "用户登陆不成功(帐号不存在/停用/密码错误)"),
    Map.entry("-10003", " 用户余额不足"),
    Map.entry("-10011", " 信息内容超长"),
    Map.entry("-10029", " 此用户没有权限从此通道发送信息(用户没有绑定该性 质的通道,比如:用户发了小灵通的号码)"),
    Map.entry("-10030", " 不能发送移动号码"),
    Map.entry("-10031", " 手机号码(段)非法"),
    Map.entry("-10057", " IP受限"),
    Map.entry("-10056", " 连接数超限"),
    Map.entry("-1002", "配置参数或userMsgId有问题"));

  // 使用List作为key，相同消息的错误码归并
  private static final Map<List<String>, String> replayMessageMapping = Map.<List<String>, String>ofEntries(
    // 成功状态
    Map.entry(List.of("DELIVRD"), SUCCESS),

    // 关键字拦截类
    Map.entry(List.of("ID:0076", "ID:1241"), KEYWORD_BLOCKED),
    Map.entry(List.of("GB:0013"), "运营商" + KEYWORD_BLOCKED),
    Map.entry(List.of("R:00870"), "短信内容触发" + KEYWORD_BLOCKED),
    Map.entry(List.of("M2:0051"), "平台" + KEYWORD_BLOCKED),

    // 黑名单类
    Map.entry(List.of("M2:0045", "DB:0141"), BLACKLIST),
    Map.entry(List.of("MA:0036", "MBBLACK", "E:BLACK", "R:00079"), OPERATOR_BLOCKED + BLACKLIST),
    Map.entry(List.of("DB:0144"), "移动运营商" + BLACKLIST),
    Map.entry(List.of("R:00141"), "移动" + BLACKLIST),

    // 频次拦截类
    Map.entry(List.of("M2:0046"), FREQUENCY_BLOCKED + "策略"),
    Map.entry(List.of("M2:0043"), "30秒内重复发送" + FREQUENCY_BLOCKED),
    Map.entry(List.of("MK:0036"), "同一号码发送过多" + FREQUENCY_BLOCKED),
    Map.entry(List.of("MK:1008"), "号码" + OVER_LIMIT),

    // 关机类
    Map.entry(List.of("UNDELIV", "REJECTD", "EXPIRED", "R:00010", "R:00093", "R:00124", "R:00815",
      "MN:0054", "MN:0029", "MN:0017", "MN:0059", "MN:0022", "MN:0053", "MN:0044",
      "MN:0036", "MK:0013", "MK:0011", "MK:0057"), POWER_OFF),

    // 停机类
    Map.entry(List.of("MK:0005", "MK:0010", "R:00013", "R:00182", "MN:0011", "R:00102"), SHUTDOWN),

    // 关机或停机类
    Map.entry(List.of("MI:0029", "MI:0005", "MI:0004", "MI:0000", "MI:0011", "MI:0084", "MI:0010",
      "MI:0017", "MI:0012", "MI:0001", "MI:0022", "MI:0054", "MI:0015", "MI:0008",
      "MI:0059", "MI:0081", "MI:0020", "MI:0051", "MI:0036", "MI:0083", "MI:0044",
      "MI:0045", "MI:0050", "MI:0080", "MI:0053", "MI:0002", "MI:0057", "MI:0075",
      "MI:0063", "R:00213", "R:00011"), POWER_OFF_OR_SHUTDOWN),

    // 过期或停机类
    Map.entry(List.of("MI:0024", "IC:0151", "MC:0055"), EXPIRED_OR_SHUTDOWN),

    // 空号类
    Map.entry(List.of("MN:0001", "R:00012", "R:00029", "R:00005", "R:00705", "MI:0013", "R:00008",
      "R:00007", "R:00869", "R:00023", "R:00640", "R:00660", "MN:0051", "ID:0013",
      "R:00104", "MN:0020", "R:00000"), EMPTY_NUMBER),

    // 空号或停机类（包含各种变体）
    Map.entry(List.of("R:00001", "MK:0001", "MK:0000", "R:00024", "R:00602", "R:00004", "R:00003",
      "R1:0013", "R1:0043", "MK:0012", "MK:0015", "MK:0022", "MK:0020", "MK:0024",
      "MK:0055", "MK:0008", "MN:0012", "R:00110", "R:00101", "MC:0151", "R:00255"), EMPTY_OR_SHUTDOWN),

    // 空号或号码不存在
    Map.entry(List.of("NOROUTE"), NUMBER_NOT_EXIST),
    Map.entry(List.of("MI:0055"), EMPTY_NUMBER + "或" + UNREACHABLE),

    // 关机、停机或空号类（通用描述）
    Map.entry(List.of("R:00006", "R:00702", "R:00760", "R:00069", "R:00015", "R:00701", "R:00055",
      "R:00054", "R:00771", "R:00765", "R:00802", "R:00059", "R:00017", "R:00726",
      "R:00801", "R:00002", "R:00009", "R:00090", "R:00022", "R:00027", "R:00067",
      "R:00020", "R:00612", "R:00713", "R:00051", "R:00680", "R:00615", "R:00053",
      "R:00706", "R:00617", "R:00092", "R:00098", "R:00714", "R:00779", "R:00636",
      "R:00181", "R:00650", "R:00057", "R:00817", "R:00019", "R:00089", "R:00050",
      "R:00613", "R:00253", "R:00048", "R:00083", "R:00619", "R:00627", "R:00711",
      "R:00827", "R:00045", "R:00088", "R:00056", "R:00099", "R:00072", "R:00254",
      "R:00718", "R:00634", "R:00764", "R:00762", "R:00814"), POWER_OFF_SHUTDOWN_EMPTY),

    // 暂停服务类
    Map.entry(List.of("R:00601", "MK:0004"), SUSPENDED),
    Map.entry(List.of("MK:0029"), SUSPENDED + "或" + POWER_OFF),

    // 地区拦截类
    Map.entry(List.of("R:00036", "MN:0174", "MK:0036"), REGIONAL_BLOCKED),

    // 无法接通类
    Map.entry(List.of("MN:0013"), UNREACHABLE),
    Map.entry(List.of("IC:0055"), POWER_OFF_OR_SHUTDOWN + "或" + UNREACHABLE),
    Map.entry(List.of("MK:0002"), POWER_OFF + "或" + UNREACHABLE),

    // 超流速类
    Map.entry(List.of("IA:0054", "R:00061"), OVER_SPEED),
    Map.entry(List.of("IB:0008", "IA:0051"), OVER_SPEED + "拦截"),

    // 收件箱满
    Map.entry(List.of("MK:0017"), INBOX_FULL),

    // 不在服务区
    Map.entry(List.of("R:00219"), "不在服务区"),

    // 响应超时
    Map.entry(List.of("R:00018", "MA:0073"), TIMEOUT),

    // 系统和通道错误
    Map.entry(List.of("M2:0010", "M1:0010"), NO_CHANNEL),
    Map.entry(List.of("MB:0008", "MB:1042", "MB:1077"), SMSC_ERROR),
    Map.entry(List.of("UNKNOWN"), "状态未知"),
    Map.entry(List.of("R:00999"), POWER_OFF + "或" + OPERATOR_BLOCKED),
    Map.entry(List.of("MC:0001"), "未收到状态报告"),

    // 队列和容量错误
    Map.entry(List.of("MB:0088", "MB:0019"), QUEUE_FULL),
    Map.entry(List.of("M1:0241"), "超日流量"),

    // 连接错误
    Map.entry(List.of("MA:0051", "ID:0070"), CONNECTION_ERROR),
    Map.entry(List.of("MB:0255"), "无法发送"),

    // 消息被拒绝
    Map.entry(List.of("MN:0000"), MESSAGE_REJECTED),
    Map.entry(List.of("R:00105"), "用户状态异常"),

    // 运营商拦截
    Map.entry(List.of("MA:0006"), OPERATOR_BLOCKED),
    Map.entry(List.of("R:00614"), "号码异常"),

    // 业务错误
    Map.entry(List.of("UT:0009", "M1:0046"), PORTABILITY_FAILED),
    Map.entry(List.of("M2:0079"), MESSAGE_CONCAT_FAILED),
    Map.entry(List.of("M2:0057"), SIGNATURE_ROUTE_ERROR),
    Map.entry(List.of("ID:0012"), BILLING_ERROR),

    // 特殊状态描述
    Map.entry(List.of("MK:0013"), "用户状态异常无法接收短信"),
    Map.entry(List.of("DB:0119"), "用户无法取消业务"));


  public static String getOrDefault(String code, String defaultStr) {
    return returnStatusDict.getOrDefault(code, defaultStr);
  }

  public static String getReplayMessage(String code) {
    if (StringUtils.isEmpty(code)) {
      return "";
    }
    return code + " " + replayMessageMapping.entrySet()
      .stream()
      .filter(entry -> entry.getKey().contains(code))
      .findFirst()
      .map(Map.Entry::getValue)
      .orElse("");
  }
}
