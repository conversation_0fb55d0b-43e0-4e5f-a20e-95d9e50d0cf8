package com.sharecrm.egress.entity;

import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

/**
 * 梦网返回状态吗
 * <p>
 * IgnoreI18nFile ignore i18n
 */
@UtilityClass
public class MonternetStatus {

  private static final Map<String, String> returnStatusDict = new HashMap<>();

  static {
    returnStatusDict.put("-1", "参数为空。信息、电话号码等有空指针,登陆失败");
    returnStatusDict.put("0", "发送成功");
    returnStatusDict.put("-12", "有异常电话号码");
    returnStatusDict.put("-14", "实际号码个数超过100");
    returnStatusDict.put("-200", "连接梦网短信服务超时");
    returnStatusDict.put("-999", "服务器内部错误");
    returnStatusDict.put("-10001", "用户登陆不成功(帐号不存在/停用/密码错误)");
    returnStatusDict.put("-10003", " 用户余额不足");
    returnStatusDict.put("-10011", " 信息内容超长");
    returnStatusDict.put("-10029", " 此用户没有权限从此通道发送信息(用户没有绑定该性 质的通道,比如:用户发了小灵通的号码)");
    returnStatusDict.put("-10030", " 不能发送移动号码");
    returnStatusDict.put("-10031", " 手机号码(段)非法");
    returnStatusDict.put("-10057", " IP受限");
    returnStatusDict.put("-10056", " 连接数超限");
    returnStatusDict.put("-1002", "配置参数或userMsgId有问题");
  }

  public static String getOrDefault(String code, String defaultStr) {
    return returnStatusDict.getOrDefault(code, defaultStr);
  }
}
