package com.sharecrm.egress.entity;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息推送记录，存入ClickHouse通用表generic-biz-log里，注意Tag的序列参考biz-log说明
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushRecord {

  public static final int PUSH_STATUS_SUCCESS = 0;
  public static final int PUSH_STATUS_FAILURE = 1;
  public static final int PUSH_STATUS_EXCEPTION = 2;

  /**
   * 日志类型是generic-biz-log的要求，必须全局唯一，不能随便变
   */
  @Tag(1)
  @Builder.Default
  private String logType = "egress-notify-push";
  @Tag(2)
  private long stamp;
  @Tag(3)
  private String app;
  @Tag(4)
  private String serverIp;
  @Tag(5)
  private String profile;
  @Tag(6)
  private String ea;
  @Tag(7)
  private String tenantId;
  @Tag(8)
  private String uid;
  @Tag(9)
  private String traceId;
  @Tag(11)
  private String objectId;
  /**
   * 消息推送的结果，成功或失败
   */
  @Tag(14)
  private String status;

  @Tag(15)
  private long cost;
  /**
   * 注意Tag的序列参考biz-log说明
   */
  @Tag(51)
  private String token;
  @Tag(52)
  private String title;
  /**
   * 发送的消息内容
   */
  @Tag(53)
  private String summary;

  /**
   * 向服务器推送的消息内容
   */
  @Tag(54)
  private String pushContent;

  @Tag(55)
  private String resultCode;

  @Tag(56)
  private String resultInfo;
  /**
   * 消息对象的文本化字符串
   */
  @Tag(57)
  private String consumerMessage;

  @Tag(58)
  private String pushServerSource;
  /**
   * 第三方服务器返回的MessageId
   */
  @Tag(59)
  private String requestId;
  @Tag(60)
  private String osVersion;
  @Tag(61)
  private String appVersion;
  /**
   * 消息类型，比如通知栏、透传
   */
  @Tag(62)
  private String notifyPushType;

  /**
   * Rocket MQ msg id
   */
  @Tag(63)
  private String mqMsgId;

  /**
   * 消息发送到第三方服务器的时间
   */
  @Tag(201)
  private long pushedTime;

  /**
   * 消息MQ消费的时间
   */
  @Tag(202)
  private long consumerTime;

  /**
   * 消息产生的时间，由MQ的provider定义的
   */
  @Tag(203)
  private long produceTime;

}

