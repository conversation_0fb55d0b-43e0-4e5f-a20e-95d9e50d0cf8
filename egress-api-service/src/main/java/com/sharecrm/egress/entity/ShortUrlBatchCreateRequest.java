package com.sharecrm.egress.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

import java.util.List;

import lombok.Data;


/**
 * 短链批量创建请求，给出长链，转成短链
 */
@Data
@Schema
public class ShortUrlBatchCreateRequest {

  /**
   * 长链地址
   */
  @Size(min = 1, max = 1000)
  private List<String> urls;

  /**
   * 企业ID，EI，如果不传从Trace中获取
   */
  private String tenantId;

  /**
   * 过期时长，单位秒，根据创建时间判断是否过期, -1代表永不过期，0是根据系统默认内置规则判断是否过期
   * <p>
   * 过期后再请求会返回404，请注意处理错误。
   */
  private Long timeout;


}
