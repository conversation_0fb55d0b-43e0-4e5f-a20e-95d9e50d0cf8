package com.sharecrm.egress.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import lombok.Data;

/**
 * 为兼容APl函数接口单独定义的结构
 */
@Data
public class LocationPointForApl {
  /**
   * 经度
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private double longitude;
  /**
   * 纬度
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private double latitude;

  public LocationPointForApl(double longitude, double latitude) {
    this.longitude = longitude;
    this.latitude = latitude;
  }

}
