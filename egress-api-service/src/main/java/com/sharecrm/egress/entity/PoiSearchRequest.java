package com.sharecrm.egress.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PoiSearchRequest {

  /**
   * 关键字，必须指定
   */
  @NotEmpty
  private List<String> keywords;

  /**
   * 类型，可选
   */
  private List<String> types;

  /**
   * 经度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double longitude;

  /**
   * 纬度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double latitude;

  /**
   * 搜索半径
   */
  private int radius;

  /**
   * 是否允许使用cache，默认是true，优先从cache查询，无特殊情况请启用cache
   */
  private boolean cache = true;

  /**
   * 每页返回的条数，最大限制为20条，默认为10条
   */
  private int pageSize = 10;
  /**
   * 分页页码，从1开始计数
   */
  private int pageNum = 1;

  /**
   * 优先选择的运营商，可以不指定，不指定时后台自动根据可用的运营商轮换选择。
   * 支持：amap: 高德, baidu: 百度, tencent: 腾讯, google: 谷歌。
   * <p>
   * 1. 国内运营商，后台自动根据可用的运营商轮换选择。
   * 2. 如果是指定Google，只能用Google。
   */
  private String firstProvider;

  /**
   * 返回信息的语言，国内默认是zh-CN，Google默认是en，只支持中文和英文
   */
  private String language;

  /**
   * 谷歌访问的下一页翻页Token，需要等待几秒才会生效
   */
  private String pageToken;

  /**
   * 自定义业务名字，用于报表统计
   */
  private String bizName;

}
