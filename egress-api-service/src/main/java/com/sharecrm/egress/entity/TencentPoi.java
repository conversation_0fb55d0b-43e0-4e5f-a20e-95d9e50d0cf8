package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TencentPoi {
  /**
   * POI唯一标识
   */
  private String id;
  /**
   * POI名称
   */
  @JsonProperty("title")
  private String name;
  /**
   * POI地址
   */
  private String address;
  @JsonProperty("tel")
  private String telephone;
  /**
   * POI分类
   */
  private String category;
  /**
   * POI类型，值说明：0:普通POI / 1:公交车站 / 2:地铁站 / 3:公交线路 / 4:行政区划
   */
  private String type;
  /**
   * POI坐标点
   */
  private LocationPoint location;
  /**
   * POI距离中心点的距离，单位米
   */
  @JsonProperty("_distance")
  private Integer distance;
  @JsonProperty("ad_info")
  private TencentAdInfo adInfo;
}
