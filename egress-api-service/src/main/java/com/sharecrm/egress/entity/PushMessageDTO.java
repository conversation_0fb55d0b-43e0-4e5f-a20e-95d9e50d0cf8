package com.sharecrm.egress.entity;

import com.facishare.oms.api.model.APNSData;
import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.Platform;
import com.facishare.oms.api.model.interaction.Interaction;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.google.common.base.MoreObjects;
import com.google.common.base.Strings;
import com.sharecrm.egress.utils.Constants;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by wuzh on 2016/4/19.
 */
@Getter
@Setter
public class PushMessageDTO {

  private String id;
  private String token;
  private int employeeId = -1;
  private String title;
  private String summary;
  private String enterpriseAccount;
  private long produceTime = -1;
  private long consumerTime = -1;
  private String consumerMessage;

  private String customItemKey;
  private String customItemValue;
  private int unreadNumber;
  private boolean withSound;
  private boolean isVibrate;
  private String soundName = "default";
  private String pushContent;

  private String pushServerSource;
  private int messageType;
  private Map bizData;

  //add new
  protected long pushedTime;
  private int pushStatus;
  private String resultCode;
  private String resultInfo;
  private String originalMessage;  //兼容字段
  private String requestId; //第三方服务器返回的MessageId
  private String separator;

  /**
   * add in 2017-4-18
   */
  private String regionId;
  private String regionTitle;
  private int regionUnreadNumber;

  /**
   * add in 2017-7-11
   */
  private APNSData apnsData;

  /**
   * add in 2017-12-7
   */
  private String osVersion;
  private String appVersion;

  /**
   * add in 2018-02-08
   */
  //免打扰
  private boolean noDisturb;

  /**
   * add in 2018-04-10
   */
  private String bizPath;

  /**
   * add in 2018-09-03
   */
  private Interaction interaction;
  private String category;

  /**
   * add in 2019-02-15
   */
  private String channelId;

  /**
   * add in 2020-02-23
   */
  private String notifyPushType;

  /**
   * Rocket MQ的消息ID，不参与业务计算，是为了出错时追溯消息来源查看消息详情
   */
  private String mqMsgId;

  private PushMessageDTO() {

  }

  public String getSenderBizName() {
    return "E." + this.getEnterpriseAccount() + "." + this.getEmployeeId();
  }

  public String combineTitleAndSummary() {
    return Strings.isNullOrEmpty(title) ? summary : title + separator + summary;
  }


  @Override
  public String toString() {
    return "PushMessageDTO{" +
      "id='" + id + '\'' +
      ", token='" + token + '\'' +
      ", category='" + category + '\'' +
      ", employeeId=" + employeeId +
      ", title='" + title + '\'' +
      ", separator='" + separator + '\'' +
      ", summary='" + summary + '\'' +
      ", enterpriseAccount='" + enterpriseAccount + '\'' +
      ", pushServerSource='" + pushServerSource + '\'' +
      ", originalMessage='" + originalMessage + '\'' +
      ", regionId='" + regionId + '\'' +
      ", regionTitle='" + regionTitle + '\'' +
      ", regionUnreadNumber='" + regionUnreadNumber + '\'' +
      ", noDisturb='" + noDisturb + '\'' +
      ", interaction='" + interaction + '\'' +
      '}';
  }

  public static final class Convert {

    public static PushMessageDTO from(ThirdPartPushCommonMessage thirdMessage) {
      PushMessageDTO ret = new PushMessageDTO();
      ret.setToken(thirdMessage.getPnsToken().getToken());
      ret.setPushServerSource(createPushServerSource(thirdMessage.getPnsToken()));
      ret.setEnterpriseAccount(thirdMessage.getPnsToken().getEnterpriseAccount());
      ret.setOsVersion(MoreObjects.firstNonNull(thirdMessage.getPnsToken().getOsVersion(), ""));
      ret.setAppVersion(MoreObjects.firstNonNull(thirdMessage.getPnsToken().getClientVersion(), ""));

      ret.setEmployeeId(thirdMessage.getNotification().getEmployeeId());
      // fuck!! you are lower!!  如果title为null， 则ios终端（pushkit)不能正确展现消息
      ret.setTitle(Strings.nullToEmpty(thirdMessage.getNotification().getTitle()));
      //Ios 的PushKit不设置title默认值
      ret.setSummary(thirdMessage.getNotification().getSummary());
      ret.setProduceTime(thirdMessage.getCreateTime());
      ret.setConsumerTime(System.currentTimeMillis());
      ret.setConsumerMessage(thirdMessage.toString());
      ret.setUnreadNumber(thirdMessage.getNotification().getUnreadNumber());
      ret.setWithSound(thirdMessage.getNotification().isRing());
      ret.setVibrate(thirdMessage.getNotification().isVibrate());
      ret.setSeparator(thirdMessage.getNotification().getSeparator());
      ret.setSeparator(Strings.nullToEmpty(thirdMessage.getNotification().getSeparator()));

      if (!Strings.isNullOrEmpty(thirdMessage.getNotification().getInviteRing())) {
        ret.setSoundName(thirdMessage.getNotification().getInviteRing());
      }

      ret.setMessageType(thirdMessage.getNotification().getType());
      ret.setBizData(thirdMessage.getNotification().getData());
      ret.setOriginalMessage(thirdMessage.getNotification().getOriginalMessage());

      ret.setRegionId(thirdMessage.getNotification().getRegionId());
      String regionTitle = thirdMessage.getNotification().getRegionTitle();
      ret.setRegionTitle(regionTitle);
      ret.setRegionUnreadNumber(thirdMessage.getNotification().getRegionUnreadNumber());

      if (Constants.PUSH_SERVER_SOURCE_APPLE_ENT.equals(ret.getPushServerSource())
        || Constants.PUSH_SERVER_SOURCE_APPLE_PROD.equals(ret.getPushServerSource())) {
        ret.setApnsData(thirdMessage.getNotification().getApnsData());
      }

      ret.setNoDisturb(thirdMessage.getNotification().isNoDisturb());
      ret.setBizPath(thirdMessage.getNotification().getBizPath());
      ret.setInteraction(thirdMessage.getNotification().getInteraction());
      ret.setCategory(thirdMessage.getNotification().getCategory());

      ret.setChannelId(thirdMessage.getPnsToken().getChannelId());
      return ret;
    }

    private static String createPushServerSource(PNSToken pnsToken) {
      if (Strings.isNullOrEmpty(pnsToken.getPushServiceSource())) {
        return pnsToken.getPlatform() == Platform.PLATFORM_IOS ?
          pnsToken.isEnterprise() ? Constants.PUSH_SERVER_SOURCE_APPLE_ENT : Constants.PUSH_SERVER_SOURCE_APPLE_PROD :
          Constants.PUSH_SERVER_SOURCE_GETUI;
      }
      return pnsToken.getPushServiceSource();
    }
  }

}

