package com.sharecrm.egress.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 坐标转换请求体
 */
@Data
public class CoordinateConvertRequest {
  /**
   * 经度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double longitude;

  /**
   * 纬度
   */
  @NotNull
  @JsonSerialize(using = JsonDoubleSerializer.class)
  private Double latitude;

  /**
   * 原坐标系值，必填。
   * <p>
   * 支持以下几种类型：
   * gps;
   * mapbar;
   * baidu;
   * amap;
   */
  @NotBlank
  private String source;

  /**
   * 转成目标坐标系值，目前只支持 amap:高德
   */
  @NotBlank
  private String target;
  
  private String bizName;
}
