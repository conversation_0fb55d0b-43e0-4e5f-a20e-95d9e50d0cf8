package com.sharecrm.egress.entity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 翻译请求体
 */
@Data
public class TranslateRequest {

  /**
   * 要翻译的文本内容
   */
  @Size(min = 1, max = 128)
  private List<String> texts;

  /**
   * 原文语言类型，不指定时Google会尝试猜。比如中文：zh。注意语言定义使用的是`ISO-639 代码`。
   */
  private String source;

  /**
   * 目标语言类型，必填
   * <p>
   * 注意语言定义使用的是`ISO-639 代码`，可能和我们定义的不同：<a href="https://cloud.google.com/translate/docs/languages?hl=zh-cn">...</a>
   */
  @NotBlank
  private String target;

  /**
   * 原文类型，只支持html和text两种类型，不填默认是text
   */
  private String format = "text";

}
