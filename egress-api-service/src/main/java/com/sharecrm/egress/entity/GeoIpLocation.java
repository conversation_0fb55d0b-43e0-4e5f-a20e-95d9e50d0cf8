package com.sharecrm.egress.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据IP查所在地址返回结果
 */
@Data
@NoArgsConstructor
public class GeoIpLocation {
  private String ip;
  private String country;
  private String province;
  private String city;

  /**
   * 地区
   */
  private String region;

  /**
   * isp 字段新版本已无法获取，需要废弃掉
   */
  private String isp = "";

  public GeoIpLocation(String ip) {
    this.ip = ip;
  }

}
