package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import lombok.Data;

import static com.sharecrm.egress.api.AMapApi.convertEmptyToNull;

/**
 * 地理编码实体类
 *
 * <AUTHOR>
 */
@Data
public class AMapGeoLocation {
  /**
   * 标准化地址
   */
  @JsonProperty("formatted_address")
  private String formattedAddress;
  /**
   * 国家
   */
  private String country;
  /**
   * 省份
   */
  private String province;
  /**
   * 城市
   */
  private String city;

  /**
   * 城市编码
   */
  @JsonProperty("citycode")
  private String cityCode;
  
  /**
   * 区县
   */
  private String district;
  /**
   * 区域编码
   */
  private String adcode;
  /**
   * 街道信息
   */
  private String street;

  /**
   * 街道门牌号
   */
  @JsonProperty("number")
  private String streetNumber;
  /**
   * 经度，纬度
   */
  private String location;
  /**
   * 匹配级别
   */
  private String level;
  /**
   * 乡镇
   */
  private String[] township;

}
