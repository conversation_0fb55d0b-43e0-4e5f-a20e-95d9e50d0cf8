package com.sharecrm.egress.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(doNotUseGetters = true)
public class IpLocationEsCache extends IpLocation {

  private Long createTime;

  private Long updateTime;

}
