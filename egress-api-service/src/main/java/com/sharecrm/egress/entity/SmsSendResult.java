package com.sharecrm.egress.entity;

import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import lombok.Data;

/**
 * 短信发送结果记录，服务内部用于记录结果
 */
@Data
public class SmsSendResult {

  /**
   * 实际发送给运营商的内容，如果有模板，是经过模板解析的
   */
  private String content;

  /**
   * 使用的签名
   */
  private String sign;

  /**
   * 短信的类型，比如验证码、通知、营销等
   */
  private String smsType;

  /**
   * 是否国际/港澳台短信,true为国际短信，false为国内短信
   */
  private boolean international;

  /**
   * 无模版的请求体
   */
  private SmsSendRequest request;

  /**
   * 按模板发送的请求体
   */
  private SmsSendByTemplate templateRequest;

  private TTSSendRequest ttsSendRequest;

  /**
   * 需要返回给公开API的response
   */
  private SmsSendResponse response = new SmsSendResponse();

  /**
   * 短信通道提供商
   */
  private SmsProvider provider;

}
