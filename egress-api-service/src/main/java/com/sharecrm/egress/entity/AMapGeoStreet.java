package com.sharecrm.egress.entity;

import com.google.common.base.MoreObjects;
import lombok.Data;

import static com.sharecrm.egress.api.AMapApi.convertEmptyToNull;

@Data
public class AMapGeoStreet {
  private String street;
  private String number;
  private String location;
  private String direction;

  /**
   * 到请求坐标的距离，单位：米
   */
  private Double distance;

  @Override
  public String toString() {
    return MoreObjects
      .toStringHelper(this)
      .omitNullValues()
      .add("street", convertEmptyToNull(street))
      .add("number", number)
      .add("location", location)
      .add("direction", convertEmptyToNull(direction))
      .add("distance", distance)
      .toString();
  }
}
