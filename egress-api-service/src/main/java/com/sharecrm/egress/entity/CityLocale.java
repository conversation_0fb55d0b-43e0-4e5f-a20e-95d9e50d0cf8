package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "城市信息的国际化描述")
public class CityLocale {
  @Schema(title = "省份", example = "Beijing")
  private String province;
  @Schema(title = "城市", example = "Beijing")
  private String city;
  @Schema(title = "运营商", example = "China Union")
  private String carrier;
  @Schema(title = "运营商，等同于carrier", example = "联通")
  private String operator;
  @Schema(title = "语言", example = "en")
  @JsonProperty("lang")
  private String language;
}
