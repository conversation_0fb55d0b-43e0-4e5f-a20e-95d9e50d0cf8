package com.sharecrm.egress.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString(doNotUseGetters = true)
@Schema(title = "地理位置信息")
public class GeoAddress {

  /**
   * 国家，比如中国
   */
  @Schema(title = "国家", example = "中国")
  private String country;
  /**
   * 省，比如北京市
   */
  @Schema(title = "省份", example = "河南省")
  private String province;
  /**
   * 市，直辖市和省份一样，其他比如郑州市
   */
  @Schema(title = "城市", example = "周口市")
  private String city;

  /**
   * 城市编码
   */
  @Schema(title = "城市编码", example = "010")
  private String cityCode;

  /**
   * 区，比如朝阳区
   */
  @Schema(title = "区县", example = "郸城县")
  private String district;
  /**
   * 街区
   */
  @Schema(title = "街道", example = "广安门街道")
  private String street;
  /**
   * 编号，比如 甲63号
   */
  @Schema(title = "门牌编码", example = "甲63号")
  private String streetNumber;
  /**
   * 乡镇
   */
  @Schema(title = "乡镇", example = "燕园街道")
  private String town;

  /**
   * 乡镇编码
   */
  @Schema(title = "乡镇编码", example = "110101001000")
  private String townCode;

  /**
   * 城市编码
   */
  @Schema(title = "城市编码", example = "110108", description = "用这个查询国家省市区县规划")
  private String adcode;
  /**
   * 相对结构化的地址
   */
  @Schema(title = "结构化的地址", example = "北京市海淀区知春路甲63号卫星大厦", description = "通常可以用来做邮寄地址")
  private String address;

  /**
   * 第一个POI信息点，一般是大厦
   */
  @Schema(title = "第一个POI信息点，一般是大厦", example = "卫星大厦", description = "第一个POI信息点的名字")
  private String featureName;

  /**
   * 经纬度，格式为“lat(纬度),lng(经度)”，注意这个名字在ES中会用，不要随便改名字
   */
  @Schema(title = "经纬度编码", example = "39.990464,116.481488", description = "格式为“lat(维度),lng(经度)”")
  private String geocode;

  /**
   * 周边地点（POI/AOI）列表，数组中每个子项为一个POI/AOI对象，可能无数据。
   * <p>
   * POI即地点，如一个便利店，往往因其面积较小，其位置一般仅会标为为一个点，而学校、小区等往往面积较大，通常会有一定的地理范围，
   * 即所谓AOI，如果所请求的经纬度在AOI内，其距离会为0，且方位描述为“内”，
   * 如果是一个面积较小的地点，或不在AOI内，距离会>0，方位描述会为具体方位词，如“东”
   */
  @Schema(title = "周边地点（POI/AOI）列表", example = "39.990464,116.481488", description = "周边地点（POI/AOI）列表，注意不同的服务商能返回的能力不同，里面的字段可能无值")
  private List<MapPoi> pois = List.of();

  /**
   * 哪个服务商，存储的是服务商类型，比如高德、百度、腾讯、谷歌等
   */
  @Schema(title = "服务商", example = "amap", description = "amap: 高德, baidu: 百度, tencent: 腾讯, google: 谷歌")
  private String provider;

  /**
   * 返回的语言类型
   */
  @Schema(title = "多语语言类型", example = "zh-CN", description = "多语语言类型，目前只支持中文和英文")
  private String language;

  public GeoAddress(String address) {
    this.address = address;
  }

  public GeoAddress(String address, String geocode) {
    this.address = address;
    this.geocode = geocode;
  }
}
