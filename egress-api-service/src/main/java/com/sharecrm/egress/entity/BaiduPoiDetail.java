package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BaiduPoiDetail {
  /**
   * 距离中心点的距离，单位为米
   */
  private Integer distance;

  /**
   * 所属分类，如hotel、cater。
   */
  private String type;
  /**
   * 标签信息
   */
  private String tag;

  /**
   * POI的别名，如“百度大厦”等。
   */
  private String alias;

  /**
   * POI的详情页
   */
  @JsonProperty("detail_url")
  private String detailUrl;

  /**
   * POI的图片信息，高级付费功能
   */
  private List<String> photos;
}
