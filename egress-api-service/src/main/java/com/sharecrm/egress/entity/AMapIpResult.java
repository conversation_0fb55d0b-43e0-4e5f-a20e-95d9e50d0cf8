package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 高德地图IP定位API返回结果
 *
 * <AUTHOR>
 * @see <a href="https://lbs.amap.com/api/webservice/guide/api/ipconfig/">API文档</a>
 */
@Data
public class AMapIpResult {
  private int status;
  private String info;
  @JsonProperty("infocode")
  private int infoCode;
  private String province;
  private String city;
  private String adcode;
  private String rectangle;
}
