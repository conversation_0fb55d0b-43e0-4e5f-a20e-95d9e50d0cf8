package com.sharecrm.egress.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * OCR 名片识别结果
 */
@Data
public class BusinessCardResponse {

  /**
   * 姓名
   */
  private String name;

  /**
   * 邮箱
   */
  private String email;

  /**
   * 公司
   */
  private String company;

  /**
   * 职位
   */
  private String title;

  /**
   * 部门
   */
  private String department;

  /**
   * 地址
   */
  private String address;

  /**
   * 手机
   */
  private String mobile;

  /**
   * 电话
   */
  private String telephone;
  /**
   * 传真
   */
  private String fax;
  /**
   * QQ
   */
  private String qq;
  /**
   * 微信
   */
  private String wechat;

  /**
   * 网址
   */
  private String url;

  /**
   * 名片识别提供商返回的原始值，可能包含一些无法归类或无法识别的字段
   */
  private Map<String, String> extended = new HashMap<>();

}
