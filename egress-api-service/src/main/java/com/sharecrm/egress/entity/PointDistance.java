package com.sharecrm.egress.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema(title = "距离信息描述")
public class PointDistance {
  /**
   * 起点到终点的距离，单位为米；若没有计算结果，值为0
   */
  @Schema(title = "起点到终点的距离", example = "13456", description = "单位为米；若没有计算结果，值为0；可能会参考路况信息")
  private int distance;
  /**
   * 表示从起点到终点的结合路况的时间，单位为秒；若没有计算结果，值为0
   */
  @Schema(title = "起点到终点的预估花费的时间", example = "1800", description = "单位为秒；若没有计算结果，值为0；可能会参考路况信息")
  private int duration;

  /**
   * 构造函数
   *
   * @param distance 距离
   * @param duration 时间
   */
  public PointDistance(int distance, int duration) {
    this.distance = distance;
    this.duration = duration;
  }
}
