package com.sharecrm.egress.entity;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import lombok.Data;

/**
 * 短信发送请求体，额外传递一些日志需要的参数
 */
@Data
public class SmsRequestWrapper<T> {

  private T request;

  /**
   * 批次ID，由我们自己生成，每次N条是同一个ID，可根据此ID按批次回查短信状态
   */
  private String batchMsgId;

  /**
   * 此次发送记录的消息ID，即发送流水号，可根据此ID查询此条短信的回执状态
   */
  private String msgId = NanoIdUtils.randomNanoId();

  /**
   * 是否国际/港澳台短信,true为国际短信，false为国内短信
   */
  private boolean international;

  public SmsRequestWrapper(T request) {
    this.request = request;
    this.batchMsgId = NanoIdUtils.randomNanoId();
  }

  public SmsRequestWrapper(T request, String batchMsgId) {
    this.request = request;
    this.batchMsgId = batchMsgId;
  }

  public SmsRequestWrapper(T request, String batchMsgId, String msgId) {
    this.request = request;
    this.batchMsgId = batchMsgId;
    this.msgId = msgId;
  }

}
