package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.base.MoreObjects;
import com.sharecrm.egress.api.CoordinateTransformer;
import com.sharecrm.egress.config.JsonDoubleSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode
@Schema(title = "地球经纬度编码")
public class LocationPoint {
  /**
   * 经度
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  @JsonProperty("lng")
  @Schema(title = "经度", example = "116.481488", description = "[-180,180]，正数为东半球，负数为西半球")
  private double longitude;
  /**
   * 纬度
   */
  @JsonSerialize(using = JsonDoubleSerializer.class)
  @JsonProperty("lat")
  @Schema(title = "纬度", example = "39.990464", description = "[-90,90]，正数为北半球，负数为南半球")
  private double latitude;

  public LocationPoint(double longitude, double latitude) {
    this.longitude = longitude;
    this.latitude = latitude;
  }

  @JsonIgnore
  public String toLongitudeLatitude() {
    return String.format("%.6f,%.6f", longitude, latitude);
  }

  @JsonIgnore
  public String toLatitudeLongitude() {
    return String.format("%.6f,%.6f", latitude, longitude);
  }

  public static LocationPoint parseLongitudeLatitude(String loc) {
    String[] lngLat = loc.split(",", 2);
    return new LocationPoint(Double.parseDouble(lngLat[0]), Double.parseDouble(lngLat[1]));
  }

  public static LocationPoint parseLatitudeLongitude(String loc) {
    String[] latLng = loc.split(",", 2);
    return new LocationPoint(Double.parseDouble(latLng[1]), Double.parseDouble(latLng[0]));
  }

  public GoogleGeoCode toGoogleGeoCode() {
    double[] values = CoordinateTransformer.gcj02ToWgs84(longitude, latitude);
    return new GoogleGeoCode(values);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this).add("longitude", String.format("%.6f", longitude)).add("latitude", String.format("%.6f", latitude)).toString();
  }
}
