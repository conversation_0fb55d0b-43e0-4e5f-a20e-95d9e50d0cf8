package com.sharecrm.egress.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "附近POI信息")
public class PoiResponse {
  @Schema(title = "总数", example = "1234", description = "总数，谷歌搜索不返回总数，只返回下一页的token")
  private int count;
  @Schema(title = "下一页", example = "kSytTzl961QdwAjgDXH5macbvqFpeGnC", description = "谷歌地图下一页的token, 用于翻页，需要等待几秒才生效")
  private String nextPageToken;
  @Schema(title = "POI信息", description = "POI信息")
  private List<MapPoi> results;
}
