package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import lombok.Data;

import static com.sharecrm.egress.api.AMapApi.convertEmptyToNull;

@Data
public class AMapGeoAddress {
  /**
   * 国家
   */
  private String country;
  /**
   * 省份
   */
  private String province;
  /**
   * 城市
   */
  private String city;
  
  /**
   * 城市编码
   */
  @JsonProperty("citycode")
  private String cityCode;
  
  /**
   * 区县
   */
  private String district;
  /**
   * 区域编码
   */
  private String adcode;
  /**
   * 街道
   */
  @JsonProperty("streetNumber")
  private AMapGeoStreet street;
  /**
   * 乡镇
   */
  @JsonProperty("township")
  private String town;
  /**
   * 乡镇编码
   */
  @JsonProperty("towncode")
  private String townCode;

  @Override
  public String toString() {
    return MoreObjects
      .toStringHelper(this)
      .omitNullValues()
      .add("country", convertEmptyToNull(country))
      .add("province", convertEmptyToNull(province))
      .add("city", convertEmptyToNull(city))
      .add("district", convertEmptyToNull(district))
      .add("adcode", convertEmptyToNull(adcode))
      .add("street", street)
      .add("town", convertEmptyToNull(town))
      .add("townCode", convertEmptyToNull(townCode))
      .toString();
  }
}
