package com.sharecrm.egress.entity;

import lombok.Getter;

@Getter
public enum AudioFormat {
    WAV("wav"),
    MP3("mp3"),
    AAC("aac"),
    OGG_OPUS("ogg-opus"),
    M4A("m4a"),
    AMR("amr"),
    SILK("silk"),
    SPEEX("speex"),
    PCM("pcm"),
    OTHER("other");

    private final String format;

    AudioFormat(String format) {
        this.format = format;
    }
    
    public static AudioFormat of(String format) {
        for (AudioFormat audioFormat : values()) {
            if (audioFormat.getFormat().equalsIgnoreCase(format)) {
                return audioFormat;
            }
        }
        return OTHER;
    }
}
