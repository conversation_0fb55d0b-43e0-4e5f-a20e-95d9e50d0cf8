package com.sharecrm.egress.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.function.ToIntFunction;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString(doNotUseGetters = true)
@Schema(title = "手机号位置信息")
public class MobileLocation {
  @Schema(title = "手机号码", example = "13800018000", description = "手机号码前7位用于识别位置信息")
  private String mobile;
  @Schema(title = "省份", example = "北京")
  private String province;
  @Schema(title = "城市", example = "北京")
  private String city;
  @Schema(title = "运营商", example = "联通")
  private String carrier;
  @Schema(title = "运营商，等同于carrier", example = "联通")
  private String operator;
  @Schema(title = "区号前缀", example = "010")
  private String code;
  @Schema(title = "城市的国际化描述")
  private CityLocale i18n;

  public MobileLocation(String mobile) {
    this.mobile = mobile;
  }

  public LocationDTO toLocationDTO(ToIntFunction<String> mapping) {
    return LocationDTO
      .builder()
      .province(mapping.applyAsInt(province))
      .city(mapping.applyAsInt(city))
      .carrier(mapping.applyAsInt(carrier))
      .code(mapping.applyAsInt(code))
      .build();
  }

}
