package com.sharecrm.egress.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class GoogleAddressComponent {
  /**
   * 地址组成部分的长文本名称。例如，美国的“俄亥俄州”在长名称中为“俄亥俄州”，在短名称中为“OH”。
   */
  @JsonProperty("long_name")
  private String longName;
  /**
   * 地址组成部分的缩写文本名称（如果有）。例如，美国的“俄亥俄州”在长名称中为“俄亥俄州”，在短名称中为“OH”。
   */
  @JsonProperty("short_name")
  private String shortName;
  /**
   * 此数组包含零个或多个标记，用于标识结果中所返回地图项的类型。例如，“芝加哥”的地理编码会返回“市行政区”，表示“芝加哥”是一个城市；同时返回“政治”，表示它是一个政治实体。
   *
   * <pre>
   * 地理编码器都支持并返回以下类型：
   * street_address 表示精确的街道地址。
   * route 表示已命名的路线（例如“US 101”）。
   * intersection 表示主要交叉路口，通常是两条主要道路的交叉路口。
   * political 表示政治实体。这种类型通常表示某些行政管理区的多边形区域。
   * country 表示国家政治实体，通常列在地理编码器所返回结果的最前面。
   * administrative_area_level_1
   * 表示国家/地区级别以下的一级行政实体。在美国，这类行政级别是指州。并不是所有国家都设有这类行政级别。在大多数情况下，administrative_area_level_1
   * 简称可高度匹配 ISO 3166-2 行政区划以及其他广为传播的列表；不过，我们无法对此做出保证，因为我们的地理编码结果基于各种信号和位置数据。
   * administrative_area_level_2
   * 表示国家/地区级别以下的二级行政实体。在美国，这类行政级别是指县。并不是所有国家都设有这类行政级别。
   * administrative_area_level_3
   * 表示国家/地区级别以下的三级行政实体。此类型表示较小的行政区划单位。并不是所有国家都设有这类行政级别。
   * administrative_area_level_4
   * 表示国家/地区级别以下的四级行政实体。此类型表示较小的行政区划单位。并不是所有国家都设有这类行政级别。
   * administrative_area_level_5
   * 表示国家/地区级别以下的五级行政实体。此类型表示较小的行政区划单位。并不是所有国家都设有这类行政级别。
   * administrative_area_level_6
   * 表示国家/地区级别以下的六级行政实体。此类型表示较小的行政区划单位。并不是所有国家都设有这类行政级别。
   * administrative_area_level_7
   * 表示国家/地区级别以下的七级行政实体。此类型表示较小的行政区划单位。并不是所有国家都设有这类行政级别。
   * colloquial_area 表示实体的常用替代名称。
   * locality 表示合并的城市或城镇政治实体。
   * sublocality 表示市行政区以下的一级行政实体。某些位置可能会收到以下任一类型：从 sublocality_level_1 到
   * sublocality_level_5。每个子级市行政区级别都是一个行政实体。数字越大表示地理区域越小。
   * neighborhood 表示已命名的街区。
   * premise 表示已命名的位置，通常是具有常见名称的一栋或一群建筑物。
   * subpremise 表示已命名位置以下的一级实体，通常是同名建筑群中的单个建筑物。
   * plus_code 表示经过编码的位置引用，衍生自纬度和经度。Plus Codes
   * 可用于取代位于虚假地点的街道地址，例如无编号的建筑物或无名街道。如需了解详情，请参阅 <a href="https://plus.codes">plus-code</a>。
   * postal_code 表示邮政编码，用于国家/地区内的地址邮寄。
   * natural_feature 表示某个明显的自然地貌。
   * airport 表示机场。
   * park 表示已命名的公园。
   * point_of_interest
   * 表示已命名的地图注点。通常情况下，这些“地图注点”是当地的著名实体，无法轻易归入其他类别，例如“帝国大厦”或“埃菲尔铁塔”。
   * </pre>
   * <p>
   * 除了上述类型之外，地址组成部分还可能包括此处列出的类型。此列表并非详尽无遗，并且随时可能会发生变化。
   *
   * <pre>
   * floor 表示某个建筑物地址的楼层。
   * establishment 通常表示某个尚未归类的地点。
   * landmark 表示附近的地点，可用作辅助导航的参考。
   * point_of_interest 表示已命名的地图注点。
   * parking 表示停车场或停车楼。
   * post_box 表示特定的邮箱。
   * postal_town 表示地理区域分组（例如 locality 和 sublocality），在某些国家/地区用于邮寄地址。
   * room 表示某个建筑物地址的房间。
   * street_number 表示精确的门牌号。
   * bus_station、train_station 和 transit_station 分别表示公交车、火车或公共交通车站的位置。
   * </pre>
   */
  private List<String> types;
}
