spring.application.name=egress-api-service
spring.config.import=cms:${spring.application.name}
spring.webflux.base-path=/egress-api-service
spring.devtools.restart.exclude=autoconf/**
fxiaoke.starter.config.cms.key=9joTB40mDLYKSH21
fxiaoke.starter.config.redis.name=fs-egress-api-service-redis
# use json serializer for ReactiveRedisTemplate
fxiaoke.starter.config.redis.reactive.template.enabled=true
# fxiaoke sentinel config, current is for notify push consumer
fxiaoke.common.sentinel.flow.rule.enabled=false
fxiaoke.common.sentinel.flow.rule.file=fs-sentinel-egress-api-service
fxiaoke.common.sentinel.authority.rule.enabled=false
fxiaoke.common.sentinel.system.rule.enabled=false
fxiaoke.common.sentinel.degrade.rule.enabled=false
fxiaoke.common.sentinel.param.flow.rule.enabled=false
# Spring ThreadPoolExecutor is auto registry to micrometer and prometheus
spring.task.execution.pool.core-size=4
spring.task.execution.pool.max-size=16
spring.task.execution.pool.queue-capacity=10000
spring.task.execution.shutdown.await-termination=true
spring.task.execution.shutdown.await-termination-period=3s
# Spring ThreadPoolTaskScheduler
spring.task.scheduling.pool.size=4
# global json config
spring.jackson.time-zone=GMT+8
spring.jackson.locale=zh_CN
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.write-empty-json-arrays=false
# bad idea but have to, some providers change their types every day
spring.jackson.deserialization.fail-on-invalid-subtype=false
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.deserialization.accept-empty-array-as-null-object=true
spring.jackson.deserialization.accept-empty-string-as-null-object=true
spring.jackson.parser.allow-unquoted-field-names=true
spring.jackson.parser.allow-single-quotes=true
spring.webflux.format.date=${spring.jackson.date-format}
spring.webflux.format.date-time=${spring.jackson.date-format}
# for ocr service, need more memory size
spring.codec.max-in-memory-size=16MB
spring.webflux.multipart.max-in-memory-size=16MB
server.netty.max-initial-line-length=16KB
# disable api-doc by default
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
# spring virtual threads, disabled default
#spring.threads.virtual.enabled=true
#spring.task.scheduling.simple.concurrency-limit=10000
#spring.task.execution.simple.concurrency-limit=10000
# spring boot actuator disable health check
management.health.mail.enabled=false
management.health.elasticsearch.enabled=false
management.health.redis.enabled=false
# GEO elasticsearch
fxiaoke.starter.config.data.elasticsearch.name=fs-egress-es-geo
spring.elasticsearch.socket-timeout=10s
