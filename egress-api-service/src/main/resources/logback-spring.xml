<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" debug="false">
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
  <!-- 敏感词过滤，暂不启用，我们有短信业务希望看手机号 -->
  <!--  <conversionRule conversionWord="msg" converterClass="com.fxiaoke.metrics.logback.MaskMessageConverter"/>-->

  <property name="APP_LOG" value="app"/>
  <property name="ERROR_LOG" value="error"/>
  <!-- fluent bit 按照 tomcat-access 采集access日志 -->
  <property name="ACCESS_LOG" value="tomcat-access"/>
  <property name="REMOTE_LOG" value="remote"/>


  <if condition='isDefined("CATALINA_HOME")'>
    <then>
      <property name="LOG_DIR" value="${CATALINA_HOME}/logs"/>
      <include resource="fs-sentinel-logging.xml"/>
    </then>
    <else>
      <property name="LOG_DIR" value="./logs"/>
      <include resource="fs-sentinel-logging-console.xml"/>
    </else>
  </if>

  <!-- 按照日期和大小轮转日志 -->
  <appender name="RollingErrorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/${ERROR_LOG}.log</file>
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <Pattern>
        %d{HH:mm:ss.SSS} %-5level [%thread] %logger{16} %X{traceId} %X{userId} %msg%n
      </Pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <!-- 日志文件输出路径及文件名格式 -->
      <fileNamePattern>${LOG_DIR}/archived/${ERROR_LOG}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <!-- 每个日志文件的最大大小 -->
      <maxFileSize>100MB</maxFileSize>
      <!-- 可保留的最大日志文件数量 -->
      <maxHistory>30</maxHistory>
      <!-- 总日志文件大小 -->
      <totalSizeCap>3GB</totalSizeCap>
    </rollingPolicy>
  </appender>

  <!-- 按照日期和大小轮转日志 -->
  <appender name="RollingAppFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/${APP_LOG}.log</file>
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <Pattern>
        %d{HH:mm:ss.SSS} %-5level [%thread] %logger{16} %X{traceId} %X{userId} %msg%n
      </Pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <!-- 日志文件输出路径及文件名格式 -->
      <fileNamePattern>${LOG_DIR}/archived/${APP_LOG}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <!-- 每个日志文件的最大大小 -->
      <maxFileSize>200MB</maxFileSize>
      <!-- 可保留的最大日志文件数量 -->
      <maxHistory>10</maxHistory>
      <!-- 总日志文件大小 -->
      <totalSizeCap>2GB</totalSizeCap>
    </rollingPolicy>
  </appender>

  <!-- 记录web访问日志 -->
  <appender name="AccessLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/${ACCESS_LOG}.log</file>
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <Pattern>%msg%n</Pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <!-- 日志文件输出路径及文件名格式 -->
      <fileNamePattern>${LOG_DIR}/archived/${ACCESS_LOG}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <!-- 每个日志文件的最大大小 -->
      <maxFileSize>20MB</maxFileSize>
      <!-- 可保留的最大日志文件数量 -->
      <maxHistory>10</maxHistory>
      <!-- 总日志文件大小 -->
      <totalSizeCap>200MB</totalSizeCap>
    </rollingPolicy>
  </appender>

  <!-- 记录访问第三方的日志 -->
  <appender name="RemoteLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/${REMOTE_LOG}.log</file>
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <Pattern>%d{HH:mm:ss.SSS} %X{traceId} %X{userId} %msg%n</Pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <!-- 日志文件输出路径及文件名格式 -->
      <fileNamePattern>${LOG_DIR}/archived/${REMOTE_LOG}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
      <!-- 每个日志文件的最大大小 -->
      <maxFileSize>20MB</maxFileSize>
      <!-- 可保留的最大日志文件数量 -->
      <maxHistory>10</maxHistory>
      <!-- 总日志文件大小 -->
      <totalSizeCap>200MB</totalSizeCap>
    </rollingPolicy>
  </appender>

  <!-- 异步日志 -->
  <appender name="AsyncErrorAppender" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 剩余容量少于5%的时候丢弃日志级别为TRACE,DEBUG与INFO 的日志，仅仅只保留WARN与ERROR级别的日志 -->
    <discardingThreshold>5</discardingThreshold>
    <!-- 队列大小，默认是256 -->
    <queueSize>1024</queueSize>
    <!-- 默认为false，在队列满的时候会阻塞而不是丢弃信息。设置为true，不会阻塞你的应用而会将消息丢弃志-->
    <neverBlock>true</neverBlock>
    <!-- 官方建议不要开启，获取调用者的数据相对来说比较昂贵。默认情况下，只有像线程名或者MDC这种"便宜"的数据会被复制。设置为 true 时，会包含调用者的信息 -->
    <includeCallerData>false</includeCallerData>
    <appender-ref ref="RollingErrorFile"/>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <!-- 异步日志 -->
  <appender name="AsyncAppAppender" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 剩余容量少于5%的时候丢弃日志级别为TRACE,DEBUG与INFO 的日志，仅仅只保留WARN与ERROR级别的日志 -->
    <discardingThreshold>5</discardingThreshold>
    <!-- 队列大小，默认是256 -->
    <queueSize>2048</queueSize>
    <!-- 默认为false，在队列满的时候会阻塞而不是丢弃信息。设置为true，不会阻塞你的应用而会将消息丢弃志-->
    <neverBlock>true</neverBlock>
    <!-- 官方建议不要开启，获取调用者的数据相对来说比较昂贵。默认情况下，只有像线程名或者MDC这种"便宜"的数据会被复制。设置为 true 时，会包含调用者的信息 -->
    <includeCallerData>false</includeCallerData>
    <appender-ref ref="RollingAppFile"/>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>DENY</onMatch>
      <onMismatch>ACCEPT</onMismatch>
    </filter>
  </appender>

  <!-- 异步日志 -->
  <appender name="AsyncAccessLogAppender" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 剩余容量少于0%的时候丢弃日志级别为TRACE,DEBUG与INFO 的日志，仅仅只保留WARN与ERROR级别的日志 -->
    <discardingThreshold>0</discardingThreshold>
    <!-- 队列大小，默认是256 -->
    <queueSize>512</queueSize>
    <!-- 默认为false，在队列满的时候会阻塞而不是丢弃信息。设置为true，不会阻塞你的应用而会将消息丢弃志-->
    <neverBlock>true</neverBlock>
    <!-- 官方建议不要开启，获取调用者的数据相对来说比较昂贵。默认情况下，只有像线程名或者MDC这种"便宜"的数据会被复制。设置为 true 时，会包含调用者的信息 -->
    <includeCallerData>false</includeCallerData>
    <appender-ref ref="AccessLogFile"/>
  </appender>

  <!-- 异步日志 -->
  <appender name="AsyncRemoteLogAppender" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 剩余容量少于0%的时候丢弃日志级别为TRACE,DEBUG与INFO 的日志，仅仅只保留WARN与ERROR级别的日志 -->
    <discardingThreshold>0</discardingThreshold>
    <!-- 队列大小，默认是256 -->
    <queueSize>1024</queueSize>
    <!-- 默认为false，在队列满的时候会阻塞而不是丢弃信息。设置为true，不会阻塞你的应用而会将消息丢弃志-->
    <neverBlock>true</neverBlock>
    <!-- 官方建议不要开启，获取调用者的数据相对来说比较昂贵。默认情况下，只有像线程名或者MDC这种"便宜"的数据会被复制。设置为 true 时，会包含调用者的信息 -->
    <includeCallerData>false</includeCallerData>
    <appender-ref ref="RemoteLogFile"/>
  </appender>

  <!-- access log使用 debug 级别，避免被metrics组件默认收集到app log里收集两份  -->
  <logger name="access-log" level="DEBUG" additivity="false">
    <appender-ref ref="AsyncAccessLogAppender"/>
  </logger>

  <!--记录WebClient请求日志-->
  <logger name="reactor.netty.http.client.HttpClient" level="DEBUG" additivity="false">
    <appender-ref ref="AsyncRemoteLogAppender"/>
  </logger>

  <!-- 应用停止时清理队列 -->
  <shutdownHook class="ch.qos.logback.core.hook.DefaultShutdownHook">
    <delay>1000</delay>
  </shutdownHook>

  <if condition='!isDefined("CATALINA_HOME")'>
    <then>
      <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="AsyncErrorAppender"/>
        <appender-ref ref="AsyncAppAppender"/>
      </root>
    </then>
    <else>
      <root level="INFO">
        <appender-ref ref="AsyncErrorAppender"/>
        <appender-ref ref="AsyncAppAppender"/>
      </root>
    </else>
  </if>
</configuration>