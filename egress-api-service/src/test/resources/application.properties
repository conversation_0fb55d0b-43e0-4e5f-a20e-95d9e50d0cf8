spring.application.name=egress-api-service
spring.webflux.base-path=/
spring.config.import=optional:cms:${spring.application.name}
fxiaoke.starter.config.cms.key=9joTB40mDLYKSH21
# disable banner when run test
spring.main.banner-mode=off
# use json serializer ReactiveRedisTemplate
fxiaoke.starter.config.redis.reactive.template.enabled=true
spring.cache.cache-names=i18nCity
spring.cache.caffeine.spec=maximumSize=1000,expireAfterAccess=5m,recordStats
sharecrm.api.sms.enabled=false
sharecrm.api.notify.push.enabled=false
sharecrm.api.short.url.enabled=false
# jackson config
spring.jackson.locale=zh_CN
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.write-empty-json-arrays=false
# bad idea but have to, some providers change their types everyday
spring.jackson.deserialization.fail-on-invalid-subtype=false
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.deserialization.accept-empty-array-as-null-object=true
spring.jackson.deserialization.accept-empty-string-as-null-object=true
spring.jackson.parser.allow-unquoted-field-names=true
spring.jackson.parser.allow-single-quotes=true
# for ocr service, need more memory size
spring.codec.max-in-memory-size=16MB
spring.webflux.multipart.max-in-memory-size=16MB
server.netty.max-initial-line-length=16KB
# config-core local first
config.mode=localNoUpdate
logging.level.root=error