package com.sharecrm.egress.dao;

import com.sharecrm.egress.entity.ShortUrlHistory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class ShortUrlHistoryMapperTest {

  @Mock
  private ShortUrlHistoryMapper shortUrlHistoryMapper;

  private ShortUrlHistory testShortUrlHistory;

  @BeforeEach
  void setUp() {
    testShortUrlHistory = new ShortUrlHistory();
    testShortUrlHistory.setId("test-history-id-123");
    testShortUrlHistory.setCode("abc123");
    testShortUrlHistory.setUrl("https://example.com/test");
    testShortUrlHistory.setCreateTime(System.currentTimeMillis());
    testShortUrlHistory.setTenantId("test-tenant");
    testShortUrlHistory.setTimeout(3600L); // 1 hour
    testShortUrlHistory.setDeleted(1L); // It's in history, so it's deleted
  }

  @Test
  void testInsert() {
    // Given
    when(shortUrlHistoryMapper.insert(any(ShortUrlHistory.class))).thenReturn(1);

    // When
    int result = shortUrlHistoryMapper.insert(testShortUrlHistory);

    // Then
    assertEquals(1, result);
    verify(shortUrlHistoryMapper).insert(testShortUrlHistory);
  }
}