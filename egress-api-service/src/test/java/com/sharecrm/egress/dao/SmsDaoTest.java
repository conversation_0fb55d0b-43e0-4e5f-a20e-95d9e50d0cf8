package com.sharecrm.egress.dao;

import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.service.LockingExecutor;
import org.junit.jupiter.api.Test;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = SmsDao.class,
  properties = {"sharecrm.api.sms.enabled=true"})
class SmsDaoTest {

  @MockitoBean(name = "smsMongoDatastore")
  private Datastore datastore;

  @MockitoBean
  private Query<SmsTemplateEntity> templateQuery;

  @MockitoBean
  private Query<SmsMongoEntity> entityQuery;

  @MockitoBean
  private LockingExecutor lockingExecutor;

  @Autowired
  private SmsDao smsDao;

  @Test
  void save() {
    when(datastore.save(any(SmsMongoEntity.class))).thenReturn(null);
    assertDoesNotThrow(() -> smsDao.save(new SmsMongoEntity()));
  }

  @Test
  void queryTemplate() {
    when(datastore.createQuery(SmsTemplateEntity.class)).thenReturn(templateQuery);
    when(templateQuery.filter(any(), any())).thenReturn(templateQuery);
    when(templateQuery.asList()).thenReturn(java.util.Collections.emptyList());
    assertDoesNotThrow(() -> smsDao.queryTemplate("test"));
  }

  @Test
  @SuppressWarnings("unchecked")
  void updateMonternetStatus() {
    // Mock the query chain
    when(datastore.createQuery(SmsMongoEntity.class)).thenReturn(entityQuery);
    when(entityQuery.field(any())).thenReturn(mock(org.mongodb.morphia.query.FieldEnd.class));

    // Mock the update operations
    UpdateOperations<SmsMongoEntity> ops = mock(UpdateOperations.class);
    when(datastore.createUpdateOperations(SmsMongoEntity.class)).thenReturn(ops);
    when(ops.set(any(), any())).thenReturn(ops);

    // Mock the findAndModify
    when(datastore.findAndModify(any(), any(), anyBoolean(), anyBoolean())).thenReturn(null);

    assertDoesNotThrow(() -> smsDao.updateMonternetStatus("test-provider", "test-serial", 0, "Success"));
  }

  @Test
  @SuppressWarnings("unchecked")
  void updateSingleSmsStatus() {
    // Mock the query chain
    when(datastore.createQuery(SmsMongoEntity.class)).thenReturn(entityQuery);
    when(entityQuery.field(any())).thenReturn(mock(org.mongodb.morphia.query.FieldEnd.class));

    // Mock the update operations
    UpdateOperations<SmsMongoEntity> ops = mock(UpdateOperations.class);
    when(datastore.createUpdateOperations(SmsMongoEntity.class)).thenReturn(ops);
    when(ops.set(any(), any())).thenReturn(ops);

    // Mock the findAndModify
    when(datastore.findAndModify(any(), any(), anyBoolean(), anyBoolean())).thenReturn(null);

    assertDoesNotThrow(() -> smsDao.updateSingleSmsStatus(
        "test-provider", "test-serial", "**********",
        true, new Date(), "0", "Success"));
  }

  @Test
  @SuppressWarnings("unchecked")
  void updateSingleSmsStatusWithException() {
    // Mock the query chain
    when(datastore.createQuery(SmsMongoEntity.class)).thenReturn(entityQuery);
    when(entityQuery.field(any())).thenReturn(mock(org.mongodb.morphia.query.FieldEnd.class));

    // Mock the update operations
    UpdateOperations<SmsMongoEntity> ops = mock(UpdateOperations.class);
    when(datastore.createUpdateOperations(SmsMongoEntity.class)).thenReturn(ops);
    when(ops.set(any(), any())).thenReturn(ops);

    // Mock the findAndModify to throw exception
    when(datastore.findAndModify(any(), any(), anyBoolean(), anyBoolean()))
        .thenThrow(new RuntimeException("Test exception"));

    assertDoesNotThrow(() -> smsDao.updateSingleSmsStatus(
        "test-provider", "test-serial", "**********",
        true, new Date(), "0", "Success"));
  }

}