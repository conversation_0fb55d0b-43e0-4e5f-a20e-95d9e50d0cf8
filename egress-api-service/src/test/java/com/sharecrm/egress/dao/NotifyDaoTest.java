package com.sharecrm.egress.dao;

import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotifyDaoTest {

  @Mock
  private NotifyProperties notifyProperties;

  private NotifyDao notifyDao;

  @BeforeEach
  void setUp() {
    notifyDao = new NotifyDao(notifyProperties);
  }

  @Test
  void testSaveWhenRecordEnabled() {
    // Given
    PushRecord pushRecord = PushRecord.builder()
        .logType("egress-notify-push")
        .stamp(System.currentTimeMillis())
        .app("testApp")
        .serverIp("127.0.0.1")
        .ea("testEa")
        .title("Test Notification")
        .summary("This is a test notification")
        .status("SUCCESS")
        .build();

    when(notifyProperties.isRecordEnabled()).thenReturn(true);

    // When - using static mock for BizLogClient
    try (MockedStatic<BizLogClient> bizLogClientMock = Mockito.mockStatic(BizLogClient.class);
        MockedStatic<ProtostuffUtil> protostuffUtilMock = Mockito.mockStatic(ProtostuffUtil.class)) {

      // Mock the serializer
      protostuffUtilMock.when(() -> ProtostuffUtil.serializer(any(PushRecord.class)))
          .thenReturn(new byte[] { 1, 2, 3 });

      notifyDao.save(pushRecord);

      // Then
      bizLogClientMock.verify(() -> BizLogClient.send(eq("generic-biz-log"), any(byte[].class)));
      protostuffUtilMock.verify(() -> ProtostuffUtil.serializer(pushRecord));
    }
  }

  @Test
  void testSaveWhenRecordDisabled() {
    // Given
    PushRecord pushRecord = PushRecord.builder()
        .logType("egress-notify-push")
        .stamp(System.currentTimeMillis())
        .app("testApp")
        .build();

    when(notifyProperties.isRecordEnabled()).thenReturn(false);

    // When - using static mock for BizLogClient
    try (MockedStatic<BizLogClient> bizLogClientMock = Mockito.mockStatic(BizLogClient.class);
        MockedStatic<ProtostuffUtil> protostuffUtilMock = Mockito.mockStatic(ProtostuffUtil.class)) {

      notifyDao.save(pushRecord);

      // Then - verify that neither BizLogClient nor ProtostuffUtil were called
      bizLogClientMock.verifyNoInteractions();
      protostuffUtilMock.verifyNoInteractions();
    }
  }
}