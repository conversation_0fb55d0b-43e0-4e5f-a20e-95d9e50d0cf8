package com.sharecrm.egress.dao;

import com.sharecrm.egress.entity.ShortUrl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ShortUrlMapperTest {

  @Mock
  private ShortUrlMapper shortUrlMapper;

  private ShortUrl testShortUrl;

  @BeforeEach
  void setUp() {
    testShortUrl = new ShortUrl();
    testShortUrl.setId("test-id-123");
    testShortUrl.setCode("abc123");
    testShortUrl.setUrl("https://example.com/test");
    testShortUrl.setCreateTime(System.currentTimeMillis());
    testShortUrl.setTenantId("test-tenant");
    testShortUrl.setTimeout(3600L); // 1 hour
    testShortUrl.setDeleted(0L);
  }

  @Test
  void testSelectByCode_whenShortUrlExists() {
    // Given
    String code = "abc123";
    when(shortUrlMapper.selectByCode(code)).thenReturn(Optional.of(testShortUrl));

    // When
    Optional<ShortUrl> result = shortUrlMapper.selectByCode(code);

    // Then
    assertTrue(result.isPresent());
    assertEquals(testShortUrl, result.get());
    assertEquals("abc123", result.get().getCode());
    assertEquals("https://example.com/test", result.get().getUrl());
  }

  @Test
  void testSelectByCode_whenShortUrlDoesNotExist() {
    // Given
    String code = "nonexistent";
    when(shortUrlMapper.selectByCode(anyString())).thenReturn(Optional.empty());

    // When
    Optional<ShortUrl> result = shortUrlMapper.selectByCode(code);

    // Then
    assertTrue(result.isEmpty());
  }
}