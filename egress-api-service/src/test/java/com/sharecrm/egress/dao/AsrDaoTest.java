package com.sharecrm.egress.dao;

import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.AsrRecTaskEntity;
import com.sharecrm.egress.service.LockingExecutor;
import org.junit.jupiter.api.Test;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.FieldEnd;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date: 2024/12/4
 * @since 1.0.0
 */
@SpringBootTest(classes = AsrDao.class,
  properties = {"sharecrm.api.asr.tencent.enabled=true"})
public class AsrDaoTest {

  @MockitoBean(name = "asrMongoDatastore")
  private Datastore datastore;

  @MockitoBean
  private Query<AsrRecTaskEntity> recTaskQuery;

  @MockitoBean
  private LockingExecutor lockingExecutor;

  @Autowired
  private AsrDao asrDao;


  @Test
  void queryByTaskId() {
    when(datastore.createQuery(AsrRecTaskEntity.class)).thenReturn(recTaskQuery);
    when(recTaskQuery.filter(any(), any())).thenReturn(recTaskQuery);
    when(recTaskQuery.get()).thenReturn(null);

    AsrRecTaskEntity result = asrDao.queryByTaskId("test-task-id");
    assertEquals(null, result);
  }

  @Test
  void queryWithNoReplay() {
    // 模拟 AsrDao 的 queryWithNoReplay 方法，直接返回空列表
    AsrDao mockAsrDao = mock(AsrDao.class);
    when(mockAsrDao.queryWithNoReplay(any())).thenReturn(Collections.emptyList());

    List<AsrRecTaskEntity> result = mockAsrDao.queryWithNoReplay("test-provider");
    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void save() {
    AsrRecTaskEntity entity = new AsrRecTaskEntity();
    entity.setTaskId("test-task-id");
    entity.setProviderId("test-provider");
    entity.setStatus(AsrQueryRecTaskResponse.STATUS_INIT);

    when(datastore.save(any(AsrRecTaskEntity.class))).thenReturn(null);

    assertDoesNotThrow(() -> asrDao.save(entity));
  }

}