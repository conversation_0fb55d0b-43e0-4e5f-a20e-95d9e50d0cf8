package com.sharecrm.egress.sms.share;

import com.sharecrm.egress.entity.RPTPack;
import com.sharecrm.egress.utils.HTTPUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class MonternetHttpClientTest {

  private MonternetHttpClient client;
  private static final String TEST_IP = "*************";
  private static final int TEST_PORT = 8080;
  private static final String TEST_ACCOUNT = "123456";
  private static final String TEST_PASSWORD = "password123";

  @BeforeEach
  void setUp() {
    client = new MonternetHttpClient(TEST_IP, TEST_PORT, TEST_ACCOUNT, TEST_PASSWORD);
  }

  @Test
  void testSendSmsSuccess() throws Exception {
    // Given
    String mobiles = "***********,***********";
    String message = "测试短信内容";
    String subPort = "1234";
    String userMsgId = "********";
    String mockXmlResponse = "<?xml version=\"1.0\" encoding=\"utf-8\"?><string xmlns=\"http://tempuri.org/\">1485643440204283743</string>";

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenReturn(mockXmlResponse.getBytes());

      // When
      int result = client.sendSms(mobiles, message, subPort, userMsgId);

      // Then
      assertEquals(0, result);
      httpUtilsMock.verify(() -> HTTPUtils.get(anyString()));
    }
  }

  @Test
  void testSendSmsWithInvalidAccount() throws Exception {
    // Given
    MonternetHttpClient invalidClient = new MonternetHttpClient(TEST_IP, TEST_PORT, "12345", TEST_PASSWORD);
    String mobiles = "***********";
    String message = "测试短信";
    String subPort = "1234";
    String userMsgId = "********";

    // When
    int result = invalidClient.sendSms(mobiles, message, subPort, userMsgId);

    // Then
    assertEquals(-1002, result); // PARAM_ERROR
  }

  @Test
  void testSendSmsWithInvalidPassword() throws Exception {
    // Given
    MonternetHttpClient invalidClient = new MonternetHttpClient(TEST_IP, TEST_PORT, TEST_ACCOUNT, "");
    String mobiles = "***********";
    String message = "测试短信";
    String subPort = "1234";
    String userMsgId = "********";

    // When
    int result = invalidClient.sendSms(mobiles, message, subPort, userMsgId);

    // Then
    assertEquals(-1002, result); // PARAM_ERROR
  }

  @Test
  void testSendSmsWithInvalidMessage() throws Exception {
    // Given
    String mobiles = "***********";
    String message = ""; // 空消息
    String subPort = "1234";
    String userMsgId = "********";

    // When
    int result = client.sendSms(mobiles, message, subPort, userMsgId);

    // Then
    assertEquals(-1002, result); // PARAM_ERROR
  }

  @Test
  void testSendSmsWithInvalidSubPort() throws Exception {
    // Given
    String mobiles = "***********";
    String message = "测试短信";
    String subPort = ""; // 空子端口
    String userMsgId = "********";

    // When
    int result = client.sendSms(mobiles, message, subPort, userMsgId);

    // Then
    assertEquals(-1002, result); // PARAM_ERROR
  }

  @Test
  void testSendSmsWithErrorResponse() throws Exception {
    // Given
    String mobiles = "***********";
    String message = "测试短信内容";
    String subPort = "1234";
    String userMsgId = "********";
    String mockErrorResponse = "<?xml version=\"1.0\" encoding=\"utf-8\"?><string xmlns=\"http://tempuri.org/\">-1</string>";

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenReturn(mockErrorResponse.getBytes());

      // When
      int result = client.sendSms(mobiles, message, subPort, userMsgId);

      // Then
      assertEquals(-1, result);
    }
  }

  @Test
  void testSendSmsWithHttpException() {
    // Given
    String mobiles = "***********";
    String message = "测试短信内容";
    String subPort = "1234";
    String userMsgId = "********";

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenThrow(new RuntimeException("Network error"));

      // When & Then
      assertThrows(RuntimeException.class, () -> {
        client.sendSms(mobiles, message, subPort, userMsgId);
      });
    }
  }

  @Test
  void testSendSmsWithInvalidXmlResponse() throws Exception {
    // Given
    String mobiles = "***********";
    String message = "测试短信内容";
    String subPort = "1234";
    String userMsgId = "********";
    String invalidXml = "invalid xml content";

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenReturn(invalidXml.getBytes());

      // When
      int result = client.sendSms(mobiles, message, subPort, userMsgId);

      // Then
      assertEquals(200, result); // XML解析异常返回200
    }
  }

  @Test
  void testGetRptSuccess() {
    // Given
    String mockXmlResponse = """
      <?xml version="1.0" encoding="utf-8"?>
      <ArrayOfString xmlns="http://tempuri.org/">
        <string>1,20231201120000,1485643440204283743,10690000,***********,********,reserve,0,DELIVRD</string>
        <string>2,20231201120100,1485643440204283744,10690000,***********,87654321,reserve,1,EXPIRED</string>
      </ArrayOfString>
      """;

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenReturn(mockXmlResponse.getBytes());

      // When
      List<RPTPack> result = client.getRpt();

      // Then
      assertNotNull(result);
      assertEquals(2, result.size());

      RPTPack firstRpt = result.get(0);
      assertEquals("20231201120000", firstRpt.getStrMoTime());
      assertEquals("1485643440204283743", firstRpt.getStrPtMsgId());
      assertEquals("10690000", firstRpt.getStrSpNumber());
      assertEquals("***********", firstRpt.getStrMobile());
      assertEquals("********", firstRpt.getStrUserMsgId());
      assertEquals("reserve", firstRpt.getStrReserve());
      assertEquals(0, firstRpt.getnStatus());
      assertEquals("DELIVRD", firstRpt.getStrErCode());

      RPTPack secondRpt = result.get(1);
      assertEquals("20231201120100", secondRpt.getStrMoTime());
      assertEquals("1485643440204283744", secondRpt.getStrPtMsgId());
      assertEquals("***********", secondRpt.getStrMobile());
      assertEquals(1, secondRpt.getnStatus());
    }
  }

  @Test
  void testGetRptWithNoData() {
    // Given
    String mockEmptyResponse = """
      <?xml version="1.0" encoding="utf-8"?>
      <ArrayOfString xmlns="http://tempuri.org/"></ArrayOfString>
      """;

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenReturn(mockEmptyResponse.getBytes());

      // When
      List<RPTPack> result = client.getRpt();

      // Then
      assertNotNull(result);
      assertEquals(0, result.size());
    }
  }

  @Test
  void testGetRptWithException() {
    // Given
    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenThrow(new RuntimeException("Network error"));

      // When
      List<RPTPack> result = client.getRpt();

      // Then
      assertNotNull(result);
      assertEquals(0, result.size()); // 异常时返回空列表
    }
  }

  @Test
  void testGetDeliverForGetWithException() {
    // Given
    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenThrow(new RuntimeException("Network error"));

      // When & Then
      assertThrows(RuntimeException.class, () -> {
        client.getDeliverForGet(new MonternetHttpClient.Params());
      });
    }
  }

  // 静态验证方法测试
  @Test
  void testValidateUserId() {
    // Valid cases
    assertTrue(MonternetHttpClient.validateUserId("123456"));

    // Invalid cases
    assertFalse(MonternetHttpClient.validateUserId("12345")); // 太短
    assertFalse(MonternetHttpClient.validateUserId("1234567")); // 太长
    assertFalse(MonternetHttpClient.validateUserId(null));
    assertFalse(MonternetHttpClient.validateUserId(""));
  }

  @Test
  void testValidatePassword() {
    // Valid cases
    assertTrue(MonternetHttpClient.validatePwd("1"));
    assertTrue(MonternetHttpClient.validatePwd("password123"));
    assertTrue(MonternetHttpClient.validatePwd("a".repeat(32))); // 32位密码

    // Invalid cases
    assertFalse(MonternetHttpClient.validatePwd(""));
    assertFalse(MonternetHttpClient.validatePwd(null));
    assertFalse(MonternetHttpClient.validatePwd("a".repeat(33))); // 超过32位
  }

  @Test
  void testValidateMessage() {
    // Valid cases
    assertTrue(MonternetHttpClient.validateMessage("test"));
    assertTrue(MonternetHttpClient.validateMessage("a".repeat(350))); // 350个字符

    // Invalid cases
    assertFalse(MonternetHttpClient.validateMessage(""));
    assertFalse(MonternetHttpClient.validateMessage(null));
    assertFalse(MonternetHttpClient.validateMessage("a".repeat(351))); // 超过350个字符
  }

  @Test
  void testValidateSubPort() {
    // Valid cases
    assertTrue(MonternetHttpClient.validateSubPort("*"));
    assertTrue(MonternetHttpClient.validateSubPort("1234"));
    assertTrue(MonternetHttpClient.validateSubPort("123456")); // 6位数字

    // Invalid cases
    assertFalse(MonternetHttpClient.validateSubPort(""));
    assertFalse(MonternetHttpClient.validateSubPort(null));
    assertFalse(MonternetHttpClient.validateSubPort("1234567")); // 超过6位
    assertFalse(MonternetHttpClient.validateSubPort("12ab")); // 包含非数字
    assertFalse(MonternetHttpClient.validateSubPort("12-34")); // 包含特殊字符
  }

  // Params内部类测试
  @Test
  void testParamsStringMethod() {
    // Given
    MonternetHttpClient.Params params = new MonternetHttpClient.Params();
    params.setUserId("123456");
    params.setPassword("password");
    params.setPszMobis("***********");
    params.setPszMsg("测试消息");
    params.setIMobiCount("1");
    params.setPszSubPort("1234");
    params.setMsgId("********");

    // When
    String result = params.string();

    // Then
    assertNotNull(result);
    assertTrue(result.startsWith("?"));
    assertTrue(result.contains("userId=123456"));
    assertTrue(result.contains("password=password"));
    assertTrue(result.contains("pszMobis=***********"));
    assertTrue(result.contains("iMobiCount=1"));
    assertTrue(result.contains("pszSubPort=1234"));
    assertTrue(result.contains("MsgId=********"));
  }

  @Test
  void testParamsStringWithNullValues() {
    // Given
    MonternetHttpClient.Params params = new MonternetHttpClient.Params();
    params.setUserId("123456");
    // 其他字段保持null

    // When
    String result = params.string();

    // Then
    assertNotNull(result);
    assertTrue(result.startsWith("?"));
    assertTrue(result.contains("userId=123456"));
    assertFalse(result.contains("password="));
    assertFalse(result.contains("pszMobis="));
  }

  @Test
  void testParamsStringWithSpecialCharacters() {
    // Given
    MonternetHttpClient.Params params = new MonternetHttpClient.Params();
    params.setUserId("123456");
    params.setPszMsg("测试消息 with 特殊字符 & 符号");

    // When
    String result = params.string();

    // Then
    assertNotNull(result);
    assertTrue(result.contains("userId=123456"));
    // URL编码后应该包含编码的特殊字符
    assertTrue(result.contains("pszMsg="));
  }

  @Test
  void testParamsStringWithAllFields() {
    // Given
    MonternetHttpClient.Params params = new MonternetHttpClient.Params();
    params.setUserId("123456");
    params.setPassword("password");
    params.setPszMobis("***********");
    params.setPszMsg("test message");
    params.setIMobiCount("1");
    params.setPszSubPort("1234");
    params.setMsgId("********");
    params.setIReqType("2");
    params.setSa("ext123");
    params.setMultixmt("batch");

    // When
    String result = params.string();

    // Then
    assertNotNull(result);
    assertTrue(result.startsWith("?"));
    assertTrue(result.contains("userId="));
    assertTrue(result.contains("password="));
    assertTrue(result.contains("pszMobis="));
    assertTrue(result.contains("pszMsg="));
    assertTrue(result.contains("iMobiCount="));
    assertTrue(result.contains("pszSubPort="));
    assertTrue(result.contains("MsgId="));
    assertTrue(result.contains("iReqType="));
    assertTrue(result.contains("Sa="));
    assertTrue(result.contains("multixmt="));
  }

  @Test
  void testSendSmsWithMultipleMobiles() throws Exception {
    // Given
    String mobiles = "***********,***********,13700137000";
    String message = "批量发送测试";
    String subPort = "1234";
    String userMsgId = "87654321";
    String mockXmlResponse = "<?xml version=\"1.0\" encoding=\"utf-8\"?><string xmlns=\"http://tempuri.org/\">1485643440204283745</string>";

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenReturn(mockXmlResponse.getBytes());

      // When
      int result = client.sendSms(mobiles, message, subPort, userMsgId);

      // Then
      assertEquals(0, result);
    }
  }

  @Test
  void testSendSmsWithLongMessage() throws Exception {
    // Given
    String mobiles = "***********";
    String message = "这是一条很长的短信内容，用于测试长短信的发送功能。".repeat(10); // 重复10次
    String subPort = "*";
    String userMsgId = "99999999";
    String mockXmlResponse = "<?xml version=\"1.0\" encoding=\"utf-8\"?><string xmlns=\"http://tempuri.org/\">1485643440204283746</string>";

    try (MockedStatic<HTTPUtils> httpUtilsMock = mockStatic(HTTPUtils.class)) {
      httpUtilsMock.when(() -> HTTPUtils.get(anyString()))
        .thenReturn(mockXmlResponse.getBytes());

      // When
      int result = client.sendSms(mobiles, message, subPort, userMsgId);

      // Then
      assertEquals(0, result);
    }
  }
}