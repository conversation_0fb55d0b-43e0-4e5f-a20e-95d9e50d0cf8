package com.sharecrm.egress.sms.aliyun;

import com.sharecrm.egress.config.SmsProperties.AliYunConfig;
import com.sharecrm.egress.service.LockingExecutor;
import com.sharecrm.egress.sms.SmsUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;
import org.springframework.scheduling.TaskScheduler;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

class AliyunStatusTaskTest {

  private AliyunStatusTask statusTask;
  private AliyunSmsSender sender;
  private AliYunConfig config;
  private TaskScheduler taskScheduler;
  private LockingExecutor lock;
  private ScheduledFuture<?> scheduledFuture;
  private List<Runnable> capturedRunnables = new ArrayList<>();

  @BeforeEach
  void setup() {
    // 创建mock对象
    config = createConfig();
    sender = mock(AliyunSmsSender.class);
    taskScheduler = mock(TaskScheduler.class);
    lock = mock(LockingExecutor.class);
    scheduledFuture = mock(ScheduledFuture.class);

    // 使用静态方法mock
    try (MockedStatic<SmsUtils> smsUtils = mockStatic(SmsUtils.class)) {
      // 模拟随机延迟
      Instant mockInstant = Instant.now().plusSeconds(100);
      smsUtils.when(() -> SmsUtils.randomDelayInstant(30, 120)).thenReturn(mockInstant);
      smsUtils.when(() -> SmsUtils.randomDelayInstant(60, 300)).thenReturn(mockInstant);

      // 设置taskScheduler的行为，并捕获传递的Runnables
      when(taskScheduler.scheduleWithFixedDelay(any(Runnable.class), any(Instant.class), any(Duration.class)))
          .thenAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            Duration delay = invocation.getArgument(2);

            // 根据延迟时间来区分和保存不同的任务
            if (delay.equals(Duration.ofSeconds(60))) {
              // SMS任务
              capturedRunnables.add(0, runnable); // SMS任务放在第一个位置
            } else if (delay.equals(Duration.ofSeconds(300))) {
              // 模板任务
              capturedRunnables.add(1, runnable); // 模板任务放在第二个位置
            }

            return scheduledFuture;
          });

      // 创建AliyunStatusTask实例
      statusTask = new AliyunStatusTask(config, sender, taskScheduler, lock);
    }
  }

  private AliYunConfig createConfig() {
    AliYunConfig cfg = new AliYunConfig();
    cfg.setId("aliyun-test");
    cfg.setSmsTaskDelay(Duration.ofSeconds(60));
    cfg.setTemplateTaskDelay(Duration.ofSeconds(300));
    return cfg;
  }

  @Test
  void shouldScheduleTasksAtInitialization() {
    // 验证定时任务已经被调度了两次（SMS和模板状态）
    verify(taskScheduler, times(2)).scheduleWithFixedDelay(any(Runnable.class), any(Instant.class),
        any(Duration.class));

    // 捕获调度方法的所有参数
    ArgumentCaptor<Duration> durationCaptor = ArgumentCaptor.forClass(Duration.class);

    // 验证调度方法被调用了两次，并捕获延迟参数
    verify(taskScheduler, times(2)).scheduleWithFixedDelay(
        any(Runnable.class),
            any(Instant.class),
        durationCaptor.capture());

    // 验证捕获的参数不为空
    assertNotNull(durationCaptor.getAllValues());

    // 验证捕获了两组参数
    assertEquals(2, durationCaptor.getAllValues().size());

    // 验证延迟参数
    List<Duration> durations = durationCaptor.getAllValues();
    assertTrue(durations.contains(Duration.ofSeconds(60))); // SMS延迟
    assertTrue(durations.contains(Duration.ofSeconds(300))); // 模板延迟

    // 确保我们捕获了两个任务
    assertEquals(2, capturedRunnables.size());
  }

  @Test
  void updateSmsStatusTaskShouldExecuteWithLocking() {
    // 确保SMS任务被正确捕获
    assertEquals(2, capturedRunnables.size());

    // 获取SMS状态更新任务
    Runnable smsTask = capturedRunnables.get(0);

    // 清除之前的交互记录，确保验证的准确性
    verify(taskScheduler, times(2)).scheduleWithFixedDelay(any(), any(), any());
    verifyNoMoreInteractions(lock);

    // 设置lock.execute方法的行为，捕获并执行传入的Runnable
    ArgumentCaptor<Runnable> lockRunnableCaptor = ArgumentCaptor.forClass(Runnable.class);

    // 执行SMS状态更新任务
    smsTask.run();

    // 验证lock.execute被调用，使用正确的参数
        verify(lock).execute(lockRunnableCaptor.capture(),
        eq("sms-task-aliyun-test"),
        eq(Duration.ofSeconds(30)),
        eq(Duration.ofSeconds(10)));

    // 执行捕获的Runnable并验证sender.updateSmsStatus被调用
    lockRunnableCaptor.getValue().run();
    verify(sender).updateSmsStatus();
  }

  @Test
  void updateTemplateStatusTaskShouldExecuteWithLocking() {
    // 确保模板任务被正确捕获
    assertEquals(2, capturedRunnables.size());

    // 获取模板状态更新任务
    Runnable templateTask = capturedRunnables.get(1);

    // 清除之前的交互记录，确保验证的准确性
    verify(taskScheduler, times(2)).scheduleWithFixedDelay(any(), any(), any());
    verifyNoMoreInteractions(lock);

    // 设置lock.execute方法的行为，捕获并执行传入的Runnable
    ArgumentCaptor<Runnable> lockRunnableCaptor = ArgumentCaptor.forClass(Runnable.class);

    // 执行模板状态更新任务
    templateTask.run();

    // 验证lock.execute被调用，使用正确的参数
        verify(lock).execute(lockRunnableCaptor.capture(),
        eq("template-task-aliyun-test"),
        eq(Duration.ofSeconds(60)),
        eq(Duration.ofSeconds(10)));

    // 执行捕获的Runnable并验证sender.updateTemplateStatus被调用
    lockRunnableCaptor.getValue().run();
    verify(sender).updateTemplateStatus();
  }

  @Test
  void destroyShouldCancelScheduledTasks() {
    // 执行destroy方法
    statusTask.destroy();

    // 验证两个ScheduledFuture都被取消了
    verify(scheduledFuture, times(2)).cancel(true);
  }
}