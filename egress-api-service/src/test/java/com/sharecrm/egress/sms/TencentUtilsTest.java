package com.sharecrm.egress.sms;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.exception.SmsArgException;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsResponseData;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.utils.JsonUtil;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatus;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class TencentUtilsTest {

  @Test
  void tencentTemplateStatus() {
    assertEquals(SmsSdkConstants.TEMPLATE_STATUS_REJECTED, TencentUtils.tencentTemplateStatus(-1L));
    assertEquals(SmsSdkConstants.TEMPLATE_STATUS_APPROVED, TencentUtils.tencentTemplateStatus(0L));
    assertEquals(SmsSdkConstants.TEMPLATE_STATUS_APPROVING, TencentUtils.tencentTemplateStatus(1L));
    assertEquals(SmsSdkConstants.TEMPLATE_STATUS_APPROVING, TencentUtils.tencentTemplateStatus(2L));
    assertThrows(SmsException.class, () -> TencentUtils.tencentTemplateStatus(3L));
  }

  @Test
  void tencentSimpleContentForReplace() {
    Map<String, String> keyMap = TencentUtils.tencentSimpleContentForReplace("你的验证码：${code}，有效期${min}分钟，请尽快完成验证");
    assertEquals(2, keyMap.size());
    assertEquals("1", keyMap.get("code"));
    assertEquals("2", keyMap.get("min"));
  }

  @Test
  void appendTemplateReplace() {
    SmsStaticTemplate template = new SmsStaticTemplate();
    template.setContent("你的验证码：{1}，有效期{2}分钟，请尽快完成验证");
    SmsStaticTemplate rs = TencentUtils.appendTemplateReplace(template);
    assertNotNull(rs.getParamReplace());
    assertEquals("你的验证码：${var1}，有效期${var2}分钟，请尽快完成验证", rs.getContent());
  }

  @Test
  void tencentContentToSimpleContent() {
    String content = "你的验证码：{1}，有效期{2}分钟，请尽快完成验证";
    String rs = TencentUtils.tencentContentToSimpleContent(content, Map.of("var1", "1", "var2", "2"));
    assertEquals("你的验证码：${var1}，有效期${var2}分钟，请尽快完成验证", rs);
  }

  @Test
  void initTencentSmsClient() {
    SmsProperties.TencentConfig config = tencentConfig();
    SmsClient client = TencentUtils.initTencentSmsClient(config);
    assertNotNull(client);
    assertNotNull(client.getClientProfile().getHttpProfile().getProxyHost());
    assertEquals(8080, client.getClientProfile().getHttpProfile().getProxyPort());
  }

  @Test
  void isSuccess() {
    assertTrue(TencentUtils.isSuccess("OK"));
    assertFalse(TencentUtils.isSuccess("Failed"));
  }

  @Test
  void tencentTemplateType() {
    SmsTemplateRequest request = new SmsTemplateRequest();
    request.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE);
    assertEquals(3L, TencentUtils.tencentTemplateType(request));
    request.setTemplateType("unknown");
    assertThrows(SmsArgException.class, () -> TencentUtils.tencentTemplateType(request));
  }


  @Test
  void tencentTemplateParamSet() {
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setTemplateParam(Map.of("min", "10", "code", "123456"));
    SmsStaticTemplate template = new SmsStaticTemplate();
    String[] rs = TencentUtils.tencentTemplateParamSet(request, template);
    assertNotNull(rs);

    template.setParamReplace(JsonUtil.mapToJsonOrNull(Map.of("code", "1", "min", "2")));
    String[] success = TencentUtils.tencentTemplateParamSet(request, template);
    assertEquals(2, success.length);
    assertEquals("123456", success[0]);
    assertEquals("10", success[1]);
  }

  @Test
  void convertResponseData() {
    SendSmsResponse resp = new SendSmsResponse();
    SendStatus status = new SendStatus();
    status.setPhoneNumber("13512345678");
    resp.setSendStatusSet(new SendStatus[]{status});
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("13512345678"));
    List<SmsResponseData> rs = TencentUtils.convertResponseData(request, resp, null);
    assertEquals(1, rs.size());
    //最后返回的手机号一定是标准传入的手机号
    assertEquals("13512345678", rs.getFirst().getPhone());

    List<SmsResponseData> failed = TencentUtils.convertResponseData(request, new SendSmsResponse(), null);
    assertEquals(1, failed.size());
    assertFalse(failed.getFirst().isSuccess());
  }


  @Test
  void simplePhoneToTencent() {
    String[] rs = TencentUtils.simplePhoneToTencent(List.of("13512345678"), false);
    assertEquals("+86" + "13512345678", rs[0]);
  }

  @NotNull
  private static SmsProperties.TencentConfig tencentConfig() {
    SmsProperties.TencentConfig config = new SmsProperties.TencentConfig();
    config.setHttpProxy("https://localhost:8080");
    return config;
  }
}