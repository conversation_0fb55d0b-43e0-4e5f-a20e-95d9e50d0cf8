package com.sharecrm.egress.sms.byteplus;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sms.BytePlusUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class BytePlusSmsSenderTest {

  private BytePlusSmsSender sender;
  private BytePlusSmsService smsService;
  private SmsProperties.BytePlusConfig config;
  private SmsDao smsDao;
  private AutoConfMQProducer smsProducer;

  @BeforeEach
  void setup() throws Exception {
    // 设置测试数据
    config = createConfig();
    smsDao = mock(SmsDao.class);
    smsProducer = mock(AutoConfMQProducer.class);
    smsService = mock(BytePlusSmsService.class);

    // 使用静态方法mock
    try (MockedStatic<BytePlusUtils> bytePlusUtils = mockStatic(BytePlusUtils.class)) {
      com.byteplus.service.sms.SmsService mockClient = mock(com.byteplus.service.sms.SmsService.class);
      bytePlusUtils.when(() -> BytePlusUtils.initBytePlusSmsClient(any())).thenReturn(mockClient);

      // 创建BytePlusSmsSender实例
      sender = new BytePlusSmsSender(config, smsDao, smsProducer);

      // 使用反射直接注入mock的smsService
      Field smsServiceField = BytePlusSmsSender.class.getDeclaredField("smsService");
      ReflectionUtils.makeAccessible(smsServiceField);
      smsServiceField.set(sender, smsService);
    }
  }

  private SmsProperties.BytePlusConfig createConfig() {
    SmsProperties.BytePlusConfig cfg = new SmsProperties.BytePlusConfig();
    cfg.setId("byteplus-test");
    cfg.setOrder(100);
    cfg.setSupports(List.of("intl", "captcha"));
    return cfg;
  }

  @Test
  void sendShouldDelegateToService() {
    // 准备测试数据
    String phone = "13800138000";
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("验证码：123456");
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(request);

    SmsSendResult expectedResult = new SmsSendResult();
    expectedResult.setRequest(request);

    // 设置mock行为
    when(smsService.sendSms(eq(phone), any())).thenReturn(expectedResult);

    // 执行测试
    SmsSendResult result = sender.send(phone, wrapper);

    // 验证结果
    assertEquals(expectedResult, result);
    verify(smsService).sendSms(eq(phone), any());
  }

  @Test
  void staticTemplatesShouldDelegateToService() {
    // 准备测试数据
    SmsTemplateQuery query = new SmsTemplateQuery();
    List<SmsStaticTemplate> expectedTemplates = List.of(new SmsStaticTemplate());

    // 设置mock行为
    when(smsService.queryStaticTemplates(query)).thenReturn(expectedTemplates);

    // 执行测试
    List<SmsStaticTemplate> result = sender.staticTemplates(query);

    // 验证结果
    assertSame(expectedTemplates, result);
    verify(smsService).queryStaticTemplates(query);
  }

  @Test
  void sendByTemplateShouldDelegateToService() {
    // 准备测试数据
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("13800138000"));
    SmsTemplateEntity template = new SmsTemplateEntity();

    SmsSendResult expectedResult = new SmsSendResult();

    // 设置mock行为
    when(smsService.sendByTemplate(any(), eq(request), eq(template))).thenReturn(expectedResult);

    // 执行测试
    SmsSendResult result = sender.sendByTemplate(request, template);

    // 验证结果
    assertEquals(expectedResult, result);
    verify(smsService).sendByTemplate(any(), eq(request), eq(template));
  }

  @Test
  void providerShouldReturnConfig() {
    // 执行测试并验证
    assertSame(config, sender.provider());
  }

  @Test
  void smsStatusCallbackShouldDelegateToService() {
    // 准备测试数据
    String ext = "test-ext";
    String body = "[{\"status\":\"success\"}]";
    MockServerWebExchange exchange = MockServerWebExchange.from(
        MockServerHttpRequest.post("/callback").build());

    // 执行测试
    sender.smsStatusCallback(ext, body, exchange);

    // 验证调用
    verify(smsService).smsStatusCallback(ext, body);
  }

  @Test
  void supportsShouldReturnConfigSupports() {
    // 执行测试
    List<String> supports = sender.supports();

    // 验证结果
    assertEquals(config.getSupports(), supports);
  }

  @Test
  void getOrderShouldReturnConfigOrder() {
    // 执行测试
    int order = sender.getOrder();

    // 验证结果
    assertEquals(100, order);
  }
}