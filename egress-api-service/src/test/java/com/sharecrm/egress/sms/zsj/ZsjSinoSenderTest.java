package com.sharecrm.egress.sms.zsj;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsSender;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ZsjSinoSenderTest {

  private ZsjSinoSender sender;
  private ZsjSinoSmsService mockSmsService;
  private SmsProperties mockProperties;

  @BeforeEach
  void setUp() {
    mockSmsService = mock(ZsjSinoSmsService.class);
    mockProperties = mock(SmsProperties.class);
    sender = new ZsjSinoSender(mockSmsService, mockProperties);
  }

  @Test
  void send() {
    // Given
    String phone = "13512345678";
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("Test message");
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(request, NanoIdUtils.randomNanoId());

    // When
    sender.send(phone, wrapper);

    // Then
    verify(mockSmsService).sendSms(phone, wrapper);
  }

  @Test
  void provider() {
    // Given
    SmsProperties.SinoConfig expectedProvider = new SmsProperties.SinoConfig();
    when(mockProperties.getSino()).thenReturn(expectedProvider);

    // When
    var result = sender.provider();

    // Then
    assertEquals(expectedProvider, result);
  }

  @Test
  void supports() {
    // When
    List<String> supports = sender.supports();

    // Then
    assertNotNull(supports);
    assertTrue(supports.contains(SmsSender.SUPPORT_CHINESE));
  }

  @Test
  void getOrder() {
    // When
    int order = sender.getOrder();

    // Then
    assertEquals(90, order); // ZsjSinoSender has order 90, not the default 100
  }

  @Test
  void testSenderInterface() {
    // Test that sender implements SmsSender interface properly
    assertTrue(sender instanceof SmsSender);

    // Test default methods that should throw exceptions
    assertThrows(Exception.class, () -> sender.sendTts(null));
    assertThrows(Exception.class, () -> sender.addTemplate(null));
    assertThrows(Exception.class, () -> sender.sendByTemplate(null, null));

    // Test default methods that should return empty
    assertTrue(sender.staticTemplates(null).isEmpty());

    // Test callback method (should not throw)
    assertDoesNotThrow(() -> sender.smsStatusCallback("ext", "body", null));
  }
}
