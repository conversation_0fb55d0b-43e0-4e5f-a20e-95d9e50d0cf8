package com.sharecrm.egress.sms.mengniu;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sms.SmsSender;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class MengniuSenderTest {

  private MengniuSender sender;
  private MengniuSmsService mockSmsService;
  private SmsProperties mockProperties;

  @BeforeEach
  void setUp() {
    mockSmsService = mock(MengniuSmsService.class);
    mockProperties = mock(SmsProperties.class);
    sender = new MengniuSender(mockSmsService, mockProperties);
  }

  @Test
  void send() {
    // Given
    String phone = "13512345678";
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("Test message");
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(request, NanoIdUtils.randomNanoId());

    // When
    sender.send(phone, wrapper);

    // Then
    verify(mockSmsService).sendSms(phone, wrapper);
  }

  @Test
  void addTemplate() {
    // Given
    SmsTemplateRequest request = new SmsTemplateRequest();
    request.setContent("Test template");

    // When
    sender.addTemplate(request);

    // Then
    verify(mockSmsService).addTemplate(request);
  }

  @Test
  void sendByTemplate() {
    // Given
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("13512345678"));
    SmsTemplateEntity template = new SmsTemplateEntity();

    // When
    sender.sendByTemplate(request, template);

    // Then
    verify(mockSmsService).sendByTemplate(any(SmsRequestWrapper.class), eq(request), eq(template));
  }

  @Test
  void smsStatusCallback() {
    // Given
    String ext = "test-ext";
    String body = "test-body";
    ServerWebExchange exchange = mock(ServerWebExchange.class);

    // When
    sender.smsStatusCallback(ext, body, exchange);

    // Then
    verify(mockSmsService).smsStatusCallback(body, exchange);
  }

  @Test
  void staticTemplates() {
    // When
    sender.staticTemplates(null);

    // Then
    verify(mockSmsService).queryStaticTemplates(null);
  }

  @Test
  void provider() {
    // Given
    SmsProperties.MengniuConfig expectedProvider = new SmsProperties.MengniuConfig();
    when(mockProperties.getMengniu()).thenReturn(expectedProvider);

    // When
    var result = sender.provider();

    // Then
    assertEquals(expectedProvider, result);
  }

  @Test
  void supports() {
    // When
    List<String> supports = sender.supports();

    // Then
    assertNotNull(supports);
    assertTrue(supports.contains(SmsSender.SUPPORT_CHINESE));
    assertTrue(supports.contains(SmsSender.SUPPORT_ADD_TEMPLATE_CHINESE));
    assertTrue(supports.contains(SmsSender.SUPPORT_SEND_TEMPLATE_CHINESE));
  }

  @Test
  void getOrder() {
    // Given
    SmsProperties.MengniuConfig mockConfig = mock(SmsProperties.MengniuConfig.class);
    when(mockConfig.getOrder()).thenReturn(SmsSender.DEFAULT_ORDER);
    when(mockProperties.getMengniu()).thenReturn(mockConfig);

    // When
    int order = sender.getOrder();

    // Then
    assertEquals(SmsSender.DEFAULT_ORDER, order);
  }
}
