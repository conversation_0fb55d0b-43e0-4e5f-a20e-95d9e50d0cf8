package com.sharecrm.egress.sms.share;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.SmsUtils;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DahantcSenderTest {

  SmsProperties.DahantcConfig config = mock(SmsProperties.DahantcConfig.class);

  DahantcSender sender = initSender(config);

  private DahantcSender initSender(SmsProperties.DahantcConfig config) {
    SmsProperties properties = mock(SmsProperties.class);
    when(config.getPassword()).thenReturn("test");
    when(properties.getDahantc()).thenReturn(config);
    return new DahantcSender(properties, mock(OkHttpSupport.class));
  }

  @Test
  void buildBody() {
    SmsSendRequest request = new SmsSendRequest();
    String phone = "12345678911";
    request.setContent("你的验证码是：12345");
    DahantcSender.DahanSmsBody body = sender.buildBody(phone, request);
    assertEquals(SmsUtils.SMS_SIGN_ZH, body.getSign());
    when(config.getUrl()).thenReturn("https://localhost");
    Request requestedBody = sender.requestBody(phone, request);
    assertNotNull(requestedBody);
    assertNotNull(requestedBody.body());
  }

  @Test
  void smsResult() {
    Response response = mock(Response.class);
    when(response.body()).thenReturn(ResponseBody.create("{\"result\":\"0\"}", MediaType.parse("application/json; charset=utf-8")));
    DahantcSender.DahanSmsResult smsResult = sender.getSmsResult(response);
    assertNotNull(smsResult);
  }
}