package com.sharecrm.egress.sms.zsj;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = ZsjCmhkSmsService.class,
  properties = {"sharecrm.api.sms.enabled=true", "sharecrm.api.sms.zhaoshang.enabled=true"})
class ZsjCmhkSmsServiceTest {

  @MockitoBean
  private SmsProperties properties;
  @MockitoBean(name = "smsHttpSupport")
  private OkHttpSupport httpClient;

  @Autowired
  private ZsjCmhkSmsService service;

  @BeforeEach
  void setup() {
    when(properties.getZhaoshang()).thenReturn(mockCmhkConfig());
    ZsjCmhkSmsService.ZsjSmsResponse response = new ZsjCmhkSmsService.ZsjSmsResponse();
    response.setCode(200);
    when(httpClient.syncExecute(any(), any())).thenReturn(response);
    ZsjCmhkSmsService.TokenResponse token = new ZsjCmhkSmsService.TokenResponse();
    token.setAccessToken("test");
    // mock token
    when(httpClient.parseObject(any(), any())).thenReturn(token);
  }

  @Test
  void sendSms() {
    SmsSendResult result = service.sendSms("13512345678", mockSmsRequest());
    assertTrue(result.getResponse().isSuccess());
  }

  @Test
  void sendIntlSms() {
    SmsSendResult result = service.sendIntlSms("13512345678", mockSmsRequest());
    assertTrue(result.getResponse().isSuccess());
  }

  @Test
  void sendEmail() {
    EmailSendRequest request = new EmailSendRequest();
    request.setCc(List.of("<EMAIL>"));
    request.setTo(List.of("<EMAIL>"));
    EmailSendResponse result = service.sendEmail(request);
    assertTrue(result.isSuccess());
  }

  private SmsProperties.ZhaoShangConfig mockCmhkConfig() {
    SmsProperties.ZhaoShangConfig config = new SmsProperties.ZhaoShangConfig();
    config.setSmsSendUrl("https://localhost");
    config.setClientId("test");
    config.setClientSecret("test");
    config.setAppId("test");
    config.setZhSignId("test");
    config.setZhTemplateId("test");
    return config;
  }

  private SmsRequestWrapper<SmsSendRequest> mockSmsRequest() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("验证码：123456");
    req.setPhones(List.of("13512345678"));
    return new SmsRequestWrapper<>(req);
  }
}