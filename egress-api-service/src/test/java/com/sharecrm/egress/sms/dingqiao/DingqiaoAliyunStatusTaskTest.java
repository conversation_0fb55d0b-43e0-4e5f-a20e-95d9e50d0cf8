package com.sharecrm.egress.sms.dingqiao;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class DingqiaoAliyunStatusTaskTest {

    @Mock
    private DingqiaoAliyunSmsSender sender;

    @InjectMocks
    private DingqiaoAliyunStatusTask task;

    @Test
    void updateSmsStatus() {
        // Execute the method
        task.updateSmsStatus();
        
        // Verify that the sender's updateSmsStatus method was called
        verify(sender).updateSmsStatus();
    }

    @Test
    void updateTemplateStatus() {
        // Execute the method
        task.updateTemplateStatus();
        
        // Verify that the sender's updateTemplateStatus method was called
        verify(sender).updateTemplateStatus();
    }
}
