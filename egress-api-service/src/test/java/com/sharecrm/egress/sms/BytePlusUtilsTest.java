package com.sharecrm.egress.sms;

import com.byteplus.http.ClientConfiguration;
import com.byteplus.http.HttpClientFactory;
import com.byteplus.model.response.SmsSendResponse;
import com.byteplus.service.sms.SmsService;
import com.byteplus.service.sms.impl.SmsServiceImpl;
import com.sharecrm.egress.config.SmsProperties;
import org.apache.http.HttpHost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

class BytePlusUtilsTest {

  @Test
  void isSuccessWithSuccessCode() {
    // BytePlus SDK的成功码是"0"
    SmsSendResponse resp = new SmsSendResponse();
    assertTrue(BytePlusUtils.isSuccess(resp));
    // 测试失败情况
    resp.setCode("-1");
    assertFalse(BytePlusUtils.isSuccess(resp));
  }

  @Test
  void isSendSuccessWithSuccessStatus() {
    // BytePlus SDK的成功状态码也是"0"
    assertTrue(BytePlusUtils.isSendSuccess("0"));

    // 测试失败情况
    assertFalse(BytePlusUtils.isSendSuccess("1"));
    assertFalse(BytePlusUtils.isSendSuccess("-1"));
    assertFalse(BytePlusUtils.isSendSuccess(""));
    assertFalse(BytePlusUtils.isSendSuccess(null));
  }

  @Test
  void initBytePlusSmsClientWithValidConfig() throws Exception {
    // Mock dependencies
    try (MockedStatic<SmsServiceImpl> smsServiceImplMockedStatic = mockStatic(SmsServiceImpl.class);
         MockedStatic<HttpHost> httpHostMockedStatic = mockStatic(HttpHost.class);
         MockedStatic<HttpClientFactory> httpClientFactoryMockedStatic = mockStatic(HttpClientFactory.class)) {

      // Prepare mocks
      SmsService mockSmsService = mock(SmsService.class);
      HttpHost mockHttpHost = mock(HttpHost.class);
      HttpClientFactory.ClientInstance mockClientInstance = mock(HttpClientFactory.ClientInstance.class);
      CloseableHttpClient mockHttpClient = mock(CloseableHttpClient.class);

      // Setup mock behavior
      when(SmsServiceImpl.getInstance(anyString())).thenReturn(mockSmsService);
      when(HttpHost.create(anyString())).thenReturn(mockHttpHost);
      when(HttpClientFactory.create(any(ClientConfiguration.class), any(HttpHost.class)))
        .thenReturn(mockClientInstance);
      when(mockClientInstance.getHttpClient()).thenReturn(mockHttpClient);

      // Create test config
      SmsProperties.BytePlusConfig config = new SmsProperties.BytePlusConfig();
      config.setRegion("cn-north-1");
      config.setAppKey("test-app-key");
      config.setAppSecret("test-app-secret");

      // Test without HTTP proxy
      SmsService result = BytePlusUtils.initBytePlusSmsClient(config);
      assertNotNull(result);

      // Test with HTTP proxy
      config.setHttpProxy("http://proxy.example.com:8080");
      result = BytePlusUtils.initBytePlusSmsClient(config);
      assertNotNull(result);
    }
  }
}