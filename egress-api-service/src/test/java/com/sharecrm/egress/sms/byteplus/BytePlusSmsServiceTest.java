package com.sharecrm.egress.sms.byteplus;

import com.byteplus.model.response.SmsSendResponse;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.utils.JsonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.mock.web.server.MockServerWebExchange;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class BytePlusSmsServiceTest {

  private BytePlusSmsService smsService;
  private SmsDao smsDao;
  private AutoConfMQProducer smsProducer;
  private com.byteplus.service.sms.SmsService bytePlusClient;
  private SmsProperties.BytePlusConfig config;

  @BeforeEach
  void setup() {
    // 创建mock对象
    smsDao = mock(SmsDao.class);
    smsProducer = mock(AutoConfMQProducer.class);
    bytePlusClient = mock(com.byteplus.service.sms.SmsService.class);
    config = mockConfig();

    // 创建要测试的对象
    smsService = new BytePlusSmsService(config, smsDao, smsProducer, bytePlusClient);
  }

  private SmsProperties.BytePlusConfig mockConfig() {
    SmsProperties.BytePlusConfig cfg = new SmsProperties.BytePlusConfig();
    cfg.setId("byteplus-test");
    cfg.setAppKey("app-key");
    cfg.setAppSecret("app-secret");
    cfg.setMessageGroupId("account-id");
    cfg.setSmsFrom("from-number");
    cfg.setZhSignName("纷享销客");
    cfg.setEnSignName("ShareCRM");

    // 添加模板
    SmsStaticTemplate template = new SmsStaticTemplate();
    template.setContent("验证码：${code}");
    template.setProviderTemplateId("SMS_123");
    template.setTemplateId("123");
    template.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE);
    cfg.setTemplates(Map.of("code", template));

    return cfg;
  }

  @Test
  void sendSmsShouldSucceed() throws Exception {
    // 准备测试数据
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("验证码：123456");
    req.setPhones(List.of("***********"));
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(req);

    // Mock BytePlus响应
    SmsSendResponse.ResultBean resultBean = new SmsSendResponse.ResultBean();
    resultBean.setMessageId(List.of("MSG123456"));

    com.byteplus.model.response.SmsSendResponse mockResponse = new com.byteplus.model.response.SmsSendResponse();
    mockResponse.setCode("0");
    mockResponse.setMessage("Success");
    mockResponse.setResult(resultBean);

    // 设置mock行为
    when(bytePlusClient.send(any())).thenReturn(mockResponse);

    // 执行测试
    SmsSendResult result = smsService.sendSms("***********", wrapper);

    // 验证结果
    assertTrue(result.getResponse().isSuccess());
    assertEquals(req, result.getRequest());
    assertEquals(1, result.getResponse().getPhones().size());
  }

  @Test
  void sendSmsWithErrorShouldHandleFailure() throws Exception {
    // 准备测试数据
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("验证码：123456");
    req.setPhones(List.of("***********"));
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(req);

    // Mock错误响应
    com.byteplus.model.response.SmsSendResponse mockResponse = new com.byteplus.model.response.SmsSendResponse();
    mockResponse.setCode("Error");
    mockResponse.setMessage("Failed to send");

    // 设置mock行为
    when(bytePlusClient.send(any())).thenReturn(mockResponse);

    // 执行测试
    SmsSendResult result = smsService.sendSms("***********", wrapper);

    // 验证结果
    assertFalse(result.getResponse().isSuccess());
    assertEquals("Failed to send", result.getResponse().getMessage());
  }

  @Test
  void sendSmsWithExceptionShouldHandleError() throws Exception {
    // 准备测试数据
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("验证码：123456");
    req.setPhones(List.of("***********"));
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(req);

    // 设置异常行为
    when(bytePlusClient.send(any())).thenThrow(new RuntimeException("a unit test connection error"));

    // 执行测试
    SmsSendResult result = smsService.sendSms("***********", wrapper);

    // 验证结果
    assertFalse(result.getResponse().isSuccess());
    assertEquals("a unit test connection error", result.getResponse().getMessage());
  }

  @Test
  void sendByTemplateShouldSucceed() throws Exception {
    // 准备测试数据
    SmsTemplateEntity template = new SmsTemplateEntity();
    template.setContent("验证码：${code}");
    template.setProviderTemplateId("SMS_123");
    template.setInternational(false);

    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("***********"));
    request.setTemplateParam(Map.of("code", "123456"));
    request.setLanguage(SmsSdkConstants.LANGUAGE_ZH);

    SmsRequestWrapper<SmsSendByTemplate> wrapper = new SmsRequestWrapper<>(request, "batch123", "msg123");

    // Mock BytePlus响应
    SmsSendResponse.ResultBean resultBean = new SmsSendResponse.ResultBean();
    resultBean.setMessageId(List.of("MSG123456"));

    com.byteplus.model.response.SmsSendResponse mockResponse = new com.byteplus.model.response.SmsSendResponse();
    mockResponse.setCode("0");
    mockResponse.setMessage("Success");
    mockResponse.setResult(resultBean);

    // 设置mock行为
    when(bytePlusClient.send(any())).thenReturn(mockResponse);

    // 执行测试
    SmsSendResult result = smsService.sendByTemplate(wrapper, request, template);

    // 验证结果
    assertTrue(result.getResponse().isSuccess());
    assertEquals("batch123", result.getResponse().getBatchMsgId());
  }

  @Test
  void queryStaticTemplatesShouldFilterTemplates() {
    // 准备查询
    SmsTemplateQuery query = new SmsTemplateQuery();
    query.setInternational(false);
    query.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE);

    // 执行测试
    List<SmsStaticTemplate> templates = smsService.queryStaticTemplates(query);

    // 验证结果
    assertNotNull(templates);
    assertEquals(1, templates.size());
    assertEquals("byteplus-test-SMS_123", templates.get(0).getTemplateId());
  }

  @Test
  void smsStatusCallbackShouldUpdateStatus() throws Exception {
    // 准备测试数据
    BytePlusStatusReport report = new BytePlusStatusReport();
    report.setAccount("testAccount");
    report.setMobile("***********");
    report.setMessageId("msg123456");
    report.setStatusCode("0"); // 成功
    report.setDescription("成功");
    report.setSendTime(1649385600000L);
    report.setRecvTime(1649385660000L);

    String body = JsonUtil.toJson(List.of(report));
    MockServerWebExchange exchange = MockServerWebExchange.from(
        org.springframework.mock.http.server.reactive.MockServerHttpRequest.post("/callback").build());

    // 设置mock行为
    SmsMongoEntity entity = new SmsMongoEntity();
    when(smsDao.updateSingleSmsStatus(
        eq(config.getId()),
        eq("msg123456"),
        any(),
        eq(true),
        any(Date.class),
        eq("0"),
        eq("成功"))).thenReturn(entity);

    // 执行测试
    smsService.smsStatusCallback("test-ext", body);

    // 验证调用
    verify(smsDao).updateSingleSmsStatus(
        eq(config.getId()),
        eq("msg123456"),
        any(),
        eq(true),
        any(Date.class),
        eq("0"),
        eq("成功"));

    // 验证MQ消息
    ArgumentCaptor<org.apache.rocketmq.common.message.Message> messageCaptor = ArgumentCaptor
        .forClass(org.apache.rocketmq.common.message.Message.class);
    verify(smsProducer).send(messageCaptor.capture());

    org.apache.rocketmq.common.message.Message mqMessage = messageCaptor.getValue();
    assertEquals(SmsSdkConstants.MQ_TOPIC_SMS_TEXT, mqMessage.getTopic());
    assertEquals(SmsSdkConstants.MQ_TAG_SMS_STATUS, mqMessage.getTags());
  }
}