package com.sharecrm.egress.sms.share;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;

import java.util.List;

import static com.sharecrm.egress.sms.SmsSender.SUPPORT_CAPTCHA_INTL;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_INTL;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ImportAutoConfiguration(RefreshAutoConfiguration.class)
@SpringBootTest(classes = {MonternetIntlSender.class, SmsProperties.class},
  properties = {"sharecrm.api.sms.enabled=true",
    "sharecrm.api.sms.monternet-intl.enabled=true",
    "sharecrm.api.sms.monternet-intl.ip=127.0.0.1"})
class MonternetIntlSenderTest {

  @Autowired
  private MonternetIntlSender sender;

  @Test
  void send() {
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("test");
    assertDoesNotThrow(() -> sender.send("13551234567", new SmsRequestWrapper<>(request)));
    //不要改原值
    assertEquals("test", request.getContent());
  }

  @Test
  void supports() {
    //别乱改
    assertTrue(sender.supports().containsAll(List.of(SUPPORT_INTL, SUPPORT_CAPTCHA_INTL)));
  }

  @Test
  void provider() {
    assertFalse(sender.provider().isExclusive());
  }
}