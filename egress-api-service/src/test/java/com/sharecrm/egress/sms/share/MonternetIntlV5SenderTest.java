package com.sharecrm.egress.sms.share;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.sharecrm.egress.sms.SmsSender.SUPPORT_CAPTCHA_INTL;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_INTL;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class MonternetIntlV5SenderTest {
  private final OkHttpSupport httpClient = mock(OkHttpSupport.class);
  private final SmsProperties properties = mock(SmsProperties.class);
  private final MonternetIntlV5Sender sender = new MonternetIntlV5Sender(properties, httpClient);

  private SmsProperties.MonternetConfig mockProperties() {
    SmsProperties.MonternetConfig config = new SmsProperties.MonternetConfig();
    config.setIp("127.0.0.1");
    config.setPort(443);
    config.setAccount("test");
    config.setPassword("");
    return config;
  }

  @Test
  void send() {
    when(properties.getMonternetIntlV5()).thenReturn(mockProperties());
    when(httpClient.parseObject(any(), any())).thenReturn(mockHttpResponse());
    String phone = "***************";
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("中文短信");
    when(properties.getMonternetIntlV5()).thenReturn(mockProperties());
    SmsSendResult rs = sender.send(phone, new SmsRequestWrapper<>(req));
    assertTrue(rs.getResponse().isSuccess());
  }

  private MonternetIntlV5Response mockHttpResponse() {
    MonternetIntlV5Response response = new MonternetIntlV5Response();
    response.setResult("0");
    response.setDesc("success");
    return response;
  }

  @Test
  void supports() {
    assertTrue(sender.supports().containsAll(List.of(SUPPORT_INTL, SUPPORT_CAPTCHA_INTL)));
  }

}