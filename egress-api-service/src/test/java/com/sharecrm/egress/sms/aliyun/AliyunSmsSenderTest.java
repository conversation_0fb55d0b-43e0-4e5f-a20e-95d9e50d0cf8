package com.sharecrm.egress.sms.aliyun;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties.AliYunConfig;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.sms.AliyunUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class AliyunSmsSenderTest {

  private AliyunSmsSender sender;
  private AliyunSmsService smsService;
  private AliYunConfig config;
  private SmsDao smsDao;
  private AutoConfMQProducer smsProducer;

  @BeforeEach
  void setup() throws Exception {
    // 创建mock对象
    config = createConfig();
    smsDao = mock(SmsDao.class);
    smsProducer = mock(AutoConfMQProducer.class);
    smsService = mock(AliyunSmsService.class);

    // 使用静态方法mock
    try (MockedStatic<AliyunUtils> aliyunUtils = mockStatic(AliyunUtils.class)) {
      com.aliyun.dyvmsapi20170525.Client ttsClient = mock(com.aliyun.dyvmsapi20170525.Client.class);
      com.aliyun.dysmsapi20170525.Client smsClient = mock(com.aliyun.dysmsapi20170525.Client.class);
      aliyunUtils.when(() -> AliyunUtils.initAliyunTtsClient(any())).thenReturn(ttsClient);
      aliyunUtils.when(() -> AliyunUtils.initAliyunSmsClient(any())).thenReturn(smsClient);

      // 创建AliyunSmsSender实例
      sender = new AliyunSmsSender(config, smsDao, smsProducer);

      // 使用反射注入mock的smsService
      Field smsServiceField = AliyunSmsSender.class.getDeclaredField("aliyunSmsService");
      ReflectionUtils.makeAccessible(smsServiceField);
      smsServiceField.set(sender, smsService);
    }
  }

  private AliYunConfig createConfig() {
    AliYunConfig cfg = new AliYunConfig();
    cfg.setId("aliyun-test");
    cfg.setOrder(100);
    cfg.setSupports(List.of("normal", "intl", "voice"));
    cfg.setAppKey("test-key-id");
    cfg.setAppSecret("test-key-secret");
    return cfg;
  }

  @Test
  void sendTtsShouldDelegateToService() {
    // 准备测试数据
    TTSSendRequest request = new TTSSendRequest();
    request.setPhones(List.of("13800138000"));

    SmsSendResult expectedResult = new SmsSendResult();

    // 设置mock行为
    when(smsService.sendTts(request)).thenReturn(expectedResult);

    // 执行测试
    SmsSendResult result = sender.sendTts(request);

    // 验证结果
    assertSame(expectedResult, result);
    verify(smsService).sendTts(request);
  }

  @Test
  void sendShouldDelegateToService() {
    // 准备测试数据
    String phone = "13800138000";
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("验证码：123456");
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(request);

    SmsSendResult expectedResult = new SmsSendResult();

    // 设置mock行为
    when(smsService.sendSms(eq(phone), any())).thenReturn(expectedResult);

    // 执行测试
    SmsSendResult result = sender.send(phone, wrapper);

    // 验证结果
    assertSame(expectedResult, result);
    verify(smsService).sendSms(eq(phone), any());
  }

  @Test
  void staticTemplatesShouldDelegateToService() {
    // 准备测试数据
    SmsTemplateQuery query = new SmsTemplateQuery();
    List<SmsStaticTemplate> expectedTemplates = List.of(new SmsStaticTemplate());

    // 设置mock行为
    when(smsService.queryStaticTemplates(query)).thenReturn(expectedTemplates);

    // 执行测试
    List<SmsStaticTemplate> result = sender.staticTemplates(query);

    // 验证结果
    assertSame(expectedTemplates, result);
    verify(smsService).queryStaticTemplates(query);
  }

  @Test
  void addTemplateShouldDelegateToService() {
    // 准备测试数据
    SmsTemplateRequest request = new SmsTemplateRequest();
    SmsTemplateDetail expectedDetail = new SmsTemplateDetail();

    // 设置mock行为
    when(smsService.addTemplate(request)).thenReturn(expectedDetail);

    // 执行测试
    SmsTemplateDetail result = sender.addTemplate(request);

    // 验证结果
    assertSame(expectedDetail, result);
    verify(smsService).addTemplate(request);
  }

  @Test
  void sendByTemplateShouldDelegateToService() {
    // 准备测试数据
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("13800138000"));
    SmsTemplateEntity template = new SmsTemplateEntity();

    SmsSendResult expectedResult = new SmsSendResult();

    // 设置mock行为
    when(smsService.sendByTemplate(any(), eq(request), eq(template))).thenReturn(expectedResult);

    // 执行测试
    SmsSendResult result = sender.sendByTemplate(request, template);

    // 验证结果
    assertSame(expectedResult, result);
    verify(smsService).sendByTemplate(any(), eq(request), eq(template));
  }

  @Test
  void updateSmsStatusShouldDelegateToService() {
    // 执行测试
    sender.updateSmsStatus();

    // 验证调用
    verify(smsService).updateSmsStatus();
  }

  @Test
  void updateTemplateStatusShouldDelegateToService() {
    // 执行测试
    sender.updateTemplateStatus();

    // 验证调用
    verify(smsService).updateTemplateStatus();
  }

  @Test
  void providerShouldReturnConfig() {
    // 执行测试并验证
    assertSame(config, sender.provider());
  }

  @Test
  void supportsShouldReturnConfigSupports() {
    // 执行测试
    List<String> supports = sender.supports();

    // 验证结果
    assertEquals(List.of("normal", "intl", "voice"), supports);
  }

  @Test
  void getOrderShouldReturnConfigOrder() {
    // 执行测试
    int order = sender.getOrder();

    // 验证结果
    assertEquals(100, order);
  }
}