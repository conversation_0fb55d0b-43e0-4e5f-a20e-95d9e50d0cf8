package com.sharecrm.egress.sms.aliyun;

import com.aliyun.dysmsapi20170525.models.CreateSmsTemplateResponse;
import com.aliyun.dysmsapi20170525.models.CreateSmsTemplateResponseBody;
import com.aliyun.dysmsapi20170525.models.GetSmsTemplateResponse;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponse;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponseBody;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.dysmsapi20170525.models.SendSmsResponseBody;
import com.aliyun.dyvmsapi20170525.Client;
import com.aliyun.dyvmsapi20170525.models.SingleCallByTtsRequest;
import com.aliyun.dyvmsapi20170525.models.SingleCallByTtsResponse;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.entity.TtsTemplate;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class AliyunSmsServiceTest {

  private final SmsDao smsDao = mock(SmsDao.class);
  private final Client ttsClient = mock(Client.class);
  private final com.aliyun.dysmsapi20170525.Client smsClient = mock(com.aliyun.dysmsapi20170525.Client.class);
  private final SmsProperties.AliYunConfig config = mockConfig();
  private final AliyunSmsService service = new AliyunSmsService(config, smsDao, null, ttsClient, smsClient);

  private SmsProperties.AliYunConfig mockConfig() {
    SmsProperties.AliYunConfig cfg = new SmsProperties.AliYunConfig();
    cfg.setTtsTemplates(Map.of("300", new TtsTemplate(300, "TTS_300", "test template")));
    SmsStaticTemplate template = new SmsStaticTemplate();
    template.setContent("验证码：${code}");
    template.setProviderTemplateId("SMS_123");
    template.setTemplateId("123");
    cfg.setTemplates(Map.of("code", template));
    return cfg;
  }

  @Test
  void singleCallByTtsRequest() {
    SingleCallByTtsRequest req = service.singleCallByTtsRequest("13511352388", "123", Map.of());
    assertEquals("123", req.getTtsCode());
    assertNull(req.getCalledShowNumber());
    config.setShowNumber("13511352388");
    req = service.singleCallByTtsRequest("13511352388", "123", Map.of());
    assertEquals("13511352388", req.getCalledShowNumber());
  }

  @Test
  @SneakyThrows
  void sendTts() {
    TTSSendRequest req = new TTSSendRequest();
    req.setTemplateId(300);
    req.setPhones(List.of("13511352388"));
    when(ttsClient.singleCallByTts(any())).thenReturn(new SingleCallByTtsResponse());
    SmsSendResult rs = service.sendTts(req);
    assertNotNull(rs.getTtsSendRequest());
    assertNotNull(rs.getContent());
  }

  @Test
  @SneakyThrows
  void sendSms() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("验证码：123456");
    req.setPhones(List.of("13512345678", "15812345678"));
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(req);

    SendSmsResponse t = new SendSmsResponse();
    t.setBody(new SendSmsResponseBody());
    when(smsClient.sendSms(any())).thenReturn(t);
    SmsSendResult rs = service.sendSms("13512345678", wrapper);
    assertEquals("验证码：123456", rs.getContent());
    //避免多发，Request中可能包含多个手机号，但是只发指定的那一个
    assertEquals(1, rs.getResponse().getPhones().size());
  }

  @Test
  @SneakyThrows
  void addTemplate() {
    SmsTemplateRequest template = new SmsTemplateRequest();
    template.setContent("${PLAT.NAME}的验证码是：${code}");
    template.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE);
    CreateSmsTemplateResponse rsp = new CreateSmsTemplateResponse();
    CreateSmsTemplateResponseBody body = new CreateSmsTemplateResponseBody();
    body.setCode("OK");
    body.setTemplateCode("SMS_123456");
    rsp.setBody(body);
    when(smsClient.createSmsTemplate(any())).thenReturn(rsp);
    SmsTemplateDetail detail = service.addTemplate(template);
    assertNotNull(detail.getTemplateId());
    assertNotNull(detail.getStatus());
    assertNotNull(detail.getProviderName());
  }

  @Test
  @SneakyThrows
  void updateSmsStatus() {
    // 因为在Task中运行，自行保护，永远不要抛出异常
    assertDoesNotThrow(service::updateSmsStatus);

    SmsMongoEntity e = new SmsMongoEntity();
    e.setSerialId("");
    e.setPhone("13512345678");
    e.setSendTime(new Date());
    SmsMongoEntity withId = new SmsMongoEntity();
    withId.setSerialId("123456789");
    withId.setPhone("13512345678");
    withId.setSendTime(new Date());

    SmsMongoEntity tts = new SmsMongoEntity();
    tts.setSerialId("123456789");
    tts.setPhone("13512345678");
    tts.setSendTime(new Date());
    tts.setSmsType(SmsSdkConstants.SMS_TYPE_TTS);

    when(smsDao.querySmsWithNoReplay(any())).thenReturn(List.of(e, withId, tts));
    QuerySendDetailsResponse rsp = new QuerySendDetailsResponse();
    rsp.setBody(new QuerySendDetailsResponseBody());
    when(smsClient.querySendDetails(any())).thenReturn(rsp);
    assertDoesNotThrow(service::updateSmsStatus);
    //要求过滤掉无效ID
    verify(smsClient, times(1)).querySendDetails(any());
  }

  @Test
  @SneakyThrows
  void updateTemplateStatus() {
    SmsTemplateEntity e2 = new SmsTemplateEntity();
    e2.setSendTime(new Date());
    // 现在时间过滤在DAO层完成，所以只返回符合条件的记录
    when(smsDao.queryTemplates(any())).thenReturn(List.of(e2));
    when(smsClient.getSmsTemplate(any())).thenReturn(new GetSmsTemplateResponse());
    assertDoesNotThrow(service::updateTemplateStatus);
    // 由于时间过滤已移到DAO层，所以只处理符合条件的记录
    verify(smsClient, times(1)).getSmsTemplate(any());
  }

}