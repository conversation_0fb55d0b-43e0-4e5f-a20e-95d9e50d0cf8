package com.sharecrm.egress.sms.mengniu;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import reactor.util.function.Tuple2;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = MengniuSmsService.class,
  properties = {"sharecrm.api.sms.enabled=true", "sharecrm.api.sms.mengniu.enabled=true"})
class MengniuSmsServiceTest {

  private static final String CALLBACK_ID = "c-id";

  @MockitoBean
  private SmsProperties properties;
  @MockitoBean(name = "mengniuHttpSupport")
  private OkHttpSupport httpClient;
  @MockitoBean
  private SmsDao smsDao;
  @MockitoBean(name = "smsRocketMQProducer")
  private ObjectProvider<AutoConfMQProducer> smsProducer;

  @Autowired
  private MengniuSmsService service;

  @BeforeEach
  void setup() {
    SmsProperties.MengniuConfig config = new SmsProperties.MengniuConfig();
    config.setApiGateway("https://localhost");
    config.setClientId("id");
    config.setClientSecret("secret");
    config.setCallbackId(CALLBACK_ID);
    config.setCallbackSecret("c-secret");

    when(properties.getMengniu()).thenReturn(config);
  }

  @Test
  void sendSms() {
    //没有短信的时候抛出异常
    assertThrows(SmsException.class, () -> service.sendSms("13512345678", mockRequest()));
  }

  @NotNull
  private static SmsRequestWrapper<SmsSendRequest> mockRequest() {
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("test");
    request.setPhones(List.of("13512345678"));
    return new SmsRequestWrapper<>(request);
  }


  @Test
  void convertTemplateRequest() {

    SmsTemplateRequest request = new SmsTemplateRequest();
    request.setContent("尊敬的用户：${txt}，你的地址是 ${city.address}");
    request.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_PROMOTION);
    request.setName("测试模板");
    Tuple2<MengniuRequest<MengniuTemplateRequest>, Map<String, String>> rs = service.convertTemplateRequest(request);

    MengniuTemplateRequest data = rs.getT1().getData();

    assertEquals(request.getName(), data.getTemplateName());
    assertEquals("尊敬的用户：${txt}，你的地址是 ${city_address}", data.getTemplateContent());
    assertEquals("city_address", rs.getT2().get("city.address"));

  }

  @Test
  void smsSendRequestWrapper() {
    SmsTemplateEntity template = mockTemplate();

    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("13512345678"));
    request.setTemplateParam(Map.of("city.address", "北京市", "txt", "欢迎光临"));
    request.setTemplateId("test-id");

    SmsProperties.MengniuConfig config = new SmsProperties.MengniuConfig();
    config.setTemplates(Map.of("t01", new SmsStaticTemplate()));
    when(properties.getMengniu()).thenReturn(config);

    MengniuRequest<MengniuSmsRequest> rs = service.smsSendRequestWrapper(request, template);

    MengniuSmsRequest data = rs.getDatas();
    assertEquals("SendSms", data.getAction());
    assertEquals("13512345678", data.getPhoneNumbers());
    assertEquals(template.getProviderTemplateId(), data.getTemplateCode());
    assertEquals("{\"txt\":\"欢迎光临\",\"city_address\":\"北京市\"}", data.getTemplateParam());

  }

  @NotNull
  private SmsTemplateEntity mockTemplate() {
    SmsTemplateEntity template = new SmsTemplateEntity();
    template.setTemplateId("test-id");
    String providerTemplateId = "mengniu-test";
    template.setProviderTemplateId(providerTemplateId);
    template.setParamReplace("{\"city.address\":\"city_address\"}");
    template.setContent("尊敬的用户：${txt}，你的地址是 ${city.address}");
    template.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_PROMOTION);
    return template;
  }

  @Test
  void addTemplate() {
    SmsTemplateRequest request = new SmsTemplateRequest();
    request.setContent("尊敬的用户：${txt}，你的地址是 ${city.address}");
    request.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_PROMOTION);
    request.setName("测试模板");
    MengniuResponse<MengniuTemplateResp> resp = new MengniuResponse<>();
    resp.setSuccess(true);
    MengniuTemplateResp data = new MengniuTemplateResp();
    data.setSuccess(true);
    data.setDatas("test-id");
    resp.setData(data);
    when(httpClient.parseObject(any(), any())).thenReturn(resp);
    SmsTemplateDetail detail = service.addTemplate(request);
    assertNotNull(detail.getTemplateId());
    assertEquals(request.getName(), detail.getName());
    //模板是原样返回的，不能替换变量
    assertEquals(request.getContent(), detail.getContent());
    assertEquals(request.getTemplateType(), detail.getTemplateType());
  }

  @Test
  void sendByTemplate() {
    SmsTemplateEntity template = mockTemplate();
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("13512345678"));
    request.setTemplateParam(Map.of("city.address", "北京市", "txt", "欢迎光临"));
    request.setTemplateId("test-id");

    MengniuResponse<MengniuSmsResponse> resp = new MengniuResponse<>();

    when(httpClient.parseObject(any(), any())).thenReturn(resp);
    SmsSendResult result = service.sendByTemplate(mockRequest(), request, template);
    assertEquals(template.getTemplateType(), result.getSmsType());
    assertEquals("尊敬的用户：欢迎光临，你的地址是 北京市", result.getContent());

  }

  @Test
  void checkCallback() {
    MockServerWebExchange exchange = MockServerWebExchange.from(MockServerHttpRequest
      .post("/api/v1/sms/callback")
      .header("ak", CALLBACK_ID)
      .header("token", "token")
      .header("timestamp", "" + System.currentTimeMillis())
      .build());
    assertThrows(SmsException.class, () -> service.checkCallback("", exchange));
    assertThrows(SmsException.class, () -> service.checkCallback("[{}]", exchange));
    assertDoesNotThrow(() -> service.smsStatusCallback("{}", exchange));
  }

  @Test
  void updateTemplateStatus() {
    SmsTemplateEntity entity = new SmsTemplateEntity();
    entity.setProviderTemplateId("mengniu-test");
    entity.setSendTime(new Date());
    when(smsDao.queryTemplates(any(SmsTemplateQuery.class))).thenReturn(List.of(entity));
    MengniuResponse<MengniuTemplateStatusResp> resp = new MengniuResponse<>();
    resp.setSuccess(true);
    MengniuTemplateStatusResp data = new MengniuTemplateStatusResp();
    data.setSuccess(true);
    MengniuTemplateStatusData datas = new MengniuTemplateStatusData();
    datas.setStatus("APPROVED");
    data.setDatas(datas);
    resp.setData(data);

    when(httpClient.parseObject(any(), any())).thenReturn(resp);
    assertDoesNotThrow(() -> service.updateTemplateStatus());
  }

}