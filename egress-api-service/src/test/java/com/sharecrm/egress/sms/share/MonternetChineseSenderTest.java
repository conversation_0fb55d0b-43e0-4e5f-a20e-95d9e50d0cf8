package com.sharecrm.egress.sms.share;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;

import static com.sharecrm.egress.sms.SmsSender.SUPPORT_CHINESE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ImportAutoConfiguration(RefreshAutoConfiguration.class)
@SpringBootTest(classes = {MonternetChineseSender.class, SmsProperties.class},
  properties = {"sharecrm.api.sms.enabled=true",
    "sharecrm.api.sms.monternet-internal.enabled=true",
    "sharecrm.api.sms.monternet-internal.ip=127.0.0.1"})
class MonternetChineseSenderTest {
  
  @MockitoBean
  private SmsDao smsDao;
  
  @MockitoBean(name = "smsRocketMQProducer")
  private AutoConfMQProducer smsProducer;

  @Autowired
  private MonternetChineseSender sender;

  @Test
  void send() {
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("test");
    assertDoesNotThrow(() -> sender.send("13551234567", new SmsRequestWrapper<>(request)));
    //不要因为签名改了原值
    assertEquals("test", request.getContent());
  }

  @Test
  void provider() {
    assertFalse(sender.provider().isExclusive());
  }

  @Test
  void supports() {
    //别乱改
    assertTrue(sender.supports().contains(SUPPORT_CHINESE));
  }
}