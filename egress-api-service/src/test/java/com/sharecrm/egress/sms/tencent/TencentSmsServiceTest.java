package com.sharecrm.egress.sms.tencent;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.entity.TtsTemplate;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.AddSmsTemplateResponse;
import com.tencentcloudapi.sms.v20210111.models.AddTemplateStatus;
import com.tencentcloudapi.sms.v20210111.models.DescribeSmsTemplateListResponse;
import com.tencentcloudapi.sms.v20210111.models.PullSmsSendStatus;
import com.tencentcloudapi.sms.v20210111.models.PullSmsSendStatusByPhoneNumberResponse;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.SneakyThrows;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class TencentSmsServiceTest {

  private final SmsDao smsDao = mock(SmsDao.class);
  private final SmsClient smsClient = mock(SmsClient.class);
  private final SmsProperties.TencentConfig config = mockConfig();
  private final TencentSmsService service = new TencentSmsService(config, smsDao, null, smsClient);

  private SmsProperties.TencentConfig mockConfig() {
    SmsProperties.TencentConfig cfg = new SmsProperties.TencentConfig();
    cfg.setHttpProxy("https://localhost:8080");

    cfg.setTtsTemplates(Map.of("300", new TtsTemplate(300, "TTS_300", "test template")));
    SmsStaticTemplate template = new SmsStaticTemplate();
    template.setContent("验证码：{1}");
    template.setProviderTemplateId("SMS_123");
    template.setTemplateId("123");
    cfg.setTemplates(Map.of("code", template));

    return cfg;
  }

  @Test
  @SneakyThrows
  void sendSms() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("验证码：123456");
    req.setPhones(List.of("13512345678", "15812345678"));
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(req);

    SendSmsResponse t = new SendSmsResponse();
    when(smsClient.SendSms(any())).thenReturn(t);
    SmsSendResult rs = service.sendSms("13512345678", wrapper);
    assertEquals("验证码：123456", rs.getContent());
    //避免多发，Request中可能包含多个手机号，但是只发指定的那一个
    assertEquals(1, rs.getResponse().getPhones().size());
  }

  @Test
  @SneakyThrows
  void addTemplate() {
    SmsTemplateRequest template = new SmsTemplateRequest();
    template.setContent("${PLAT.NAME}的验证码是：${code}");
    template.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE);
    com.tencentcloudapi.sms.v20210111.models.AddSmsTemplateResponse rsp = new AddSmsTemplateResponse();
    AddTemplateStatus body = new AddTemplateStatus();
    body.setTemplateId("123456");
    rsp.setAddTemplateStatus(body);
    when(smsClient.AddSmsTemplate(any())).thenReturn(rsp);
    SmsTemplateDetail detail = service.addTemplate(template);
    assertNotNull(detail.getTemplateId());
    assertNotNull(detail.getStatus());
    assertNotNull(detail.getProviderName());
  }

  @Test
  @SneakyThrows
  void updateSmsStatus() {
    // 因为在Task中运行，自行保护，永远不要抛出异常
    assertDoesNotThrow(service::updateSmsStatus);

    SmsMongoEntity e = new SmsMongoEntity();
    e.setSerialId("");
    e.setPhone("13512345678");
    e.setSendTime(new Date());
    SmsMongoEntity withId = new SmsMongoEntity();
    withId.setSerialId("123456789");
    withId.setPhone("13512345678");
    withId.setSendTime(new Date());

    SmsMongoEntity tts = new SmsMongoEntity();
    tts.setSerialId("123456789");
    tts.setPhone("13512345678");
    tts.setSendTime(new Date());
    tts.setSmsType(SmsSdkConstants.SMS_TYPE_TTS);

    when(smsDao.querySmsWithNoReplay(any())).thenReturn(List.of(e, withId, tts));
    PullSmsSendStatusByPhoneNumberResponse rsp = new PullSmsSendStatusByPhoneNumberResponse();
    rsp.setPullSmsSendStatusSet(new PullSmsSendStatus[]{new PullSmsSendStatus()});
    when(smsClient.PullSmsSendStatusByPhoneNumber(any())).thenReturn(rsp);
    assertDoesNotThrow(service::updateSmsStatus);
    //要求过滤掉无效ID
    verify(smsClient, times(1)).PullSmsSendStatusByPhoneNumber(any());
  }

  @Test
  @SneakyThrows
  void updateTemplateStatus() {
    SmsTemplateEntity e = new SmsTemplateEntity();
    e.setSendTime(DateUtils.addMonths(new Date(), -7));
    SmsTemplateEntity e2 = new SmsTemplateEntity();
    e2.setProviderTemplateId("12345");
    e2.setSendTime(new Date());
    when(smsDao.queryTemplates(any())).thenReturn(List.of(e, e2));
    when(smsClient.DescribeSmsTemplateList(any())).thenReturn(new DescribeSmsTemplateListResponse());
    assertDoesNotThrow(service::updateTemplateStatus);
    //要求过滤掉过期时间的
    verify(smsClient, times(1)).DescribeSmsTemplateList(any());
  }
}