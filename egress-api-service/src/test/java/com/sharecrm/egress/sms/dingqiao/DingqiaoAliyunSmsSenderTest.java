package com.sharecrm.egress.sms.dingqiao;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.sms.SmsSender;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ImportAutoConfiguration(RefreshAutoConfiguration.class)
@SpringBootTest(classes = {DingqiaoAliyunSmsSender.class, SmsProperties.class},
  properties = {"sharecrm.api.sms.enabled=true",
    "sharecrm.api.sms.dingqiao-ali.enabled=true",
    "sharecrm.api.sms.dingqiao-ali.supports=send-by-template-chinese,add-template-chinese"})

class DingqiaoAliyunSmsSenderTest {

  @MockitoBean
  private SmsDao smsDao;

  @MockitoBean
  private AutoConfMQProducer autoConfMQProducer;

  @Autowired
  private DingqiaoAliyunSmsSender sender;

  @Test
  void provider() {
    //ID不可修改
    assertEquals("dingqiao-aliyun", sender.provider().getId());
  }

  @Test
  void supports() {
    assertTrue(sender.supports().contains(SmsSender.SUPPORT_ADD_TEMPLATE_CHINESE));
    assertTrue(sender.supports().contains(SmsSender.SUPPORT_SEND_TEMPLATE_CHINESE));
  }

}