package com.sharecrm.egress.sms;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsPageRequest;
import com.sharecrm.egress.entity.SmsPageResult;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import com.sharecrm.egress.service.DirtyWordService;
import com.sharecrm.egress.service.RedisService;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sharecrm.egress.sms.SmsSender.SUPPORT_CAPTCHA_INTL;
import static com.sharecrm.egress.sms.SmsSender.SUPPORT_INTL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = SmsReallyService.class,
  properties = {"sharecrm.api.sms.enabled=true"})
class SmsReallyServiceTest {


  @MockitoBean
  private RedisService redisService;
  @MockitoBean
  private DirtyWordService dirtyWordService;
  @MockitoBean
  private SmsProperties smsProperties;
  @MockitoBean
  private ApplicationContext applicationContext;

  @MockitoBean(name = "taskScheduler")
  private AsyncTaskExecutor executor;

  @MockitoBean
  private SmsDao smsDao;
  @MockitoBean
  private ObjectProvider<AutoConfMQProducer> smsProducer;

  @Autowired
  private SmsReallyService service;

  @Test
  void notUsed() {
    assertNotNull(service);
  }


  @Test
  void selectIntlFilterType() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("test");
    assertEquals(SUPPORT_INTL, service.selectIntlFilterType(req));
    req.setContent("Verify Code:123456");
    assertEquals(SUPPORT_CAPTCHA_INTL, service.selectIntlFilterType(req));
  }

  @Test
  void onErrorResumeResult() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("test");
    req.setPhones(List.of("13512345678"));
    SmsSendResult rs = service.onErrorResumeResult(req, new SmsException("test"));
    assertFalse(rs.getResponse().isSuccess());
  }

  @Test
  void singleMongoEntity() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("test");
    req.setPhones(List.of("13512345678"));
    SmsSendResult rs = service.onErrorResumeResult(req, new SmsException("test"));
    // 凑齐家族
    rs.setTemplateRequest(new SmsSendByTemplate());
    rs.setTtsSendRequest(new TTSSendRequest());
    rs.setProvider(new SmsProperties.MengniuConfig());
    rs.setContent("test-rs");
    SmsMongoEntity entity = service.singleMongoEntity(rs, rs.getResponse().getPhones().getFirst());
    assertNotNull(entity);
    assertEquals(rs.getContent(), entity.getContent());
  }

  @Test
  void validateIgnoreOrRateLimit() {
    when(smsProperties.getIgnorePhones()).thenReturn(List.of("136*"));
    when(smsProperties.getIgnoreLimitBizNames()).thenReturn(List.of("ignore"));
    assertFalse(service.validateIgnoreOrRateLimit("13612345678", "any", RedisService.KEY_SMS).getT1());
    assertFalse(service.validateIgnoreOrRateLimit("15912345678", "not", RedisService.KEY_SMS).getT1());
    assertTrue(service.validateIgnoreOrRateLimit("15912345678", "ignore", RedisService.KEY_SMS).getT1());
  }

//  @Test
//  void distinctAndStaticFirst() {
//
//    SmsTemplateDetail t = new SmsTemplateDetail();
//    t.setTemplateId("database");
//    t.setProviderId("test");
//    t.setName("test");
//    t.setContent("test");
//    Stream<SmsTemplateDetail> dbTemplates = Stream.of(t);
//
//    SmsTemplateDetail t2 = new SmsTemplateDetail();
//    t2.setTemplateId("static");
//    t2.setProviderId("test");
//    t2.setName("test");
//    t2.setContent("test");
//    Stream<SmsTemplateDetail> staticTemplates = Stream.of(t2);
//    List<SmsTemplateDetail> rs = service.distinctAndStaticFirst(staticTemplates, dbTemplates);
//    assertEquals(t2.getTemplateId(), rs.getFirst().getTemplateId());
//  }

  @Test
  void sendChineseSms() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("test");
    req.setPhones(List.of("13512345678"));
    MockServerWebExchange exchange = mockExchange();
    StepVerifier
      .create(service.sendChineseSms(req, exchange))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @NotNull
  private static MockServerWebExchange mockExchange() {
    return MockServerWebExchange.from(MockServerHttpRequest
      .post("/api/v1/sms/test")
      .header("x-fs-ei", "9901")
      .build());
  }

  @Test
  void sendIntlSms() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("test");
    req.setPhones(List.of("08613512345678"));
    MockServerWebExchange exchange = mockExchange();
    StepVerifier
      .create(service.sendIntlSms(req, exchange))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void sendCodeCall() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("123456");
    req.setPhones(List.of("13512345678"));
    StepVerifier
      .create(service.sendCodeCall(req, mockExchange()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryExclusiveProvider() {
    StepVerifier
      .create(service.queryExclusiveProvider(1, mockExchange()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryTemplates() {
    SmsTemplateEntity entity = new SmsTemplateEntity();
    entity.setProviderTemplateId("mengniu-test");
    when(smsDao.queryTemplates(any(SmsTemplateQuery.class))).thenReturn(List.of(entity));
    StepVerifier
      .create(service.queryTemplates(new SmsTemplateQuery(), mockExchange()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryTemplate() {
    SmsTemplateEntity entity = new SmsTemplateEntity();
    entity.setProviderTemplateId("mengniu-test");
    entity.setTemplateId("mengniu-test");
    when(smsDao.queryTemplates(any(SmsTemplateQuery.class))).thenReturn(List.of(entity));
    StepVerifier
      .create(service.queryTemplate("mengniu-test", mockExchange()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void addTemplate() {
    SmsTemplateRequest request = new SmsTemplateRequest();
    request.setContent("尊敬的用户：${txt}，你的地址是 ${city.address}");
    request.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_PROMOTION);
    request.setName("测试模板");
    StepVerifier
      .create(service.addTemplate(request, mockExchange()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void sendByTemplate() {
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setPhones(List.of("13512345678"));
    request.setTemplateParam(Map.of("city.address", "北京市", "txt", "欢迎光临"));
    request.setTemplateId("test-id");
    StepVerifier
      .create(service.sendByTemplate(request, mockExchange()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryHistories() {
    when(smsDao.findHistories(any())).thenReturn(mockSmsPageResult());
    StepVerifier
      .create(service.queryHistories(new SmsPageRequest()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @NotNull
  private SmsPageResult mockSmsPageResult() {
    SmsPageResult rs = new SmsPageResult();
    SmsMongoEntity entity = new SmsMongoEntity();
    entity.setContent("test");
    rs.setResults(List.of(entity));
    return rs;
  }

  @Test
  void querySmsStatus() {
    when(smsDao.findHistories(any())).thenReturn(mockSmsPageResult());
    StepVerifier
      .create(service.querySmsStatus(new SmsPageRequest()))
      //必须抹除敏感信息，内容中包含验证码不能对外开放
      .expectNextMatches(s -> Objects.isNull(s.getResults().getFirst().getContent()))
      .verifyComplete();
  }

  @Test
  void sendTts() {
    TTSSendRequest req = new TTSSendRequest();
    req.setPhones(Arrays.asList("13512345678"));
    StepVerifier.create(service.sendTts(req, mockExchange()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

}