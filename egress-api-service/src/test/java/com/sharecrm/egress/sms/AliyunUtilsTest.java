package com.sharecrm.egress.sms;

import com.aliyun.dysmsapi20170525.Client;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.entity.TtsTemplate;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.TTSSendRequest;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import reactor.util.function.Tuple2;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class AliyunUtilsTest {


  @NotNull
  private SmsProperties.AliYunConfig mockAliConfig() {
    SmsProperties.AliYunConfig config = new SmsProperties.AliYunConfig();
    config.setAppKey("appKey");
    config.setAppSecret("appSecret");
    config.setHttpProxy("https://localhost:8080");
    return config;
  }

  @Test
  void buildTtsParams() {
    // test buildTtsParams
    TtsTemplate template = new TtsTemplate();
    template.setTemplateId(1);
    template.setContent("你的验证码是：${code}，有效期 ${min} 分钟");
    TTSSendRequest request = new TTSSendRequest();
    request.setTemplateParams(new String[]{"12345", "10"});
    Map<String, String> params = AliyunUtils.buildTtsParams(template, request);
    assertEquals("12345", params.get("code"));
    assertEquals("10", params.get("min"));
  }

  @Test
  void selectTtsTemplate() {
    //没有的时候抛出异常
    assertThrows(SmsException.class, () -> AliyunUtils.selectTtsTemplate(888, List.of()));
    TtsTemplate template = new TtsTemplate();
    template.setTemplateId(888);
    template.setProviderTemplateId("test");
    assertEquals("test", AliyunUtils.selectTtsTemplate(888, List.of(template)).getProviderTemplateId());
  }

  @Test
  void selectSmsTemplateOrder() {
    //这两个模板都能匹配上，如果不排序的话，取的变量值可能是 "123456 (Do not tell others)", 而不是 "123456"
    SmsStaticTemplate codeEnShort = new SmsStaticTemplate();
    codeEnShort.setProviderTemplateId("code-test-id-en-short");
    codeEnShort.setTemplateId("test-code-default-en-short");
    codeEnShort.setContent("Verification Code: ${code} , valid for 15 mins , please complete the verification as soon as possible.");

    SmsStaticTemplate codeEn = new SmsStaticTemplate();
    codeEn.setProviderTemplateId("code-test-id-en");
    codeEn.setTemplateId("test-code-default-en");
    codeEn.setContent("Verification Code: ${code} (Do not tell others), valid for 15 mins , please complete the verification as soon as possible.");

    SmsSendRequest request = new SmsSendRequest();
    request.setContent("Verification Code: 123456 (Do not tell others), valid for 15 mins , please complete the verification as soon as possible.");
    Tuple2<SmsSendByTemplate, SmsTemplateEntity> rs = AliyunUtils.selectSmsTemplate("13512345678", request, List.of(codeEnShort, codeEn));

    assertEquals("code-test-id-en", rs.getT2().getProviderTemplateId());
    assertEquals("123456", rs.getT1().getTemplateParam().get("code"));

    //有时候短信内容中又缺少空格    
    request.setContent("Verification Code:654321, valid for 15 mins , please complete the verification as soon as possible.");
    rs = AliyunUtils.selectSmsTemplate("13512345678", request, List.of(codeEnShort, codeEn));

    assertEquals("code-test-id-en-short", rs.getT2().getProviderTemplateId());
    assertEquals("654321", rs.getT1().getTemplateParam().get("code"));

    request.setContent("Verification Code: 654321 , valid for 15 mins , please complete the verification as soon as possible.");
    rs = AliyunUtils.selectSmsTemplate("13512345678", request, List.of(codeEnShort, codeEn));

    assertEquals("code-test-id-en-short", rs.getT2().getProviderTemplateId());
    assertEquals("654321", rs.getT1().getTemplateParam().get("code"));
  }

  @Test
  void selectSmsTemplate() {
    SmsSendRequest request = new SmsSendRequest();
    request.setContent("你的验证码是：12345");
    //没有短信的时候抛出异常
    assertThrows(SmsException.class, () -> AliyunUtils.selectSmsTemplate("13512345678", request, List.of()));

    SmsStaticTemplate codeTemplate = new SmsStaticTemplate();
    codeTemplate.setProviderTemplateId("code-test");
    codeTemplate.setTemplateId("test");
    codeTemplate.setContent("你的验证码是：${code}");


    Tuple2<SmsSendByTemplate, SmsTemplateEntity> rs = AliyunUtils.selectSmsTemplate("13512345678", request, List.of(codeTemplate));
    assertEquals("code-test", rs.getT2().getProviderTemplateId());


    SmsStaticTemplate addUserTemplate = new SmsStaticTemplate();
    addUserTemplate.setProviderTemplateId("test-add-user");
    addUserTemplate.setTemplateId("test-add-user-id");
    addUserTemplate.setContent("贵公司部署了纷享销客连接型CRM，企业名称 ${name}。");

    //测试*号正则
    request.setContent("贵公司部署了纷享销客连接型CRM，企业名称 河北**公司。");

    rs = AliyunUtils.selectSmsTemplate("13512345678", request, List.of(addUserTemplate));
    assertEquals("test-add-user", rs.getT2().getProviderTemplateId());

    //如果找不到完全匹配的模板，使用通用的模板
    SmsStaticTemplate all = new SmsStaticTemplate();
    String allId = "all-test";
    all.setAsDefaultContent(true);
    all.setProviderTemplateId(allId);
    all.setTemplateId("test-all");
    all.setContent("尊敬的用户：${txt}");

    request.setContent("欢迎光临");
    rs = AliyunUtils.selectSmsTemplate("13512345678", request, List.of(codeTemplate, all));

    assertEquals(allId, rs.getT2().getProviderTemplateId());


    //如果找不到完全匹配的模板，又是验证码短信，优先选择验证码模板
    SmsStaticTemplate code = new SmsStaticTemplate();
    String codeId = "code-test-id";
    code.setAsDefaultCaptcha(true);
    code.setProviderTemplateId(codeId);
    code.setTemplateId("test-code-default");
    code.setContent("验证码：${code}（请勿转告他人），有效期15分钟，请尽快完成验证。");

    //干扰因素
    SmsStaticTemplate codeEn = new SmsStaticTemplate();
    codeEn.setAsDefaultCaptcha(true);
    codeEn.setProviderTemplateId("code-test-id-en");
    codeEn.setTemplateId("test-code-default-en");
    codeEn.setContent("Verification Code：${code} (Do not tell others), valid for 15 mins , please complete the verification as soon as possible.");
    codeEn.setLanguages(List.of(SmsSdkConstants.LANGUAGE_EN));

    //虽然内容不完全匹配，但都是验证码，优先走验证码
    request.setContent("验证码：888999（请勿转告他人），有效期10分钟。");
    rs = AliyunUtils.selectSmsTemplate("13512345678", request, List.of(codeTemplate, all, code, codeEn));

    assertEquals(codeId, rs.getT2().getProviderTemplateId());
    assertEquals("888999", rs.getT1().getTemplateParam().get("code"));

  }

  @Test
  void initAliyunTtsClient() throws Exception {
    com.aliyun.dyvmsapi20170525.Client client = AliyunUtils.initAliyunTtsClient(mockAliConfig());
    assertNotNull(client);
    //别乱改Endpoint
    assertEquals("dyvmsapi.aliyuncs.com", client._endpoint);
    assertNotNull(client.getAccessKeySecret());
  }


  @Test
  void initAliyunSmsClient() throws Exception {
    Client client = AliyunUtils.initAliyunSmsClient(mockAliConfig());
    assertNotNull(client);
    //别乱改Endpoint
    assertEquals("dysmsapi.aliyuncs.com", client._endpoint);
    assertNotNull(client.getAccessKeySecret());
  }

  @Test
  void safeStrToDate() {
    assertEquals(new Date(1719400177000L), AliyunUtils.safeStrToDate("2024-06-26 19:09:37"));
    assertNotNull(AliyunUtils.safeStrToDate(""));
    assertNotNull(AliyunUtils.safeStrToDate("2024-06-26"));
  }

  @Test
  void aliyunPhoneNumbers() {
    assertEquals("13512345678,13812345678", AliyunUtils.aliyunPhoneNumbers(List.of("13512345678", "13812345678"), false));
    assertEquals("33745718646,447367598525", AliyunUtils.aliyunPhoneNumbers(List.of("0033745718646", "00447367598525"), true));
  }

}