package com.sharecrm.egress.sms.share;

import com.sharecrm.egress.config.SmsProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class MonternetStatusTaskTest {

  @Mock
  private MonternetChineseSender monternetChineseSender;

  private MonternetStatusTask monternetStatusTask;

  @BeforeEach
  void setUp() {
    monternetStatusTask = new MonternetStatusTask(monternetChineseSender, new SmsProperties());
  }

  @Test
  void testRunSuccess() {
    // When
    monternetStatusTask.run();

    // Then
    verify(monternetChineseSender).updateStatus();
  }

}
