package com.sharecrm.egress.sms.share;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class MonternetStatusTaskTest {

  @Mock
  private MonternetChineseSender monternetChineseSender;

  private MonternetStatusTask monternetStatusTask;

  @BeforeEach
  void setUp() {
    monternetStatusTask = new MonternetStatusTask(monternetChineseSender);
  }

  @Test
  void testRunSuccess() {
    // When
    monternetStatusTask.run();

    // Then
    verify(monternetChineseSender).updateStatus();
  }

  @Test
  void testRunWithException() {
    // Given
    doThrow(new RuntimeException("Test exception")).when(monternetChineseSender).updateStatus();

    // When - should not throw exception
    monternetStatusTask.run();

    // Then
    verify(monternetChineseSender).updateStatus();
  }
}
