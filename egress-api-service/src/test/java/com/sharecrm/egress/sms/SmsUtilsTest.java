package com.sharecrm.egress.sms;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsPageResult;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.entity.SmsStaticTemplate;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.exception.SmsArgException;
import com.sharecrm.egress.sdk.SmsSdkConstants;
import com.sharecrm.egress.sdk.entity.SmsResponseData;
import com.sharecrm.egress.sdk.entity.SmsSendByTemplate;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sdk.entity.SmsTemplateQuery;
import com.sharecrm.egress.sdk.entity.SmsTemplateRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

import static com.sharecrm.egress.sms.SmsUtils.aliyunKeyReplacer;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class SmsUtilsTest {

  private static final List<String> mocks = List.of("No.123");
  private static final List<String> EMPTY_LIST = List.of();

  @Test
  void isPhone() {
    assertTrue(SmsUtils.isPhone("***********"));
    assertFalse(SmsUtils.isPhone("861380013800"));
    assertFalse(SmsUtils.isPhone("0x138"));
  }

  @Test
  void isIntlPhone() {
    assertTrue(SmsUtils.isIntlPhone("0861380013800"));
    assertFalse(SmsUtils.isIntlPhone("***********"));
    assertFalse(SmsUtils.isIntlPhone("0x138"));
  }

  @Test
  void containIllegalPassword() {
    assertFalse(SmsUtils.containIllegalPassword(null));
    assertFalse(SmsUtils.containIllegalPassword(""));
    assertFalse(SmsUtils.containIllegalPassword(" "));
    assertFalse(SmsUtils.containIllegalPassword("和密码 12345+* 登录"));
    assertFalse(SmsUtils.containIllegalPassword("用户名和密码验证登录系统"));
    assertFalse(SmsUtils.containIllegalPassword("您可凭手机号 133 获取短信动态密码登录。"));
    assertFalse(SmsUtils.containIllegalPassword("您可凭手机号  13913439823  和密码 1 登录。点击链接"));
    assertFalse(SmsUtils.containIllegalPassword("您可凭手机号  13913439823  和密码 1**+.* 登录。点击链接"));
    assertTrue(SmsUtils.containIllegalPassword("您可凭手机号133和密码jia君羊7登录。"));
    assertTrue(SmsUtils.containIllegalPassword("您可凭手机号  133  和密码 jia君羊7 登录。"));
    assertTrue(SmsUtils.containIllegalPassword("您可凭手机号  133  和密码 jia君羊7 登录。点击链接"));
  }

  @Test
  void testGetCaptcha() {
    assertEquals("123456", SmsUtils.getCaptcha("您的验证码是123456。"));
    assertEquals("123456", SmsUtils.getCaptcha("您的验证码是：123456。请注意保管。"));
    assertEquals("", SmsUtils.getCaptcha("【阿里巴巴】您的验证码是：12345。"));
  }

  @Test
  void testRandomToken() {
    String token = SmsUtils.randomToken();
    assertEquals(10, token.length());
  }

  @Test
  void validateInternal() {
    assertThrows(SmsArgException.class, () -> SmsUtils.validateInternal(EMPTY_LIST));
    assertThrows(SmsArgException.class, () -> SmsUtils.validateInternal(mocks));
  }

  @Test
  void validateTts() {
    assertThrows(SmsArgException.class, () -> SmsUtils.validateTts(EMPTY_LIST));
    assertThrows(SmsArgException.class, () -> SmsUtils.validateTts(mocks));
  }

  @Test
  void validateIntl() {
    assertThrows(SmsArgException.class, () -> SmsUtils.validateTts(EMPTY_LIST));
    assertThrows(SmsArgException.class, () -> SmsUtils.validateTts(mocks));
  }

  @Test
  void validatePhones() {
    assertThrows(SmsArgException.class, () -> SmsUtils.validatePhones(EMPTY_LIST, true));
    assertThrows(SmsArgException.class, () -> SmsUtils.validatePhones(List.of("0861380013800"), false));
    assertDoesNotThrow(() -> SmsUtils.validatePhones(List.of("***********"), false));
    assertDoesNotThrow(() -> SmsUtils.validatePhones(List.of("0861380013800"), true));
  }

  @Test
  void isCaptchaSms() {
    assertTrue(SmsUtils.isCaptchaSms("验证码：149691（请勿转告他人），有效期15分钟，请尽快完成验证。"));
    assertTrue(SmsUtils.isCaptchaSms("Verification Code: 149691"));
    assertFalse(SmsUtils.isCaptchaSms("这是我的电话短号149691"));
  }

  @Test
  void isInvalidatedPhoneByMessage() {
    assertTrue(SmsUtils.isInvalidatedPhoneByMessage("无可用号码:135"));
    assertTrue(SmsUtils.isInvalidatedPhoneByMessage("8527654321 invalid mobile number with countryCode"));
    assertFalse(SmsUtils.isInvalidatedPhoneByMessage(""));
    assertFalse(SmsUtils.isInvalidatedPhoneByMessage(null));
  }

  @Test
  void replaceTemplateContext() {
    SmsSendByTemplate request = new SmsSendByTemplate();
    request.setTemplateParam(Map.of("code", "123456"));
    Map<String, String> map = SmsUtils.replaceTemplateParamValue(request, e -> "OK");
    map.forEach((key, value) -> assertEquals("OK", value));
  }

  @Test
  void testZsjReplaceContext() {
    String context = "尊敬的用户，您的验证码是：12345。如非本人操作，请忽略本短信。";
    String result = SmsUtils.zsjReplaceContent(context);
    assertEquals("您的验证码是：12345", result);
  }

  @Test
  void mengniuReplaceContent() {
    String context = "您的验证码是：12345。请勿泄露！";
    String result = SmsUtils.mengniuReplaceContent(context, "尊敬的用户:");
    assertEquals("您的验证码是：12345。请勿泄露", result);
  }

  @Test
  void replaceContext() {
    SmsSendRequest req = new SmsSendRequest();
    req.setContent("邀请您加入，您的验证码是：12345。");
    assertEquals("邀您加入，您的验证码是：12345。", SmsUtils.replaceContext(req).getContent());
    req.setContent("【纷享销客】您的验证码是：12345。");
    assertEquals("您的验证码是：12345。", SmsUtils.replaceContext(req).getContent());
    req.setContent("[ShareCRM]Code:12345");
    assertEquals("Code:12345", SmsUtils.replaceContext(req).getContent());
  }

  @Test
  void replaceContextWithMobileNumbers() {
    SmsSendRequest req = new SmsSendRequest();

    // 测试单个手机号替换
    req.setContent("您的手机号13812345678已绑定成功");
    assertEquals("您的手机号138****5678已绑定成功", SmsUtils.replaceContext(req).getContent());

    // 测试多个手机号替换
    req.setContent("联系人：13912345678，备用联系人：15987654321");
    assertEquals("联系人：139****5678，备用联系人：159****4321", SmsUtils.replaceContext(req).getContent());

    // 测试不同运营商的手机号
    req.setContent("移动：13512345678，联通：15612345678，电信：18912345678");
    assertEquals("移动：135****5678，联通：156****5678，电信：189****5678", SmsUtils.replaceContext(req).getContent());

    // 测试包含其他内容的文本
    req.setContent("【纷享销客】您的手机号13812345678验证码是12345");
    assertEquals("您的手机号138****5678验证码是12345", SmsUtils.replaceContext(req).getContent());

    // 测试不是手机号的数字（不应该被替换）
    req.setContent("订单号：202313345678901，金额：1234.56");
    assertEquals("订单号：202313345678901，金额：1234.56", SmsUtils.replaceContext(req).getContent());

    // 测试更多边界情况
    req.setContent("账号：1234567890123456789，手机：13812345678");
    assertEquals("账号：1234567890123456789，手机：138****5678", SmsUtils.replaceContext(req).getContent());

    // 测试紧挨着的数字
    req.setContent("编号12313812345678901234");
    assertEquals("编号12313812345678901234", SmsUtils.replaceContext(req).getContent());

    // 测试独立的手机号（前后有空格或标点）
    req.setContent("手机号码 13812345678 已验证");
    assertEquals("手机号码 138****5678 已验证", SmsUtils.replaceContext(req).getContent());

    // 测试手机号在开头和结尾
    req.setContent("13812345678是您的联系方式");
    assertEquals("138****5678是您的联系方式", SmsUtils.replaceContext(req).getContent());

    req.setContent("您的联系方式是13812345678");
    assertEquals("您的联系方式是138****5678", SmsUtils.replaceContext(req).getContent());
    
    req.setContent("您可凭手机号 180****3456 获取短信动态密码");
    assertEquals("您可凭手机号 180****3456 获取短信动态密码", SmsUtils.replaceContext(req).getContent());

    // 测试空内容
    req.setContent("");
    assertEquals("", SmsUtils.replaceContext(req).getContent());

    // 测试null内容
    req.setContent(null);
    assertNull(SmsUtils.replaceContext(req).getContent());
  }

  @Test
  void replaceSign() {
    String zhSign = "蒙牛";
    String enSign = "Mengniu";
    assertEquals("您的验证码是：12345", SmsUtils.replaceSign("您的验证码是：12345", zhSign, enSign));
    assertEquals("您的验证码是：12345", SmsUtils.replaceSign("【蒙牛】您的验证码是：12345", zhSign, enSign));
    assertEquals("您的验证码是：12345", SmsUtils.replaceSign("【蒙牛】您的验证码是：12345", zhSign, ""));
    assertEquals("您的验证码是：12345，蒙牛欢迎你", SmsUtils.replaceSign("【蒙牛】您的验证码是：12345，蒙牛欢迎你", zhSign, enSign));
    assertEquals("您的验证码是：12345", SmsUtils.replaceSign("[Mengniu]您的验证码是：12345", zhSign, enSign));
  }

  @Test
  void testAmendIntlPhone() {
    assertEquals("0086***********", SmsUtils.amendIntlPhone("000086***********"));
    assertEquals("0086***********", SmsUtils.amendIntlPhone("00086***********"));
  }

  @Test
  void phoneToE164() {
    assertEquals("+447367598525", SmsUtils.phoneToE164("00447367598525", true));
    assertEquals("+16175551212", SmsUtils.phoneToE164("+16175551212", true));
    assertEquals("+16175551212", SmsUtils.phoneToE164("0016175551212", true));
    assertEquals("+16175551212", SmsUtils.phoneToE164("00016175551212", true));
    assertEquals("+2348033730967", SmsUtils.phoneToE164("002348033730967", true));
    assertEquals("+86***********", SmsUtils.phoneToE164("0086***********", true));
    assertEquals("+86***********", SmsUtils.phoneToE164("+86***********", true));
    assertEquals("+86***********", SmsUtils.phoneToE164("***********", false));
  }

  @Test
  void getCaptcha() {
    assertEquals("149691", SmsUtils.getStrictCaptcha("验证码：149691（请勿转告他人）。").orElseThrow());
    assertEquals("149691", SmsUtils.getStrictCaptcha("Verification Code: 149691").orElseThrow());
    assertFalse(SmsUtils.getStrictCaptcha("这是我的电话短号: 149691").isPresent());
    assertFalse(SmsUtils.getStrictCaptcha("要我的验证码干什么").isPresent());
  }

  @Test
  void containChinese() {
    assertTrue(SmsUtils.containChinese("这是我的电话短号: 149691"));
    assertFalse(SmsUtils.containChinese("who is that"));
  }

  @Test
  void containPrioritizeEnMsg() {
    assertTrue(SmsUtils.containPrioritizeEnMsg(
        "Welcome to ShareCRM(kk06)! To access your account(Company Name: ShareCRM 080-正式企业), please click on the"));
    assertTrue(SmsUtils.containPrioritizeEnMsg("Your company has deployed a connected CRM with ShareCRM"));
    assertFalse(SmsUtils.containPrioritizeEnMsg("who is that"));
  }

  @Test
  void getMd5() {
    assertEquals("35f774a9b3dbaf2c8f8c20914ca04547", SmsUtils.getMd5("asGzAfE6Kx6"));
  }

  @Test
  void testRemoveTheFirstZero() {
    assertEquals("***********", SmsUtils.removeTheFirstZero("***********"));
    assertEquals("00157", SmsUtils.removeTheFirstZero("00157"));
    assertEquals("00208", SmsUtils.removeTheFirstZero("00208"));
    assertEquals("002129", SmsUtils.removeTheFirstZero("002129"));
    assertEquals("0099901", SmsUtils.removeTheFirstZero("0099901"));
  }

  @Test
  void sensitiveMasker() {
    SmsPageResult source = new SmsPageResult();
    SmsMongoEntity entity = new SmsMongoEntity();
    entity.setContent("test");
    source.setResults(List.of(entity));
    SmsPageResult rs = SmsUtils.sensitiveMasker(source);
    assertFalse(rs.getResults().isEmpty());
    assertNull(rs.getResults().get(0).getContent());
  }

  @Test
  void sensitiveMaskerContent() {
    assertEquals("您的验证码是 ****56", SmsUtils.sensitiveMaskerContent("您的验证码是 123456"));
    assertFalse(SmsUtils.sensitiveMaskerContent("您可凭密码 test-key 登录").contains("test-key"));
  }

  @Test
  void extractValues() {
    Map<String, String> map = SmsUtils.extractValues("您的验证码是 ${code}，有效期10分钟", "您的验证码是 123456，有效期10分钟");
    assertEquals("123456", map.get("code"));

    // 互联的这个模板，公众号可能为空
    map = SmsUtils.extractValues("关注公众号${official_account}绑定身份使用互联业务", "关注公众号绑定身份使用互联业务");
    assertEquals("", map.get("official_account"));

    map = SmsUtils.extractValues("关注公众号${official_account}绑定身份使用互联业务", "关注公众号test绑定身份使用互联业务");
    assertEquals("test", map.get("official_account"));

    map = SmsUtils.extractValues("您好！[${ea}]欢迎使用互联业务。", "您好！[90925]欢迎使用互联业务。");
    assertEquals("90925", map.get("ea"));

    map = SmsUtils.extractValues("Dear (${ea}) nice to meet your.", "Dear (like) nice to meet your.");
    assertEquals("like", map.get("ea"));

    // 故意多加了几个空格
    map = SmsUtils.extractValues("Dear ${ea} nice to meet ${friend}.", "Dear  (like)  nice to meet [your] .");
    assertEquals("(like)", map.get("ea"));
    assertEquals("[your]", map.get("friend"));

    map = SmsUtils.extractValues(
        "Verification Code:${code} (Do not tell others), valid for 15 mins , please complete the verification as soon as possible.",
        "Verification Code:123456 (Do not tell others), valid for 15 mins , please complete the verification as soon as possible.");
    assertEquals("123456", map.get("code"));

    map = SmsUtils.extractValues("您好！[${ea}]通过互联业务平台把您的互联帐号${phone}的密码重置为\"${password}\"，您可以重新登录后使用互联业务。",
        "您好！[90925]通过互联业务平台把您的互联帐号13242991295的密码重置为\"e424b65\"，您可以重新登录后使用互联业务。");
    assertEquals("90925", map.get("ea"));

    // 文本无字符测试
    map = SmsUtils.extractValues("验证码：${code}", "验证码：123456");
    assertEquals("123456", map.get("code"));

    // 文本前后无字符测试
    map = SmsUtils.extractValues("${name}的验证码：${code}", "集成平台的验证码：123456");
    assertEquals("集成平台", map.get("name"));
    assertEquals("123456", map.get("code"));

    // 不同云用的格式不一样
    map = SmsUtils.extractValues(SmsUtils.SMS_PRE_NO_DOLLAR, "您的验证码是{user.code}，有效期{time}分钟", "您的验证码是123456，有效期10分钟");
    assertEquals("123456", map.get("user.code"));
    assertEquals("10", map.get("time"));
  }

  @Test
  void isAllowEi() {
    assertTrue(SmsUtils.isAllowEi("white:EI.777421|EI.777437|EI.40160001-40200000", 777421));
    assertTrue(SmsUtils.isAllowEi("white:EI.777421|EI.777437|EI.40160001-40200000", 777437));
    assertTrue(SmsUtils.isAllowEi("white:EI.777421|EI.777437|EI.40160001-40200000", 40160002));
    assertTrue(SmsUtils.isAllowEi("white:EI.777421|EI.777437|EI.40160001-40200000", 40200000));
    assertTrue(SmsUtils.isAllowEi("white:*.*", 887421));
    assertTrue(SmsUtils.isAllowEi("white:*", 887421));

    assertFalse(SmsUtils.isAllowEi("white:EI.777421|EI.777437|EI.40160001-40200000", null));
    assertFalse(SmsUtils.isAllowEi("white:EI.777421|EI.777437|EI.40160001-40200000", 887421));

  }

  @Test
  void isAllowAll() {
    assertTrue(SmsUtils.isAllowAll("*"));
    assertTrue(SmsUtils.isAllowAll("white:*.*"));
    assertTrue(SmsUtils.isAllowAll("white:*"));
    assertFalse(SmsUtils.isAllowAll("white:EI.777421|EI.777437|EI.40160001-40200000"));
    assertFalse(SmsUtils.isAllowAll(""));
  }

  @Test
  void grayRuleByEi() {
    String rule = SmsUtils.grayRuleByEi(100);
    assertEquals("white:EI.100", rule);
    assertTrue(SmsUtils.isAllowEi(rule, 100));
    assertFalse(SmsUtils.isAllowEi(rule, 101));
  }

  @Test
  void filterSmsTemplate() {
    SmsTemplateQuery query = new SmsTemplateQuery();
    SmsStaticTemplate template = new SmsStaticTemplate();
    assertTrue(SmsUtils.filterSmsTemplate(query, template));
  }

  @Test
  void replaceTemplateContent() {

    String content = "您的领导是 ${Leader.Name}，他的年龄是 ${age}";

    Map<String, String> map = SmsUtils.paramKeyForReplace(content, SmsUtils::aliyunTemplateParamReplacer);

    BiFunction<String, Map.Entry<String, String>, String> replacer = (s, e) -> aliyunKeyReplacer(s, e.getKey(),
        e.getValue());

    String newContext = SmsUtils.replaceTemplateContent(content, map, replacer);

    assertEquals("您的领导是 ${leader_name}，他的年龄是 ${age}", newContext);

    // 无变量替换时原样返回
    assertEquals(content, SmsUtils.replaceTemplateContent(content, Map.of(), replacer));
  }

  @Test
  void initTemplateDetail() {
    String id = "test-id";
    SmsTemplateRequest request = new SmsTemplateRequest();
    request.setContent("test-content");
    SmsTemplateDetail detail = SmsUtils.initTemplateDetail(mockSmsProvider(), request, id);
    assertEquals(SmsSdkConstants.TEMPLATE_STATUS_INIT, detail.getStatus());
    assertEquals(request.getContent(), detail.getContent());
    // 这两个字段很重要，校验一下
    assertNotNull(detail.getProviderId());
    assertNotNull(detail.getProviderName());

  }

  @NotNull
  private static SmsProperties.MengniuConfig mockSmsProvider() {
    SmsProperties.MengniuConfig config = new SmsProperties.MengniuConfig();
    config.setId("provider-id");
    config.setName("mengniu");
    return config;
  }

  @Test
  void initTemplateEntity() {
    String id = "test-id";
    SmsTemplateRequest request = new SmsTemplateRequest();
    request.setContent("test-content");
    SmsTemplateEntity entity = SmsUtils.initTemplateEntity(mockSmsProvider(), id, "provider-id", request,
        Map.of("city.address", "city_address"));
    assertEquals(SmsSdkConstants.TEMPLATE_STATUS_INIT, entity.getStatus());
    assertEquals(request.getContent(), entity.getContent());
    assertEquals("{\"city.address\":\"city_address\"}", entity.getParamReplace());
  }

  @Test
  void chooseSign() {
    assertEquals(SmsUtils.SMS_SIGN_EN, SmsUtils.chooseSign("", "this is a test"));
    assertEquals(SmsUtils.SMS_SIGN_ZH, SmsUtils.chooseSign("", "验证码"));
    assertEquals(SmsUtils.SMS_SIGN_EN, SmsUtils.chooseSign(SmsSdkConstants.LANGUAGE_EN, "验证码"));
    assertEquals(SmsUtils.SMS_SIGN_EN,
        SmsUtils.chooseSign("", "Your company has deployed a connected CRM with ShareCRM, enterprise name 纷享专用"));
    assertEquals(SmsUtils.SMS_SIGN_ZH,
        SmsUtils.chooseSign(SmsSdkConstants.LANGUAGE_ZH, "验证码", Map.of(), null, null, false));
    assertNull(SmsUtils.chooseSign(SmsSdkConstants.LANGUAGE_ZH, "验证码", Map.of(), null, null, true));
  }

  @Test
  void filterByEi() {
    SmsSender all = mockSender("white:*");

    assertTrue(SmsUtils.filterByEi("100", all));
    assertTrue(SmsUtils.filterByEi("", all));

    SmsSender sender = mockSender("white:EI.7701|EI.7702|EI.7703");
    assertFalse(SmsUtils.filterByEi("100", sender));
    assertTrue(SmsUtils.filterByEi("7701", sender));
    assertTrue(SmsUtils.filterByEi("7701,7703", sender));
    assertFalse(SmsUtils.filterByEi("7701,100", sender));
  }

  @NotNull
  private static SmsSender mockSender(String rule) {
    return new SmsSender() {
      @Override
      public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> request) {
        return null;
      }

      @Override
      public SmsProvider provider() {
        return new SmsProperties.MonternetConfig() {
          @Override
          public String getAllowAccounts() {
            return rule;
          }

          @Override
          public boolean isExclusive() {
            return true;
          }
        };
      }
    };
  }

  @Test
  void simpleSmsProvider() {
    SmsProperties.MengniuConfig config = mockSmsProvider();
    SmsProvider provider = SmsUtils.simpleSmsProvider(config);
    assertEquals(config.getId(), provider.getId());
    assertFalse(provider instanceof SmsProperties.MengniuConfig);
  }

  @Test
  void assertLength() {
    assertThrows(SmsArgException.class, () -> SmsUtils.assertLength("name", 1, 2, "name"));
    assertDoesNotThrow(() -> SmsUtils.assertLength("name", 1, 10, "name"));
    assertDoesNotThrow(() -> SmsUtils.assertLength("", 0, 10, "name"));
  }

  @Test
  void validateAddTemplate() {
    SmsTemplateRequest req = new SmsTemplateRequest();
    req.setEnterpriseId(123456);
    assertThrows(SmsArgException.class, () -> SmsUtils.validateAddTemplate(req));
    req.setName("合法模板名字");
    req.setContent("合法模板内容");
    assertThrows(SmsArgException.class, () -> SmsUtils.validateAddTemplate(req));
    req.setRemark("合法模板说明");
    req.setTemplateType(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE);
    assertDoesNotThrow(() -> SmsUtils.validateAddTemplate(req));
  }

  @Test
  void validateTemplateType() {
    assertThrows(SmsArgException.class, () -> SmsUtils.validateTemplateType("test-type"));
    assertDoesNotThrow(() -> SmsUtils.validateTemplateType(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE));
  }

  @Test
  void checkSmsSendResult() {
    SmsRequestWrapper<SmsSendRequest> wrapper = new SmsRequestWrapper<>(new SmsSendRequest(),
        NanoIdUtils.randomNanoId());
    SmsSendResult rs = SmsUtils.success(null, wrapper, "135", "");
    assertTrue(rs.getResponse().isSuccess());
    assertEquals(wrapper.getBatchMsgId(), rs.getResponse().getBatchMsgId());
    assertFalse(SmsUtils.failed(null, wrapper, "135", "").getResponse().isSuccess());
  }

  @Test
  void selectSmsType() {
    assertEquals(SmsSdkConstants.TEMPLATE_TYPE_VERIFY_CODE, SmsUtils.selectSmsType("您的验证码是123456"));
    assertEquals(SmsSdkConstants.TEMPLATE_TYPE_NOTIFICATION, SmsUtils.selectSmsType("欢迎下载使用"));
  }

  @Test
  void renderTemplate() {
    assertEquals("您的验证码是123456", SmsUtils.renderTemplate("您的验证码是${code}", Map.of("code", "123456")));
    assertEquals("您的验证码是123456", SmsUtils.renderTemplate("您的验证码是${user.code}", Map.of("user.code", "123456")));
    assertEquals("您的验证码是${user.code}", SmsUtils.renderTemplate("您的验证码是${user.code}", Map.of()));
  }

  @Test
  void templateStatus() {
    assertEquals("src", SmsUtils.templateStatus(Map.of(), "src"));
    assertEquals("src", SmsUtils.templateStatus(Map.of("src", "known"), "known"));
  }

  @Test
  void zipSmsSendResult() {
    SmsResponseData failed = new SmsResponseData(false, "135", "", "");
    assertFalse(SmsUtils.zipSmsSendResult(List.of(failed)).getResponse().isSuccess());
    SmsResponseData success = new SmsResponseData(true, "135", "", "");
    assertTrue(SmsUtils.zipSmsSendResult(List.of(success)).getResponse().isSuccess());
  }

  @Test
  void adapterSmsSize() {
    assertEquals(1, SmsUtils.adapterSmsSize(30));
    assertEquals(1, SmsUtils.adapterSmsSize(70));
    assertEquals(2, SmsUtils.adapterSmsSize(71));
    assertEquals(2, SmsUtils.adapterSmsSize(134));
    assertEquals(3, SmsUtils.adapterSmsSize(135));
    assertEquals(10, SmsUtils.adapterSmsSize(669));
  }

  @Test
  void randomDelayInstant() {
    Instant instant = SmsUtils.randomDelayInstant(30, 60);
    assertTrue(instant.isAfter(Instant.now()));
  }

  @Test
  void tryGuessMobile() {
    assertTrue(SmsUtils.tryGuessMobile("13552346839"));
  }

  @Test
  void zsjReplaceContent() {
    // Test removing prefixes
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("尊敬的用户：验证码是123456"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("尊敬的用户，验证码是123456"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("Dear valued user,验证码是123456"));

    // Test removing suffixes
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456如非本人操作，请勿泄露。"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456如非本人操作，请勿泄露"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456如非本人操作，请忽略本短信。"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456如非本人操作，请忽略本短信"));

    // Test removing trailing punctuation
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456！"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456!"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456，"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456,"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456。"));
    assertEquals("验证码是123456", SmsUtils.zsjReplaceContent("验证码是123456."));
  }

  @Test
  void aliyunReplaceContent() {
    // Test with template containing "尊敬的用户"
    String template1 = "尊敬的用户：${content}，如非本人操作，请忽略本短信。";
    assertEquals("验证码是123456", SmsUtils.aliyunReplaceContent("尊敬的用户：验证码是123456", template1));

    // Test with template containing "Dear valued user"
    String template2 = "Dear valued user,${content},please ignore if not you.";
    assertEquals("验证码是123456", SmsUtils.aliyunReplaceContent("Dear valued user,验证码是123456", template2));

    // Test with template not containing special prefixes
    String template3 = "您的验证码是${code}";
    assertEquals("验证码是123456", SmsUtils.aliyunReplaceContent("验证码是123456", template3));
  }

  @Test
  void mengniuReplaceContentAdvanced() {
    // Test that mengniuReplaceContent delegates to aliyunReplaceContent
    String template = "尊敬的用户：${content}，如非本人操作，请忽略本短信。";
    String content = "尊敬的用户：验证码是123456";
    assertEquals(SmsUtils.aliyunReplaceContent(content, template),
        SmsUtils.mengniuReplaceContent(content, template));
  }

  @Test
  void getMd5Advanced() {
    // Test MD5 encryption
    String input = "test123";
    String md5 = SmsUtils.getMd5(input);
    assertNotNull(md5);
    assertEquals(32, md5.length()); // MD5 hash is always 32 characters

    // Test that same input produces same hash
    assertEquals(md5, SmsUtils.getMd5(input));

    // Test that different input produces different hash
    assertNotEquals(md5, SmsUtils.getMd5("test456"));
  }

  @Test
  void randomToken() {
    // Test random token generation
    String token1 = SmsUtils.randomToken();
    String token2 = SmsUtils.randomToken();

    assertNotNull(token1);
    assertNotNull(token2);
    assertEquals(10, token1.length());
    assertEquals(10, token2.length());
    assertNotEquals(token1, token2); // Should be different
  }

  @Test
  void randomTemplateId() {
    // Test random template ID generation
    String id1 = SmsUtils.randomTemplateId();
    String id2 = SmsUtils.randomTemplateId();

    assertNotNull(id1);
    assertNotNull(id2);
    assertTrue(id1.startsWith("fs-sms-"));
    assertTrue(id2.startsWith("fs-sms-"));
    assertEquals(19, id1.length()); // "fs-sms-" (7) + 12 random chars
    assertEquals(19, id2.length());
    assertNotEquals(id1, id2); // Should be different
  }

}