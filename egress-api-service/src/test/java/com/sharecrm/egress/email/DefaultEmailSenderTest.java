package com.sharecrm.egress.email;

import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import jakarta.mail.internet.MimeMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSender;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DefaultEmailSenderTest {

  @Mock
  private JavaMailSender mailSender;

  @Mock
  private MailProperties mailProperties;

  @Mock
  private MimeMessage mimeMessage;

  @InjectMocks
  private DefaultEmailSender emailSender;

  @BeforeEach
  void setUp() {
    when(mailProperties.getUsername()).thenReturn("<EMAIL>");
    when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
  }

  @Test
  void send_success() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    // Act
    EmailSendResponse response = emailSender.send(request);

    // Assert
    assertTrue(response.isSuccess());
    verify(mailSender).send(mimeMessage);
  }

  @Test
  void send_withCc_success() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("<EMAIL>"));
    request.setCc(List.of("<EMAIL>", "<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    // Act
    EmailSendResponse response = emailSender.send(request);

    // Assert
    assertTrue(response.isSuccess());
    verify(mailSender).send(mimeMessage);
  }

  @Test
  void send_withException_returnsFailure() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    // Simulate an exception during email sending
    doThrow(new RuntimeException("Simulated failure")).when(mailSender).send(any(MimeMessage.class));

    // Act
    EmailSendResponse response = emailSender.send(request);

    // Assert
    assertFalse(response.isSuccess());
    assertEquals("server error", response.getMessage());
  }

  @Test
  void send_withMailSendException_returnsFailure() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    // Simulate a mail send exception with an unchecked exception
    doThrow(new MailSendException("Mail sending error")).when(mailSender).send(any(MimeMessage.class));

    // Act
    EmailSendResponse response = emailSender.send(request);

    // Assert
    assertFalse(response.isSuccess());
    assertEquals("server error", response.getMessage());
  }
}