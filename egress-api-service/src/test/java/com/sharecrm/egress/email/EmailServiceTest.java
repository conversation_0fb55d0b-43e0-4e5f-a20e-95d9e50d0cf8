package com.sharecrm.egress.email;

import com.sharecrm.egress.entity.EmailInnerRequest;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.core.task.TaskExecutor;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EmailServiceTest {

  @Mock
  private ObjectProvider<EmailSender> senderObjectProvider;

  @Mock
  private TaskExecutor taskExecutor;

  @Mock
  private DefaultEmailSender emailSender;

  @InjectMocks
  private EmailService emailService;

  @Test
  void sendEmail_withInvalidEmail_shouldReturnFailure() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("invalid-email"));

    // Act & Assert
    StepVerifier.create(emailService.sendEmail(request))
        .expectNextMatches(response -> !response.isSuccess() && "request validate failed".equals(response.getMessage()))
        .verifyComplete();

    // Verify no interactions with the email sender
    verifyNoInteractions(senderObjectProvider);
    verifyNoInteractions(taskExecutor);
  }

  @Test
  void sendEmail_withValidEmail_shouldReturnSuccess() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    when(senderObjectProvider.stream()).thenReturn(Stream.of(emailSender));

    // Act & Assert
    StepVerifier.create(emailService.sendEmail(request))
        .expectNextMatches(response -> response.isSuccess() && "accepted".equals(response.getMessage()))
        .verifyComplete();

    // Verify that email was sent asynchronously
    verify(taskExecutor).execute(any(Runnable.class));
  }

  @Test
  void sendEmail_withInvalidCcEmail_shouldReturnFailure() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("<EMAIL>"));
    request.setCc(List.of("invalid-cc-email"));

    // Act & Assert
    StepVerifier.create(emailService.sendEmail(request))
        .expectNextMatches(response -> !response.isSuccess() && "request validate failed".equals(response.getMessage()))
        .verifyComplete();

    // Verify no interactions with the email sender
    verifyNoInteractions(senderObjectProvider);
    verifyNoInteractions(taskExecutor);
  }

  @Test
  void sendEmail_withNoEmailProvider_shouldReturnFailure() {
    // Arrange
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(List.of("<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    when(senderObjectProvider.stream()).thenReturn(Stream.empty());

    // Act & Assert
    StepVerifier.create(emailService.sendEmail(request))
        .expectNextMatches(response -> !response.isSuccess() && "no email sender found".equals(response.getMessage()))
        .verifyComplete();

    // Verify no task execution
    verifyNoInteractions(taskExecutor);
  }

  @Test
  void sendInnerEmail_shouldConvertAndSendEmail() {
    // Arrange
    EmailInnerRequest innerRequest = new EmailInnerRequest();
    innerRequest.setTo("<EMAIL>,<EMAIL>");
    innerRequest.setCc("<EMAIL>,<EMAIL>");
    innerRequest.setSubject("Inner Test Subject");
    innerRequest.setContent("Inner Test Content");

    when(senderObjectProvider.stream()).thenReturn(Stream.of(emailSender));

    // Capture the converted request
    ArgumentCaptor<Runnable> runnableCaptor = ArgumentCaptor.forClass(Runnable.class);

    // Act & Assert
    StepVerifier.create(emailService.sendInnerEmail(innerRequest))
        .expectNextMatches(response -> response.isSuccess() && "accepted".equals(response.getMessage()))
        .verifyComplete();

    // Verify the task executor was called with a runnable
    verify(taskExecutor).execute(runnableCaptor.capture());

    // We can't easily verify the inner workings of the runnable without complex
    // mocking
    // But we can verify that it was called
    assertNotNull(runnableCaptor.getValue());
  }

  @Test
  void sendInnerEmail_withEmptyCc_shouldHandleItCorrectly() {
    // Arrange
    EmailInnerRequest innerRequest = new EmailInnerRequest();
    innerRequest.setTo("<EMAIL>");
    innerRequest.setCc(""); // Empty CC
    innerRequest.setSubject("Test Subject");
    innerRequest.setContent("Test Content");

    when(senderObjectProvider.stream()).thenReturn(Stream.of(emailSender));

    // Act & Assert
    StepVerifier.create(emailService.sendInnerEmail(innerRequest))
        .expectNextMatches(EmailSendResponse::isSuccess)
        .verifyComplete();

    // Verify the task executor was called
    verify(taskExecutor).execute(any(Runnable.class));
  }

  @Test
  void sendInnerEmail_withNullCc_shouldHandleItCorrectly() {
    // Arrange
    EmailInnerRequest innerRequest = new EmailInnerRequest();
    innerRequest.setTo("<EMAIL>");
    innerRequest.setCc(null); // Null CC
    innerRequest.setSubject("Test Subject");
    innerRequest.setContent("Test Content");

    when(senderObjectProvider.stream()).thenReturn(Stream.of(emailSender));

    // Act & Assert
    StepVerifier.create(emailService.sendInnerEmail(innerRequest))
        .expectNextMatches(EmailSendResponse::isSuccess)
        .verifyComplete();

    // Verify the task executor was called
    verify(taskExecutor).execute(any(Runnable.class));
  }
}