package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.IpLocationRequest;
import com.sharecrm.egress.geo.GeoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(IpLocationController.class)
class IpLocationControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockitoBean
  private GeoService geoService;

  @Test
  void queryByPost() {
    when(geoService.queryIpLocation(any())).thenReturn(Mono.just(new IpLocation()));
    // verify
    IpLocationRequest request = new IpLocationRequest();
    request.setIp("*********");
    webTestClient
      .post()
      .uri("/api/v2/ip/query-location")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .exists();
  }

  @Test
  void findByIp() {
    IpLocation data = new IpLocation();
    data.setCity("北京");
    when(geoService.queryIpLocation(any(), any(), any())).thenReturn(Mono.just(data));
    // verify
    IpLocationRequest request = new IpLocationRequest();
    request.setIp("*********");
    webTestClient
      .get()
      .uri("/api/v2/ip/find?ip=*********")
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.city")
      .isEqualTo("北京");
  }
  
  @Test
  void batchQuery() {
    // mock
    when(geoService.queryIpLocation(any(), any(), any())).thenReturn(Mono.just(new IpLocation()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/ip/locations")
        .queryParam("ips", "*********")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .exists();
  }
  
}