package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.GoogleTranslation;
import com.sharecrm.egress.entity.TranslateRequest;
import com.sharecrm.egress.entity.TranslateResponse;
import com.sharecrm.egress.service.TranslateService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(TranslateController.class)
class TranslateControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockBean
  private TranslateService translateService;

  @Test
  void translateSuccess() {
    // Given
    GoogleTranslation translation1 = new GoogleTranslation();
    translation1.setTranslatedText("Hello");
    GoogleTranslation translation2 = new GoogleTranslation();
    translation2.setTranslatedText("World");
    
    TranslateResponse response = new TranslateResponse();
    response.setTranslations(List.of(translation1, translation2));

    when(translateService.translate(any())).thenReturn(Mono.just(response));

    TranslateRequest request = new TranslateRequest();
    request.setTexts(List.of("你好", "世界"));
    request.setSource("zh");
    request.setTarget("en");

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.message").isEqualTo("ok")
        .jsonPath("$.data.translations").isArray()
        .jsonPath("$.data.translations[0].translatedText").isEqualTo("Hello")
        .jsonPath("$.data.translations[1].translatedText").isEqualTo("World");
  }

  @Test
  void translateWithSingleText() {
    // Given
    GoogleTranslation translation = new GoogleTranslation();
    translation.setTranslatedText("Thank you");
    
    TranslateResponse response = new TranslateResponse();
    response.setTranslations(List.of(translation));

    when(translateService.translate(any())).thenReturn(Mono.just(response));

    TranslateRequest request = new TranslateRequest();
    request.setTexts(List.of("谢谢"));
    request.setSource("zh");
    request.setTarget("en");

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.message").isEqualTo("ok")
        .jsonPath("$.data.translations").isArray()
        .jsonPath("$.data.translations[0].translatedText").isEqualTo("Thank you");
  }

  @Test
  void translateWithEmptyResult() {
    // Given
    TranslateResponse response = new TranslateResponse();
    response.setTranslations(List.of()); // 空结果

    when(translateService.translate(any())).thenReturn(Mono.just(response));

    TranslateRequest request = new TranslateRequest();
    request.setTexts(List.of("测试文本"));
    request.setSource("zh");
    request.setTarget("en");

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.message").isEqualTo("ok")
        .jsonPath("$.data.translations").isArray()
        .jsonPath("$.data.translations").isEmpty();
  }

  @Test
  void translateWithDifferentLanguages() {
    // Given
    GoogleTranslation translation = new GoogleTranslation();
    translation.setTranslatedText("Bonjour");
    
    TranslateResponse response = new TranslateResponse();
    response.setTranslations(List.of(translation));

    when(translateService.translate(any())).thenReturn(Mono.just(response));

    TranslateRequest request = new TranslateRequest();
    request.setTexts(List.of("Hello"));
    request.setSource("en");
    request.setTarget("fr");

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.message").isEqualTo("ok")
        .jsonPath("$.data.translations[0].translatedText").isEqualTo("Bonjour");
  }

  @Test
  void translateServiceError() {
    // Given
    when(translateService.translate(any()))
        .thenReturn(Mono.error(new RuntimeException("Translation service error")));

    TranslateRequest request = new TranslateRequest();
    request.setTexts(List.of("测试"));
    request.setSource("zh");
    request.setTarget("en");

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void translateWithMalformedJson() {
    // Given
    String malformedJson = "{\"texts\":[\"test\",\"source\":\"zh\""; // 缺少闭合括号

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(malformedJson)
        .exchange()
        .expectStatus()
        .is4xxClientError();
  }

  @Test
  void translateWithMultipleTexts() {
    // Given
    GoogleTranslation translation1 = new GoogleTranslation();
    translation1.setTranslatedText("Good morning");
    GoogleTranslation translation2 = new GoogleTranslation();
    translation2.setTranslatedText("Good afternoon");
    GoogleTranslation translation3 = new GoogleTranslation();
    translation3.setTranslatedText("Good evening");
    
    TranslateResponse response = new TranslateResponse();
    response.setTranslations(List.of(translation1, translation2, translation3));

    when(translateService.translate(any())).thenReturn(Mono.just(response));

    TranslateRequest request = new TranslateRequest();
    request.setTexts(List.of("早上好", "下午好", "晚上好"));
    request.setSource("zh");
    request.setTarget("en");

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.message").isEqualTo("ok")
        .jsonPath("$.data.translations").isArray()
        .jsonPath("$.data.translations.length()").isEqualTo(3)
        .jsonPath("$.data.translations[0].translatedText").isEqualTo("Good morning")
        .jsonPath("$.data.translations[1].translatedText").isEqualTo("Good afternoon")
        .jsonPath("$.data.translations[2].translatedText").isEqualTo("Good evening");
  }

  @Test
  void translateWithNullTexts() {
    // Given
    TranslateRequest request = new TranslateRequest();
    request.setTexts(null);
    request.setSource("zh");
    request.setTarget("en");

    // When & Then
    webTestClient
        .post()
        .uri("/translates")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .is4xxClientError(); // 验证失败
  }
}
