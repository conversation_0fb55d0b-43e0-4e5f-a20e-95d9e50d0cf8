package com.sharecrm.egress.web;

import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.utils.Constants;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.List;

import static org.mockito.Mockito.when;

@WebFluxTest(controllers = DrivingRouteController.class)
class DrivingRouteControllerTest {
  @Autowired
  private WebTestClient webTestClient;
  @MockBean
  private GeoService routeService;

  @Test
  void testDrivingWithoutCache() {
    // Setup
    LocationPoint origin = LocationPoint.parseLatitudeLongitude("39.991680,116.332930");
    LocationPoint destination = LocationPoint.parseLatitudeLongitude("39.901820,116.391020");
    List<LocationPoint> points = Lists.newArrayList();
    Splitter.on(CharMatcher.anyOf(";|")).split("39.917520,116.397170;39.921900,116.478070").forEach(s -> points.add(LocationPoint.parseLatitudeLongitude(s)));
    DrivingRouteRequest request = DrivingRouteRequest.builder().from(origin).to(destination).waypoints(points).build();
    PointDistance distance = new PointDistance(2, 5000);
    // Mock
    when(routeService.queryDriving(request, false, "")).thenReturn(Mono.just(distance));
    // Test
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/route/driving")
        .queryParam("from", "39.991680,116.332930")
        .queryParam("to", "39.901820,116.391020")
        .queryParam("waypoints", "39.917520,116.397170;39.921900,116.478070")
        .queryParam("cache", "false")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.data", distance);
  }

  @Test
  void testDrivingNotFound() {
    // Setup
    LocationPoint origin = LocationPoint.parseLatitudeLongitude("39.991680,116.332930");
    LocationPoint destination = LocationPoint.parseLatitudeLongitude("39.901820,116.391020");
    List<LocationPoint> points = Lists.newArrayList();
    Splitter.on(CharMatcher.anyOf(";|")).split("39.917520,116.397170;39.921900,116.478070").forEach(s -> points.add(LocationPoint.parseLatitudeLongitude(s)));
    DrivingRouteRequest request = DrivingRouteRequest.builder().from(origin).to(destination).waypoints(points).build();
    // Mock
    when(routeService.queryDriving(request, false, "")).thenReturn(Mono.empty());
    // Test
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/route/driving")
        .queryParam("from", "39.991680,116.332930")
        .queryParam("to", "39.901820,116.391020")
        .queryParam("waypoints", "39.917520,116.397170;39.921900,116.478070")
        .queryParam("cache", "false")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(404);
  }

  @Test
  void testDrivingGoogleError() {
    // Setup
    LocationPoint origin = LocationPoint.parseLatitudeLongitude("39.991680,116.332930");
    LocationPoint destination = LocationPoint.parseLatitudeLongitude("39.901820,116.391020");
    List<LocationPoint> points = Lists.newArrayList();
    Splitter.on(CharMatcher.anyOf(";|")).split("39.917520,116.397170;39.921900,116.478070").forEach(s -> points.add(LocationPoint.parseLatitudeLongitude(s)));
    DrivingRouteRequest request = DrivingRouteRequest.builder().from(origin).to(destination).waypoints(points)
      .build();
    // Mock
    when(routeService.queryDriving(request, false, Constants.MAP_PROVIDER_GOOGLE)).thenReturn(Mono.error(new RuntimeException("Error")));
    // Test
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/route/driving/google")
        .queryParam("from", "39.991680,116.332930")
        .queryParam("to", "39.901820,116.391020")
        .queryParam("waypoints", "39.917520,116.397170;39.921900,116.478070")
        .queryParam("cache", "false")
        .build())
      .exchange()
      .expectStatus()
      .is5xxServerError()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(500)
      .jsonPath("$.message")
      .isEqualTo("Error");
  }
}