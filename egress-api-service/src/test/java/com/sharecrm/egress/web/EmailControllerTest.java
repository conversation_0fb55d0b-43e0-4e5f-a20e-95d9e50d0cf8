package com.sharecrm.egress.web;

import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.email.EmailService;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date: 2024/12/9
 * @since 1.0.0
 */
@WebFluxTest(EmailController.class)
@Import(TestBeanConfig.class)
public class EmailControllerTest {

  @MockitoBean
  private EmailService emailService;

  @Autowired
  private WebTestClient webTestClient;

  @Test
  void sendEmail() {
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(Arrays.asList("12321"));
    request.setCc(Arrays.asList("<EMAIL>"));
    request.setSubject("test subject");
    request.setContent("test content");
    EmailSendResponse rs = new EmailSendResponse();
    rs.setSuccess(Boolean.TRUE);
    when(emailService.sendEmail(any(EmailSendRequest.class))).thenReturn(Mono.just(new EmailSendResponse()));

    webTestClient.post()
      .uri("/api/v2/emails")
      .contentType(MediaType.APPLICATION_JSON)
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .exists();

  }

  @Test
  void sendEmailWithSuccessResponse() {
    // Given
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(Arrays.asList("<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    EmailSendResponse response = new EmailSendResponse();
    response.setSuccess(true);
    response.setMessage("Email sent successfully");
    when(emailService.sendEmail(any(EmailSendRequest.class))).thenReturn(Mono.just(response));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/emails")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.success").isEqualTo(true)
        .jsonPath("$.data.message").isEqualTo("Email sent successfully");
  }

  @Test
  void sendEmailWithMultipleRecipients() {
    // Given
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(Arrays.asList("<EMAIL>", "<EMAIL>"));
    request.setCc(Arrays.asList("<EMAIL>", "<EMAIL>"));
    request.setSubject("Multiple Recipients Test");
    request.setContent("This is a test email with multiple recipients");

    EmailSendResponse response = new EmailSendResponse();
    response.setSuccess(true);
    when(emailService.sendEmail(any(EmailSendRequest.class))).thenReturn(Mono.just(response));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/emails")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.success").isEqualTo(true);
  }

  @Test
  void sendEmailWithError() {
    // Given
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(Arrays.asList("invalid-email"));
    request.setSubject("Test");
    request.setContent("Test");

    when(emailService.sendEmail(any(EmailSendRequest.class)))
        .thenReturn(Mono.error(new RuntimeException("Email service error")));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/emails")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void sendEmailWithFailureResponse() {
    // Given
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(Arrays.asList("<EMAIL>"));
    request.setSubject("Test Subject");
    request.setContent("Test Content");

    EmailSendResponse response = new EmailSendResponse();
    response.setSuccess(false);
    response.setMessage("Failed to send email");
    when(emailService.sendEmail(any(EmailSendRequest.class))).thenReturn(Mono.just(response));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/emails")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isBadRequest()
        .expectBody()
        .jsonPath("$.code").isEqualTo(400)
        .jsonPath("$.data.success").isEqualTo(false)
        .jsonPath("$.data.message").isEqualTo("Failed to send email");
  }

  @Test
  void sendEmailWithEmptyRequest() {
    // Given
    EmailSendRequest request = new EmailSendRequest();
    // All fields are null/empty - this should fail validation

    // When & Then
    webTestClient.post()
        .uri("/api/v2/emails")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isBadRequest()
        .expectBody()
        .jsonPath("$.code").isEqualTo(400);
  }

  @Test
  void sendEmailWithHtmlContent() {
    // Given
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(Arrays.asList("<EMAIL>"));
    request.setSubject("HTML Email Test");
    request.setContent("<html><body><h1>Hello World</h1><p>This is an HTML email.</p></body></html>");

    EmailSendResponse response = new EmailSendResponse();
    response.setSuccess(true);
    when(emailService.sendEmail(any(EmailSendRequest.class))).thenReturn(Mono.just(response));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/emails")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.success").isEqualTo(true);
  }
}