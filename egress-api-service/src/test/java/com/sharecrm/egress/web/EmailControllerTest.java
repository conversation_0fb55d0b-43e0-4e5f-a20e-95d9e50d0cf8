package com.sharecrm.egress.web;

import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.email.EmailService;
import com.sharecrm.egress.entity.EmailSendRequest;
import com.sharecrm.egress.entity.EmailSendResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date: 2024/12/9
 * @since 1.0.0
 */
@WebFluxTest(EmailController.class)
@Import(TestBeanConfig.class)
public class EmailControllerTest {

  @MockBean
  private EmailService emailService;

  @Autowired
  private WebTestClient webTestClient;

  @Test
  void sendEmail() {
    EmailSendRequest request = new EmailSendRequest();
    request.setTo(Arrays.asList("12321"));
    request.setCc(Arrays.asList("<EMAIL>"));
    request.setSubject("test subject");
    request.setContent("test content");
    EmailSendResponse rs = new EmailSendResponse();
    rs.setSuccess(Boolean.TRUE);
    when(emailService.sendEmail(any(EmailSendRequest.class))).thenReturn(Mono.just(new EmailSendResponse()));

    webTestClient.post()
      .uri("/api/v2/emails")
      .contentType(MediaType.APPLICATION_JSON)
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .exists();

  }
}