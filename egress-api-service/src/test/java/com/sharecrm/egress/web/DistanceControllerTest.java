package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.geo.GeoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


@WebFluxTest(controllers = DistanceController.class)
class DistanceControllerTest {
  @Autowired
  private WebTestClient webTestClient;
  @MockBean
  private GeoService service;

  @Test
  void testMatrixEndpointSuccess() {
    //setup
    List<PointDistance> data = List.of(new PointDistance(1, 1000), new PointDistance(2, 2000));
    // Mock
    when(service.queryDistance(any(), any(), any(Boolean.class))).thenReturn(Mono.just(data));
    // Test
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/distance/matrix")
        .queryParam("from", "40.056878,116.30815;40.063597,116.364973")
        .queryParam("to", "39.901820,116.391020")
        .queryParam("cache", "false")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.data", data);
  }

  @Test
  void testMatrixEndpointNotFound() {
    // Mock
    when(service.queryDistance(any(), any(), any(Boolean.class))).thenReturn(Mono.empty());
    // Test
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/distance/matrix")
        .queryParam("from", "40.056878,116.30815;40.063597,116.364973")
        .queryParam("to", "39.901820,116.391020")
        .queryParam("cache", "false")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200);
  }

  @Test
  void testMatrixEndpointInternalError() {
    // Mock
    when(service.queryDistance(any(), any(), any(Boolean.class))).thenReturn(Mono.error(new RuntimeException("Error")));
    // Test
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/distance/matrix")
        .queryParam("from", "40.056878,116.30815;40.063597,116.364973")
        .queryParam("to", "39.901820,116.391020")
        .queryParam("cache", "false")
        .build())
      .exchange()
      .expectStatus()
      .is5xxServerError()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(500)
      .jsonPath("$.message")
      .isEqualTo("Error");
  }
}
