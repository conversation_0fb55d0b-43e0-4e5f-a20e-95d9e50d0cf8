package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.MobileLocation;
import com.sharecrm.egress.service.MobileService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(MobileController.class)
class MobileControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockitoBean
  private MobileService mobileService;


  @Test
  void query() {
    // mock
    when(mobileService.query(any(String.class), any(String.class))).thenReturn(Mono.just(new MobileLocation()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/mobile/query")
        .queryParam("mobile", "13552346839")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .exists();
  }

  @Test
  void batchQuery() {
    // mock
    when(mobileService.query(any(String.class), any(String.class))).thenReturn(Mono.just(new MobileLocation()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/mobile/batch-query")
        .queryParam("mobiles", "13552346839,13852346839")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .exists();
  }

  @Test
  void restQueryForProxy() {
    // mock
    String number = "13552346839";
    MobileLocation data = new MobileLocation();
    data.setMobile(number);
    when(mobileService.query(any(String.class), any(String.class))).thenReturn(Mono.just(data));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/mobile/rest/query")
        .queryParam("mobile", number)
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.mobile")
      .isEqualTo(number);
  }
}