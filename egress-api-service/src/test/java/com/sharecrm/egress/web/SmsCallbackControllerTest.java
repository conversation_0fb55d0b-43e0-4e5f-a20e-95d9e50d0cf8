package com.sharecrm.egress.web;

import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.sms.SmsSender;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@WebFluxTest(SmsCallbackController.class)
class SmsCallbackControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockBean
  private SmsProperties smsProperties;

  @MockBean
  private ApplicationContext applicationContext;

  @MockBean
  private SmsSender mockSmsSender;

  @BeforeEach
  void setUp() {
    // Mock SmsProperties
    SmsProperties.MengniuConfig mengniuConfig = mock(SmsProperties.MengniuConfig.class);
    when(mengniuConfig.getId()).thenReturn("mengniu");
    when(smsProperties.getMengniu()).thenReturn(mengniuConfig);

    // Mock SmsSender for mengniu
    SmsProvider mengniuProvider = mock(SmsProvider.class);
    when(mengniuProvider.getId()).thenReturn("mengniu");
    SmsSender mengniuSender = mock(SmsSender.class);
    when(mengniuSender.provider()).thenReturn(mengniuProvider);

    // Mock SmsSender for test-provider
    SmsProvider testProvider = mock(SmsProvider.class);
    when(testProvider.getId()).thenReturn("test-provider");
    when(mockSmsSender.provider()).thenReturn(testProvider);

    // Mock ApplicationContext
    Map<String, SmsSender> senderBeans = new HashMap<>();
    senderBeans.put("mengniuSender", mengniuSender);
    senderBeans.put("testSender", mockSmsSender);
    when(applicationContext.getBeansOfType(SmsSender.class)).thenReturn(senderBeans);
  }

  @Test
  void mengniuSmsStatusCallback() {
    // Given
    String messageType = "delivery";
    String requestBody = "{\"status\":\"delivered\",\"messageId\":\"123\"}";

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/mengniu/messages/{messageType}", messageType)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(requestBody)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");

    // Verify that the callback was processed
    verify(mockSmsSender, timeout(1000)).smsStatusCallback(eq(messageType), eq(requestBody), any());
  }

  @Test
  void smsStatusCallbackWithJson() {
    // Given
    String providerId = "test-provider";
    String ext = "status";
    String requestBody = "{\"status\":\"delivered\",\"messageId\":\"456\"}";

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/providers/{id}/{ext}", providerId, ext)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(requestBody)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");

    // Verify that the callback was processed
    verify(mockSmsSender, timeout(1000)).smsStatusCallback(eq(ext), eq(requestBody), any());
  }

  @Test
  void smsStatusCallbackWithTextPlain() {
    // Given
    String providerId = "test-provider";
    String ext = "status";
    String requestBody = "status=delivered&messageId=789";

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/providers/{id}/{ext}", providerId, ext)
        .contentType(MediaType.TEXT_PLAIN)
        .bodyValue(requestBody)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");

    // Verify that the callback was processed
    verify(mockSmsSender, timeout(1000)).smsStatusCallback(eq(ext), eq(requestBody), any());
  }

  @Test
  void smsStatusCallbackWithXml() {
    // Given
    String providerId = "test-provider";
    String ext = "status";
    String requestBody = "<status><messageId>101112</messageId><status>delivered</status></status>";

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/providers/{id}/{ext}", providerId, ext)
        .contentType(MediaType.TEXT_XML)
        .bodyValue(requestBody)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");

    // Verify that the callback was processed
    verify(mockSmsSender, timeout(1000)).smsStatusCallback(eq(ext), eq(requestBody), any());
  }

  @Test
  void smsStatusCallbackFormData() {
    // Given
    String providerId = "test-provider";
    String ext = "status";
    MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
    formData.add("status", "delivered");
    formData.add("messageId", "131415");

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/providers/{id}/{ext}", providerId, ext)
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .bodyValue(formData)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");

    // Verify that the callback was processed with JSON representation of form data
    verify(mockSmsSender, timeout(1000)).smsStatusCallback(eq(ext), anyString(), any());
  }

  @Test
  void smsStatusCallbackWithNonMatchingProvider() {
    // Given
    String providerId = "non-existing-provider";
    String ext = "status";
    String requestBody = "{\"status\":\"delivered\"}";

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/providers/{id}/{ext}", providerId, ext)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(requestBody)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");

    // Verify that no callback was processed since provider doesn't match
    verify(mockSmsSender, never()).smsStatusCallback(anyString(), anyString(), any());
  }

  @Test
  void mengniuSmsStatusCallbackWithEmptyBody() {
    // Given
    String messageType = "delivery";
    String requestBody = "";

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/mengniu/messages/{messageType}", messageType)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(requestBody)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");
  }

  @Test
  void smsStatusCallbackWithEmptyFormData() {
    // Given
    String providerId = "test-provider";
    String ext = "status";
    MultiValueMap<String, String> emptyFormData = new LinkedMultiValueMap<>();

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/sms-callback/providers/{id}/{ext}", providerId, ext)
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .bodyValue(emptyFormData)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody(String.class)
        .isEqualTo("OK");
  }
}
