package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.DirtyWordsRequest;
import com.sharecrm.egress.entity.HasDirtyWordsResponse;
import com.sharecrm.egress.entity.ParseDirtyWordsItem;
import com.sharecrm.egress.entity.ParseDirtyWordsResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.DirtyWordService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(controllers = DirtyWordController.class)
class DirtyWordControllerTest {

  @Autowired
  private WebTestClient webTestClient;
  @MockitoBean
  private DirtyWordService dirtyWordService;

  @Test
  void parseFunction() {
    //setup
    List<ParseDirtyWordsItem> data = List.of(new ParseDirtyWordsItem("test", "index"));
    // Mock
    when(dirtyWordService.parseForFunction(any(), any())).thenReturn(data);
    // Test
    webTestClient
      .post()
      .uri("/api/v2/dirty-words/parse/{group}", "sms")
      .bodyValue("this is text")
      .exchange()
      .expectStatus()
      .isOk()
      .expectBodyList(new ParameterizedTypeReference<ParseDirtyWordsItem>() {
      })
      .hasSize(data.size());
  }

  @Test
  void hasDirtyWords() {
    DirtyWordsRequest request = new DirtyWordsRequest();
    request.setGroup("test");
    request.setText("this is text");
    ResponseEntity<EgressApiResponse<HasDirtyWordsResponse>> response = new ResponseEntity<>(HttpStatus.OK);
    when(dirtyWordService.hasDirtyWords(any())).thenReturn(response);

    webTestClient
      .post()
      .uri("/api/v2/dirty-words/has")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk();
  }
  
  @Test
  void parse(){
    DirtyWordsRequest request = new DirtyWordsRequest();
    request.setGroup("test");
    request.setText("this is text");
    ResponseEntity<EgressApiResponse<ParseDirtyWordsResponse>> response = new ResponseEntity<>(HttpStatus.OK);
    when(dirtyWordService.parse(any(DirtyWordsRequest.class))).thenReturn(response);
    webTestClient
      .post()
      .uri("/api/v2/dirty-words/parse")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk();
    
  }
}