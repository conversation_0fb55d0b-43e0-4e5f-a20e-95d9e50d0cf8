package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.utils.Constants;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@WebFluxTest(GeoCodeController.class)
class GeoCodeControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockitoBean
  private GeoService geocodeService;

  @Test
  void encodeEndpointSuccess() {
    // mock
    when(geocodeService.queryLocationPoint(any(String.class), any(String.class), any(Boolean.class), any(String.class))).thenReturn(Mono.just(new LocationPoint()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/geocode/encode")
        .queryParam("address", "北京市朝阳区望京街道阜通东大街6号")
        .queryParam("city", "北京市")
        .queryParam("cache", false)
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .exists();
  }

  @Test
  void geocodeEndpointNotFound() {
    // mock
    when(geocodeService.queryLocationPoint(any(String.class), any(String.class), any(Boolean.class), eq(Constants.MAP_PROVIDER_GOOGLE))).thenReturn(Mono.empty());
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/geocode/encode/google")
        .queryParam("address", "北京市朝阳区望京街道阜通东大街6号")
        .queryParam("city", "北京市")
        .queryParam("cache", false)
        .build())
      .exchange()
      .expectStatus()
      .isOk();
  }
}
