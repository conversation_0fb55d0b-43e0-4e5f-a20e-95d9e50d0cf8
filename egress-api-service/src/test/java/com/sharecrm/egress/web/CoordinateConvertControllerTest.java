package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.CoordinateConvertRequest;
import com.sharecrm.egress.entity.CoordinateConvertResponse;
import com.sharecrm.egress.geo.GeoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(CoordinateConvertController.class)
class CoordinateConvertControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockBean
  private GeoService geoService;

  @Test
  void convert() {
    CoordinateConvertResponse data = new CoordinateConvertResponse();
    data.setLatitude(100.02);
    data.setLongitude(36.01);
    // mock
    when(geoService.coordinateConvert(any())).thenReturn(Mono.just(data));
    // verify
    CoordinateConvertRequest request = new CoordinateConvertRequest();
    request.setLatitude(100.0);
    request.setLongitude(36.0);
    request.setSource("baidu");
    request.setTarget("amap");
    webTestClient
      .post()
      .uri("/api/v2/coordinate/convert")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .isEqualTo(data);
  }
}