package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.geo.GeoService;
import com.sharecrm.egress.utils.Constants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

@WebFluxTest(PoiSearchController.class)
class PoiSearchControllerTest {
  @Autowired
  private WebTestClient webTestClient;

  @MockBean
  private GeoService poiSearchService;

  @BeforeEach
  void setup() {
    reset(poiSearchService);
  }

  @Test
  void around() {
    // setup
    String latLng = "40.050930,116.301030";
    PoiResponse data = new PoiResponse();

    // mock
    when(poiSearchService.queryPoiAround(any(), eq(""))).thenReturn(Mono.just(data));

    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/poi/around")
        .queryParam("latLng", latLng)
        .queryParam("radius", 1000)
        .queryParam("keyword", "银行")
        .queryParam("cache", true)
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .isEqualTo(data);
  }

  @Test
  void aroundByGoogle() {
    // setup
    String latLng = "40.050930,116.301030";
    PoiResponse data = new PoiResponse();

    // mock
    when(poiSearchService.queryPoiAround(any(), eq(Constants.MAP_PROVIDER_GOOGLE))).thenReturn(Mono.just(data));

    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/poi/around/google")
        .queryParam("latLng", latLng)
        .queryParam("radius", 1000)
        .queryParam("keyword", "银行")
        .queryParam("language", "en")
        .queryParam("cache", true)
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .isEqualTo(data);
  }

  @Test
  void testPoiAroundNotFound() {
    // setup
    String latLng = "40.050930,116.301030";
    // mock
    when(poiSearchService.queryPoiAround(any(), any(String.class))).thenReturn(Mono.empty());
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/poi/around")
        .queryParam("latLng", latLng)
        .queryParam("radius", 1000)
        .queryParam("keyword", "银行")
        .queryParam("cache", false)
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(404);
  }

  @Test
  void testReverseGeocodeServerError() {
    // setup
    String latLng = "40.050930,116.301030";
    // mock
    // mock
    when(poiSearchService.queryPoiAround(any(), eq(""))).thenReturn(Mono.error(new RuntimeException("SERVER FAIL")));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/poi/around")
        .queryParam("latLng", latLng)
        .queryParam("radius", 1000)
        .queryParam("keyword", "酒店")
        .queryParam("cache", false)
        .build())
      .exchange()
      .expectStatus()
      .is5xxServerError()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(500)
      .jsonPath("$.message")
      .isEqualTo("SERVER FAIL")
      .jsonPath("$.data")
      .doesNotExist();
  }
}