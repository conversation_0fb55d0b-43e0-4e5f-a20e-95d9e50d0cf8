package com.sharecrm.egress.web;

import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.OcrService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date: 2024/12/6
 * @since 1.0.0
 */
@WebFluxTest(OcrController.class)
@Import(TestBeanConfig.class)
public class OcrControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockBean
  private OcrService ocrService;

  @Test
  void businessCard() {
    BusinessCardResponse rs = new BusinessCardResponse();
    rs.setName("test");

    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("test");
    when(ocrService.businessCard(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));

    webTestClient.post()
      .uri("/api/v2/ocr/business-cards")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .json("{\"code\":200,\"message\":\"ok\"}");
  }
}