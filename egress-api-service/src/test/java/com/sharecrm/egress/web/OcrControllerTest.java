package com.sharecrm.egress.web;

import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.OcrService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date: 2024/12/6
 * @since 1.0.0
 */
@WebFluxTest(OcrController.class)
@Import(TestBeanConfig.class)
public class OcrControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockitoBean
  private OcrService ocrService;

  @Test
  void businessCard() {
    BusinessCardResponse rs = new BusinessCardResponse();
    rs.setName("test");

    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("test");
    when(ocrService.businessCard(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));

    webTestClient.post()
      .uri("/api/v2/ocr/business-cards")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .json("{\"code\":200,\"message\":\"ok\"}");
  }

  @Test
  void businessCardWithCompleteData() {
    // Given
    BusinessCardResponse rs = new BusinessCardResponse();
    rs.setName("张三");
    rs.setCompany("测试公司");
    rs.setTitle("经理");
    rs.setMobile("***********");
    rs.setEmail("<EMAIL>");

    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64(
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
    when(ocrService.businessCard(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/ocr/business-cards")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.name").isEqualTo("张三")
        .jsonPath("$.data.company").isEqualTo("测试公司")
        .jsonPath("$.data.title").isEqualTo("经理")
        .jsonPath("$.data.mobile").isEqualTo("***********")
        .jsonPath("$.data.email").isEqualTo("<EMAIL>");
  }

  @Test
  void businessCardWithError() {
    // Given
    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("invalid_base64");
    when(ocrService.businessCard(any()))
        .thenReturn(Mono.error(new RuntimeException("OCR service error")));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/ocr/business-cards")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void businessCardWithEmptyResult() {
    // Given
    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("test_image");
    when(ocrService.businessCard(any())).thenReturn(Mono.just(EgressApiResponse.ok(null)));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/ocr/business-cards")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data").isEmpty();
  }

  @Test
  void businessCardWithEmptyRequest() {
    // Given
    BusinessCardRequest request = new BusinessCardRequest();
    // imageBase64 is null
    when(ocrService.businessCard(any())).thenReturn(Mono.just(EgressApiResponse.ok(new BusinessCardResponse())));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/ocr/business-cards")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200);
  }

  @Test
  void businessCardWithPartialData() {
    // Given
    BusinessCardResponse rs = new BusinessCardResponse();
    rs.setName("李四");
    rs.setMobile("***********");
    // 其他字段为空

    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("partial_data_image");
    when(ocrService.businessCard(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));

    // When & Then
    webTestClient.post()
        .uri("/api/v2/ocr/business-cards")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.name").isEqualTo("李四")
        .jsonPath("$.data.mobile").isEqualTo("***********");
  }
}