package com.sharecrm.egress.web;


import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.AsrService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@WebFluxTest(controllers = ASRController.class)
@Import(TestBeanConfig.class)
class AsrControllerTest {

  @Autowired
  private WebTestClient webTestClient;
  @MockitoBean
  private AsrService mockAsrService;

  @Test
  void testSuccessGetValues() {
    TencentSpeechResponse rs = new TencentSpeechResponse();
    rs.setContent("test result");
    when(mockAsrService.speechRecognition(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));
    webTestClient.post().uri("/api/v1/asr/sentence-recognition")
      .bodyValue(new byte[]{1, 2, 2})
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody().json("{\"code\":200,\"message\":\"ok\",\"data\":{\"content\":\"test result\"}}");
  }

  @Test
  void testEmptyValueFunction() {
    when(mockAsrService.speechRecognition(any())).thenReturn(Mono.empty());
    webTestClient.post().uri("/api/v1/asr/sentence-recognition")
      .bodyValue(new byte[]{})
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .json("{\"code\":200,\"message\":\"ok\",\"data\":{}}");
  }

  @Test
  void createRecTask() {
    AsrCreateRecTaskResponse rs = new AsrCreateRecTaskResponse();
    rs.setTaskId("123456");
    when(mockAsrService.createRecTask(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));
    webTestClient.post().uri("/api/v1/asr/rec-task")
      .bodyValue(new AsrCreateRecTaskRequest())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody().json("{\"code\":200,\"message\":\"ok\",\"data\":{\"taskId\":\"123456\"}}");
  }

  @Test
  void speechRecognitionWithError() {
    // Given
    when(mockAsrService.speechRecognition(any()))
        .thenReturn(Mono.error(new RuntimeException("ASR service error")));

    // When & Then
    webTestClient.post().uri("/api/v1/asr/sentence-recognition")
        .bodyValue(new byte[] { 1, 2, 3 })
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void createRecTaskWithError() {
    // Given
    when(mockAsrService.createRecTask(any()))
        .thenReturn(Mono.error(new RuntimeException("Create task error")));

    // When & Then
    webTestClient.post().uri("/api/v1/asr/rec-task")
        .bodyValue(new AsrCreateRecTaskRequest())
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void speechRecognitionWithLargeAudio() {
    // Given
    TencentSpeechResponse rs = new TencentSpeechResponse();
    rs.setContent("large audio recognition result");
    when(mockAsrService.speechRecognition(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));

    // When & Then
    byte[] largeAudio = new byte[1024]; // 模拟大音频文件
    webTestClient.post().uri("/api/v1/asr/sentence-recognition")
        .bodyValue(largeAudio)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.content").isEqualTo("large audio recognition result");
  }

  @Test
  void createRecTaskWithCompleteRequest() {
    // Given
    AsrCreateRecTaskRequest request = new AsrCreateRecTaskRequest();
    request.setUrl("http://example.com/audio.wav");
    request.setChannelNum(1);
    request.setEngineModelType("16k_zh");

    AsrCreateRecTaskResponse rs = new AsrCreateRecTaskResponse();
    rs.setTaskId("task-789");
    when(mockAsrService.createRecTask(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));

    // When & Then
    webTestClient.post().uri("/api/v1/asr/rec-task")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.taskId").isEqualTo("task-789");
  }

  @Test
  void speechRecognitionWithNullResult() {
    // Given
    when(mockAsrService.speechRecognition(any())).thenReturn(Mono.just(EgressApiResponse.ok(null)));

    // When & Then
    webTestClient.post().uri("/api/v1/asr/sentence-recognition")
        .bodyValue(new byte[] { 1, 2, 3 })
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data").isEmpty();
  }

}
