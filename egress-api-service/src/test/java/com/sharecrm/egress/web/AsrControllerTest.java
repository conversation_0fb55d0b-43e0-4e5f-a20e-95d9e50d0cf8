package com.sharecrm.egress.web;


import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.AsrService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@WebFluxTest(controllers = ASRController.class)
@Import(TestBeanConfig.class)
class AsrControllerTest {

  @Autowired
  private WebTestClient webTestClient;
  @MockBean
  private AsrService mockAsrService;

  @Test
  void testSuccessGetValues() {
    TencentSpeechResponse rs = new TencentSpeechResponse();
    rs.setContent("test result");
    when(mockAsrService.speechRecognition(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));
    webTestClient.post().uri("/api/v1/asr/sentence-recognition")
      .bodyValue(new byte[]{1, 2, 2})
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody().json("{\"code\":200,\"message\":\"ok\",\"data\":{\"content\":\"test result\"}}");
  }

  @Test
  void testEmptyValueFunction() {
    when(mockAsrService.speechRecognition(any())).thenReturn(Mono.empty());
    webTestClient.post().uri("/api/v1/asr/sentence-recognition")
      .bodyValue(new byte[]{})
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .json("{\"code\":200,\"message\":\"ok\",\"data\":{}}");
  }

  @Test
  void createRecTask() {
    AsrCreateRecTaskResponse rs = new AsrCreateRecTaskResponse();
    rs.setTaskId("123456");
    when(mockAsrService.createRecTask(any())).thenReturn(Mono.just(EgressApiResponse.ok(rs)));
    webTestClient.post().uri("/api/v1/asr/rec-task")
      .bodyValue(new AsrCreateRecTaskRequest())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody().json("{\"code\":200,\"message\":\"ok\",\"data\":{\"taskId\":\"123456\"}}");
  }


}
