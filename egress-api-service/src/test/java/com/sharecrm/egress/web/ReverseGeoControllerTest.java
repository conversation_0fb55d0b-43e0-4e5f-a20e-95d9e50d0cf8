package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import com.sharecrm.egress.geo.GeoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(ReverseGeoController.class)
class ReverseGeoControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockBean
  private GeoService geoService;

  @Test
  void decodeByPost() {
    GeoAddress data = new GeoAddress();
    data.setAddress("卫星大厦");
    // mock
    when(geoService.queryReverseGeoAddress(any())).thenReturn(Mono.just(data));
    // verify
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(100.0);
    request.setLongitude(36.0);
    webTestClient
      .post()
      .uri("/api/v2/reverse-geocode/reverse")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200)
      .jsonPath("$.message")
      .isEqualTo("ok")
      .jsonPath("$.data")
      .isEqualTo(data);
  }

  @Test
  void decode() {
    GeoAddress data = new GeoAddress();
    data.setAddress("卫星大厦");
    // mock
    when(geoService.queryReverseGeoAddress(any(ReverseGeoRequest.class))).thenReturn(Mono.just(data));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/reverse-geocode/decode")
        .queryParam("latLng", "39.990464,116.481488")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200);
  }


  @Test
  void batchDecode() {
    GeoAddress data = new GeoAddress();
    data.setAddress("卫星大厦");
    // mock
    when(geoService.queryReverseGeoAddress(any(ReverseGeoRequest.class))).thenReturn(Mono.just(data));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/reverse-geocode/batch-decode")
        .queryParam("codes", "39.990464,116.481488")
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200);

  }

  @Test
  void queryGoogle() {
    GeoAddress data = new GeoAddress();
    data.setAddress("卫星大厦");
    when(geoService.queryReverseGeoAddress(any(ReverseGeoRequest.class),any(String.class))).thenReturn(Mono.just(data));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/reverse-geocode/decode/google")
        .queryParam("latLng", "39.990464,116.481488")
        .queryParam("cache",Boolean.FALSE)
        .build())
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .jsonPath("$.code")
      .isEqualTo(200);
  }

}
