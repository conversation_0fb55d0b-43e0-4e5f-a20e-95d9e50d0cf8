package com.sharecrm.egress.web;

import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sms.SmsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(value = SmsController.class, properties = "sharecrm.api.sms.enabled=true")
class SmsControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockitoBean
  private SmsService smsService;

  @Test
  void sendForFunction() {
    // mock
    when(smsService.sendChineseSms(any(), any())).thenReturn(Mono.just(new SmsSendResponse()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/sms/api/json/send")
        .queryParam("phone", "13552346839")
        .queryParam("param", "test")
        .build())
      .exchange()
      .expectStatus()
      .isOk();
  }

  @Test
  void queryTemplateById() {
    // mock
    when(smsService.queryTemplate(any(), any())).thenReturn(Mono.just(new SmsTemplateDetail()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/sms/templates/100")
        .build())
      .exchange()
      .expectStatus()
      .isOk();
  }

}