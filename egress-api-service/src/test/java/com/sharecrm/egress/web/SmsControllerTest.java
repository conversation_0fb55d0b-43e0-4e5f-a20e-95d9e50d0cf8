package com.sharecrm.egress.web;

import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.entity.SmsTemplateDetail;
import com.sharecrm.egress.sms.SmsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(value = SmsController.class, properties = "sharecrm.api.sms.enabled=true")
class SmsControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockitoBean
  private SmsService smsService;

  @Test
  void sendForFunction() {
    // mock
    when(smsService.sendChineseSms(any(), any())).thenReturn(Mono.just(new SmsSendResponse()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/sms/api/json/send")
        .queryParam("phone", "13552346839")
        .queryParam("param", "test")
        .build())
      .exchange()
      .expectStatus()
      .isOk();
  }

  @Test
  void queryTemplateById() {
    // mock
    when(smsService.queryTemplate(any(), any())).thenReturn(Mono.just(new SmsTemplateDetail()));
    // verify
    webTestClient
      .get()
      .uri(uriBuilder -> uriBuilder
        .path("/api/v2/sms/templates/100")
        .build())
      .exchange()
      .expectStatus()
      .isOk();
  }

  @Test
  void sendForFunctionWithMultipleParams() {
    // mock
    when(smsService.sendChineseSms(any(), any())).thenReturn(Mono.just(new SmsSendResponse()));
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("phone", "13552346839")
            .queryParam("param", "test")
            .queryParam("templateId", "123")
            .build())
        .exchange()
        .expectStatus()
        .isOk();
  }

  @Test
  void sendForFunctionWithInvalidPhone() {
    // mock
    when(smsService.sendChineseSms(any(), any())).thenReturn(Mono.just(new SmsSendResponse()));
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("phone", "invalid-phone")
            .queryParam("param", "test")
            .build())
        .exchange()
        .expectStatus()
        .isOk(); // Controller should handle validation
  }

  @Test
  void sendForFunctionWithEmptyParam() {
    // mock
    when(smsService.sendChineseSms(any(), any())).thenReturn(Mono.just(new SmsSendResponse()));
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("phone", "13552346839")
            .queryParam("param", "")
            .build())
        .exchange()
        .expectStatus()
        .isOk();
  }

  @Test
  void queryTemplateByIdWithDifferentId() {
    // mock
    when(smsService.queryTemplate(any(), any())).thenReturn(Mono.just(new SmsTemplateDetail()));
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/templates/999")
            .build())
        .exchange()
        .expectStatus()
        .isOk();
  }

  @Test
  void sendForFunctionWithServiceError() {
    // mock
    when(smsService.sendChineseSms(any(), any()))
        .thenReturn(Mono.error(new RuntimeException("SMS service error")));
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("phone", "13552346839")
            .queryParam("param", "test")
            .build())
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void queryTemplateByIdWithServiceError() {
    // mock
    when(smsService.queryTemplate(any(), any()))
        .thenReturn(Mono.error(new RuntimeException("Template service error")));
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/templates/100")
            .build())
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void sendForFunctionWithSuccessResponse() {
    // mock
    SmsSendResponse response = new SmsSendResponse();
    response.setSuccess(true);
    response.setMessage("SMS sent successfully");
    when(smsService.sendChineseSms(any(), any())).thenReturn(Mono.just(response));

    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("phone", "13552346839")
            .queryParam("param", "test")
            .build())
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.success").isEqualTo(true)
        .jsonPath("$.message").isEqualTo("SMS sent successfully");
  }

  @Test
  void queryTemplateByIdWithCompleteResponse() {
    // mock
    SmsTemplateDetail template = new SmsTemplateDetail();
    template.setTemplateId("100");
    template.setName("Test Template");
    template.setContent("Hello {1}, your code is {2}");
    when(smsService.queryTemplate(any(), any())).thenReturn(Mono.just(template));

    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/templates/100")
            .build())
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.templateId").isEqualTo("100")
        .jsonPath("$.data.name").isEqualTo("Test Template")
        .jsonPath("$.data.content").isEqualTo("Hello {1}, your code is {2}");
  }

  @Test
  void sendForFunctionWithFailureResponse() {
    // mock
    SmsSendResponse response = new SmsSendResponse();
    response.setSuccess(false);
    response.setMessage("Failed to send SMS");
    when(smsService.sendChineseSms(any(), any())).thenReturn(Mono.just(response));

    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("phone", "13552346839")
            .queryParam("param", "test")
            .build())
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.success").isEqualTo(false)
        .jsonPath("$.message").isEqualTo("Failed to send SMS");
  }

  @Test
  void sendForFunctionWithMissingPhone() {
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("param", "test")
            .build())
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.success").isEqualTo(false)
        .jsonPath("$.message").isEqualTo("param is empty");
  }

  @Test
  void sendForFunctionWithMissingParam() {
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/api/json/send")
            .queryParam("phone", "13552346839")
            .build())
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.success").isEqualTo(false)
        .jsonPath("$.message").isEqualTo("param is empty");
  }

  @Test
  void queryTemplateByIdWithInvalidId() {
    // mock
    when(smsService.queryTemplate(any(), any())).thenReturn(Mono.just(new SmsTemplateDetail()));
    // verify
    webTestClient
        .get()
        .uri(uriBuilder -> uriBuilder
            .path("/api/v2/sms/templates/invalid-id")
            .build())
        .exchange()
        .expectStatus()
        .isOk(); // Controller should handle this gracefully
  }

}