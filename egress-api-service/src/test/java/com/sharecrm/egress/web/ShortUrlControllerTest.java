package com.sharecrm.egress.web;

import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.entity.ShortUrlBatchCreateRequest;
import com.sharecrm.egress.entity.ShortUrlBatchCreateResponse;
import com.sharecrm.egress.entity.ShortUrlCreateRequest;
import com.sharecrm.egress.entity.ShortUrlCreateResponse;
import com.sharecrm.egress.entity.ShortUrlOriginalResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.ShortUrlService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date: 2024/12/6
 * @since 1.0.0
 */
@WebFluxTest(value = ShortUrlController.class, properties = "sharecrm.api.short.url.enabled=true")
@Import({TestBeanConfig.class})
public class ShortUrlControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockBean
  private ShortUrlService shortUrlService;


  @Test
  void redirectOriginalUrl() {
    String code = "123";
    String userAgent = "test";
    when(shortUrlService.getOriginalUrl(eq(code), eq(userAgent))).thenReturn(Mono.just(new ShortUrlOriginalResponse()));
    webTestClient.get()
      .uri("/api/v2/public-short-urls/{code}", code)
      .header("User-Agent", userAgent)
      .exchange()
      .expectStatus()
      .isFound();
  }

  @Test
  void create() {
    ShortUrlCreateRequest request = new ShortUrlCreateRequest();
    request.setUrl("https://www.baidu.com");
    ShortUrlCreateResponse response = new ShortUrlCreateResponse();
    when(shortUrlService.createShortUrl(eq(request))).thenReturn(Mono.just(EgressApiResponse.ok(response)));

    webTestClient.post()
      .uri("/api/v2/private-short-urls")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .json("{\"code\":200,\"message\":\"ok\"}");
  }

  @Test
  void batchCreate() {
    ShortUrlBatchCreateRequest request = new ShortUrlBatchCreateRequest();
    request.setUrls(Arrays.asList("https://www.baidu.com"));
    ShortUrlBatchCreateResponse response = new ShortUrlBatchCreateResponse();
    when(shortUrlService.batchCreateShortUrl(eq(request))).thenReturn(Mono.just(EgressApiResponse.ok(response)));

    webTestClient.post()
      .uri("/api/v2/private-short-urls/batch")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .json("{\"code\":200,\"message\":\"ok\"}");
  }

  @Test
  void getOriginalUrl() {
    String code = "123";
    String userAgent = "test";
    when(shortUrlService.getOriginalUrl(any(String.class), any(String.class))).thenReturn(Mono.just(new ShortUrlOriginalResponse()));
    webTestClient.get()
      .uri("/api/v2/private-short-urls/{code}", code)
      .header("User-Agent", userAgent)
      .exchange()
      .expectStatus()
      .isOk()
      .expectBody()
      .json("{\"code\":200,\"message\":\"ok\"}");
  }
}