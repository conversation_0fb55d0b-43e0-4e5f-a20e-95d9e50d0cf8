package com.sharecrm.egress.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ThirdPushClientExceptionTest {

    @Test
    void testDefaultConstructor() {
        ThirdPushClientException exception = new ThirdPushClientException();
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    void testConstructorWithMessage() {
        String errorMessage = "Push client error";
        ThirdPushClientException exception = new ThirdPushClientException(errorMessage);
        
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }
    
    @Test
    void testConstructorWithCause() {
        Throwable cause = new RuntimeException("Original error");
        ThirdPushClientException exception = new ThirdPushClientException(cause);
        
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    void testConstructorWithMessageAndCause() {
        String errorMessage = "Push client error";
        Throwable cause = new RuntimeException("Original error");
        ThirdPushClientException exception = new ThirdPushClientException(errorMessage, cause);
        
        assertEquals(errorMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    void testExceptionInheritance() {
        ThirdPushClientException exception = new ThirdPushClientException();
        assertTrue(exception instanceof ThirdPushException);
    }
}
