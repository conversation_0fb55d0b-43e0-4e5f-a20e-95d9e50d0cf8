package com.sharecrm.egress.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ThirdPushExceptionTest {

    @Test
    void testDefaultConstructor() {
        ThirdPushException exception = new ThirdPushException();
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    void testConstructorWithMessage() {
        String errorMessage = "Push notification error";
        ThirdPushException exception = new ThirdPushException(errorMessage);
        
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }
    
    @Test
    void testConstructorWithCause() {
        Throwable cause = new RuntimeException("Original error");
        ThirdPushException exception = new ThirdPushException(cause);
        
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    void testConstructorWithMessageAndCause() {
        String errorMessage = "Push notification error";
        Throwable cause = new RuntimeException("Original error");
        ThirdPushException exception = new ThirdPushException(errorMessage, cause);
        
        assertEquals(errorMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    void testExceptionInheritance() {
        ThirdPushException exception = new ThirdPushException();
        assertTrue(exception instanceof RuntimeException);
    }
}
