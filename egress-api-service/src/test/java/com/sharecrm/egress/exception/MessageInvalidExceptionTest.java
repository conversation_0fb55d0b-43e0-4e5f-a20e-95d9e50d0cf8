package com.sharecrm.egress.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class MessageInvalidExceptionTest {

    @Test
    void testDefaultConstructor() {
        MessageInvalidException exception = new MessageInvalidException();
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    void testConstructorWithMessage() {
        String errorMessage = "Invalid message format";
        MessageInvalidException exception = new MessageInvalidException(errorMessage);
        
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }
    
    @Test
    void testConstructorWithCause() {
        Throwable cause = new RuntimeException("Original error");
        MessageInvalidException exception = new MessageInvalidException(cause);
        
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    void testConstructorWithMessageAndCause() {
        String errorMessage = "Invalid message format";
        Throwable cause = new RuntimeException("Original error");
        MessageInvalidException exception = new MessageInvalidException(errorMessage, cause);
        
        assertEquals(errorMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    void testExceptionInheritance() {
        MessageInvalidException exception = new MessageInvalidException();
        assertTrue(exception instanceof ThirdPushException);
    }
}
