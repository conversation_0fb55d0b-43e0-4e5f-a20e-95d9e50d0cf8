package com.sharecrm.egress.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SmsArgExceptionTest {

    @Test
    void testConstructorWithMessage() {
        String errorMessage = "Invalid SMS arguments";
        SmsArgException exception = new SmsArgException(errorMessage);
        
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }
    
    @Test
    void testExceptionInheritance() {
        SmsArgException exception = new SmsArgException("Test");
        assertTrue(exception instanceof RuntimeException);
    }
}
