package com.sharecrm.egress.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SmsExceptionTest {

    @Test
    void testConstructorWithMessage() {
        String errorMessage = "SMS service error";
        SmsException exception = new SmsException(errorMessage);
        
        assertEquals(errorMessage, exception.getMessage());
        assertNull(exception.getCause());
    }
    
    @Test
    void testExceptionInheritance() {
        SmsException exception = new SmsException("Test");
        assertTrue(exception instanceof RuntimeException);
    }
}
