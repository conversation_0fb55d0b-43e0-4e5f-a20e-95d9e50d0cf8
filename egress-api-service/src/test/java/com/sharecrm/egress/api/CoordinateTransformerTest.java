package com.sharecrm.egress.api;

import com.sharecrm.egress.entity.LocationPoint;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 以天安门区域的地理位置，进行对应地址转换
 */
@Slf4j
class CoordinateTransformerTest {
  @Test
  void wgs84ToGcj02() {
    double wgsLng = 116.391349;
    double wgsLat = 39.907375;
    double[] values = CoordinateTransformer.wgs84ToGcj02(wgsLng, wgsLat);
    log.info("wgs84ToGcj02, wgs-84({},{}), gcj-02({},{})", wgsLng, wgsLat, values[0], values[1]);
    assertEquals(116.39759019123527, values[0], 0.000001);
    assertEquals(39.90877629414095, values[1], 0.000001);
  }

  @Test
  void wgs84ToBd09() {
    double wgsLng = 116.391349;
    double wgsLat = 39.907375;
    double[] values = CoordinateTransformer.wgs84ToBd09(wgsLng, wgsLat);
    log.info("wgs84ToGcj02, wgs-84({},{}), bd-09({},{})", wgsLng, wgsLat, values[0], values[1]);
    assertEquals(116.40396302524593, values[0], 0.000001);
    assertEquals(39.915119833724745, values[1], 0.000001);
  }

  @Test
  void gcj02ToWgs84() {
    double gcjLng = 116.397590;
    double gcjLat = 39.908776;
    double[] values = CoordinateTransformer.gcj02ToWgs84(gcjLng, gcjLat);
    log.info("gcj02ToWgs84, gcj-02({},{}), wgs-84({},{})", gcjLng, gcjLat, values[0], values[1]);
    assertEquals(116.39134640021364, values[0], 0.000001);
    assertEquals(39.90737247778593, values[1], 0.000001);
  }

  @Test
  void gcj02ToBd09() {
    double gcjLng = 116.397590;
    double gcjLat = 39.908776;
    double[] values = CoordinateTransformer.gcj02ToBd09(gcjLng, gcjLat);
    log.info("gcj02ToBd09, gcj-02({},{}), bd-09({},{})", gcjLng, gcjLat, values[0], values[1]);
    assertEquals(116.40396283442355, values[0], 0.000001);
    assertEquals(39.915119539232755, values[1], 0.000001);
  }

  @Test
  void bd09ToGcj02() {
    double bdLng = 116.403963;
    double bdLat = 39.915120;
    double[] values = CoordinateTransformer.bd09ToGcj02(bdLng, bdLat);
    log.info("bd09ToGcj02, bd-09({},{}), gcj-02({},{})", bdLng, bdLat, values[0], values[1]);
    assertEquals(116.39759036679348, values[0], 0.000001);
    assertEquals(39.90877686490143, values[1], 0.000001);
  }

  @Test
  void bd09ToWgs84() {
    double bdLng = 116.403963;
    double bdLat = 39.915120;
    double[] values = CoordinateTransformer.bd09ToWgs84(bdLng, bdLat);
    log.info("bd09ToGcj02, bd-09({},{}), wgs-84({},{})", bdLng, bdLat, values[0], values[1]);
    assertEquals(116.39134676682664, values[0], 0.000001);
    assertEquals(39.9073733425708, values[1], 0.000001);
  }

  @Test
  void testGetDistance() {
    // 北京市西城区广外街道马连道中里一区
    LocationPoint from = LocationPoint.parseLatitudeLongitude("39.889049,116.325275");
    // 北京市海淀区知春路甲63号卫星大厦
    LocationPoint to = LocationPoint.parseLatitudeLongitude("39.976809,116.332463");
    double distance = CoordinateTransformer.getDistance(from.getLongitude(), from.getLatitude(), to.getLongitude(), to.getLatitude());
    log.info("testGetDistance, from: ({}), to: ({}), distance: {}m", from.toLongitudeLatitude(), to.toLongitudeLatitude(), distance);
    assertEquals(9788.8126, distance, 0.0001);
  }
}