package com.sharecrm.egress.api;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BaiduApiTest {

  @Test
  void testConvertEmptyToNull_withEmptyString() {
    // Given
    String emptyString = "";
    
    // When
    String result = BaiduApi.convertEmptyToNull(emptyString);
    
    // Then
    assertNull(result);
  }

  @Test
  void testConvertEmptyToNull_withEmptyArray() {
    // Given
    String emptyArray = "[]";
    
    // When
    String result = BaiduApi.convertEmptyToNull(emptyArray);
    
    // Then
    assertNull(result);
  }

  @Test
  void testConvertEmptyToNull_withValidString() {
    // Given
    String validString = "valid content";
    
    // When
    String result = BaiduApi.convertEmptyToNull(validString);
    
    // Then
    assertEquals("valid content", result);
  }

  @Test
  void testConvertEmptyToNull_withNullString() {
    // Given
    String nullString = null;
    
    // When
    String result = BaiduApi.convertEmptyToNull(nullString);
    
    // Then
    assertNull(result);
  }

  @Test
  void testConvertEmptyToNull_withEmptyList() {
    // Given
    List<String> emptyList = Collections.emptyList();
    
    // When
    List<String> result = BaiduApi.convertEmptyToNull(emptyList);
    
    // Then
    assertNull(result);
  }

  @Test
  void testConvertEmptyToNull_withValidList() {
    // Given
    List<String> validList = Arrays.asList("item1", "item2");
    
    // When
    List<String> result = BaiduApi.convertEmptyToNull(validList);
    
    // Then
    assertEquals(validList, result);
    assertEquals(2, result.size());
  }

  @Test
  void testConvertEmptyToNull_withNullList() {
    // Given
    List<String> nullList = null;
    
    // When
    List<String> result = BaiduApi.convertEmptyToNull(nullList);
    
    // Then
    assertNull(result);
  }

  @Test
  void testConvertEmptyToNull_withEmptyArray() {
    // Given
    String[] emptyArray = new String[0];
    
    // When
    String[] result = BaiduApi.convertEmptyToNull(emptyArray);
    
    // Then
    assertNull(result);
  }

  @Test
  void testConvertEmptyToNull_withValidArray() {
    // Given
    String[] validArray = {"item1", "item2"};
    
    // When
    String[] result = BaiduApi.convertEmptyToNull(validArray);
    
    // Then
    assertEquals(validArray, result);
    assertEquals(2, result.length);
  }

  @Test
  void testConvertEmptyToNull_withNullArray() {
    // Given
    String[] nullArray = null;
    
    // When
    String[] result = BaiduApi.convertEmptyToNull(nullArray);
    
    // Then
    assertNull(result);
  }
}
