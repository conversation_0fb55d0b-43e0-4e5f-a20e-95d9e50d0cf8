package com.sharecrm.egress.api;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class BaiduErrorCodeTest {

  @Test
  void testGetMessage_withKnownCodes() {
    // Test known error codes
    assertEquals("正常", BaiduErrorCode.getMessage(0));
    assertEquals("服务器内部错误", BaiduErrorCode.getMessage(1));
    assertEquals("参数无效", BaiduErrorCode.getMessage(2));
    assertEquals("权限校验失败", BaiduErrorCode.getMessage(3));
    assertEquals("配额校验失败", BaiduErrorCode.getMessage(4));
    assertEquals("ak不存在或者非法", BaiduErrorCode.getMessage(5));
    assertEquals("无返回结果", BaiduErrorCode.getMessage(7));
    assertEquals("上传内容超过8M", BaiduErrorCode.getMessage(10));
    assertEquals("AK参数不存在", BaiduErrorCode.getMessage(101));
    assertEquals("APP不存在，AK有误请检查再重试", BaiduErrorCode.getMessage(200));
    assertEquals("永久配额超限，限制访问", BaiduErrorCode.getMessage(301));
    assertEquals("当前并发量已经超过约定并发配额，限制访问", BaiduErrorCode.getMessage(401));
  }

  @Test
  void testGetMessage_withUnknownCodesStartingWith2() {
    // Test unknown codes starting with 2 (should return "无权限")
    assertEquals("无权限", BaiduErrorCode.getMessage(299));
    assertEquals("无权限", BaiduErrorCode.getMessage(280));
  }

  @Test
  void testGetMessage_withUnknownCodesStartingWith3() {
    // Test unknown codes starting with 3 (should return "配额校验失败")
    assertEquals("配额校验失败", BaiduErrorCode.getMessage(399));
    assertEquals("配额校验失败", BaiduErrorCode.getMessage(350));
  }

  @Test
  void testGetMessage_withUnknownCodesStartingWith4() {
    // Test unknown codes starting with 4 (should return "并发超过限制")
    assertEquals("并发超过限制", BaiduErrorCode.getMessage(499));
    assertEquals("并发超过限制", BaiduErrorCode.getMessage(450));
  }

  @Test
  void testGetMessage_withOtherUnknownCodes() {
    // Test other unknown codes (should return "未知错误")
    assertEquals("未知错误", BaiduErrorCode.getMessage(599));
    assertEquals("未知错误", BaiduErrorCode.getMessage(150));
    assertEquals("未知错误", BaiduErrorCode.getMessage(999));
  }

  @Test
  void testGetMessage_withSmallUnknownCodes() {
    // Test small unknown codes (should return "UNKNOWN")
    assertEquals("UNKNOWN", BaiduErrorCode.getMessage(99));
    assertEquals("UNKNOWN", BaiduErrorCode.getMessage(50));
    assertEquals("UNKNOWN", BaiduErrorCode.getMessage(8));
  }

  @Test
  void testGetMessage_withNegativeCodes() {
    // Test negative codes (should return "UNKNOWN")
    assertEquals("UNKNOWN", BaiduErrorCode.getMessage(-1));
    assertEquals("UNKNOWN", BaiduErrorCode.getMessage(-100));
  }
}
