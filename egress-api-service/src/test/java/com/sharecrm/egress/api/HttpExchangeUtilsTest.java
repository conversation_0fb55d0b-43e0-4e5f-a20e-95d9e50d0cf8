package com.sharecrm.egress.api;

import com.sharecrm.egress.config.ConnectionProperties;
import com.sharecrm.egress.config.MapProperties;
import org.junit.jupiter.api.Test;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class HttpExchangeUtilsTest {

  private static final ExchangeStrategies strategies = ExchangeStrategies.empty().build();

  @Test
  void buildAMapApi() {
    MapProperties.AMapConfig config = new MapProperties.AMapConfig();
    config.setKey("test-key");
    assertNotNull(HttpExchangeUtils.buildAMapApi(strategies, config));
  }

  @Test
  void buildBaiduApi() {
    MapProperties.BaiduConfig config = new MapProperties.BaiduConfig();
    config.setKey("test-key");
    assertNotNull(HttpExchangeUtils.buildBaiduApi(strategies, config));
  }

  @Test
  void buildTencentMapApi() {
    MapProperties.TencentConfig config = new MapProperties.TencentConfig();
    config.setKey("test-key");
    assertNotNull(HttpExchangeUtils.buildTencentMapApi(strategies, config));
  }

  @Test
  void buildGoogleMapApi() {
    MapProperties.GoogleConfig config = new MapProperties.GoogleConfig();
    config.setKey("test-key");
    assertNotNull(HttpExchangeUtils.buildGoogleMapApi(strategies, config));
  }

  @Test
  void buildGoogleRouteApi() {
    MapProperties.GoogleConfig config = new MapProperties.GoogleConfig();
    config.setKey("test-key");
    assertNotNull(HttpExchangeUtils.buildGoogleRouteApi(strategies, config));
  }

  @Test
  void connector() {
    ClientHttpConnector connector = HttpExchangeUtils.connector(new ConnectionProperties());
    assertNotNull(connector);
  }

}