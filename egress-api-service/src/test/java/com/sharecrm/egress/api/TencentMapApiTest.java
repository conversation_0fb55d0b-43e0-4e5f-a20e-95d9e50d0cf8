package com.sharecrm.egress.api;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * TencentMapApi测试类
 * 由于TencentMapApi是一个接口，主要测试其存在性和基本结构
 */
class TencentMapApiTest {

  @Test
  void testTencentMapApiInterface() {
    // 验证接口存在且可以被引用
    Class<?> apiClass = TencentMapApi.class;
    assertNotNull(apiClass);
    
    // 验证是接口
    assert apiClass.isInterface();
    
    // 验证有方法定义
    assert apiClass.getDeclaredMethods().length > 0;
  }
}
