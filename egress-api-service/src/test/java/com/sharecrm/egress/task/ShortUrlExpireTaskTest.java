package com.sharecrm.egress.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sharecrm.egress.config.ShortUrlProperties;
import com.sharecrm.egress.dao.ShortUrlHistoryMapper;
import com.sharecrm.egress.dao.ShortUrlMapper;
import com.sharecrm.egress.entity.ShortUrl;
import com.sharecrm.egress.entity.ShortUrlHistory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ShortUrlExpireTaskTest {

  @Mock
  private ShortUrlProperties properties;

  @Mock
  private ShortUrlMapper shortUrlMapper;

  @Mock
  private ShortUrlHistoryMapper shortUrlHistoryMapper;

  private ShortUrlExpireTask task;

  @BeforeEach
  public void setup() {
    task = new ShortUrlExpireTask(properties, shortUrlMapper, shortUrlHistoryMapper);
  }

  @Test
  public void testDoExpire() {
    // Setup test data
    ShortUrl url1 = createShortUrl("code1", "http://example.com/1",
        System.currentTimeMillis() - 100 * 24 * 60 * 60 * 1000L); // 100 days old
    ShortUrl url2 = createShortUrl("code2", "http://example.com/2", System.currentTimeMillis()); // new

    Page<ShortUrl> page = new Page<>(1, 100);
    page.setRecords(Arrays.asList(url1, url2));

    // Mock the default expire days
    when(properties.getDefaultExpireDay()).thenReturn(30);

    // Mock expireRule map
    Map<String, Integer> expireRules = new HashMap<>();
    expireRules.put("example.com", 60);
    when(properties.getExpireRule()).thenReturn(expireRules);

    // Mock the shortUrlMapper behavior
    when(shortUrlMapper.selectPage(any(), any())).thenReturn(page).thenReturn(new Page<>(2, 100));

    // Call the method
    task.doExpire();

    // Verify the expected behavior
    // The first URL should be expired because it's older than the rule (60 days)
    verify(shortUrlHistoryMapper, times(1)).insert(any(ShortUrlHistory.class));
    verify(shortUrlMapper, times(1)).updateById(any(ShortUrl.class));

    // Verify the URL was correctly marked as expired
    ArgumentCaptor<ShortUrl> urlCaptor = ArgumentCaptor.forClass(ShortUrl.class);
    verify(shortUrlMapper).updateById(urlCaptor.capture());
    ShortUrl capturedUrl = urlCaptor.getValue();
    assertEquals("code1", capturedUrl.getCode());
    // Check that the deleted field is set (meaning it's marked for deletion)
    assert capturedUrl.getDeleted() > 0;
  }

  @Test
  public void testDoExpireDelete() {
    // Setup test data
    ShortUrl url1 = createShortUrl("code1", "http://example.com/1",
        System.currentTimeMillis() - 100 * 24 * 60 * 60 * 1000L);
    url1.setDeleted(System.currentTimeMillis() - 400 * 24 * 60 * 60 * 1000L); // Marked as deleted 400 days ago

    ShortUrl url2 = createShortUrl("code2", "http://example.com/2", System.currentTimeMillis());
    url2.setDeleted(System.currentTimeMillis() - 10 * 24 * 60 * 60 * 1000L); // Marked as deleted 10 days ago

    Page<ShortUrl> page = new Page<>(1, 100);
    page.setRecords(Arrays.asList(url1, url2));

    // Mock the default delete days
    when(properties.getDefaultDeleteDay()).thenReturn(180);

    // Mock the shortUrlMapper behavior for deleted URLs
    when(shortUrlMapper.selectPage(any(), any())).thenReturn(page).thenReturn(new Page<>(2, 100));

    // Call the method
    task.doExpireDelete();

    // Count the interactions - should be called twice (once with results, once
    // empty)
    verify(shortUrlMapper, times(2)).selectPage(any(), any());
  }

  @Test
  public void testDoExpire_Exception() {
    // Mock an exception during processing
    when(shortUrlMapper.selectPage(any(), any())).thenThrow(new RuntimeException("Test exception"));

    // Call the method - should catch the exception and not propagate it
    task.doExpire();

    // Verify no further processing happened
    verify(shortUrlHistoryMapper, never()).insert(any());
    verify(shortUrlMapper, never()).updateById(any());
  }

  @Test
  public void testDoExpireDelete_Exception() {
    // Mock an exception during processing
    when(shortUrlMapper.selectPage(any(), any())).thenThrow(new RuntimeException("Test exception"));

    // Call the method - should catch the exception and not propagate it
    task.doExpireDelete();

    // Verify it was called once and no further processing
    verify(shortUrlMapper, times(1)).selectPage(any(), any());
  }

  private ShortUrl createShortUrl(String code, String url, long createTime) {
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setCode(code);
    shortUrl.setUrl(url);
    shortUrl.setCreateTime(createTime);
    shortUrl.setDeleted(0L); // Not deleted by default
    return shortUrl;
  }
}