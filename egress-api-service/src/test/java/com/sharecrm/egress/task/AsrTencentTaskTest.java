package com.sharecrm.egress.task;

import com.sharecrm.egress.service.AsrTencentProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AsrTencentTaskTest {

  @Mock
  private AsrTencentProvider asrTencentProvider;

  private AsrTencentTask task;

  @BeforeEach
  public void setup() {
    task = new AsrTencentTask(asrTencentProvider);
  }

  @Test
  void testDoTask() {
    // Call the method
    task.doTask();

    // Verify it called the provider's method
    verify(asrTencentProvider, times(1)).updateRecTaskStatus();
  }

  @Test
  void testDoTask_Exception() {
    // Setup - mock an exception
    doThrow(new RuntimeException("Test exception")).when(asrTencentProvider).updateRecTaskStatus();

    // Call the method - should catch the exception and not propagate it
    task.doTask();

    // Verify it still attempted to call the provider's method
    verify(asrTencentProvider, times(1)).updateRecTaskStatus();
  }
}