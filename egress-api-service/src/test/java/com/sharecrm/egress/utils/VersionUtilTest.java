package com.sharecrm.egress.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class VersionUtilTest {

  @Test
  void getIosMajorVersion() {
    assertEquals(9, VersionUtil.getIosMajorVersion("9.60"));
  }

  @Test
  void getAppVersion() {
    assertEquals(960, VersionUtil.getAppVersion("960"));
  }

  @Test
  void isNeiCe() {
    assertTrue(VersionUtil.isNeiCe("20000000"));
    assertFalse(VersionUtil.isNeiCe("960"));
  }
}