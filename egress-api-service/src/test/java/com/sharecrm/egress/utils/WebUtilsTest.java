package com.sharecrm.egress.utils;

import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class WebUtilsTest {

  @Test
  void okHttpRequest() {
    String headerKey = "id";
    String headerValue = "id-value";
    Request req = WebUtils.okHttpJsonPost("https://localhost", "test", Map.of(headerKey, headerValue));
    assertEquals(headerValue, req.header(headerKey));
    assertEquals("application/json", req.header("Content-Type"));
  }

  @Test
  void okHttpPostWithJsonContentType() {
    String url = "https://api.example.com";
    String requestBody = "{\"key\":\"value\"}";
    Map<String, String> headers = Map.of("Authorization", "Bearer token");

    Request request = WebUtils.okHttpPost(url, requestBody, "application/json", headers);

    assertEquals(url + "/", request.url().toString());
    assertEquals("application/json", request.header("Content-Type"));
    assertEquals("Bearer token", request.header("Authorization"));
    assertEquals("POST", request.method());

    // 验证请求体
    RequestBody body = request.body();
    assertNotNull(body);
    assertEquals(MediaType.parse("application/json; charset=UTF-8"), body.contentType());
  }

  @Test
  void okHttpPostWithStreamContentType() {
    String url = "https://api.example.com";
    String requestBody = "binary data";
    Map<String, String> headers = Map.of("Authorization", "Bearer token");

    Request request = WebUtils.okHttpPost(url, requestBody, "application/octet-stream", headers);

    assertEquals(url + "/", request.url().toString());
    assertEquals("application/octet-stream", request.header("Content-Type"));
    assertEquals("Bearer token", request.header("Authorization"));
    assertEquals("POST", request.method());

    // 验证请求体
    RequestBody body = request.body();
    assertNotNull(body);
    assertEquals(MediaType.parse("application/octet-stream"), body.contentType());
  }

  @Test
  void streamBody() {
    // 测试字符串输入
    String stringInput = "test data";
    RequestBody stringBody = WebUtils.streamBody(stringInput);
    assertEquals(MediaType.parse("application/octet-stream"), stringBody.contentType());

    // 测试对象输入
    Map<String, String> objectInput = Map.of("key", "value");
    RequestBody objectBody = WebUtils.streamBody(objectInput);
    assertEquals(MediaType.parse("application/octet-stream"), objectBody.contentType());
  }

  @Test
  void jsonBody() {
    // 测试字符串输入
    String stringInput = "{\"key\":\"value\"}";
    RequestBody stringBody = WebUtils.jsonBody(stringInput);
    assertEquals(MediaType.parse("application/json; charset=UTF-8"), stringBody.contentType());

    // 测试对象输入
    Map<String, String> objectInput = Map.of("key", "value");
    RequestBody objectBody = WebUtils.jsonBody(objectInput);
    assertEquals(MediaType.parse("application/json; charset=UTF-8"), objectBody.contentType());
  }

  @Test
  void getEaFromHeader() {
    String ea = "test-ei";
    MockServerWebExchange exchange = mockExchange("X-fs-Enterprise-Account", ea);
    String header = WebUtils.getEaFromHeader(exchange);
    assertEquals(ea, header);
  }

  @ParameterizedTest
  @ValueSource(strings = {"x-fs-ea", "X-fs-Enterprise-Account"})
  void getEaFromHeaderWithDifferentHeaderNames(String headerName) {
    String ea = "test-ea";
    MockServerWebExchange exchange = mockExchange(headerName, ea);
    String header = WebUtils.getEaFromHeader(exchange);
    assertEquals(ea, header);
  }

  @Test
  void getEaFromHeaderWhenHeaderNotPresent() {
    MockServerWebExchange exchange = mockExchange("Other-Header", "value");
    String header = WebUtils.getEaFromHeader(exchange);
    assertNull(header);
  }

  @Test
  void getEiFromHeader() {
    String ei = "test-ei";
    MockServerWebExchange exchange = mockExchange("X-fs-Enterprise-Id", ei);
    String header = WebUtils.getEiFromHeader(exchange);
    assertEquals(ei, header);
  }

  @ParameterizedTest
  @ValueSource(strings = {"x-fs-ei", "X-fs-Enterprise-Id"})
  void getEiFromHeaderWithDifferentHeaderNames(String headerName) {
    String ei = "test-ei";
    MockServerWebExchange exchange = mockExchange(headerName, ei);
    String header = WebUtils.getEiFromHeader(exchange);
    assertEquals(ei, header);
  }

  @Test
  void getEiFromHeaderWhenHeaderNotPresent() {
    MockServerWebExchange exchange = mockExchange("Other-Header", "value");
    String header = WebUtils.getEiFromHeader(exchange);
    assertNull(header);
  }

  @Test
  void fluxTrace() {
    StepVerifier.create(WebUtils.fluxTrace())
      // 绝不允许返回null
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void traceId() {
    assertNotNull(WebUtils.traceId());
  }

  @Test
  void firstNotBlankValue() {
    List<String> keys = Arrays.asList("key1", "key2", "key3");

    // 测试第一个键存在
    MultiValueMap<String, String> map1 = new LinkedMultiValueMap<>();
    map1.add("key1", "value1");
    Optional<String> result1 = WebUtils.firstNotBlankValue(keys, map1);
    assertTrue(result1.isPresent());
    assertEquals("value1", result1.get());

    // 测试第二个键存在
    MultiValueMap<String, String> map2 = new LinkedMultiValueMap<>();
    map2.add("key2", "value2");
    Optional<String> result2 = WebUtils.firstNotBlankValue(keys, map2);
    assertTrue(result2.isPresent());
    assertEquals("value2", result2.get());

    // 测试空值
    MultiValueMap<String, String> map3 = new LinkedMultiValueMap<>();
    map3.add("key1", "");
    map3.add("key2", "value2");
    Optional<String> result3 = WebUtils.firstNotBlankValue(keys, map3);
    assertTrue(result3.isPresent());
    assertEquals("value2", result3.get());

    // 测试空白值
    MultiValueMap<String, String> map4 = new LinkedMultiValueMap<>();
    map4.add("key1", "   ");
    map4.add("key2", "value2");
    Optional<String> result4 = WebUtils.firstNotBlankValue(keys, map4);
    assertTrue(result4.isPresent());
    assertEquals("value2", result4.get());

    // 测试所有键都不存在
    MultiValueMap<String, String> map5 = new LinkedMultiValueMap<>();
    map5.add("other", "value");
    Optional<String> result5 = WebUtils.firstNotBlankValue(keys, map5);
    assertEquals(Optional.empty(), result5);
  }

  @ParameterizedTest
  @MethodSource("provideHeadersForFirstNotBlankValue")
  void testFirstNotBlankValueWithVariousInputs(List<String> keys, MultiValueMap<String, String> headers,
                                               String expected) {
    Optional<String> result = WebUtils.firstNotBlankValue(keys, headers);
    if (expected == null) {
      assertEquals(Optional.empty(), result);
    } else {
      assertTrue(result.isPresent());
      assertEquals(expected, result.get());
    }
  }

  static Stream<Arguments> provideHeadersForFirstNotBlankValue() {
    List<String> keys = Arrays.asList("key1", "key2", "key3");

    MultiValueMap<String, String> map1 = new LinkedMultiValueMap<>();
    map1.add("key1", "value1");

    MultiValueMap<String, String> map2 = new LinkedMultiValueMap<>();
    map2.add("key2", "value2");

    MultiValueMap<String, String> map3 = new LinkedMultiValueMap<>();
    map3.add("key1", "");
    map3.add("key2", "value2");

    MultiValueMap<String, String> map4 = new LinkedMultiValueMap<>();
    map4.add("key1", "   ");
    map4.add("key2", "value2");

    MultiValueMap<String, String> map5 = new LinkedMultiValueMap<>();
    map5.add("other", "value");

    MultiValueMap<String, String> map6 = new LinkedMultiValueMap<>();

    return Stream.of(
      Arguments.of(keys, map1, "value1"),
      Arguments.of(keys, map2, "value2"),
      Arguments.of(keys, map3, "value2"),
      Arguments.of(keys, map4, "value2"),
      Arguments.of(keys, map5, null),
      Arguments.of(keys, map6, null),
      Arguments.of(Collections.emptyList(), map1, null),
      Arguments.of(Collections.emptyList(), map1, null));
  }

  @NotNull
  private static MockServerWebExchange mockExchange(String key, String val) {
    return MockServerWebExchange.builder(MockServerHttpRequest.get("/")
      .header(key, val)
      .build()).build();
  }
}