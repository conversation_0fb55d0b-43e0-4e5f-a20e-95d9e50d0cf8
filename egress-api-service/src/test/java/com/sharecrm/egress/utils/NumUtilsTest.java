package com.sharecrm.egress.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class NumUtilsTest {

  @Test
  void range() {
    // 测试小于最小值的情况
    assertEquals(1, NumUtils.range(1, 99, -1));
    assertEquals(1, NumUtils.range(1, 99, 0));
    assertEquals(1, NumUtils.range(1, 99, 1));

    // 测试在范围内的情况
    assertEquals(10, NumUtils.range(1, 99, 10));
    assertEquals(50, NumUtils.range(1, 99, 50));

    // 测试等于最大值的情况
    assertEquals(99, NumUtils.range(1, 99, 99));

    // 测试大于最大值的情况
    assertEquals(99, NumUtils.range(1, 99, 100));
    assertEquals(99, NumUtils.range(1, 99, 1000));

    // 测试最小值等于最大值的情况
    assertEquals(5, NumUtils.range(5, 5, 1));
    assertEquals(5, NumUtils.range(5, 5, 5));
    assertEquals(5, NumUtils.range(5, 5, 10));

    // 测试最小值大于最大值的情况（这种情况下应该返回最大值，因为先取min再取max）
    assertEquals(10, NumUtils.range(10, 5, 1));
    assertEquals(10, NumUtils.range(10, 5, 7));
    assertEquals(10, NumUtils.range(10, 5, 15));
  }

  @Test
  void allowNullOrElse() {
    // 测试第一个参数不为 null 的情况
    assertEquals(Long.valueOf(123), NumUtils.allowNullOrElse(123L, "456"));
    assertEquals(Long.valueOf(0), NumUtils.allowNullOrElse(0L, "456"));
    assertEquals(Long.valueOf(-1), NumUtils.allowNullOrElse(-1L, "456"));

    // 测试第一个参数为 null，第二个参数可以转换为 Long 的情况
    assertEquals(Long.valueOf(456), NumUtils.allowNullOrElse(null, "456"));
    assertEquals(Long.valueOf(0), NumUtils.allowNullOrElse(null, "0"));
    assertEquals(Long.valueOf(-1), NumUtils.allowNullOrElse(null, "-1"));

    // 测试第一个参数为 null，第二个参数不能转换为 Long 的情况
    assertNull(NumUtils.allowNullOrElse(null, "abc"));
    assertNull(NumUtils.allowNullOrElse(null, ""));
    assertNull(NumUtils.allowNullOrElse(null, null));
  }

  @ParameterizedTest
  @MethodSource("provideLongStrings")
  void toLongOrNull_validInput(String input, Long expected) {
    assertEquals(expected, NumUtils.toLongOrNull(input));
  }

  static Stream<Arguments> provideLongStrings() {
    return Stream.of(
        Arguments.of("0", 0L),
        Arguments.of("1", 1L),
        Arguments.of("-1", -1L),
        Arguments.of("9223372036854775807", Long.MAX_VALUE),
        Arguments.of("-9223372036854775808", Long.MIN_VALUE),
        Arguments.of("00123", 123L), // 前导零会被忽略
        Arguments.of("+123", 123L) // 加号会被正确处理
    );
  }

  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = { " ", "  ", "\t", "\n" })
  void toLongOrNull_emptyOrBlankInput(String input) {
    assertNull(NumUtils.toLongOrNull(input));
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "abc", // 非数字字符
      "123a", // 数字和字母混合
      "9223372036854775808", // 超过 Long.MAX_VALUE
      "-9223372036854775809", // 小于 Long.MIN_VALUE
      "123.45", // 小数
      "1,234", // 带逗号的数字
      "0x123", // 十六进制
      "0b1010" // 二进制
  })
  void toLongOrNull_invalidInput(String input) {
    assertNull(NumUtils.toLongOrNull(input));
  }
}