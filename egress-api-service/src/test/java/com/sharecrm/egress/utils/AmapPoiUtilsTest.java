package com.sharecrm.egress.utils;

import com.sharecrm.egress.entity.AmapPoiCode;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class AmapPoiUtilsTest {

  @Test
  void nameToCode() {
    Optional<AmapPoiCode> code = AmapPoiUtils.nameToCode("汽车服务;汽车服务相关;汽车服务相关");
    assertTrue(code.isPresent());
    assertEquals("010000", code.get().getCode());
  }
}