package com.sharecrm.egress.utils;

import com.sharecrm.egress.entity.JwtProperties;

import java.time.Duration;

public class JwtTestConfig {

  public static JwtProperties properties() {
    JwtProperties conf = new JwtProperties();
    conf.setPrivateKey("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");
    conf.setKeyId("test");
    conf.setSubAccount("test");
    conf.setTokenUri("https://oauth-login.cloud.huawei.com/oauth2/v3/token");
    conf.setTimeout(Duration.ofHours(1));
    return conf;
  }
}
