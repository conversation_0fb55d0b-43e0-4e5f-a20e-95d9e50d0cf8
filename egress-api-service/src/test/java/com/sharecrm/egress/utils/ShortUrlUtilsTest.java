package com.sharecrm.egress.utils;

import com.sharecrm.egress.entity.ShortUrl;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ShortUrlUtilsTest {

  @Test
  void isExpire() {
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setUrl("https://localhost");
    shortUrl.setCreateTime(Instant.now().minus(61, ChronoUnit.DAYS).toEpochMilli());
    assertFalse(ShortUrlUtils.isExpire(shortUrl, Map.of(), 62));
    assertTrue(ShortUrlUtils.isExpire(shortUrl, Map.of(), 60));
    //指定时间优先
    assertTrue(ShortUrlUtils.isExpire(shortUrl, Map.of("localhost", 10), 62));
  }

  @Test
  void isExpireWithNeverExpireTimeout() {
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setUrl("https://localhost");
    shortUrl.setCreateTime(Instant.now().minus(100, ChronoUnit.DAYS).toEpochMilli());
    shortUrl.setTimeout(-1L); // 永不过期

    assertFalse(ShortUrlUtils.isExpire(shortUrl, Map.of(), 30));
  }

  @Test
  void isExpireWithSpecificTimeout() {
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setUrl("https://localhost");
    shortUrl.setCreateTime(Instant.now().minus(2, ChronoUnit.MINUTES).toEpochMilli());
    shortUrl.setTimeout(60L); // 60秒后过期

    assertTrue(ShortUrlUtils.isExpire(shortUrl, Map.of(), 30));

    // 设置未过期的情况
    shortUrl.setCreateTime(Instant.now().minus(30, ChronoUnit.SECONDS).toEpochMilli());
    assertFalse(ShortUrlUtils.isExpire(shortUrl, Map.of(), 30));
  }

  @Test
  void isExpireWithMultipleRules() {
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setUrl("https://test.example.com/path");
    shortUrl.setCreateTime(Instant.now().minus(25, ChronoUnit.DAYS).toEpochMilli());

    // 测试多个规则匹配，应该取最长有效期
    Map<String, Integer> rules = Map.of(
        "test", 10,
        "example", 20,
        "path", 5);

    // 最长有效期是 20 天，而我们设置了 25 天前创建，所以应该过期
    assertTrue(ShortUrlUtils.isExpire(shortUrl, rules, 30));

    // 重新设置创建时间为 15 天前，应该未过期（最长有效期是 20 天）
    shortUrl.setCreateTime(Instant.now().minus(15, ChronoUnit.DAYS).toEpochMilli());
    assertFalse(ShortUrlUtils.isExpire(shortUrl, rules, 30));
  }

  @Test
  void isDeleteExpire() {
    ShortUrl shortUrl = new ShortUrl();

    // 测试未删除的情况
    shortUrl.setDeleted(0L);
    assertFalse(ShortUrlUtils.isDeleteExpire(shortUrl, 7));

    // 测试已删除但未超过保留期的情况
    long deletedTime = Instant.now().minus(5, ChronoUnit.DAYS).toEpochMilli();
    shortUrl.setDeleted(deletedTime);
    assertFalse(ShortUrlUtils.isDeleteExpire(shortUrl, 7));

    // 测试已删除且超过保留期的情况
    deletedTime = Instant.now().minus(10, ChronoUnit.DAYS).toEpochMilli();
    shortUrl.setDeleted(deletedTime);
    assertTrue(ShortUrlUtils.isDeleteExpire(shortUrl, 7));

    // 测试 deleted 为 null 的情况
    shortUrl.setDeleted(null);
    assertFalse(ShortUrlUtils.isDeleteExpire(shortUrl, 7));
  }

  @Test
  void testPrivateExpireTimeMethod() throws Exception {
    // 使用反射测试私有方法 expireTime
    Method expireTimeMethod = ShortUrlUtils.class.getDeclaredMethod("expireTime", ShortUrl.class);
    expireTimeMethod.setAccessible(true);

    ShortUrl shortUrl = new ShortUrl();
    long deletedTime = Instant.now().toEpochMilli();
    shortUrl.setDeleted(deletedTime);

    LocalDateTime result = (LocalDateTime) expireTimeMethod.invoke(null, shortUrl);

    LocalDateTime expected = LocalDateTime.ofInstant(Instant.ofEpochMilli(deletedTime), ZoneId.systemDefault());
    assertEquals(expected, result);
  }

  @Test
  void testPrivateCreateTimeMethod() throws Exception {
    // 使用反射测试私有方法 createTime
    Method createTimeMethod = ShortUrlUtils.class.getDeclaredMethod("createTime", ShortUrl.class);
    createTimeMethod.setAccessible(true);

    ShortUrl shortUrl = new ShortUrl();
    long createTime = Instant.now().toEpochMilli();
    shortUrl.setCreateTime(createTime);

    LocalDateTime result = (LocalDateTime) createTimeMethod.invoke(null, shortUrl);

    LocalDateTime expected = LocalDateTime.ofInstant(Instant.ofEpochMilli(createTime), ZoneId.systemDefault());
    assertEquals(expected, result);
  }

  @Test
  void testSplitDateSet() {
    String start = "20230101000000";
    String end = "20230101003000";
    int minPart = 10; // 每10分钟一个分区

    List<Date> result = ShortUrlUtils.splitDateSet(start, end, minPart);

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 应该有5个日期点：起始时间、10分钟后、20分钟后、30分钟后、40分钟后
    // 注意：根据 splitDateSet 方法的实现，endCalendar 会加 1 分钟，
    // 并且循环结束后还会添加一个额外的日期点
    assertEquals(5, result.size());

    // 验证第一个日期
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    LocalDateTime startDateTime = LocalDateTime.parse(start, formatter);
    Date startDate = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());

    assertEquals(startDate.getTime(), result.get(0).getTime());

    // 验证时间间隔
    long tenMinutesInMillis = 10 * 60 * 1000;
    assertEquals(tenMinutesInMillis, result.get(1).getTime() - result.get(0).getTime());
    assertEquals(tenMinutesInMillis, result.get(2).getTime() - result.get(1).getTime());
  }

  @Test
  void isSpecialUrl() {
    assertEquals("123456", ShortUrlUtils.specialUrl("123456", ""));
    assertEquals("4811", ShortUrlUtils.specialUrl("nc", "android"));
    assertEquals("1001", ShortUrlUtils.specialUrl("app", "ipad"));

    // 测试大小写不敏感
    assertEquals("4811", ShortUrlUtils.specialUrl("NC", "android"));
    assertEquals("1002", ShortUrlUtils.specialUrl("A", "Android"));

    // 测试其他用户代理
    assertEquals("nc", ShortUrlUtils.specialUrl("nc", "Mozilla/5.0"));
  }

  @Test
  void randomCode() {
    assertEquals(6, ShortUrlUtils.randomCode(6).length());
    assertEquals(8, ShortUrlUtils.randomCode(8).length());

    // 验证生成的代码只包含指定的字符
    String code = ShortUrlUtils.randomCode(100);
    assertTrue(code.matches("^[0-9a-z]+$"));
  }

  @Test
  void readWelcomeHtml() {
    String html = ShortUrlUtils.readWelcomeHtml();
    assertTrue(html.contains("备案号"));
  }
}