package com.sharecrm.egress.utils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsTemplateEntity;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class JsonUtilTest {

  @Test
  void toBytes() {
    SmsMongoEntity entity = new SmsMongoEntity();
    entity.setId(new ObjectId());
    byte[] bytes = JsonUtil.toBytes(entity);
    assertNotNull(bytes);
    String str = JsonUtil.toJson(entity);
    assertNotNull(str);

    // 验证 toBytes 和 toJson 的一致性
    assertArrayEquals(str.getBytes(StandardCharsets.UTF_8), bytes);

    SmsMongoEntity sms = JsonUtil.getGson().fromJson(str, SmsMongoEntity.class);
    assertNotNull(sms);
    assertEquals(sms.getId(), entity.getId());
  }

  @Test
  void getGson() {
    // 测试默认 Gson 实例
    Gson defaultGson = JsonUtil.getGson();
    assertNotNull(defaultGson);

    // 测试包含 null 值的 Gson 实例
    Gson includeNullGson = JsonUtil.getGson(true);
    assertNotNull(includeNullGson);

    // 验证默认情况下不序列化 null 值
    String str = defaultGson.toJson(new PushRecord());
    assertFalse(str.contains("token"));

    // 验证 includeNull=true 时序列化 null 值
    str = includeNullGson.toJson(new PushRecord());
    assertTrue(str.contains("token"));

    // 验证两个不同的 Gson 实例
    assertFalse(defaultGson.equals(includeNullGson));
  }

  @Test
  void fromJson() {
    // 测试基本的 JSON 反序列化
    SmsTemplateEntity rs = JsonUtil.fromJson("{\"id\":\"669f88887cb84358d8d9bbe5\",\"updateTime\":\"2024-07-23 16:29:20\"}", SmsTemplateEntity.class);
    assertEquals("669f88887cb84358d8d9bbe5", rs.getId().toHexString());
    assertNotNull(rs.getUpdateTime());

    // 测试使用 Type 参数的 fromJson 方法
    Type listType = new TypeToken<List<String>>() {
    }.getType();
    List<String> stringList = JsonUtil.fromJson("[\"a\", \"b\", \"c\"]", listType);
    assertEquals(Arrays.asList("a", "b", "c"), stringList);

    // 测试无效的 JSON
    assertThrows(Exception.class, () -> JsonUtil.fromJson("{invalid json}", SmsTemplateEntity.class));

    // 测试 null 输入
    assertNull(JsonUtil.fromJson(null, SmsTemplateEntity.class));
  }

  @Test
  void jsonDateTime() {
    SmsMongoEntity entity = new SmsMongoEntity();
    // 2024-06-28 10:29:24
    // 使用整秒的时间戳，避免毫秒精度问题
    entity.setSendTime(new Date(1719541764000L));
    String json = JsonUtil.toJson(entity);
    assertTrue(json.contains("2024-06-28 10:29:24"));

    // 测试日期格式的反序列化
    SmsMongoEntity deserialized = JsonUtil.fromJson(json, SmsMongoEntity.class);
    // 比较时间戳而不是对象实例
    assertEquals(entity.getSendTime().getTime(), deserialized.getSendTime().getTime());
  }

  @ParameterizedTest
  @MethodSource("provideMapToJsonTestCases")
  void mapToJsonOrNull(Map<String, String> input, String expected) {
    String result = JsonUtil.mapToJsonOrNull(input);
    if (expected == null) {
      assertNull(result);
    } else if (input != null && input.size() == 1) {
      // 对于单个键值对，可以直接比较
      assertEquals(expected, result);
    } else if (input != null && input.size() > 1) {
      // 对于多个键值对，由于Map的顺序不确定，我们需要比较JSON对象的内容而不是字符串
      Map<String, String> resultMap = JsonUtil.jsonToMap(result);
      Map<String, String> expectedMap = JsonUtil.jsonToMap(expected);
      assertEquals(expectedMap, resultMap);
    }
  }

  static Stream<Arguments> provideMapToJsonTestCases() {
    return Stream.of(
        Arguments.of(null, null),
        Arguments.of(Map.of(), null),
        Arguments.of(Map.of("key", "value"), "{\"key\":\"value\"}"),
        Arguments.of(Map.of("key1", "value1", "key2", "value2"),
            "{\"key1\":\"value1\",\"key2\":\"value2\"}"),
        Arguments.of(Map.of("key", "value with spaces"), "{\"key\":\"value with spaces\"}"),
        Arguments.of(Map.of("key", "value\"with\"quotes"), "{\"key\":\"value\\\"with\\\"quotes\"}"));
  }

  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = { "{}", "{\"key\":\"value\"}", "{\"key1\":\"value1\",\"key2\":\"value2\"}" })
  void jsonToMap(String input) {
    Map<String, String> result = JsonUtil.jsonToMap(input);
    assertNotNull(result);

    if (input == null || input.isEmpty()) {
      assertTrue(result.isEmpty());
    } else if (input.equals("{}")) {
      assertTrue(result.isEmpty());
    } else if (input.equals("{\"key\":\"value\"}")) {
      assertEquals(1, result.size());
      assertEquals("value", result.get("key"));
    } else if (input.equals("{\"key1\":\"value1\",\"key2\":\"value2\"}")) {
      assertEquals(2, result.size());
      assertEquals("value1", result.get("key1"));
      assertEquals("value2", result.get("key2"));
    }
  }

  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = { "[]", "[{\"key\":\"value\"}]", "[{\"key1\":\"value1\"},{\"key2\":\"value2\"}]" })
  void jsonToListMap(String input) {
    List<Map<String, String>> result = JsonUtil.jsonToListMap(input);
    assertNotNull(result);

    if (input == null || input.isEmpty() || input.equals("[]")) {
      assertTrue(result.isEmpty());
    } else if (input.equals("[{\"key\":\"value\"}]")) {
      assertEquals(1, result.size());
      assertEquals("value", result.get(0).get("key"));
    } else if (input.equals("[{\"key1\":\"value1\"},{\"key2\":\"value2\"}]")) {
      assertEquals(2, result.size());
      assertEquals("value1", result.get(0).get("key1"));
      assertEquals("value2", result.get(1).get("key2"));
    }
  }

  @Test
  void jsonToListMapWithBooleanValues() {
    // 测试布尔值被转换为字符串
    assertEquals(Map.of("success", "true"), JsonUtil.jsonToListMap("[{\"success\":true}]").get(0));
    assertEquals(Map.of("success", "false"), JsonUtil.jsonToListMap("[{\"success\":false}]").get(0));
  }

  @Test
  void tupleToString() {
    List<LocationPoint> t1 = List.of(new LocationPoint(0.1, 0.2));
    List<LocationPoint> t2 = List.of(new LocationPoint(100.1, 100.2));
    Tuple2<List<LocationPoint>, List<LocationPoint>> objects = Tuples.of(t1, t2);
    String json = JsonUtil.toJson(objects);
    assertEquals("{\"t1\":[{\"longitude\":0.1,\"latitude\":0.2}],\"t2\":[{\"longitude\":100.1,\"latitude\":100.2}]}", json);
  }

  @Test
  void objectIdSerialization() {
    // 测试 ObjectId 的序列化
    ObjectId id = new ObjectId();
    Map<String, ObjectId> map = new HashMap<>();
    map.put("id", id);

    String json = JsonUtil.toJson(map);
    assertTrue(json.contains(id.toHexString()));

    // 测试 ObjectId 的反序列化
    Type mapType = new TypeToken<Map<String, ObjectId>>() {
    }.getType();
    Map<String, ObjectId> deserializedMap = JsonUtil.fromJson(json, mapType);
    assertEquals(id, deserializedMap.get("id"));
  }

  @Test
  void complexObjectSerialization() {
    // 创建一个复杂对象
    Map<String, Object> complexObject = new HashMap<>();
    complexObject.put("string", "value");
    complexObject.put("number", 123);
    complexObject.put("boolean", true);
    complexObject.put("null", null);
    complexObject.put("array", Arrays.asList(1, 2, 3));

    Map<String, String> nestedMap = new HashMap<>();
    nestedMap.put("nestedKey", "nestedValue");
    complexObject.put("object", nestedMap);

    // 序列化
    String json = JsonUtil.toJson(complexObject);
    assertNotNull(json);

    // 反序列化
    Type mapType = new TypeToken<Map<String, Object>>() {
    }.getType();
    Map<String, Object> deserializedMap = JsonUtil.fromJson(json, mapType);

    assertEquals("value", deserializedMap.get("string"));
    assertEquals(123.0, ((Number) deserializedMap.get("number")).doubleValue());
    assertEquals(true, deserializedMap.get("boolean"));

    // 注意：默认情况下，null 值不会被序列化
    assertFalse(deserializedMap.containsKey("null"));

    // 检查数组
    assertTrue(deserializedMap.get("array") instanceof ArrayList);
    ArrayList<?> array = (ArrayList<?>) deserializedMap.get("array");
    assertEquals(3, array.size());

    // 检查嵌套对象
    assertTrue(deserializedMap.get("object") instanceof Map);
    Map<?, ?> nestedObject = (Map<?, ?>) deserializedMap.get("object");
    assertEquals("nestedValue", nestedObject.get("nestedKey"));
  }
}