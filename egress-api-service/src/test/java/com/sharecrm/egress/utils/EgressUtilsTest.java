package com.sharecrm.egress.utils;

import com.sharecrm.egress.config.Weighted;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class EgressUtilsTest {

  @Test
  void nextDayStart() {
    Instant instant = EgressUtils.nextDayStart();
    ZonedDateTime resultZdt = instant.atZone(ZoneId.systemDefault());
    ZonedDateTime expectedNextDay = Instant.now().atZone(ZoneId.systemDefault()).toLocalDate()
      .plusDays(1).atStartOfDay(ZoneId.systemDefault());

    // 应该是明天的0点加上0-60秒的随机数
    assertTrue(resultZdt.isAfter(expectedNextDay.minusSeconds(1)));
    assertTrue(resultZdt.isBefore(expectedNextDay.plusMinutes(2)));
  }

  @Test
  void filterRefreshScopeBeans() {
    List<String> rs = EgressUtils.filterRefreshScopeBeans(Map.of("scopedTarget.str", "scopedTarget",
      "str", "value"));
    assertArrayEquals(new String[]{"value"}, rs.toArray());
  }

  @Test
  void filterRefreshScopeBeansWithMultipleEntries() {
    Map<String, String> beansMap = new HashMap<>();
    beansMap.put("scopedTarget.bean1", "scopedTarget1");
    beansMap.put("bean1", "value1");
    beansMap.put("scopedTarget.bean2", "scopedTarget2");
    beansMap.put("bean2", "value2");
    beansMap.put("regularBean", "regularValue");

    List<String> result = EgressUtils.filterRefreshScopeBeans(beansMap);

    assertEquals(3, result.size());
    assertTrue(result.contains("value1"));
    assertTrue(result.contains("value2"));
    assertTrue(result.contains("regularValue"));
  }

  @Test
  void filterRefreshScopeBeansWithEmptyMap() {
    List<String> result = EgressUtils.filterRefreshScopeBeans(Collections.emptyMap());
    assertTrue(result.isEmpty());
  }

  @Test
  void roundRobin() {
    List<String> servers = List.of("a", "b", "c");
    AtomicInteger counter = new AtomicInteger(0);
    assertNotEquals(EgressUtils.roundRobin(servers, counter), EgressUtils.roundRobin(servers, counter));
    assertNotEquals(EgressUtils.roundRobin(servers, counter), EgressUtils.roundRobin(servers, counter));
    assertNotEquals(EgressUtils.roundRobin(servers, counter), EgressUtils.roundRobin(servers, counter));
  }

  @Test
  void roundRobinWithEqualWeights() {
    List<String> servers = List.of("a", "b", "c");
    AtomicInteger counter = new AtomicInteger(0);

    // 记录每个服务器被选择的次数
    Map<String, Integer> selectionCount = new HashMap<>();
    for (int i = 0; i < 30; i++) {
      String selected = EgressUtils.roundRobin(servers, counter);
      selectionCount.put(selected, selectionCount.getOrDefault(selected, 0) + 1);
    }

    // 验证每个服务器被选择的次数相等（因为权重相等）
    assertEquals(10, selectionCount.get("a"));
    assertEquals(10, selectionCount.get("b"));
    assertEquals(10, selectionCount.get("c"));
  }

  @Test
  void roundRobinWithSingleServer() {
    List<String> servers = List.of("a");
    AtomicInteger counter = new AtomicInteger(0);

    // 无论调用多少次，都应该返回同一个服务器
    for (int i = 0; i < 10; i++) {
      assertEquals("a", EgressUtils.roundRobin(servers, counter));
    }
  }

  @Test
  void weightedRoundRobin() {
    List<WeightedServer> servers = List.of(new WeightedServer("a", 3),
      new WeightedServer("b", 1),
      new WeightedServer("c", 5));
    AtomicInteger counter = new AtomicInteger(0);
    for (int i = 0; i < 3; i++) {
      assertEquals("a", EgressUtils.roundRobin(servers, counter).name());
    }
    assertEquals("b", EgressUtils.roundRobin(servers, counter).name());
    for (int i = 0; i < 5; i++) {
      assertEquals("c", EgressUtils.roundRobin(servers, counter).name());
    }
  }

  @Test
  void weightedRoundRobinWithLargeCounter() {
    List<WeightedServer> servers = List.of(new WeightedServer("a", 2),
      new WeightedServer("b", 3));
    AtomicInteger counter = new AtomicInteger(1000); // 大的初始值

    // 记录每个服务器被选择的次数
    Map<String, Integer> selectionCount = new HashMap<>();
    for (int i = 0; i < 50; i++) {
      WeightedServer selected = EgressUtils.roundRobin(servers, counter);
      selectionCount.put(selected.name(), selectionCount.getOrDefault(selected.name(), 0) + 1);
    }

    // 验证选择比例接近权重比例
    double ratio = (double) selectionCount.get("a") / selectionCount.get("b");
    assertEquals(2.0 / 3.0, ratio, 0.1); // 允许一定的误差
  }

  @Test
  void weightedRoundRobinWithZeroWeight() {
    // 创建一个包含零权重服务器的列表（应该被视为权重1）
    List<WeightedServer> servers = List.of(new WeightedServer("a", 2),
      new WeightedServer("b", 0),
      new WeightedServer("c", 3));
    AtomicInteger counter = new AtomicInteger(0);

    // 收集10个完整周期的选择结果
    List<String> selections = new ArrayList<>();
    for (int i = 0; i < 60; i++) {
      selections.add(EgressUtils.roundRobin(servers, counter).name());
    }

    // 验证b虽然权重为0，但实际上被视为权重1
    long countA = selections.stream().filter(s -> s.equals("a")).count();
    long countB = selections.stream().filter(s -> s.equals("b")).count();
    long countC = selections.stream().filter(s -> s.equals("c")).count();

    assertEquals(2, countA / countB, 0.2); // a的选择次数应该是b的2倍
    assertEquals(3, countC / countB, 0.2); // c的选择次数应该是b的3倍
  }

  @ParameterizedTest
  @MethodSource("provideWeightedServers")
  void testRoundRobinWithDifferentWeights(List<WeightedServer> servers, Map<String, Double> expectedRatios) {
    AtomicInteger counter = new AtomicInteger(0);

    // 收集足够多的选择结果以确保统计有意义
    Map<String, Integer> selectionCount = new HashMap<>();
    int totalSelections = 1000;
    for (int i = 0; i < totalSelections; i++) {
      WeightedServer selected = EgressUtils.roundRobin(servers, counter);
      selectionCount.put(selected.name(), selectionCount.getOrDefault(selected.name(), 0) + 1);
    }

    // 计算总权重
    int totalWeight = servers.stream().mapToInt(WeightedServer::weight).sum();

    // 验证每个服务器的选择比例接近其权重比例
    for (WeightedServer server : servers) {
      double expectedRatio = (double) server.weight() / totalWeight;
      double actualRatio = (double) selectionCount.getOrDefault(server.name(), 0) / totalSelections;
      assertEquals(expectedRatio, actualRatio, 0.05); // 允许5%的误差
    }
  }

  static Stream<Arguments> provideWeightedServers() {
    return Stream.of(
      // 均匀权重
      Arguments.of(
        List.of(new WeightedServer("a", 1), new WeightedServer("b", 1), new WeightedServer("c", 1)),
        Map.of("a", 1.0 / 3, "b", 1.0 / 3, "c", 1.0 / 3)),
      // 不均匀权重
      Arguments.of(
        List.of(new WeightedServer("a", 5), new WeightedServer("b", 3), new WeightedServer("c", 2)),
        Map.of("a", 5.0 / 10, "b", 3.0 / 10, "c", 2.0 / 10)),
      // 包含最小权重1的服务器
      Arguments.of(
        List.of(new WeightedServer("a", 10), new WeightedServer("b", 1)),
        Map.of("a", 10.0 / 11, "b", 1.0 / 11)));
  }

  record WeightedServer(String name, int weight) implements Weighted {
    @Override
    public int getWeight() {
      return weight;
    }
  }

}