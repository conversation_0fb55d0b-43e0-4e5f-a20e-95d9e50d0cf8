package com.sharecrm.egress.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.net.InetSocketAddress;
import java.net.URI;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

class NetUtilsTest {

  @Test
  void uri() {
    URI uri = NetUtils.uri("localhost:8080");
    assertEquals("http", uri.getScheme());
    uri = NetUtils.uri("https://localhost:8080");
    assertEquals("https", uri.getScheme());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "localhost:8080",
      "example.com",
      "***********:8443",
      "http://localhost:8080",
      "https://example.com:443"
  })
  void testUriWithValidInputs(String input) {
    URI uri = NetUtils.uri(input);
    assertEquals(input.contains("://") ? input.split("://")[0] : "http", uri.getScheme());

    // 验证主机部分
    String expectedHost;
    if (input.contains("://")) {
      expectedHost = input.split("://")[1].split(":")[0];
    } else if (input.contains(":")) {
      expectedHost = input.split(":")[0];
    } else {
      expectedHost = input;
    }
    assertEquals(expectedHost, uri.getHost());
  }

  @Test
  void testUriWithInvalidInput() {
    // 测试无效的URI，应该抛出异常
    assertThrows(IllegalArgumentException.class, () -> NetUtils.uri("://invalid"));
  }

  @Test
  void proxyAddress() {
    assertEquals(InetSocketAddress.createUnresolved("localhost", 8080), NetUtils.proxyAddress("localhost:8080"));
    assertEquals(InetSocketAddress.createUnresolved("localhost", 8080), NetUtils.proxyAddress("localhost:8080", -1));
    assertEquals(InetSocketAddress.createUnresolved("localhost", 9090), NetUtils.proxyAddress("localhost", 9090));
  }

  @ParameterizedTest
  @MethodSource("provideProxyAddressTestCases")
  void testProxyAddressWithVariousInputs(String host, int port, String expectedHost, int expectedPort) {
    InetSocketAddress address = NetUtils.proxyAddress(host, port);
    assertEquals(expectedHost, address.getHostString());
    assertEquals(expectedPort, address.getPort());
  }

  static Stream<Arguments> provideProxyAddressTestCases() {
    return Stream.of(
        // 主机名:端口格式
        Arguments.of("example.com:8080", -1, "example.com", 8080),
        Arguments.of("***********:443", -1, "***********", 443),

        // 主机名和单独的端口
        Arguments.of("example.com", 8080, "example.com", 8080),
        Arguments.of("***********", 443, "***********", 443),

        // 带协议的URL
        Arguments.of("http://example.com:8080", -1, "example.com", 8080),
        Arguments.of("https://***********:443", -1, "***********", 443),

        // 端口覆盖
        Arguments.of("example.com:8080", 9090, "example.com", 9090),
        Arguments.of("http://example.com:8080", 9090, "example.com", 9090));
  }

  @Test
  void maskQuery() {
    assertNull(NetUtils.maskQuery(""));
    assertEquals("ak=abc", NetUtils.maskQuery("ak=abc"));
    assertEquals("ak=abc***3def&key=abc***3def&sn=abc***3def&token=abc***3def", NetUtils.maskQuery("ak=abc123def&key=abc123def&sn=abc123def&token=abc123def"));
  }

  @ParameterizedTest
  @NullAndEmptySource
  void testMaskQueryWithNullOrEmpty(String input) {
    assertNull(NetUtils.maskQuery(input));
  }

  @ParameterizedTest
  @MethodSource("provideMaskQueryTestCases")
  void testMaskQueryWithVariousInputs(String input, String expected) {
    assertEquals(expected, NetUtils.maskQuery(input));
  }

  static Stream<Arguments> provideMaskQueryTestCases() {
    return Stream.of(
        // 不需要掩码的查询参数
        Arguments.of("param=value", "param=value"),
        Arguments.of("param1=value1&param2=value2", "param1=value1&param2=value2"),

        // 需要掩码的单个参数
        Arguments.of("key=abcdefghij", "key=abc***ghij"),
        Arguments.of("ak=abcdefghij", "ak=abc***ghij"),
        Arguments.of("sn=abcdefghij", "sn=abc***ghij"),
        Arguments.of("token=abcdefghij", "token=abc***ghij"),

        // 需要掩码的多个参数
        Arguments.of("key=abcdefghij&param=value", "key=abc***ghij&param=value"),
        Arguments.of("param=value&key=abcdefghij", "param=value&key=abc***ghij"),
        Arguments.of("key=abcdefghij&ak=1234567890", "key=abc***ghij&ak=123***7890"),

        // 短于8个字符的令牌不会被掩码
        Arguments.of("key=abc", "key=abc"),
        Arguments.of("key=abcdefg", "key=abcdefg"),

        // 无值的参数
        Arguments.of("key=", "key="),
        Arguments.of("key", "key"),

        // 格式不正确的查询字符串
        Arguments.of("key:value", "key:value"),
        Arguments.of("=value", "=value"));
  }

  @ParameterizedTest
  @MethodSource("provideMaskTokenTestCases")
  void testMaskToken(String token, String expected) {
    assertEquals(expected, NetUtils.maskToken(token));
  }

  static Stream<Arguments> provideMaskTokenTestCases() {
    return Stream.of(
        // null 和空字符串
        Arguments.of(null, null),
        Arguments.of("", ""),

        // 短于8个字符的令牌不会被掩码
        Arguments.of("abc", "abc"),
        Arguments.of("abcdefg", "abcdefg"),

        // 正好8个字符的令牌
        Arguments.of("abcdefgh", "abcdefgh"),

        // 长于8个字符的令牌会被掩码
        Arguments.of("abcdefghi", "abc***fghi"),
        Arguments.of("abcdefghij", "abc***ghij"),
        Arguments.of("abcdefghijklmnopqrstuvwxyz", "abc***wxyz"));
  }

  @Test
  void cleanUrl() {
    assertEquals("/egress-api-service/api/v2/ip", NetUtils.cleanUrl("/egress-api-service/api/v2/ip/rest/find"));
    assertEquals("/egress-api-service/api/v2/ip", NetUtils.cleanUrl("/egress-api-service/api/v2/ip/locations"));
    assertEquals("/egress-api-service/api/v2", NetUtils.cleanUrl("/egress-api-service/api/v2"));
    assertEquals("/egress-api-service/translates", NetUtils.cleanUrl("/egress-api-service/translates"));
    assertEquals("", NetUtils.cleanUrl("/actuator/health/readiness"));
    assertEquals("", NetUtils.cleanUrl("/actuator/metrics"));
  }

  @ParameterizedTest
      @MethodSource("provideCleanUrlTestCases")
  void testCleanUrlWithVariousInputs(String input, String expected) {
    assertEquals(expected, NetUtils.cleanUrl(input));
  }

  static Stream<Arguments> provideCleanUrlTestCases() {
    return Stream.of(
        // actuator 路径应该返回空字符串
        Arguments.of("/actuator/health", ""),
        Arguments.of("/actuator/metrics/jvm.memory.used", ""),

        // 正常路径应该保留前4级
        Arguments.of("/a/b/c/d/e/f/g", "/a/b/c/d"),
        Arguments.of("/a/b/c/d", "/a/b/c/d"),
        Arguments.of("/a/b/c", "/a/b/c"),
        Arguments.of("/a/b", "/a/b"),
        Arguments.of("/a", "/a"),

        // 空路径
        Arguments.of("", ""),

        // 不以斜杠开头的路径
        Arguments.of("a/b/c/d/e/f/g", "a/b/c/d/e"));
  }
}