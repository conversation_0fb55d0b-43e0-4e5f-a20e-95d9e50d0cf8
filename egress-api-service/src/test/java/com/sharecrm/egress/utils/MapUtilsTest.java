package com.sharecrm.egress.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class MapUtilsTest {

  @Test
  void isAmapSuccess() {
    assertTrue(MapUtils.isAmapSuccess(1, 10000));
    assertFalse(MapUtils.isAmapSuccess(1, 100));
    assertFalse(MapUtils.isAmapSuccess(2, 10000));
    assertFalse(MapUtils.isAmapSuccess(0, 0));
  }

  @Test
  void isAmapHasData() {
    // 测试带有 count 参数的方法
    assertTrue(MapUtils.isAmapHasData(1, 10000, 1, "Data"));
    assertFalse(MapUtils.isAmapHasData(0, 10000, 0, null));
    assertFalse(MapUtils.isAmapHasData(1, 10000, 0, "Data"));
    assertFalse(MapUtils.isAmapHasData(1, 10000, 1, null));
    assertFalse(MapUtils.isAmapHasData(1, 20000, 1, "Data"));

    // 测试不带 count 参数的方法（默认 count=1）
    assertTrue(MapUtils.isAmapHasData(1, 10000, "Data"));
    assertFalse(MapUtils.isAmapHasData(1, 10000, null));
    assertFalse(MapUtils.isAmapHasData(0, 10000, "Data"));
    assertFalse(MapUtils.isAmapHasData(1, 20000, "Data"));
  }

  @Test
  void amapErrorMessage() {
    // 测试提供了 info 的情况
    assertEquals("测试错误", MapUtils.amapErrorMessage("测试错误", 10001));

    // 测试 info 为 null，使用 AMapErrorCode 中的错误信息
    assertEquals("key不正确或过期", MapUtils.amapErrorMessage(null, 10001));
    assertEquals("请求正常", MapUtils.amapErrorMessage(null, 10000));

    // 测试未知错误码
    assertEquals("UNKNOWN", MapUtils.amapErrorMessage(null, 99999));

    // 测试以 300 开头的错误码
    assertEquals("引擎响应失败，请查询请求参数", MapUtils.amapErrorMessage(null, 30099));
  }

  @Test
  void join() {
    assertNull(MapUtils.join("|", null));
    assertEquals("a|b", MapUtils.join("|", List.of("a", "b")));
    assertEquals("a", MapUtils.join("|", Collections.singletonList("a")));
    assertNull(MapUtils.join("|", new ArrayList<>()));
    assertEquals("a,b,c", MapUtils.join(",", List.of("a", "b", "c")));
  }

  @ParameterizedTest
  @ValueSource(strings = { "", "zh", "zh-CN", "zh-TW", "zh-HK" })
  void testIsEmptyOrZhWithChineseLanguages(String language) {
    assertTrue(MapUtils.isEmptyOrZh(language));
  }

  @ParameterizedTest
  @ValueSource(strings = { "en", "en-US", "fr", "de", "ja" })
  void testIsEmptyOrZhWithNonChineseLanguages(String language) {
    assertFalse(MapUtils.isEmptyOrZh(language));
  }

  @ParameterizedTest
  @MethodSource("provideLanguagesForAmapLanguage")
  void testAmapLanguage(String input, String expected) {
    assertEquals(expected, MapUtils.amapLanguage(input));
  }

  static Stream<Arguments> provideLanguagesForAmapLanguage() {
    return Stream.of(
        Arguments.of(null, Constants.ZH_CN),
        Arguments.of("", Constants.ZH_CN),
        Arguments.of("zh", Constants.ZH_CN),
        Arguments.of("zh-CN", Constants.ZH_CN),
        Arguments.of("zh-TW", Constants.ZH_CN),
        Arguments.of("en", "en"),
        Arguments.of("en-US", "en"),
        Arguments.of("fr", "en"),
        Arguments.of("de", "en"));
  }

  @ParameterizedTest
  @MethodSource("provideLanguagesForBaiduLanguage")
  void testBaiduLanguage(String input, String expected) {
    assertEquals(expected, MapUtils.baiduLanguage(input));
  }

  static Stream<Arguments> provideLanguagesForBaiduLanguage() {
    return Stream.of(
        Arguments.of(null, Constants.ZH_CN),
        Arguments.of("", Constants.ZH_CN),
        Arguments.of("zh", Constants.ZH_CN),
        Arguments.of("zh-CN", Constants.ZH_CN),
        Arguments.of("zh-TW", Constants.ZH_CN),
        Arguments.of("en", "en"),
        Arguments.of("en-US", "en"),
        Arguments.of("fr", "en"),
        Arguments.of("de", "en"));
  }

  @ParameterizedTest
  @MethodSource("provideLanguagesForTencentLanguage")
  void testTencentLanguage(String input, String expected) {
    assertEquals(expected, MapUtils.tencentLanguage(input));
  }

  static Stream<Arguments> provideLanguagesForTencentLanguage() {
    return Stream.of(
        Arguments.of(null, Constants.ZH_CN),
        Arguments.of("", Constants.ZH_CN),
        Arguments.of("zh", Constants.ZH_CN),
        Arguments.of("zh-CN", Constants.ZH_CN),
        Arguments.of("zh-TW", Constants.ZH_CN),
        Arguments.of("en", "en"),
        Arguments.of("en-US", "en"),
        Arguments.of("fr", "en"),
        Arguments.of("de", "en"));
  }

  @ParameterizedTest
  @MethodSource("provideLanguagesForMaxmindLanguage")
  void testMaxmindLanguage(String input, String expected) {
    assertEquals(expected, MapUtils.maxmindLanguage(input));
  }

  static Stream<Arguments> provideLanguagesForMaxmindLanguage() {
    return Stream.of(
        Arguments.of(null, Constants.ZH_CN),
        Arguments.of("", Constants.ZH_CN),
        Arguments.of("zh", Constants.ZH_CN),
        Arguments.of("zh-CN", "zh-CN"),
        Arguments.of("zh-TW", "zh-TW"),
        Arguments.of("en", "en"),
        Arguments.of("en-US", "en"),
        Arguments.of("en-GB", "en"),
        Arguments.of("fr", "fr"),
        Arguments.of("de", "de"));
  }

  @ParameterizedTest
  @MethodSource("provideLanguagesForGoogleLanguage")
  void testGoogleLanguage(String input, String expected) {
    assertEquals(expected, MapUtils.googleLanguage(input));
  }

  static Stream<Arguments> provideLanguagesForGoogleLanguage() {
    return Stream.of(
        Arguments.of(null, "en-US"),
        Arguments.of("", "en-US"),
        Arguments.of("zh", "zh"),
        Arguments.of("zh-CN", "zh-CN"),
        Arguments.of("zh-TW", "zh-TW"),
        Arguments.of("en", "en"),
        Arguments.of("en-US", "en-US"),
        Arguments.of("fr", "fr"),
        Arguments.of("de", "de"));
  }

  @ParameterizedTest
  @MethodSource("provideCountriesForIsChina")
  void testIsChina(String country, boolean expected) {
    assertEquals(expected, MapUtils.isChina(country));
  }

  static Stream<Arguments> provideCountriesForIsChina() {
    return Stream.of(
        Arguments.of(null, false),
        Arguments.of("", false),
        Arguments.of("中国", true),
        Arguments.of("China", true),
        Arguments.of("CHINA", true),
        Arguments.of("china", true),
        Arguments.of("USA", false),
        Arguments.of("美国", false),
        Arguments.of("日本", false));
  }

  @ParameterizedTest
  @MethodSource("provideLanguagesForCountryChina")
  void testCountryChina(String language, String expected) {
    assertEquals(expected, MapUtils.countryChina(language));
  }

  static Stream<Arguments> provideLanguagesForCountryChina() {
    return Stream.of(
        Arguments.of(null, Constants.CHINA),
        Arguments.of("", Constants.CHINA),
        Arguments.of("zh", Constants.CHINA),
        Arguments.of("zh-CN", Constants.CHINA),
        Arguments.of("zh-TW", Constants.CHINA),
        Arguments.of("en", Constants.CHINA_EN),
        Arguments.of("en-US", Constants.CHINA_EN),
        Arguments.of("fr", Constants.CHINA_EN),
        Arguments.of("de", Constants.CHINA_EN));
  }
}