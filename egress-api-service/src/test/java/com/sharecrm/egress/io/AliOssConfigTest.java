package com.sharecrm.egress.io;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class AliOssConfigTest {

  @Test
  void testAliOssConfigCreation() {
    AliOssConfig config = new AliOssConfig(
        "oss-cn-hangzhou.aliyuncs.com",
        "accessKey123",
        "secretKey456",
        "test-bucket",
        "test-object.jpg");

    assertEquals("oss-cn-hangzhou.aliyuncs.com", config.endpoint());
    assertEquals("accessKey123", config.accessKeyId());
    assertEquals("secretKey456", config.secretAccessKey());
    assertEquals("test-bucket", config.bucketName());
    assertEquals("test-object.jpg", config.objectName());
  }

  @Test
  void testAliOssConfigEquality() {
    AliOssConfig config1 = new AliOssConfig(
        "oss-cn-hangzhou.aliyuncs.com",
        "accessKey123",
        "secretKey456",
        "test-bucket",
        "test-object.jpg");

    AliOssConfig config2 = new AliOssConfig(
        "oss-cn-hangzhou.aliyuncs.com",
        "accessKey123",
        "secretKey456",
        "test-bucket",
        "test-object.jpg");

    AliOssConfig config3 = new AliOssConfig(
        "oss-cn-beijing.aliyuncs.com",
        "accessKey123",
        "secretKey456",
        "test-bucket",
        "test-object.jpg");

    assertEquals(config1, config2);
    assertNotEquals(config1, config3);
  }

  @Test
  void testAliOssConfigToString() {
    AliOssConfig config = new AliOssConfig(
        "oss-cn-hangzhou.aliyuncs.com",
        "accessKey123",
        "secretKey456",
        "test-bucket",
        "test-object.jpg");

    String toString = config.toString();

    assertTrue(toString.contains("oss-cn-hangzhou.aliyuncs.com"));
    assertTrue(toString.contains("accessKey123"));
    assertTrue(toString.contains("secretKey456"));
    assertTrue(toString.contains("test-bucket"));
    assertTrue(toString.contains("test-object.jpg"));
  }
}