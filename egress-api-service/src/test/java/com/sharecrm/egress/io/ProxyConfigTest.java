package com.sharecrm.egress.io;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class ProxyConfigTest {

  @Test
  void testProxyConfigCreation() {
    ProxyConfig proxyConfig = new ProxyConfig("proxy.example.com", 8080, "user", "password");

    assertEquals("proxy.example.com", proxyConfig.host());
    assertEquals(8080, proxyConfig.port());
    assertEquals("user", proxyConfig.username());
    assertEquals("password", proxyConfig.password());
  }

  @Test
  void testProxyConfigEquality() {
    ProxyConfig config1 = new ProxyConfig("proxy.example.com", 8080, "user", "password");
    ProxyConfig config2 = new ProxyConfig("proxy.example.com", 8080, "user", "password");
    ProxyConfig config3 = new ProxyConfig("other.example.com", 8080, "user", "password");

    assertEquals(config1, config2);
    assertNotEquals(config1, config3);
  }

  @Test
  void testProxyConfigToString() {
    ProxyConfig proxyConfig = new ProxyConfig("proxy.example.com", 8080, "user", "password");
    String toString = proxyConfig.toString();

    assertTrue(toString.contains("proxy.example.com"));
    assertTrue(toString.contains("8080"));
    assertTrue(toString.contains("user"));
    assertTrue(toString.contains("password"));
  }
}