package com.sharecrm.egress.service;

import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.ObjectProvider;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AsrServiceTest {

  @Mock
  private ObjectProvider<AsrProvider> asrProviders;

  @Mock
  private AsrProvider asrProvider;

  private AsrService asrService;

  @BeforeEach
  void setUp() {
    asrService = new AsrService(asrProviders);

    // Set up common behavior
    when(asrProviders.getIfAvailable()).thenReturn(asrProvider);
  }

  @Test
  void shouldPerformSpeechRecognition() {
    // Given
    byte[] audioBytes = "test audio data".getBytes();
    TencentSpeechResponse expectedResponse = new TencentSpeechResponse();
    expectedResponse.setContent("recognized text");

    EgressApiResponse<TencentSpeechResponse> apiResponse = new EgressApiResponse<>(200, "Success", expectedResponse);

    when(asrProvider.speechRecognition(audioBytes)).thenReturn(apiResponse);

    // When
    Mono<EgressApiResponse<TencentSpeechResponse>> result = asrService.speechRecognition(Mono.just(audioBytes));

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertNotNull(response);
          assertEquals(200, response.getCode());
          assertEquals("Success", response.getMessage());
          assertEquals("recognized text", response.getData().getContent());
        })
        .verifyComplete();

    verify(asrProvider).speechRecognition(audioBytes);
  }

  @Test
  void shouldCreateRecognitionTask() {
    // Given
    AsrCreateRecTaskRequest request = new AsrCreateRecTaskRequest();
    request.setEngineModelType("16k_zh");
    request.setUrl("https://example.com/audio.mp3");

    AsrCreateRecTaskResponse expectedResponse = new AsrCreateRecTaskResponse();
    expectedResponse.setTaskId("task123");

    EgressApiResponse<AsrCreateRecTaskResponse> apiResponse = new EgressApiResponse<>(200, "Success", expectedResponse);

    when(asrProvider.createRecTask(request)).thenReturn(apiResponse);

    // When
    Mono<EgressApiResponse<AsrCreateRecTaskResponse>> result = asrService.createRecTask(Mono.just(request));

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertNotNull(response);
          assertEquals(200, response.getCode());
          assertEquals("Success", response.getMessage());
          assertEquals("task123", response.getData().getTaskId());
        })
        .verifyComplete();

    verify(asrProvider).createRecTask(request);
  }

  @Test
  void shouldQueryRecognitionTask() {
    // Given
    String taskId = "task123";

    AsrQueryRecTaskResponse expectedResponse = new AsrQueryRecTaskResponse();
    expectedResponse.setTaskId(taskId);
    expectedResponse.setStatus(AsrQueryRecTaskResponse.STATUS_SUCCESS); // Completed status
    expectedResponse.setText("transcribed text");

    EgressApiResponse<AsrQueryRecTaskResponse> apiResponse = new EgressApiResponse<>(200, "Success",
        expectedResponse);

    when(asrProvider.queryRecTask(taskId)).thenReturn(apiResponse);

    // When
    Mono<EgressApiResponse<AsrQueryRecTaskResponse>> result = asrService.queryRecTask(taskId);

      // Then
      StepVerifier.create(result)
          .assertNext(response -> {
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertEquals("Success", response.getMessage());
            assertEquals(taskId, response.getData().getTaskId());
            assertEquals(AsrQueryRecTaskResponse.STATUS_SUCCESS, response.getData().getStatus());
            assertEquals("transcribed text", response.getData().getText());
          })
          .verifyComplete();

      verify(asrProvider).queryRecTask(taskId);
      }

      @Test
      void shouldHandleNoAvailableProvider() {
        // Given
        when(asrProviders.getIfAvailable()).thenReturn(null);

        // When
        Mono<EgressApiResponse<TencentSpeechResponse>> result = asrService
            .speechRecognition(Mono.just("test".getBytes()));

        // Then
        StepVerifier.create(result)
            .verifyComplete(); // Expect completion with no value, not an error
      }
}
