package com.sharecrm.egress.service;

import net.javacrumbs.shedlock.core.LockConfiguration;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.core.LockingTaskExecutor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LockingExecutorTest {

  @Mock
  private LockProvider lockProvider;

  private LockingExecutor lockingExecutor;

  @BeforeEach
  void setUp() {
    lockingExecutor = new LockingExecutor(lockProvider);
  }

  @Test
  void testExecuteWithDefaultValues() throws Throwable {
    // Create a spy on the executor to verify the task execution
    LockingExecutor spy = spy(lockingExecutor);

    // Setup the task with a flag to verify it was executed
    AtomicBoolean executed = new AtomicBoolean(false);
    Runnable task = () -> executed.set(true);

    // Mock the overloaded method to capture arguments and verify
    doNothing().when(spy).execute(
        any(Runnable.class),
        anyString(),
        any(Duration.class),
        any(Duration.class));

    // Execute
    spy.execute(task, "testLock");

    // Verify the execute method was called with the correct parameters
    verify(spy).execute(
        eq(task),
        eq("testLock"),
        eq(Duration.ofMinutes(20)),
        eq(Duration.ofMinutes(10)));
  }

  @Test
  void testExecuteWithCustomValues() throws Throwable {
    // Create a test with custom duration values
    Duration lockAtMostFor = Duration.ofMinutes(5);
    Duration lockAtLeastFor = Duration.ofMinutes(2);

    // Setup the task with a flag to verify it was executed
    AtomicBoolean executed = new AtomicBoolean(false);
    Runnable task = () -> executed.set(true);

    // Create a mock LockingTaskExecutor and inject it using reflection
    LockingTaskExecutor mockExecutor = mock(LockingTaskExecutor.class);
    java.lang.reflect.Field field = LockingExecutor.class.getDeclaredField("executor");
    field.setAccessible(true);
    field.set(lockingExecutor, mockExecutor);

    // Set up the executor behavior using doAnswer to capture and run the task
    doAnswer(invocation -> {
      Runnable capturedTask = invocation.getArgument(0);
      capturedTask.run();
      return null;
    }).when(mockExecutor).executeWithLock(any(Runnable.class), any(LockConfiguration.class));

    // Execute
    lockingExecutor.execute(task, "testLock", lockAtMostFor, lockAtLeastFor);

    // Verify the task was executed
    assertTrue(executed.get());

    // Verify the executeWithLock method was called
    ArgumentCaptor<LockConfiguration> configCaptor = ArgumentCaptor.forClass(LockConfiguration.class);
    verify(mockExecutor).executeWithLock(eq(task), configCaptor.capture());

    // Verify the lock configuration
    LockConfiguration capturedConfig = configCaptor.getValue();
    assertEquals("testLock", capturedConfig.getName());
    assertEquals(lockAtMostFor, capturedConfig.getLockAtMostFor());
    assertEquals(lockAtLeastFor, capturedConfig.getLockAtLeastFor());
  }

  @Test
  void testExecuteWithLockingFailure() throws Throwable {
    // Setup
    Duration lockAtMostFor = Duration.ofMinutes(5);
    Duration lockAtLeastFor = Duration.ofMinutes(2);

    // Create a task that should run
    AtomicBoolean executed = new AtomicBoolean(false);
    Runnable task = () -> executed.set(true);

    // Create a mock executor that throws an exception when locking
    LockingTaskExecutor mockExecutor = mock(LockingTaskExecutor.class);
    doThrow(new RuntimeException("Locking failed"))
        .when(mockExecutor).executeWithLock(any(Runnable.class), any(LockConfiguration.class));

    // Inject mock executor
    java.lang.reflect.Field field = LockingExecutor.class.getDeclaredField("executor");
    field.setAccessible(true);
    field.set(lockingExecutor, mockExecutor);

    // Execute with expected exception
    assertThrows(RuntimeException.class,
        () -> lockingExecutor.execute(task, "testLock", lockAtMostFor, lockAtLeastFor));

    // Verify task was not executed due to locking failure
    assertFalse(executed.get());

    // Verify the exception came from the expected place
    verify(mockExecutor).executeWithLock(eq(task), any(LockConfiguration.class));
  }

  @Test
  void testLockConfiguration() throws Throwable {
    // Setup for a specific lock configuration validation test
    String lockName = "specificLockName";
    Duration lockAtMostFor = Duration.ofMinutes(15);
    Duration lockAtLeastFor = Duration.ofMinutes(5);

    // Create a task
    Runnable task = () -> {
    };

    // Create a mock executor to capture the lock configuration
    LockingTaskExecutor mockExecutor = mock(LockingTaskExecutor.class);

    // Inject mock executor
    java.lang.reflect.Field field = LockingExecutor.class.getDeclaredField("executor");
    field.setAccessible(true);
    field.set(lockingExecutor, mockExecutor);

    // Execute
    lockingExecutor.execute(task, lockName, lockAtMostFor, lockAtLeastFor);

    // Capture and verify the lock configuration details
    ArgumentCaptor<LockConfiguration> configCaptor = ArgumentCaptor.forClass(LockConfiguration.class);
    verify(mockExecutor).executeWithLock(eq(task), configCaptor.capture());

    LockConfiguration config = configCaptor.getValue();
    assertEquals(lockName, config.getName());
    assertEquals(lockAtMostFor, config.getLockAtMostFor());
    assertEquals(lockAtLeastFor, config.getLockAtLeastFor());

    // Just verify the config was created, without checking the specific creation
    // time
    // which might not be accessible through the public API
    assertNotNull(config);
  }
}