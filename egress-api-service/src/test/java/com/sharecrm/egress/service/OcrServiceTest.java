package com.sharecrm.egress.service;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.context.ApplicationContext;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class OcrServiceTest {

  @Mock
  private ApplicationContext applicationContext;

  @Mock
  private FsGrayReleaseBiz grayReleaseBiz;

  private OcrService ocrService;

  @BeforeEach
  void setUp() {
    try (MockedStatic<FsGrayRelease> grayReleaseMock = Mockito.mockStatic(FsGrayRelease.class)) {
      grayReleaseMock.when(() -> FsGrayRelease.getInstance(anyString())).thenReturn(grayReleaseBiz);
      when(grayReleaseBiz.isAllow(anyString(), anyString())).thenReturn(true);
      ocrService = new OcrService(applicationContext);
    }
  }

  @Test
  void testBusinessCardWithSingleProvider() {
    // Create mock providers
    OcrProvider provider = mock(OcrProvider.class);

    // Setup mock response
    BusinessCardResponse responseData = new BusinessCardResponse();
    responseData.setName("John Doe");
    responseData.setMobile("**********");
    responseData.setEmail("<EMAIL>");
    responseData.setCompany("Example Corp");
    responseData.setTitle("CEO");
    responseData.setAddress("123 Main St");
    responseData.setUrl("example.com");

    EgressApiResponse<BusinessCardResponse> mockResponse = EgressApiResponse.ok(responseData);

    // Configure mock behavior
    when(provider.id()).thenReturn("tencent");
    when(provider.getOrder()).thenReturn(100);
    when(provider.businessCard(any())).thenReturn(mockResponse);

    // Mock application context to return our provider
    Map<String, OcrProvider> providerMap = new HashMap<>();
    providerMap.put("tencentProvider", provider);
    when(applicationContext.getBeansOfType(OcrProvider.class)).thenReturn(providerMap);

    // Create request
    BusinessCardRequest request = new BusinessCardRequest();
    request.setUserId("test-user");
    request.setImageBase64("base64-encoded-image-data");

    // Execute and verify
    Mono<EgressApiResponse<BusinessCardResponse>> result = ocrService.businessCard(Mono.just(request));

    StepVerifier.create(result)
        .expectNextMatches(response -> response.getCode() == 200 &&
            "John Doe".equals(response.getData().getName()) &&
            "**********".equals(response.getData().getMobile()))
        .verifyComplete();
  }

  @Test
  void testBusinessCardWithFallbackProvider() {
    // Create mock providers
    OcrProvider primaryProvider = mock(OcrProvider.class);
    OcrProvider fallbackProvider = mock(OcrProvider.class);

    // Primary provider returns incomplete data
    BusinessCardResponse primaryResponse = new BusinessCardResponse();
    primaryResponse.setName("John Doe"); // Only name is available
    primaryResponse.setExtended(new HashMap<>());

    // Fallback provider fills in missing data
    BusinessCardResponse fallbackResponse = new BusinessCardResponse();
    fallbackResponse.setName("John Doe");
    fallbackResponse.setMobile("**********");
    fallbackResponse.setEmail("<EMAIL>");
    fallbackResponse.setCompany("Example Corp");
    fallbackResponse.setAddress("123 Main St");
    fallbackResponse.setExtended(new HashMap<>());

    // Configure mock behavior
    when(primaryProvider.id()).thenReturn("tencent");
    when(primaryProvider.getOrder()).thenReturn(100);
    when(primaryProvider.businessCard(any())).thenReturn(EgressApiResponse.ok(primaryResponse));

    when(fallbackProvider.id()).thenReturn("huawei");
    when(fallbackProvider.getOrder()).thenReturn(200);
    when(fallbackProvider.businessCard(any())).thenReturn(EgressApiResponse.ok(fallbackResponse));

    // Mock application context to return our providers
    Map<String, OcrProvider> providerMap = new HashMap<>();
    providerMap.put("tencentProvider", primaryProvider);
    providerMap.put("huaweiProvider", fallbackProvider);
    when(applicationContext.getBeansOfType(OcrProvider.class)).thenReturn(providerMap);

    // Create request
    BusinessCardRequest request = new BusinessCardRequest();
    request.setUserId("test-user");
    request.setImageBase64("base64-encoded-image-data");

    // Execute and verify
    Mono<EgressApiResponse<BusinessCardResponse>> result = ocrService.businessCard(Mono.just(request));

    StepVerifier.create(result)
        .expectNextMatches(response -> response.getCode() == 200 &&
            "John Doe".equals(response.getData().getName()) &&
            "**********".equals(response.getData().getMobile()) &&
            "<EMAIL>".equals(response.getData().getEmail()) &&
            "Example Corp".equals(response.getData().getCompany()) &&
            "123 Main St".equals(response.getData().getAddress()))
        .verifyComplete();
  }

  @Test
  void testBusinessCardWithAllProvidersReturningEmpty() {
    // Create mock provider that returns null data
    OcrProvider provider = mock(OcrProvider.class);

    // Configure mock behavior
    when(provider.id()).thenReturn("tencent");
    when(provider.getOrder()).thenReturn(100);
    when(provider.businessCard(any())).thenReturn(EgressApiResponse.ok(null));

    // Mock application context to return our provider
    Map<String, OcrProvider> providerMap = new HashMap<>();
    providerMap.put("tencentProvider", provider);
    when(applicationContext.getBeansOfType(OcrProvider.class)).thenReturn(providerMap);

    // Create request
    BusinessCardRequest request = new BusinessCardRequest();
    request.setUserId("test-user");
    request.setImageBase64("base64-encoded-image-data");

    // Execute and verify
    Mono<EgressApiResponse<BusinessCardResponse>> result = ocrService.businessCard(Mono.just(request));

    StepVerifier.create(result)
        .expectNextMatches(response -> response.getCode() == 200 &&
            response.getData() != null &&
            response.getData().getName() == null)
        .verifyComplete();
  }

  @Test
  void testBusinessCardWithPrimaryReturningNullAndFallbackWithData() {
    // Create mock providers
    OcrProvider primaryProvider = mock(OcrProvider.class);
    OcrProvider fallbackProvider = mock(OcrProvider.class);

    // Primary provider returns null data
    EgressApiResponse<BusinessCardResponse> primaryResponse = EgressApiResponse.ok(null);

    // Fallback provider returns complete data
    BusinessCardResponse fallbackData = new BusinessCardResponse();
    fallbackData.setName("Jane Smith");
    fallbackData.setMobile("**********");
    fallbackData.setEmail("<EMAIL>");
    fallbackData.setCompany("Another Corp");
    fallbackData.setAddress("456 Second St");
    fallbackData.setUrl("anotherexample.com");
    fallbackData.setExtended(new HashMap<>());

    EgressApiResponse<BusinessCardResponse> fallbackResponse = EgressApiResponse.ok(fallbackData);

    // Configure mock behavior
    when(primaryProvider.id()).thenReturn("tencent");
    when(primaryProvider.getOrder()).thenReturn(100);
    when(primaryProvider.businessCard(any())).thenReturn(primaryResponse);

    when(fallbackProvider.id()).thenReturn("huawei");
    when(fallbackProvider.getOrder()).thenReturn(200);
    when(fallbackProvider.businessCard(any())).thenReturn(fallbackResponse);

    // Mock application context to return our providers
    Map<String, OcrProvider> providerMap = new HashMap<>();
    providerMap.put("tencentProvider", primaryProvider);
    providerMap.put("huaweiProvider", fallbackProvider);
    when(applicationContext.getBeansOfType(OcrProvider.class)).thenReturn(providerMap);

    // Create request
    BusinessCardRequest request = new BusinessCardRequest();
    request.setUserId("test-user");
    request.setImageBase64("base64-encoded-image-data");

    // Execute and verify
    Mono<EgressApiResponse<BusinessCardResponse>> result = ocrService.businessCard(Mono.just(request));

    StepVerifier.create(result)
        .expectNextMatches(response -> response.getCode() == 200 &&
            "Jane Smith".equals(response.getData().getName()) &&
            "**********".equals(response.getData().getMobile()) &&
            "<EMAIL>".equals(response.getData().getEmail()))
        .verifyComplete();
  }

  @Test
  void testBusinessCardWithNoGrayRelease() {
    // Create mock providers
    OcrProvider tencentProvider = mock(OcrProvider.class);
    OcrProvider huaweiProvider = mock(OcrProvider.class);

    // Configure mock behavior
    when(tencentProvider.id()).thenReturn("tencent");
    when(tencentProvider.getOrder()).thenReturn(100);
    when(tencentProvider.businessCard(any())).thenReturn(EgressApiResponse.ok(new BusinessCardResponse()));

    when(huaweiProvider.id()).thenReturn("huawei");
    when(huaweiProvider.getOrder()).thenReturn(200);

    // Mock application context to return our providers
    Map<String, OcrProvider> providerMap = new HashMap<>();
    providerMap.put("tencentProvider", tencentProvider);
    providerMap.put("huaweiProvider", huaweiProvider);
    when(applicationContext.getBeansOfType(OcrProvider.class)).thenReturn(providerMap);

    // Override gray release to disallow huawei but allow tencent
    when(grayReleaseBiz.isAllow("ocr-tencent", "test-user")).thenReturn(true);
    when(grayReleaseBiz.isAllow("ocr-huawei", "test-user")).thenReturn(false);

    // Create request
    BusinessCardRequest request = new BusinessCardRequest();
    request.setUserId("test-user");
    request.setImageBase64("base64-encoded-image-data");

    // Execute
    Mono<EgressApiResponse<BusinessCardResponse>> result = ocrService.businessCard(Mono.just(request));

    // Verify that only tencent provider is used due to gray release rules
    StepVerifier.create(result)
        .expectNextMatches(response -> response.getCode() == 200)
        .verifyComplete();

    // Verify only tencent provider was called
    Mockito.verify(tencentProvider).businessCard(any());
    Mockito.verify(huaweiProvider, Mockito.never()).businessCard(any());
  }

  @Test
  void testBusinessCardWithEmptyUserId() {
    // Create mock providers
    OcrProvider provider = mock(OcrProvider.class);

    // Setup mock response
    BusinessCardResponse responseData = new BusinessCardResponse();
    responseData.setName("John Doe");
    responseData.setMobile("**********");
    responseData.setEmail("<EMAIL>");
    responseData.setCompany("Example Corp");
    responseData.setAddress("123 Main St");
    responseData.setUrl("example.com");

    EgressApiResponse<BusinessCardResponse> mockResponse = EgressApiResponse.ok(responseData);

    // Configure mock behavior
    when(provider.id()).thenReturn("tencent");
    when(provider.getOrder()).thenReturn(100);
    when(provider.businessCard(any())).thenReturn(mockResponse);

    // Mock application context to return our provider
    Map<String, OcrProvider> providerMap = new HashMap<>();
    providerMap.put("tencentProvider", provider);
    when(applicationContext.getBeansOfType(OcrProvider.class)).thenReturn(providerMap);

    // Create request with empty userId
    BusinessCardRequest request = new BusinessCardRequest();
    request.setUserId(""); // Empty userId should be handled
    request.setImageBase64("base64-encoded-image-data");

    // Execute and verify
    Mono<EgressApiResponse<BusinessCardResponse>> result = ocrService.businessCard(Mono.just(request));

    StepVerifier.create(result)
        .expectNextMatches(response -> response.getCode() == 200 &&
            "John Doe".equals(response.getData().getName()))
        .verifyComplete();
  }
}