package com.sharecrm.egress.service;

import com.huaweicloud.sdk.ocr.v1.OcrClient;
import com.huaweicloud.sdk.ocr.v1.model.BusinessCardResult;
import com.huaweicloud.sdk.ocr.v1.model.RecognizeBusinessCardRequest;
import com.huaweicloud.sdk.ocr.v1.model.RecognizeBusinessCardResponse;
import com.sharecrm.egress.config.OcrProperties;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sms.SmsUtils;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OcrHuaweiProviderTest {

  @Mock
  private OcrClient ocrClient;

  private OcrProperties ocrProperties;
  private OcrHuaweiProvider ocrHuaweiProvider;

  @BeforeEach
  void setUp() {
    ocrProperties = new OcrProperties();
    OcrProperties.HuaweiConfig huaweiConfig = ocrProperties.getHuawei();
    huaweiConfig.setId("test-huawei");
    huaweiConfig.setOrder(100);
    huaweiConfig.setEnabled(true);

    ocrHuaweiProvider = new OcrHuaweiProvider(() -> ocrClient, ocrProperties);
  }

  @Test
  void testBusinessCardSuccess() throws Exception {
    // 准备测试数据
    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("data:image/jpeg;base64,SGVsbG8gV29ybGQ="); // "Hello World" in Base64

    // 模拟华为OCR客户端响应
    RecognizeBusinessCardResponse response = new RecognizeBusinessCardResponse();
    response.setHttpStatusCode(HttpStatus.OK.value());

    BusinessCardResult result = new BusinessCardResult();
    result.setName(Arrays.asList("张三", "Zhang San"));
    result.setEmail(Collections.singletonList("<EMAIL>"));
    result.setCompany(Arrays.asList("示例公司", "Example Company"));
    result.setDepartment(Collections.singletonList("研发部"));
    result.setTitle(Collections.singletonList("高级工程师"));
    result.setAddress(Collections.singletonList("北京市海淀区"));
    result.setPhone(Arrays.asList("***********", "010-12345678"));
    result.setFax(Collections.singletonList("010-87654321"));
    result.setWebsite(Arrays.asList("www.example.com", "<EMAIL>"));

    // 添加额外信息 - 使用Map代替ExtraInfo类
    List<Map<String, String>> extraInfoList = new ArrayList<>();

    // 创建QQ信息
    Map<String, String> qqInfo = new HashMap<>();
    qqInfo.put("item", "QQ");
    qqInfo.put("value", "*********");
    extraInfoList.add(qqInfo);

    // 创建微信信息
    Map<String, String> wechatInfo = new HashMap<>();
    wechatInfo.put("item", "微信");
    wechatInfo.put("value", "wxid_123456");
    extraInfoList.add(wechatInfo);

    // 设置额外信息列表
    Method setExtraInfoListMethod = result.getClass().getMethod("setExtraInfoList", List.class);
    setExtraInfoListMethod.invoke(result, extraInfoList);

    response.setResult(result);

    when(ocrClient.recognizeBusinessCard(any(RecognizeBusinessCardRequest.class))).thenReturn(response);

    // 模拟SmsUtils的静态方法
    try (MockedStatic<SmsUtils> mockedSmsUtils = mockStatic(SmsUtils.class)) {
      // 模拟containChinese方法
      mockedSmsUtils.when(() -> SmsUtils.containChinese(anyString())).thenAnswer(invocation -> {
        String text = invocation.getArgument(0);
        return text != null && text.matches(".*[\\u4E00-\\u9FA5]+.*");
      });

      // 模拟tryGuessMobile方法
      mockedSmsUtils.when(() -> SmsUtils.tryGuessMobile(anyString())).thenAnswer(invocation -> {
        String phone = invocation.getArgument(0);
        return phone != null && phone.matches("\\d{11}");
      });

      // 模拟isChinaMobile方法
      mockedSmsUtils.when(() -> SmsUtils.isChinaMobile(anyString())).thenAnswer(invocation -> {
        String phone = invocation.getArgument(0);
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
      });

      // 执行测试
      EgressApiResponse<BusinessCardResponse> apiResponse = ocrHuaweiProvider.businessCard(request);

      // 验证结果
      assertNotNull(apiResponse);

      // 验证请求参数
      ArgumentCaptor<RecognizeBusinessCardRequest> requestCaptor = ArgumentCaptor
        .forClass(RecognizeBusinessCardRequest.class);
      verify(ocrClient).recognizeBusinessCard(requestCaptor.capture());

      RecognizeBusinessCardRequest capturedRequest = requestCaptor.getValue();
      assertNotNull(capturedRequest.getBody());
      assertTrue(capturedRequest.getBody().getDetectDirection());
      assertEquals("SGVsbG8gV29ybGQ=", capturedRequest.getBody().getImage()); // 验证Base64前缀被移除
    }
  }

  @Test
  void testBusinessCardWithImageBytes() throws Exception {
    // 准备测试数据
    BusinessCardRequest request = new BusinessCardRequest();
    byte[] imageBytes = "Hello World".getBytes();
    request.setImageBytes(imageBytes);

    // 模拟华为OCR客户端响应
    RecognizeBusinessCardResponse response = new RecognizeBusinessCardResponse();
    response.setHttpStatusCode(HttpStatus.OK.value());

    BusinessCardResult result = new BusinessCardResult();
    result.setName(Collections.singletonList("张三"));
    response.setResult(result);

    when(ocrClient.recognizeBusinessCard(any(RecognizeBusinessCardRequest.class))).thenReturn(response);

    // 模拟SmsUtils的静态方法
    try (MockedStatic<SmsUtils> mockedSmsUtils = mockStatic(SmsUtils.class)) {
      // 模拟containChinese方法
      mockedSmsUtils.when(() -> SmsUtils.containChinese(anyString())).thenReturn(true);

      // 执行测试
      EgressApiResponse<BusinessCardResponse> apiResponse = ocrHuaweiProvider.businessCard(request);

      // 验证结果
      assertNotNull(apiResponse);
      assertEquals(HttpStatus.OK.value(), apiResponse.getCode());
      assertNotNull(apiResponse.getData());

      // 验证请求参数
      ArgumentCaptor<RecognizeBusinessCardRequest> requestCaptor = ArgumentCaptor
        .forClass(RecognizeBusinessCardRequest.class);
      verify(ocrClient).recognizeBusinessCard(requestCaptor.capture());

      RecognizeBusinessCardRequest capturedRequest = requestCaptor.getValue();
      assertNotNull(capturedRequest.getBody());
      assertEquals(Base64.encodeBase64String(imageBytes), capturedRequest.getBody().getImage());
    }
  }

  @Test
  void testBusinessCardHttpError() throws Exception {
    // 准备测试数据
    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("SGVsbG8gV29ybGQ=");

    // 模拟华为OCR客户端响应 - HTTP错误
    RecognizeBusinessCardResponse response = new RecognizeBusinessCardResponse();
    response.setHttpStatusCode(HttpStatus.BAD_REQUEST.value());

    when(ocrClient.recognizeBusinessCard(any(RecognizeBusinessCardRequest.class))).thenReturn(response);

    // 执行测试
    EgressApiResponse<BusinessCardResponse> apiResponse = ocrHuaweiProvider.businessCard(request);

    // 验证结果
    assertNotNull(apiResponse);
    assertEquals(HttpStatus.OK.value(), apiResponse.getCode());
    assertNull(apiResponse.getData());
  }

  @Test
  void testBusinessCardNullResult() throws Exception {
    // 准备测试数据
    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("SGVsbG8gV29ybGQ=");

    // 模拟华为OCR客户端响应 - 结果为null
    RecognizeBusinessCardResponse response = new RecognizeBusinessCardResponse();
    response.setHttpStatusCode(HttpStatus.OK.value());
    response.setResult(null);

    when(ocrClient.recognizeBusinessCard(any(RecognizeBusinessCardRequest.class))).thenReturn(response);

    // 执行测试
    EgressApiResponse<BusinessCardResponse> apiResponse = ocrHuaweiProvider.businessCard(request);

    // 验证结果
    assertNotNull(apiResponse);
    assertEquals(HttpStatus.OK.value(), apiResponse.getCode());
    assertNull(apiResponse.getData());
  }

  @Test
  void testBusinessCardException() throws Exception {
    // 准备测试数据
    BusinessCardRequest request = new BusinessCardRequest();
    request.setImageBase64("SGVsbG8gV29ybGQ=");

    // 模拟华为OCR客户端抛出异常
    when(ocrClient.recognizeBusinessCard(any(RecognizeBusinessCardRequest.class)))
      .thenThrow(new RuntimeException("Test exception"));

    // 执行测试
    EgressApiResponse<BusinessCardResponse> apiResponse = ocrHuaweiProvider.businessCard(request);

    // 验证结果
    assertNotNull(apiResponse);
    assertEquals(HttpStatus.BAD_REQUEST.value(), apiResponse.getCode());
    assertEquals("business card provider call failed", apiResponse.getMessage());
  }

  @Test
  void testId() {
    assertEquals("test-huawei", ocrHuaweiProvider.id());
  }

  @Test
  void testGetOrder() {
    assertEquals(100, ocrHuaweiProvider.getOrder());
  }

  @Test
  void testAdapterName() throws Exception {
    // 使用反射调用私有方法
    Method adapterNameMethod = OcrHuaweiProvider.class.getDeclaredMethod("adapterName", List.class);
    adapterNameMethod.setAccessible(true);

    // 模拟SmsUtils的静态方法
    try (MockedStatic<SmsUtils> mockedSmsUtils = mockStatic(SmsUtils.class)) {
      // 模拟containChinese方法
      mockedSmsUtils.when(() -> SmsUtils.containChinese(anyString())).thenAnswer(invocation -> {
        String text = invocation.getArgument(0);
        return text != null && text.matches(".*[\\u4E00-\\u9FA5]+.*");
      });

      // 测试中文名字
      List<String> chineseName = Arrays.asList("张三", "Zhang San");
      String result1 = (String) adapterNameMethod.invoke(ocrHuaweiProvider, chineseName);
      assertEquals("张三", result1);

      // 测试英文名字
      List<String> englishName = Arrays.asList("John", "Doe");
      String result2 = (String) adapterNameMethod.invoke(ocrHuaweiProvider, englishName);
      assertEquals("John Doe", result2);

      // 测试空列表
      String result3 = (String) adapterNameMethod.invoke(ocrHuaweiProvider, Collections.emptyList());
      assertEquals("", result3);

      // 测试null
      String result4 = (String) adapterNameMethod.invoke(ocrHuaweiProvider, (Object) null);
      assertEquals("", result4);
    }
  }

  @Test
  void testAdapterCompany() throws Exception {
    // 使用反射调用私有方法
    Method adapterCompanyMethod = OcrHuaweiProvider.class.getDeclaredMethod("adapterCompany", List.class);
    adapterCompanyMethod.setAccessible(true);

    // 测试公司名称，包含简称和全称
    List<String> companyNames = Arrays.asList("ABC", "ABC科技有限公司");
    String result1 = (String) adapterCompanyMethod.invoke(ocrHuaweiProvider, companyNames);
    assertEquals("ABC科技有限公司", result1);

    // 测试不同的公司名称
    List<String> differentCompanyNames = Arrays.asList("XYZ", "ABC科技有限公司");
    String result2 = (String) adapterCompanyMethod.invoke(ocrHuaweiProvider, differentCompanyNames);
    assertEquals("XYZ ABC科技有限公司", result2);

    // 测试空列表
    String result3 = (String) adapterCompanyMethod.invoke(ocrHuaweiProvider, Collections.emptyList());
    assertEquals("", result3);

    // 测试null
    String result4 = (String) adapterCompanyMethod.invoke(ocrHuaweiProvider, (Object) null);
    assertEquals("", result4);
  }

  @Test
  void testAdapterWebsites() throws Exception {
    // 使用反射调用私有方法
    Method adapterWebsitesMethod = OcrHuaweiProvider.class.getDeclaredMethod("adapterWebsites",
      BusinessCardResponse.class, BusinessCardResult.class);
    adapterWebsitesMethod.setAccessible(true);

    // 准备测试数据
    BusinessCardResponse response = new BusinessCardResponse();
    BusinessCardResult result = new BusinessCardResult();

    // 测试包含网址和邮箱的情况
    result.setWebsite(Arrays.asList("www.example.com", "<EMAIL>"));
    adapterWebsitesMethod.invoke(ocrHuaweiProvider, response, result);

    assertEquals("www.example.com", response.getUrl());
    assertEquals("<EMAIL>", response.getEmail());

    // 测试只有网址的情况
    response = new BusinessCardResponse();
    result.setWebsite(Collections.singletonList("www.example.org"));
    adapterWebsitesMethod.invoke(ocrHuaweiProvider, response, result);

    assertEquals("www.example.org", response.getUrl());
    assertNull(response.getEmail());

    // 测试只有邮箱的情况
    response = new BusinessCardResponse();
    result.setWebsite(Collections.singletonList("<EMAIL>"));
    adapterWebsitesMethod.invoke(ocrHuaweiProvider, response, result);

    assertEquals("", response.getUrl());
    assertEquals("<EMAIL>", response.getEmail());

    // 测试已有邮箱，再添加新邮箱的情况
    response = new BusinessCardResponse();
    response.setEmail("<EMAIL>");
    result.setWebsite(Collections.singletonList("<EMAIL>"));
    adapterWebsitesMethod.invoke(ocrHuaweiProvider, response, result);

    assertEquals("", response.getUrl());
    assertEquals("<EMAIL>", response.getEmail());
  }

  @Test
  void testAdapterPhones() throws Exception {
    // 使用反射调用私有方法
    Method adapterPhonesMethod = OcrHuaweiProvider.class.getDeclaredMethod("adapterPhones",
      BusinessCardResponse.class, BusinessCardResult.class);
    adapterPhonesMethod.setAccessible(true);

    // 模拟SmsUtils的静态方法
    try (MockedStatic<SmsUtils> mockedSmsUtils = mockStatic(SmsUtils.class)) {
      // 模拟tryGuessMobile方法
      mockedSmsUtils.when(() -> SmsUtils.tryGuessMobile(anyString())).thenAnswer(invocation -> {
        String phone = invocation.getArgument(0);
        return phone != null && phone.matches("\\d{11}");
      });

      // 模拟isChinaMobile方法
      mockedSmsUtils.when(() -> SmsUtils.isChinaMobile(anyString())).thenAnswer(invocation -> {
        String phone = invocation.getArgument(0);
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
      });

      // 准备测试数据
      BusinessCardResponse response = new BusinessCardResponse();
      BusinessCardResult result = new BusinessCardResult();

      // 测试包含手机号和座机号的情况
      result.setPhone(Arrays.asList("***********", "010-12345678"));
      adapterPhonesMethod.invoke(ocrHuaweiProvider, response, result);

      assertEquals("***********", response.getMobile());
      assertEquals("010-12345678", response.getTelephone());

      // 测试只有手机号的情况
      response = new BusinessCardResponse();
      result.setPhone(Collections.singletonList("***********"));
      adapterPhonesMethod.invoke(ocrHuaweiProvider, response, result);

      assertEquals("***********", response.getMobile());
      assertEquals("", response.getTelephone());

      // 测试只有座机号的情况
      response = new BusinessCardResponse();
      result.setPhone(Collections.singletonList("010-87654321"));
      adapterPhonesMethod.invoke(ocrHuaweiProvider, response, result);

      assertEquals("", response.getMobile());
      assertEquals("010-87654321", response.getTelephone());

      // 测试包含括号的情况
      response = new BusinessCardResponse();
      result.setPhone(Arrays.asList("(", "***********", ")"));
      adapterPhonesMethod.invoke(ocrHuaweiProvider, response, result);

      assertEquals("***********", response.getMobile());
      assertEquals("", response.getTelephone());

      // 测试空格和特殊字符的情况
      response = new BusinessCardResponse();
      result.setPhone(Collections.singletonList("138 0013 8000"));
      adapterPhonesMethod.invoke(ocrHuaweiProvider, response, result);

      assertEquals("***********", response.getMobile());
      assertEquals("", response.getTelephone());
    }
  }

  @Test
  void testTrimFilterValues() throws Exception {
    // 使用反射调用私有方法
    Method trimFilterValuesMethod = OcrHuaweiProvider.class.getDeclaredMethod("trimFilterValues", List.class);
    trimFilterValuesMethod.setAccessible(true);

    // 测试正常情况
    List<String> values = Arrays.asList(" 测试 ", "  示例  ", "example");
    @SuppressWarnings("unchecked")
    List<String> result1 = (List<String>) trimFilterValuesMethod.invoke(ocrHuaweiProvider, values);
    assertEquals(Arrays.asList("测试", "示例", "example"), result1);

    // 测试包含特殊字符的情况
    List<String> specialValues = Arrays.asList("/测试/", ".示例", ":example", "：sample");
    @SuppressWarnings("unchecked")
    List<String> result2 = (List<String>) trimFilterValuesMethod.invoke(ocrHuaweiProvider, specialValues);
    assertEquals(Arrays.asList("测试", "示例", "example", "sample"), result2);

    // 测试空字符串的情况
    List<String> emptyValues = Arrays.asList("", " ", "  ");
    @SuppressWarnings("unchecked")
    List<String> result3 = (List<String>) trimFilterValuesMethod.invoke(ocrHuaweiProvider, emptyValues);
    assertEquals(Collections.emptyList(), result3);

    // 测试null的情况
    @SuppressWarnings("unchecked")
    List<String> result4 = (List<String>) trimFilterValuesMethod.invoke(ocrHuaweiProvider, (Object) null);
    assertEquals(Collections.emptyList(), result4);
  }

  @Test
  void testJoinTrim() throws Exception {
    // 使用反射调用私有方法
    Method joinTrimMethod = OcrHuaweiProvider.class.getDeclaredMethod("joinTrim", List.class);
    joinTrimMethod.setAccessible(true);

    // 测试正常情况
    List<String> values = Arrays.asList(" 测试 ", "  示例  ", "example");
    String result1 = (String) joinTrimMethod.invoke(ocrHuaweiProvider, values);
    assertEquals("测试 示例 example", result1);

    // 测试空列表的情况
    String result2 = (String) joinTrimMethod.invoke(ocrHuaweiProvider, Collections.emptyList());
    assertEquals("", result2);

    // 测试null的情况
    String result3 = (String) joinTrimMethod.invoke(ocrHuaweiProvider, (Object) null);
    assertEquals("", result3);
  }

  @Test
  void testBusinessCardRequest() throws Exception {
    // 使用反射调用私有方法
    Method businessCardRequestMethod = OcrHuaweiProvider.class.getDeclaredMethod("businessCardRequest",
      BusinessCardRequest.class);
    businessCardRequestMethod.setAccessible(true);

    // 测试使用imageBase64（不带前缀）
    BusinessCardRequest request1 = new BusinessCardRequest();
    request1.setImageBase64("SGVsbG8gV29ybGQ=");
    RecognizeBusinessCardRequest result1 = (RecognizeBusinessCardRequest) businessCardRequestMethod
      .invoke(ocrHuaweiProvider, request1);

    assertNotNull(result1);
    assertNotNull(result1.getBody());
    assertTrue(result1.getBody().getDetectDirection());
    assertEquals("SGVsbG8gV29ybGQ=", result1.getBody().getImage());

    // 测试使用imageBase64（带前缀）
    BusinessCardRequest request2 = new BusinessCardRequest();
    request2.setImageBase64("data:image/jpeg;base64,SGVsbG8gV29ybGQ=");
    RecognizeBusinessCardRequest result2 = (RecognizeBusinessCardRequest) businessCardRequestMethod
      .invoke(ocrHuaweiProvider, request2);

    assertNotNull(result2);
    assertNotNull(result2.getBody());
    assertTrue(result2.getBody().getDetectDirection());
    assertEquals("SGVsbG8gV29ybGQ=", result2.getBody().getImage());

    // 测试使用imageBytes
    BusinessCardRequest request3 = new BusinessCardRequest();
    byte[] imageBytes = "Hello World".getBytes();
    request3.setImageBytes(imageBytes);
    RecognizeBusinessCardRequest result3 = (RecognizeBusinessCardRequest) businessCardRequestMethod
      .invoke(ocrHuaweiProvider, request3);

    assertNotNull(result3);
    assertNotNull(result3.getBody());
    assertTrue(result3.getBody().getDetectDirection());
    assertEquals(Base64.encodeBase64String(imageBytes), result3.getBody().getImage());
  }
}
