package com.sharecrm.egress.service;

import com.sharecrm.egress.config.OcrProperties;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardInfo;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRResponse;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class OcrTencentProviderTest {
  private final OcrClient client = mock(OcrClient.class);
  private final OcrTencentProvider provider = new OcrTencentProvider(() -> client, new OcrProperties());

  @Test
  @SneakyThrows
  void businessCard() {
    BusinessCardRequest req = new BusinessCardRequest();
    req.setImageBase64("123");
    BusinessCardOCRResponse rsp = new BusinessCardOCRResponse();
    BusinessCardInfo info = new BusinessCardInfo();
    info.setName("email");
    info.setValue("<EMAIL>");
    rsp.setBusinessCardInfos(new BusinessCardInfo[]{info});
    when(client.BusinessCardOCR(any())).thenReturn(rsp);
    EgressApiResponse<BusinessCardResponse> rs = provider.businessCard(req);
    assertNotNull(rs.getData());
    assertEquals("<EMAIL>", rs.getData().getEmail());
  }
}