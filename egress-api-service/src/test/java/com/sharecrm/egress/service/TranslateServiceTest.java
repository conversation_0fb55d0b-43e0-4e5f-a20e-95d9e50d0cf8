package com.sharecrm.egress.service;

import com.sharecrm.egress.api.GoogleTranslateApi;
import com.sharecrm.egress.config.TranslateProperties;
import com.sharecrm.egress.entity.GoogleTranslateResult;
import com.sharecrm.egress.entity.TranslateRequest;
import com.sharecrm.egress.entity.TranslateResponse;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class TranslateServiceTest {

  private final GoogleTranslateApi api = mock(GoogleTranslateApi.class);
  private final TranslateProperties properties = mock(TranslateProperties.class);
  private final TranslateService service = new TranslateService(api, properties);

  @Test
  void translate() {
    when(properties.getGoogle()).thenReturn(new TranslateProperties.GoogleConfig());
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.just(new GoogleTranslateResult()));
    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of("test"));
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));
    StepVerifier.create(translate).expectNextCount(1).verifyComplete();
  }

  @Test
  void translateWithMultipleTexts() {
    // Given
    TranslateProperties.GoogleConfig config = new TranslateProperties.GoogleConfig();
    config.setApiKey("test-api-key");
    when(properties.getGoogle()).thenReturn(config);

    GoogleTranslateResult result = new GoogleTranslateResult();
    // GoogleTranslateResult doesn't have setTranslatedText, it has data property
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.just(result));

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of("hello", "world", "test"));
    req.setSource("en");
    req.setTarget("zh");

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate)
        .expectNextMatches(response -> response.getTranslations() != null &&
            response.getTranslations().size() == 3)
        .verifyComplete();
  }

  @Test
  void translateWithEmptyTexts() {
    // Given
    when(properties.getGoogle()).thenReturn(new TranslateProperties.GoogleConfig());
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.just(new GoogleTranslateResult()));

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of());
    req.setSource("en");
    req.setTarget("zh");

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate)
        .expectNextMatches(response -> response.getTranslations() != null &&
            response.getTranslations().isEmpty())
        .verifyComplete();
  }

  @Test
  void translateWithApiError() {
    // Given
    when(properties.getGoogle()).thenReturn(new TranslateProperties.GoogleConfig());
    when(api.translate(any(), any(), any(), any(), any()))
        .thenReturn(Mono.error(new RuntimeException("API Error")));

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of("test"));
    req.setSource("en");
    req.setTarget("zh");

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate)
        .expectError(RuntimeException.class)
        .verify();
  }

  @Test
  void translateWithNullResult() {
    // Given
    when(properties.getGoogle()).thenReturn(new TranslateProperties.GoogleConfig());
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.empty());

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of("test"));

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate)
        .expectNextMatches(response -> response.getTranslations() != null)
        .verifyComplete();
  }

  @Test
  void translateWithDifferentLanguages() {
    // Given
    TranslateProperties.GoogleConfig config = new TranslateProperties.GoogleConfig();
    config.setApiKey("test-key");
    when(properties.getGoogle()).thenReturn(config);

    GoogleTranslateResult result = new GoogleTranslateResult();
    // GoogleTranslateResult doesn't have setTranslatedText, it has data property
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.just(result));

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of("hello"));
    req.setSource("en");
    req.setTarget("fr");

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate)
        .expectNextMatches(response -> response.getTranslations() != null &&
            response.getTranslations().size() == 1)
        .verifyComplete();
  }
}