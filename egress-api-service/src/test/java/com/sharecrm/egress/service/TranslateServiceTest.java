package com.sharecrm.egress.service;

import com.sharecrm.egress.api.GoogleTranslateApi;
import com.sharecrm.egress.config.TranslateProperties;
import com.sharecrm.egress.entity.GoogleTranslateResult;
import com.sharecrm.egress.entity.TranslateRequest;
import com.sharecrm.egress.entity.TranslateResponse;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class TranslateServiceTest {

  private final GoogleTranslateApi api = mock(GoogleTranslateApi.class);
  private final TranslateProperties properties = mock(TranslateProperties.class);
  private final TranslateService service = new TranslateService(api, properties);

  @Test
  void translate() {
    // Given
    when(properties.getGoogle()).thenReturn(new TranslateProperties.GoogleConfig());
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.just(new GoogleTranslateResult()));

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of("test"));

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate).expectNextCount(1).verifyComplete();
  }

  @Test
  void translateWithEmptyTexts() {
    // Given
    when(properties.getGoogle()).thenReturn(new TranslateProperties.GoogleConfig());
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.just(new GoogleTranslateResult()));

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of());
    req.setSource("en");
    req.setTarget("zh");

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate)
        .expectNextMatches(response -> response.getTranslations() != null)
        .verifyComplete();
  }

  @Test
  void translateWithMultipleTexts() {
    // Given
    when(properties.getGoogle()).thenReturn(new TranslateProperties.GoogleConfig());
    when(api.translate(any(), any(), any(), any(), any())).thenReturn(Mono.just(new GoogleTranslateResult()));

    TranslateRequest req = new TranslateRequest();
    req.setTexts(List.of("hello", "world"));
    req.setSource("en");
    req.setTarget("zh");

    // When
    Mono<TranslateResponse> translate = service.translate(Mono.just(req));

    // Then
    StepVerifier.create(translate)
        .expectNextMatches(response -> response.getTranslations() != null)
        .verifyComplete();
  }
}