package com.sharecrm.egress.service;

import com.sharecrm.egress.config.MapProperties;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

class GeoLite2ServiceTest {
  private final GeoLite2Service service = new GeoLite2Service(new MapProperties());

  @Test
  void testLookupByIp() {
    //凑数，太慢，不要真的执行    
    assertThrows(Exception.class, () -> service.lookup("*************", "en"));
  }
}
