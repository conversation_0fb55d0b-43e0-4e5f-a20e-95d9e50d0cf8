package com.sharecrm.egress.service;

import com.fxiaoke.words.exception.ParseException;
import com.sharecrm.egress.entity.DirtyWordsRequest;
import com.sharecrm.egress.entity.ParseDirtyWordsItem;
import com.sharecrm.egress.entity.ParseDirtyWordsResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class DirtyWordServiceTest {

  private final DirtyWordService service = new DirtyWordService();

  @Test
  void parseForFunction() {
    List<ParseDirtyWordsItem> items = service.parseForFunction("sms", "test");
    assertNotNull(items);
    assertEquals(0, items.size());
  }

  @Test
  void parse() {
    ResponseEntity<EgressApiResponse<ParseDirtyWordsResponse>> parse = service.parse(new DirtyWordsRequest());
    assertNotNull(parse);
  }

  @Test
  void hasDirtyWords() throws ParseException {
    assertTrue(service.hasDirtyWords("sms", "not-allow"));
    assertNotNull(service.hasDirtyWords(new DirtyWordsRequest()));
  }

  @Test
  void smsHasDirtyWords() {
    assertTrue(service.smsHasDirtyWords("not-allow"));
  }
}