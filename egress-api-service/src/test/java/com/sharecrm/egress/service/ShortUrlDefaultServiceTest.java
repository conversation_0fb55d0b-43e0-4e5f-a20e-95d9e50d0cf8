package com.sharecrm.egress.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sharecrm.egress.config.FluxTrace;
import com.sharecrm.egress.config.ShortUrlProperties;
import com.sharecrm.egress.dao.ShortUrlMapper;
import com.sharecrm.egress.entity.ShortUrl;
import com.sharecrm.egress.entity.ShortUrlBatchCreateRequest;
import com.sharecrm.egress.entity.ShortUrlBatchCreateResponse;
import com.sharecrm.egress.entity.ShortUrlCreateRequest;
import com.sharecrm.egress.entity.ShortUrlCreateResponse;
import com.sharecrm.egress.entity.ShortUrlOriginalResponse;
import com.sharecrm.egress.entity.ShortUrlQueryRequest;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.utils.SingleExecutorUtils;
import com.sharecrm.egress.utils.StupidUtils;
import com.sharecrm.egress.utils.WebUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ShortUrlDefaultServiceTest {

  @Mock
  private ShortUrlProperties properties;

  @Mock
  private ShortUrlMapper shortUrlMapper;

  @Mock
  private TransactionTemplate transactionTemplate;

  private ShortUrlDefaultService service;

  @BeforeEach
  void setUp() {
    service = new ShortUrlDefaultService(properties, shortUrlMapper, transactionTemplate);

    // 使用lenient模式避免不必要的存根警告
    lenient().when(properties.getCodeLength()).thenReturn(6);
    lenient().when(properties.getDomain()).thenReturn("https://t.cn");
    lenient().when(properties.getMaxRetry()).thenReturn(3);
    lenient().when(properties.isTimeoutEnabled()).thenReturn(false);
    lenient().when(properties.getDefaultExpireDay()).thenReturn(180);
  }

  @Test
  void shouldCreateShortUrl() {
    // Given
    ShortUrlCreateRequest request = new ShortUrlCreateRequest();
    request.setUrl("https://www.example.com");
    request.setTenantId("test-tenant");

    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setCode("abc123");
    shortUrl.setUrl("https://www.example.com");
    shortUrl.setTenantId("test-tenant");

    // Mock transaction template to perform the action
    when(transactionTemplate.execute(any())).thenReturn(shortUrl);

    // When/Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      // Use FluxTrace instead of WebUtils.WebTraceInfo
      FluxTrace trace = new FluxTrace();
      trace.setEi("test-tenant");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<EgressApiResponse<ShortUrlCreateResponse>> result = service.createShortUrl(request);

      StepVerifier.create(result)
          .assertNext(response -> {
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertNotNull(response.getData());
            assertEquals("https://t.cn/abc123", response.getData().getShortUrl());
            assertEquals("abc123", response.getData().getCode());
          })
          .verifyComplete();
    }
  }

  @Test
  void shouldBatchCreateShortUrls() {
    // Given
    ShortUrlBatchCreateRequest request = new ShortUrlBatchCreateRequest();
    request.setUrls(List.of("https://www.example1.com", "https://www.example2.com"));
    request.setTenantId("test-tenant");

    ShortUrl shortUrl1 = new ShortUrl();
    shortUrl1.setCode("abc123");
    shortUrl1.setUrl("https://www.example1.com");
    shortUrl1.setTenantId("test-tenant");

    ShortUrl shortUrl2 = new ShortUrl();
    shortUrl2.setCode("def456");
    shortUrl2.setUrl("https://www.example2.com");
    shortUrl2.setTenantId("test-tenant");

    // Mock transaction template to perform the actions
    when(transactionTemplate.execute(any())).thenReturn(shortUrl1).thenReturn(shortUrl2);

    // When/Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      // Use FluxTrace instead of WebUtils.WebTraceInfo
      FluxTrace trace = new FluxTrace();
      trace.setEi("test-tenant");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<EgressApiResponse<ShortUrlBatchCreateResponse>> result = service.batchCreateShortUrl(request);

      StepVerifier.create(result)
          .assertNext(response -> {
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertNotNull(response.getData());

            ShortUrlBatchCreateResponse data = response.getData();
            assertEquals(2, data.getResult().size());

            ShortUrlCreateResponse response1 = data.getResult().get("https://www.example1.com");
            assertEquals("https://t.cn/abc123", response1.getShortUrl());
            assertEquals("abc123", response1.getCode());

            ShortUrlCreateResponse response2 = data.getResult().get("https://www.example2.com");
            assertEquals("https://t.cn/def456", response2.getShortUrl());
            assertEquals("def456", response2.getCode());
          })
          .verifyComplete();
    }
  }

  @Test
  void shouldGetOriginalUrl() {
    // Given
    String code = "abc123";
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setCode(code);
    shortUrl.setUrl("https://www.example.com");
    shortUrl.setTenantId("test-tenant");
    shortUrl.setTimeout(0L);
    shortUrl.setCreateTime(System.currentTimeMillis());

    when(shortUrlMapper.selectByCode(code)).thenReturn(Optional.of(shortUrl));

    // When/Then
    Mono<ShortUrlOriginalResponse> result = service.getOriginalUrl(code, "User-Agent");

    StepVerifier.create(result)
        .assertNext(response -> {
          assertNotNull(response);
          assertEquals("https://www.example.com", response.getOriginalUrl());
        })
        .verifyComplete();
  }

  @Test
  void shouldQueryPageOfShortUrls() {
    // Given
    ShortUrlQueryRequest request = new ShortUrlQueryRequest();
    request.setCurrent(1);
    request.setSize(10);
    request.setStartTime(System.currentTimeMillis() - 86400000); // 1 day ago
    request.setEndTime(System.currentTimeMillis());

    Page<ShortUrl> page = new Page<>(1, 10);
    page.setRecords(List.of(
        createShortUrl("abc123", "https://www.example1.com", "test-tenant"),
        createShortUrl("def456", "https://www.example2.com", "test-tenant")));
    page.setTotal(2);

    // Capture the query wrapper
    ArgumentCaptor<LambdaQueryWrapper<ShortUrl>> wrapperCaptor = ArgumentCaptor.forClass(LambdaQueryWrapper.class);
    when(shortUrlMapper.selectPage(any(Page.class), wrapperCaptor.capture())).thenReturn(page);

    // When/Then
    Mono<Page<ShortUrl>> result = service.pageQuery(Mono.just(request));

    StepVerifier.create(result)
        .assertNext(response -> {
          assertNotNull(response);
          assertEquals(2, response.getRecords().size());
          assertEquals(2, response.getTotal());
        })
        .verifyComplete();
  }

  @Test
  void shouldCreateUrl() {
    // Given
    ShortUrlCreateRequest request = new ShortUrlCreateRequest();
    request.setUrl("https://www.example.com");
    request.setTenantId("test-tenant");

    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setCode("abc123");
    shortUrl.setUrl("https://www.example.com");
    shortUrl.setTenantId("test-tenant");

    // Mock transaction template to perform the action
    when(transactionTemplate.execute(any())).thenReturn(shortUrl);

    // When/Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      // Use FluxTrace instead of WebUtils.WebTraceInfo
      FluxTrace trace = new FluxTrace();
      trace.setEi("test-tenant");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<EgressApiResponse<ShortUrl>> result = service.createUrl(request);

      StepVerifier.create(result)
          .assertNext(response -> {
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertNotNull(response.getData());
            assertEquals("abc123", response.getData().getCode());
            assertEquals("https://www.example.com", response.getData().getUrl());
            assertEquals("test-tenant", response.getData().getTenantId());
          })
          .verifyComplete();
    }
  }

  @Test
  void shouldQueryOriginal() {
    // Given
    String code = "abc123";
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setCode(code);
    shortUrl.setUrl("https://www.example.com");
    shortUrl.setTenantId("test-tenant");
    shortUrl.setTimeout(0L);
    shortUrl.setCreateTime(System.currentTimeMillis());

    when(shortUrlMapper.selectByCode(code)).thenReturn(Optional.of(shortUrl));

    // When/Then
    Mono<ShortUrl> result = service.queryOriginal(code);

    StepVerifier.create(result)
        .assertNext(response -> {
          assertNotNull(response);
          assertEquals(code, response.getCode());
          assertEquals("https://www.example.com", response.getUrl());
          assertEquals("test-tenant", response.getTenantId());
        })
        .verifyComplete();
  }

  @Test
  void shouldSyncFromRemote() {
    // Given
    String start = "2023-01-01";
    String end = "2023-01-02";

    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    try (MockedStatic<SingleExecutorUtils> executorMock = Mockito.mockStatic(SingleExecutorUtils.class)) {
      // When/Then
      Mono<String> result = service.syncFromRemote(start, end);

      StepVerifier.create(result)
          .assertNext(response -> {
            assertEquals("start:2023-01-01,end:2023-01-02", response);
            executorMock.verify(() -> SingleExecutorUtils.execute(any(Runnable.class)));
          })
          .verifyComplete();
    }
  }

  @Test
  void shouldReturnErrorWhenProxyUrlIsEmpty() {
    // Given
    String start = "2023-01-01";
    String end = "2023-01-02";

    when(properties.getProxyEgressUrl()).thenReturn("");

    // When/Then
    Mono<String> result = service.syncFromRemote(start, end);

    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals("Error: proxyEgressUrl is empty", response);
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleTimeoutShortUrl() {
    // Given
    String code = "abc123";
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setCode(code);
    shortUrl.setUrl("https://www.example.com");
    shortUrl.setTenantId("test-tenant");
    shortUrl.setTimeout(1000L); // Set timeout
    shortUrl.setCreateTime(System.currentTimeMillis() - 200 * 24 * 60 * 60 * 1000L); // 200 days ago

    when(shortUrlMapper.selectByCode(code)).thenReturn(Optional.of(shortUrl));
    when(properties.isTimeoutEnabled()).thenReturn(true);

    Map<String, Integer> expireRules = new HashMap<>();
    expireRules.put("https://www.example.com", 100); // 100 days
    when(properties.getExpireRule()).thenReturn(expireRules);

    // When/Then
    Mono<ShortUrlOriginalResponse> result = service.getOriginalUrl(code, "User-Agent");

    StepVerifier.create(result)
        .verifyComplete(); // Should complete empty because URL is timed out
  }

  @Test
  void shouldHandleSelectByCodeWithoutDoubleRead() {
    // Given
    String code = "abc123";
    ShortUrl shortUrl = createShortUrl(code, "https://www.example.com", "test-tenant");

    when(shortUrlMapper.selectByCode(code)).thenReturn(Optional.of(shortUrl));

    // When
    ShortUrl result = service.selectByCode(code);

    // Then
    assertNotNull(result);
    assertEquals(code, result.getCode());
    assertEquals("https://www.example.com", result.getUrl());
  }

  @Test
  void shouldHandleSelectByCodeWhenNotFound() {
    // Given
    String code = "nonexistent";

    when(shortUrlMapper.selectByCode(code)).thenReturn(Optional.empty());
    when(properties.isDoubleReadEnabled()).thenReturn(false);

    // When
    ShortUrl result = service.selectByCode(code);

    // Then
    assertNull(result);
    verify(properties).isDoubleReadEnabled();
  }

  @Test
  void shouldHandleGetOriginalUrlWithSpecialCode() {
    // Given
    String code = "abcd"; // 4-character code (special case)

    // Setup ossValue map with reflection
    try {
      Field ossValueField = ShortUrlDefaultService.class.getDeclaredField("ossValue");
      ossValueField.setAccessible(true);
      Map<String, String> ossValue = new HashMap<>();
      ossValue.put("abcd", "https://special.example.com");
      ossValueField.set(service, ossValue);
    } catch (Exception e) {
      fail("Failed to set up test: " + e.getMessage());
    }

    // When/Then
    Mono<ShortUrlOriginalResponse> result = service.getOriginalUrl(code, "User-Agent");

    StepVerifier.create(result)
        .assertNext(response -> {
          assertNotNull(response);
          assertEquals("https://special.example.com", response.getOriginalUrl());
        })
        .verifyComplete();
  }

  @Test
  void shouldRejectInvalidCodeLength() {
    // Given
    String code = "abc12"; // 5-character code (invalid)

    // When/Then
    Mono<ShortUrlOriginalResponse> result = service.getOriginalUrl(code, "User-Agent");

    StepVerifier.create(result)
        .verifyComplete(); // Should complete empty because code length is invalid
  }

  private ShortUrl createShortUrl(String code, String url, String tenantId) {
    ShortUrl shortUrl = new ShortUrl();
    shortUrl.setCode(code);
    shortUrl.setUrl(url);
    shortUrl.setTenantId(tenantId);
    shortUrl.setCreateTime(System.currentTimeMillis());
    shortUrl.setDeleted(0L);
    return shortUrl;
  }

  @Test
  void shouldHandleTryCreateOneUrlWithInvalidUrl() {
    // Given
    ShortUrlCreateRequest request = new ShortUrlCreateRequest();
    request.setUrl(""); // Empty URL
    request.setTenantId("test-tenant");

    // When/Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      FluxTrace trace = new FluxTrace();
      trace.setEi("test-tenant");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<EgressApiResponse<ShortUrl>> result = service.createUrl(request);

      StepVerifier.create(result)
          .verifyComplete(); // Should complete empty because URL is invalid
    }
  }

  @Test
  void shouldHandleTryCreateOneUrlWithTooLongUrl() {
    // Given
    ShortUrlCreateRequest request = new ShortUrlCreateRequest();
    // Create a URL that is longer than 2000 characters
    StringBuilder longUrl = new StringBuilder("https://www.example.com/");
    while (longUrl.length() <= 2000) {
      longUrl.append("very-long-path-segment-that-exceeds-the-maximum-allowed-length-for-urls-in-the-system/");
    }
    request.setUrl(longUrl.toString());
    request.setTenantId("test-tenant");

    // When/Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      FluxTrace trace = new FluxTrace();
      trace.setEi("test-tenant");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<EgressApiResponse<ShortUrl>> result = service.createUrl(request);

      StepVerifier.create(result)
          .verifyComplete(); // Should complete empty because URL is too long
    }
  }

  @Test
  void shouldHandleIsDoubleWriteTrue() throws Exception {
    // Given
    when(properties.isDoubleWriteEnabled()).thenReturn(true);
    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    // Use reflection to access private method
    Method isDoubleWriteMethod = ShortUrlDefaultService.class.getDeclaredMethod("isDoubleWrite");
    isDoubleWriteMethod.setAccessible(true);

    // When
    boolean result = (boolean) isDoubleWriteMethod.invoke(service);

    // Then
    assertTrue(result);
    verify(properties).isDoubleWriteEnabled();
    verify(properties).getProxyEgressUrl();
  }

  @Test
  void shouldHandleIsDoubleWriteFalseWhenDisabled() throws Exception {
    // Given
    when(properties.isDoubleWriteEnabled()).thenReturn(false);
    // Don't stub getProxyEgressUrl since it won't be called

    // Use reflection to access private method
    Method isDoubleWriteMethod = ShortUrlDefaultService.class.getDeclaredMethod("isDoubleWrite");
    isDoubleWriteMethod.setAccessible(true);

    // When
    boolean result = (boolean) isDoubleWriteMethod.invoke(service);

    // Then
    assertFalse(result);
    verify(properties).isDoubleWriteEnabled();
    verify(properties, never()).getProxyEgressUrl();
  }

  @Test
  void shouldHandleIsDoubleWriteFalseWhenUrlEmpty() throws Exception {
    // Given
    when(properties.isDoubleWriteEnabled()).thenReturn(true);
    when(properties.getProxyEgressUrl()).thenReturn("");

    // Use reflection to access private method
    Method isDoubleWriteMethod = ShortUrlDefaultService.class.getDeclaredMethod("isDoubleWrite");
    isDoubleWriteMethod.setAccessible(true);

    // When
    boolean result = (boolean) isDoubleWriteMethod.invoke(service);

    // Then
    assertFalse(result);
    verify(properties).isDoubleWriteEnabled();
    verify(properties).getProxyEgressUrl();
  }

  @Test
  void shouldHandleIsDoubleReadTrue() throws Exception {
    // Given
    when(properties.isDoubleReadEnabled()).thenReturn(true);
    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    // Use reflection to access private method
    Method isDoubleReadMethod = ShortUrlDefaultService.class.getDeclaredMethod("isDoubleRead");
    isDoubleReadMethod.setAccessible(true);

    // When
    boolean result = (boolean) isDoubleReadMethod.invoke(service);

    // Then
    assertTrue(result);
    verify(properties).isDoubleReadEnabled();
    verify(properties).getProxyEgressUrl();
  }

  @Test
  void shouldHandleIsDoubleReadFalseWhenDisabled() throws Exception {
    // Given
    when(properties.isDoubleReadEnabled()).thenReturn(false);
    // Don't stub getProxyEgressUrl since it won't be called

    // Use reflection to access private method
    Method isDoubleReadMethod = ShortUrlDefaultService.class.getDeclaredMethod("isDoubleRead");
    isDoubleReadMethod.setAccessible(true);

    // When
    boolean result = (boolean) isDoubleReadMethod.invoke(service);

    // Then
    assertFalse(result);
    verify(properties).isDoubleReadEnabled();
    verify(properties, never()).getProxyEgressUrl();
  }

  @Test
  void shouldHandleIsDoubleReadFalseWhenUrlEmpty() throws Exception {
    // Given
    when(properties.isDoubleReadEnabled()).thenReturn(true);
    when(properties.getProxyEgressUrl()).thenReturn("");

    // Use reflection to access private method
    Method isDoubleReadMethod = ShortUrlDefaultService.class.getDeclaredMethod("isDoubleRead");
    isDoubleReadMethod.setAccessible(true);

    // When
    boolean result = (boolean) isDoubleReadMethod.invoke(service);

    // Then
    assertFalse(result);
    verify(properties).isDoubleReadEnabled();
    verify(properties).getProxyEgressUrl();
  }

  @Test
  void shouldHandleSaveLocalSuccess() throws Exception {
    // Given
    ShortUrl shortUrl = createShortUrl("abc123", "https://www.example.com", "test-tenant");

    // Use reflection to access private method
    Method saveLocalMethod = ShortUrlDefaultService.class.getDeclaredMethod("saveLocal", ShortUrl.class);
    saveLocalMethod.setAccessible(true);

    // When
    saveLocalMethod.invoke(service, shortUrl);

    // Then
    verify(shortUrlMapper).insert(shortUrl);
  }

  @Test
  void shouldHandleSaveLocalFailure() throws Exception {
    // Given
    ShortUrl shortUrl = createShortUrl("abc123", "https://www.example.com", "test-tenant");
    doThrow(new RuntimeException("Database error")).when(shortUrlMapper).insert(shortUrl);

    // Use reflection to access private method
    Method saveLocalMethod = ShortUrlDefaultService.class.getDeclaredMethod("saveLocal", ShortUrl.class);
    saveLocalMethod.setAccessible(true);

    // When/Then
    assertThrows(Exception.class, () -> saveLocalMethod.invoke(service, shortUrl));
    verify(shortUrlMapper).insert(shortUrl);
  }

  @Test
  void shouldHandleTrySaveLocalSuccess() throws Exception {
    // Given
    ShortUrl shortUrl = createShortUrl("abc123", "https://www.example.com", "test-tenant");

    // Use reflection to access private method
    Method trySaveLocalMethod = ShortUrlDefaultService.class.getDeclaredMethod("trySaveLocal", ShortUrl.class);
    trySaveLocalMethod.setAccessible(true);

    // When
    trySaveLocalMethod.invoke(service, shortUrl);

    // Then
    verify(shortUrlMapper).insert(shortUrl);
  }

  @Test
  void shouldHandleTrySaveLocalWithNullShortUrl() throws Exception {
    // Use reflection to access private method
    Method trySaveLocalMethod = ShortUrlDefaultService.class.getDeclaredMethod("trySaveLocal", ShortUrl.class);
    trySaveLocalMethod.setAccessible(true);

    // When
    trySaveLocalMethod.invoke(service, (ShortUrl) null);

    // Then
    verify(shortUrlMapper, never()).insert(any());
  }

  @Test
  void shouldHandleTrySaveLocalWithException() throws Exception {
    // Given
    ShortUrl shortUrl = createShortUrl("abc123", "https://www.example.com", "test-tenant");
    doThrow(new RuntimeException("Database error")).when(shortUrlMapper).insert(shortUrl);

    // Use reflection to access private method
    Method trySaveLocalMethod = ShortUrlDefaultService.class.getDeclaredMethod("trySaveLocal", ShortUrl.class);
    trySaveLocalMethod.setAccessible(true);

    // When - should not throw exception
    trySaveLocalMethod.invoke(service, shortUrl);

    // Then
    verify(shortUrlMapper).insert(shortUrl);
  }

  @Test
  void shouldHandleSaveLocalIfNotExistWhenExists() throws Exception {
    // Given
    ShortUrl shortUrl = createShortUrl("abc123", "https://www.example.com", "test-tenant");
    when(shortUrlMapper.selectByCode("abc123")).thenReturn(Optional.of(shortUrl));

    // Use reflection to access private method
    Method saveLocalIfNotExistMethod = ShortUrlDefaultService.class.getDeclaredMethod("saveLocalIfNotExist",
        ShortUrl.class);
    saveLocalIfNotExistMethod.setAccessible(true);

    // When
    saveLocalIfNotExistMethod.invoke(service, shortUrl);

    // Then
    verify(shortUrlMapper).selectByCode("abc123");
    verify(shortUrlMapper, never()).insert(any());
  }

  @Test
  void shouldHandleSaveLocalIfNotExistWhenNotExists() throws Exception {
    // Given
    ShortUrl shortUrl = createShortUrl("abc123", "https://www.example.com", "test-tenant");
    when(shortUrlMapper.selectByCode("abc123")).thenReturn(Optional.empty());

    // Use reflection to access private method
    Method saveLocalIfNotExistMethod = ShortUrlDefaultService.class.getDeclaredMethod("saveLocalIfNotExist",
        ShortUrl.class);
    saveLocalIfNotExistMethod.setAccessible(true);

    try (MockedStatic<StupidUtils> stupidUtilsMock = Mockito.mockStatic(StupidUtils.class)) {
      // When
      saveLocalIfNotExistMethod.invoke(service, shortUrl);

      // Then
      verify(shortUrlMapper).selectByCode("abc123");
      verify(shortUrlMapper).insert(shortUrl);
      stupidUtilsMock.verify(() -> StupidUtils.sleep(anyLong()));
    }
  }

  @Test
  void shouldHandleSaveLocalIfNotExistWithException() throws Exception {
    // Given
    ShortUrl shortUrl = createShortUrl("abc123", "https://www.example.com", "test-tenant");
    when(shortUrlMapper.selectByCode("abc123")).thenReturn(Optional.empty());
    doThrow(new RuntimeException("Database error")).when(shortUrlMapper).insert(shortUrl);

    // Use reflection to access private method
    Method saveLocalIfNotExistMethod = ShortUrlDefaultService.class.getDeclaredMethod("saveLocalIfNotExist",
        ShortUrl.class);
    saveLocalIfNotExistMethod.setAccessible(true);

    try (MockedStatic<StupidUtils> stupidUtilsMock = Mockito.mockStatic(StupidUtils.class)) {
      // When - should not throw exception
      saveLocalIfNotExistMethod.invoke(service, shortUrl);

      // Then
      verify(shortUrlMapper).selectByCode("abc123");
      verify(shortUrlMapper).insert(shortUrl);
      stupidUtilsMock.verify(() -> StupidUtils.sleep(anyLong()));
    }
  }

  @Test
  void shouldHandleDoCreateRemote() throws Exception {
    // Given
    String url = "https://www.example.com";
    String tenantId = "test-tenant";
    Long timeout = 1000L;

    ShortUrl shortUrl = createShortUrl("abc123", url, tenantId);
    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    // Create a mock RestTemplate
    RestTemplate mockRestTemplate = mock(RestTemplate.class);

    // Set the mock RestTemplate using reflection
    Field restTemplateField = ShortUrlDefaultService.class.getDeclaredField("restTemplate");
    restTemplateField.setAccessible(true);
    restTemplateField.set(service, mockRestTemplate);

    // Mock the response
    ResponseEntity<EgressApiResponse<ShortUrl>> responseEntity = mock(ResponseEntity.class);
    EgressApiResponse<ShortUrl> apiResponse = new EgressApiResponse<>();
    apiResponse.setCode(200);
    apiResponse.setData(shortUrl);

    when(responseEntity.getBody()).thenReturn(apiResponse);
    when(mockRestTemplate.exchange(
        anyString(),
        eq(HttpMethod.POST),
        any(HttpEntity.class),
        any(ParameterizedTypeReference.class))).thenReturn(responseEntity);

    // Use reflection to access private method
    Method doCreateRemoteMethod = ShortUrlDefaultService.class.getDeclaredMethod("doCreateRemote", String.class,
        String.class, Long.class);
    doCreateRemoteMethod.setAccessible(true);

    // When
    ShortUrl result = (ShortUrl) doCreateRemoteMethod.invoke(service, url, tenantId, timeout);

    // Then
    assertNotNull(result);
    assertEquals("abc123", result.getCode());
    assertEquals(url, result.getUrl());
    assertEquals(tenantId, result.getTenantId());
  }

  @Test
  void shouldHandleDoCreateRemoteWithException() throws Exception {
    // Given
    String url = "https://www.example.com";
    String tenantId = "test-tenant";
    Long timeout = 1000L;

    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    // Create a mock RestTemplate
    RestTemplate mockRestTemplate = mock(RestTemplate.class);

    // Set the mock RestTemplate using reflection
    Field restTemplateField = ShortUrlDefaultService.class.getDeclaredField("restTemplate");
    restTemplateField.setAccessible(true);
    restTemplateField.set(service, mockRestTemplate);

    // Mock the exception
    when(mockRestTemplate.exchange(
        anyString(),
        eq(HttpMethod.POST),
        any(HttpEntity.class),
        any(ParameterizedTypeReference.class))).thenThrow(new RuntimeException("API error"));

    // Use reflection to access private method
    Method doCreateRemoteMethod = ShortUrlDefaultService.class.getDeclaredMethod("doCreateRemote", String.class,
        String.class, Long.class);
    doCreateRemoteMethod.setAccessible(true);

    // When/Then
    assertThrows(Exception.class, () -> doCreateRemoteMethod.invoke(service, url, tenantId, timeout));
  }

  @Test
  void shouldHandleTryReadAndSaveAgain() throws Exception {
    // Given
    String code = "abc123";
    ShortUrl shortUrl = createShortUrl(code, "https://www.example.com", "test-tenant");

    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    // Create a mock RestTemplate
    RestTemplate mockRestTemplate = mock(RestTemplate.class);

    // Set the mock RestTemplate using reflection
    Field restTemplateField = ShortUrlDefaultService.class.getDeclaredField("restTemplate");
    restTemplateField.setAccessible(true);
    restTemplateField.set(service, mockRestTemplate);

    // Mock the response
    ResponseEntity<EgressApiResponse<ShortUrl>> responseEntity = mock(ResponseEntity.class);
    EgressApiResponse<ShortUrl> apiResponse = new EgressApiResponse<>();
    apiResponse.setCode(200);
    apiResponse.setData(shortUrl);

    when(responseEntity.getBody()).thenReturn(apiResponse);
    when(mockRestTemplate.exchange(
        anyString(),
        eq(HttpMethod.GET),
        any(),
        any(ParameterizedTypeReference.class))).thenReturn(responseEntity);

    // Use reflection to access private method
    Method tryReadAndSaveAgainMethod = ShortUrlDefaultService.class.getDeclaredMethod("tryReadAndSaveAgain",
        String.class);
    tryReadAndSaveAgainMethod.setAccessible(true);

    // When
    ShortUrl result = (ShortUrl) tryReadAndSaveAgainMethod.invoke(service, code);

    // Then
    assertNotNull(result);
    assertEquals(code, result.getCode());
    assertEquals("https://www.example.com", result.getUrl());
    assertEquals("test-tenant", result.getTenantId());
  }

  @Test
  void shouldHandleTryReadAndSaveAgainWithNullResponse() throws Exception {
    // Given
    String code = "abc123";

    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    // Create a mock RestTemplate
    RestTemplate mockRestTemplate = mock(RestTemplate.class);

    // Set the mock RestTemplate using reflection
    Field restTemplateField = ShortUrlDefaultService.class.getDeclaredField("restTemplate");
    restTemplateField.setAccessible(true);
    restTemplateField.set(service, mockRestTemplate);

    // Mock the response with null data
    ResponseEntity<EgressApiResponse<ShortUrl>> responseEntity = mock(ResponseEntity.class);
    EgressApiResponse<ShortUrl> apiResponse = new EgressApiResponse<>();
    apiResponse.setCode(200);
    apiResponse.setData(null);

    when(responseEntity.getBody()).thenReturn(apiResponse);
    when(mockRestTemplate.exchange(
        anyString(),
        eq(HttpMethod.GET),
        any(),
        any(ParameterizedTypeReference.class))).thenReturn(responseEntity);

    // Use reflection to access private method
    Method tryReadAndSaveAgainMethod = ShortUrlDefaultService.class.getDeclaredMethod("tryReadAndSaveAgain",
        String.class);
    tryReadAndSaveAgainMethod.setAccessible(true);

    // When
    ShortUrl result = (ShortUrl) tryReadAndSaveAgainMethod.invoke(service, code);

    // Then
    assertNull(result);
  }

  @Test
  void shouldHandleTryReadAndSaveAgainWithException() throws Exception {
    // Given
    String code = "abc123";

    when(properties.getProxyEgressUrl()).thenReturn("https://api.example.com");

    // Create a mock RestTemplate
    RestTemplate mockRestTemplate = mock(RestTemplate.class);

    // Set the mock RestTemplate using reflection
    Field restTemplateField = ShortUrlDefaultService.class.getDeclaredField("restTemplate");
    restTemplateField.setAccessible(true);
    restTemplateField.set(service, mockRestTemplate);

    // Mock the exception
    when(mockRestTemplate.exchange(
        anyString(),
        eq(HttpMethod.GET),
        any(),
        any(ParameterizedTypeReference.class))).thenThrow(new RuntimeException("API error"));

    // Use reflection to access private method
    Method tryReadAndSaveAgainMethod = ShortUrlDefaultService.class.getDeclaredMethod("tryReadAndSaveAgain",
        String.class);
    tryReadAndSaveAgainMethod.setAccessible(true);

    // When
    ShortUrl result = (ShortUrl) tryReadAndSaveAgainMethod.invoke(service, code);

    // Then
    assertNull(result);
  }
}
