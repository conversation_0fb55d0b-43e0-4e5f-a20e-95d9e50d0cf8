package com.sharecrm.egress.service;

import com.sharecrm.egress.api.I18nApi;
import com.sharecrm.egress.entity.PhoneAreaLang;
import com.sharecrm.egress.entity.PhoneAreaLangResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = I18nLocationService.class)
class I18nLocationServiceTest {

  @MockitoBean
  private I18nApi i18nApi;
  @Autowired
  private I18nLocationService i18nLocationService;

  @Test
  void queryChinese() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    PhoneAreaLang lang = new PhoneAreaLang();
    lang.setProvinceLang("河南省");
    lang.setCityLang("周口市");
    data.setResult(lang);
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));
    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "zh-CN"))
      .expectNextMatches(loc -> "河南省".equals(loc.getProvince()) && "周口市".equals(loc.getCity()))
      .verifyComplete();
  }

  @Test
  void queryEnglish() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "en-US"))
      .expectNextMatches(loc -> "China Unicom".equals(loc.getCarrier()))
      .verifyComplete();
  }
}