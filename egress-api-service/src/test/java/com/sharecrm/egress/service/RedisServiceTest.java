package com.sharecrm.egress.service;

import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.utils.JsonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RedisServiceTest {

  @Mock
  private StringRedisTemplate redisTemplate;

  @Mock
  private ValueOperations<String, String> valueOperations;

  @Mock
  private SetOperations<String, String> setOperations;

  private RedisService redisService;

  @BeforeEach
  void setUp() {
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    when(redisTemplate.opsForSet()).thenReturn(setOperations);
    redisService = new RedisService(redisTemplate);
  }

  @Test
  void testSmsSetIfAbsent_Success() {
    // Setup
    String phone = "1234567890";
    String key = "test-key";
    when(valueOperations.setIfAbsent(eq(RedisService.KEY_SMS + phone), eq("1"), any(Duration.class)))
        .thenReturn(true);

    // Execute
    boolean result = redisService.smsSetIfAbsent(phone, RedisService.KEY_SMS);

    // Verify
    assertTrue(result);
    verify(valueOperations).setIfAbsent(eq(RedisService.KEY_SMS + phone), eq("1"), any(Duration.class));
  }

  @Test
  void testSmsSetIfAbsent_Failure() {
    // Setup
    String phone = "1234567890";
    String key = "test-key";
    when(valueOperations.setIfAbsent(eq(RedisService.KEY_SMS + phone), eq("1"), any(Duration.class)))
        .thenReturn(false);

    // Execute
    boolean result = redisService.smsSetIfAbsent(phone, RedisService.KEY_SMS);

    // Verify
    assertFalse(result);
    verify(valueOperations).setIfAbsent(eq(RedisService.KEY_SMS + phone), eq("1"), any(Duration.class));
  }

  @Test
  void testSmsSetIfAbsent_Exception() {
    // Setup
    String phone = "1234567890";
    String key = "test-key";
    when(valueOperations.setIfAbsent(eq(RedisService.KEY_SMS + phone), eq("1"), any(Duration.class)))
        .thenThrow(new RuntimeException("Redis error"));

    // Execute
    boolean result = redisService.smsSetIfAbsent(phone, RedisService.KEY_SMS);

    // Verify - should return true when there's an exception
    assertTrue(result);
  }

  @Test
  void testGetChannel_Success() {
    // Setup
    String phone = "1234567890";
    Set<String> channels = Set.of("channel1", "channel2");
    when(setOperations.members(RedisService.KEY_SMS_CHANNEL + phone)).thenReturn(channels);

    // Execute
    Set<String> result = redisService.getChannel(phone);

    // Verify
    assertEquals(channels, result);
    verify(setOperations).members(RedisService.KEY_SMS_CHANNEL + phone);
  }

  @Test
  void testGetChannel_Exception() {
    // Setup
    String phone = "1234567890";
    when(setOperations.members(RedisService.KEY_SMS_CHANNEL + phone))
        .thenThrow(new RuntimeException("Redis error"));

    // Execute
    Set<String> result = redisService.getChannel(phone);

    // Verify - should return empty set on exception
    assertTrue(result.isEmpty());
  }

  @Test
  void testSaveSmsTemplate() {
    // Setup
    SmsTemplateEntity template = new SmsTemplateEntity();
    template.setTemplateId("T123456");
    template.setContent("Test template content");
    template.setStatus("APPROVED");

    // Execute
    redisService.saveSmsTemplate(template);

    // Verify
    verify(valueOperations).set(
        eq(RedisService.KEY_SMS_TEMPLATE + "T123456"),
        eq(JsonUtil.toJson(template)),
        eq(Duration.ofDays(7)));
  }

  @Test
  void testQuerySmsTemplate_Found() {
    // Setup
    SmsTemplateEntity template = new SmsTemplateEntity();
    template.setTemplateId("T123456");
    template.setContent("Test template content");
    template.setStatus("APPROVED");

    String jsonTemplate = JsonUtil.toJson(template);
    when(valueOperations.get(RedisService.KEY_SMS_TEMPLATE + "T123456")).thenReturn(jsonTemplate);

    // Execute
    SmsTemplateEntity result = redisService.querySmsTemplate("T123456");

    // Verify
    assertNotNull(result);
    assertEquals("T123456", result.getTemplateId());
    assertEquals("Test template content", result.getContent());
    assertEquals("APPROVED", result.getStatus());
  }

  @Test
  void testQuerySmsTemplate_NotFound() {
    // Setup
    when(valueOperations.get(RedisService.KEY_SMS_TEMPLATE + "T123456")).thenReturn(null);

    // Execute
    SmsTemplateEntity result = redisService.querySmsTemplate("T123456");

    // Verify
    assertNull(result);
  }

  @Test
  void testQuerySmsTemplate_Exception() {
    // Setup
    when(valueOperations.get(RedisService.KEY_SMS_TEMPLATE + "T123456"))
        .thenThrow(new RuntimeException("Redis error"));

    // Execute
    SmsTemplateEntity result = redisService.querySmsTemplate("T123456");

    // Verify - should return null on exception
    assertNull(result);
  }

  @Test
  void testSaveChannel() {
    // Setup
    String phone = "1234567890";
    String channel = "testChannel";
    when(setOperations.getOperations()).thenReturn(redisTemplate);

    // Execute
    redisService.saveChannel(phone, channel);

    // Verify
    verify(setOperations).add(RedisService.KEY_SMS_CHANNEL + phone, channel);
    verify(redisTemplate).expire(RedisService.KEY_SMS_CHANNEL + phone, Duration.ofSeconds(180));
  }

  @Test
  void testSaveChannel_Exception() {
    // Setup
    String phone = "1234567890";
    String channel = "testChannel";
    when(setOperations.add(RedisService.KEY_SMS_CHANNEL + phone, channel))
        .thenThrow(new RuntimeException("Redis error"));

    // Execute & Verify - should not throw exception
    assertDoesNotThrow(() -> redisService.saveChannel(phone, channel));
  }
}