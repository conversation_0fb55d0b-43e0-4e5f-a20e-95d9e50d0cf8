package com.sharecrm.egress.service;

import com.fxiaoke.enterpriserelation2.service.AuthService;
import com.sharecrm.egress.config.EgressProperties;
import com.sharecrm.egress.entity.CookieToAuthArgument;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.http.HttpCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ActiveSessionServiceTest {

  @Mock
  private EgressProperties egressProperties;

  @Mock
  private ActiveSessionApi activeSessionApi;

  @Mock
  private AuthService authService;

  @Mock
  private ServerWebExchange exchange;

  @Mock
  private ServerHttpRequest request;

  private ActiveSessionService activeSessionService;

  @BeforeEach
  void setUp() {
    when(egressProperties.getSessionTimeout()).thenReturn(Duration.ofMinutes(30));
    when(egressProperties.getSessionMaximumSize()).thenReturn(10000);
    when(egressProperties.getFsAuthCookies()).thenReturn(List.of("fs_token", "euc_token"));
    when(egressProperties.getEnterpriseAuthCookies()).thenReturn(List.of("er_token"));
    when(exchange.getRequest()).thenReturn(request);

    activeSessionService = new ActiveSessionService(egressProperties, activeSessionApi, authService);
  }

  @Test
  void testHandleRefreshedEvent() {
    // Execute
    activeSessionService.handleRefreshedEvent(new RefreshScopeRefreshedEvent());

    // Verify the service is still functional after refresh
    when(egressProperties.isSkipCookieAuth()).thenReturn(true);

    Mono<Boolean> result = activeSessionService.checkCookie(exchange);
    StepVerifier.create(result)
      .expectNext(true)
      .verifyComplete();
  }

  @Test
  void testCheckCookie_EmptyResponseFromApi() {
    // Setup
    String cookieValue = "no-response-cookie";
    MultiValueMap<String, HttpCookie> cookies = new LinkedMultiValueMap<>();
    cookies.add("fs_token", new HttpCookie("fs_token", cookieValue));
    when(request.getCookies()).thenReturn(cookies);

    // Mock API returning empty response
    when(activeSessionApi.cookieToAuthXC(any(CookieToAuthArgument.class)))
      .thenReturn(Mono.empty());

    // Execute
    Mono<Boolean> result = activeSessionService.checkCookie(exchange);

    // Verify - should return false for empty response
    StepVerifier.create(result)
      .expectNext(false)
      .verifyComplete();
  }

}