package com.sharecrm.egress.service;

import com.sharecrm.egress.config.EgressProperties;
import com.sharecrm.egress.entity.CityLocale;
import com.sharecrm.egress.entity.MobileLocation;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = MobileService.class)
class MobileServiceTest {

  @MockitoBean
  private I18nLocationService i18nService;
  @MockitoBean
  private MobileCacheService cache;
  @MockitoBean
  private EgressProperties properties;
  @Autowired
  private MobileService mobileService;


  @Test
  void query() {
    // 手机号不合规，返回empty
    StepVerifier.create(mobileService.query("135", "")).verifyComplete();
    String city = "北京";
    MobileLocation data = new MobileLocation();
    data.setCity(city);
    when(cache.getAsync("1234567")).thenReturn(Mono.just(data));
    when(properties.getCityFullNames()).thenReturn(Map.of(city, "北京市别名"));
    when(i18nService.translate(any(), any(), any(), any())).thenReturn(Mono.just(new CityLocale()));
    StepVerifier.create(mobileService.query("12345678901", "zh-CN"))
      .expectNextMatches(loc -> loc.getCity().equals("北京市别名"))
      .verifyComplete();
  }
}