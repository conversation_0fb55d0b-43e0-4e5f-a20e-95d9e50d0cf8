package com.sharecrm.egress.service;

import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.MobileLocation;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

class MobileCacheServiceTest {

  private static final MobileCacheService service = new MobileCacheService(mockProperties());

  @NotNull
  private static MapProperties mockProperties() {
    MapProperties properties = new MapProperties();
    properties.setMaxmind(new MapProperties.MaxmindConfig());
    return properties;
  }

  @BeforeAll
  static void init() throws IOException {
    service.init();
  }

  @Test
  void lineToLocation() {
    // 格式：1983947,0394,河南,周口,移动
    assertNull(service.lineToLocation("1983947,0394"));
    MobileLocation location = service.lineToLocation("1983947,0394,河南,周口,移动");
    assertNotNull(location);
    assertEquals("河南", location.getProvince());
    assertEquals("周口", location.getCity());
    // 模拟的文件里只有一行 (mobile=1300000, province=山东, city=济南, carrier=联通, operator=联通, code=0531, i18n=null)
    Mono<MobileLocation> async = service.getAsync("1300000");
    StepVerifier.create(async)
      .expectNextMatches(e -> e.getCity().equals("济南"))
      .verifyComplete();
  }

  @AfterAll
  static void close() {
    service.close();
  }

}