package com.sharecrm.egress.service;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.AsrProperties;
import com.sharecrm.egress.config.AsrProperties.TencentConfig;
import com.sharecrm.egress.dao.AsrDao;
import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.AsrRecTaskEntity;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.CreateRecTaskResponse;
import com.tencentcloudapi.asr.v20190614.models.DescribeTaskStatusResponse;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionResponse;
import com.tencentcloudapi.asr.v20190614.models.Task;
import com.tencentcloudapi.asr.v20190614.models.TaskStatus;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import lombok.SneakyThrows;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class AsrTencentProviderTest {

  private AsrClient client;
  private AsrDao asrDao;
  private AsrProperties properties;
  private AutoConfMQProducer producer;
  private AsrTencentProvider provider;

  @BeforeEach
  void setUp() {
    client = mock(AsrClient.class);
    asrDao = mock(AsrDao.class);
    properties = mockAsrProperties();
    producer = mock(AutoConfMQProducer.class);
    SendResult sendResult = mock(SendResult.class);
    when(producer.send(any(Message.class))).thenReturn(sendResult);

    provider = new AsrTencentProvider(client, properties, asrDao, producer);
  }

  private AsrProperties mockAsrProperties() {
    AsrProperties properties = new AsrProperties();
    TencentConfig tencent = properties.getTencent();
    tencent.setDefaultRecEngine("16k_zh");
    return properties;
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void speechRecognition_successful() {
    // Given
    byte[] audioData = "test audio data".getBytes();
    SentenceRecognitionResponse response = new SentenceRecognitionResponse();
    response.setResult("recognized text");
    when(client.SentenceRecognition(any())).thenReturn(response);

    // When
    EgressApiResponse<TencentSpeechResponse> result = provider.speechRecognition(audioData);

    // Then
    assertEquals(400, result.getCode());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void speechRecognition_nullResponse() {
    // Given
    byte[] audioData = "test audio data".getBytes();
    when(client.SentenceRecognition(any())).thenReturn(null);

    // When
    EgressApiResponse<TencentSpeechResponse> result = provider.speechRecognition(audioData);

    // Then
    assertEquals(400, result.getCode());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void speechRecognition_nullResult() {
    // Given
    byte[] audioData = "test audio data".getBytes();
    SentenceRecognitionResponse response = new SentenceRecognitionResponse();
    response.setResult(null);
    when(client.SentenceRecognition(any())).thenReturn(response);

    // When
    EgressApiResponse<TencentSpeechResponse> result = provider.speechRecognition(audioData);

    // Then
    assertEquals(400, result.getCode());
  }

  @Test
  void speechRecognition_unsupportedFormat() {
    // Given
    byte[] audioData = new byte[] { 0, 1, 2, 3 }; // 不支持的格式

    // When
    EgressApiResponse<TencentSpeechResponse> result = provider.speechRecognition(audioData);

    // Then
    assertEquals(400, result.getCode());
    assertEquals("audio format is not supported.", result.getMessage());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void speechRecognition_exception() {
    // Given
    byte[] audioData = "test audio data".getBytes();
    when(client.SentenceRecognition(any())).thenThrow(new TencentCloudSDKException("error"));

    // When
    EgressApiResponse<TencentSpeechResponse> result = provider.speechRecognition(audioData);

    // Then
    assertEquals(400, result.getCode());
    assertEquals("audio format is not supported.", result.getMessage());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void createRecTask_withUrl() {
    // Given
    AsrCreateRecTaskRequest request = new AsrCreateRecTaskRequest();
    request.setUrl("https://example.com/audio.mp3");

    CreateRecTaskResponse response = new CreateRecTaskResponse();
    Task task = new Task();
    task.setTaskId(123456L);
    response.setData(task);

    when(client.CreateRecTask(any())).thenReturn(response);

    // When
    EgressApiResponse<AsrCreateRecTaskResponse> result = provider.createRecTask(request);

    // Then
    assertEquals(200, result.getCode());
    assertNotNull(result.getData());
    assertNotNull(result.getData().getTaskId());
    verify(asrDao, times(1)).save(any());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void createRecTask_withData() {
    // Given
    AsrCreateRecTaskRequest request = new AsrCreateRecTaskRequest();
    request.setData("test audio data".getBytes());
    request.setEngineModelType("16k_zh");
    request.setChannelNum(2);
    request.setSpeakerDiarization(0);
    request.setSpeakerNumber(0);
    request.setResTextFormat(0);

    CreateRecTaskResponse response = new CreateRecTaskResponse();
    Task task = new Task();
    task.setTaskId(123456L);
    response.setData(task);

    when(client.CreateRecTask(any())).thenReturn(response);

    // When
    EgressApiResponse<AsrCreateRecTaskResponse> result = provider.createRecTask(request);

    // Then
    assertEquals(200, result.getCode());
    assertNotNull(result.getData());
    assertNotNull(result.getData().getTaskId());
    verify(asrDao, times(1)).save(any());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void createRecTask_nullResponse() {
    // Given
    AsrCreateRecTaskRequest request = new AsrCreateRecTaskRequest();
    request.setUrl("https://example.com/audio.mp3");

    when(client.CreateRecTask(any())).thenReturn(null);

    // When
    EgressApiResponse<AsrCreateRecTaskResponse> result = provider.createRecTask(request);

    // Then
    assertEquals(500, result.getCode());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void createRecTask_exception() {
    // Given
    AsrCreateRecTaskRequest request = new AsrCreateRecTaskRequest();
    request.setUrl("https://example.com/audio.mp3");

    when(client.CreateRecTask(any())).thenThrow(new RuntimeException("Test exception"));

    // When
    EgressApiResponse<AsrCreateRecTaskResponse> result = provider.createRecTask(request);

    // Then
    assertEquals(400, result.getCode());
    assertEquals("create rec task server failed.", result.getMessage());
  }

  @Test
  void queryRecTask_notFound() {
    // Given
    when(asrDao.queryByTaskId("nonexistent")).thenReturn(null);

    // When
    EgressApiResponse<AsrQueryRecTaskResponse> result = provider.queryRecTask("nonexistent");

    // Then
    assertEquals(404, result.getCode());
    assertNull(result.getData());
  }

  @Test
  void queryRecTask_found() {
    // Given
    AsrRecTaskEntity entity = new AsrRecTaskEntity();
    entity.setTaskId("task123");
    entity.setStatus(AsrQueryRecTaskResponse.STATUS_SUCCESS);
    entity.setText("transcribed text");
    entity.setMessage("success message");
    entity.setAudioDuration(60000L);

    when(asrDao.queryByTaskId("task123")).thenReturn(entity);

    // When
    EgressApiResponse<AsrQueryRecTaskResponse> result = provider.queryRecTask("task123");

    // Then
    assertEquals(200, result.getCode());
    assertNotNull(result.getData());
    assertEquals("task123", result.getData().getTaskId());
    assertEquals(AsrQueryRecTaskResponse.STATUS_SUCCESS, result.getData().getStatus());
    assertEquals("transcribed text", result.getData().getText());
    assertEquals("success message", result.getData().getMessage());
    assertEquals(60000L, result.getData().getAudioDuration());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void updateRecTaskStatus_successful() {
    // Given
    AsrRecTaskEntity entity = new AsrRecTaskEntity();
    entity.setTaskId("task123");
    entity.setProviderTaskId("123456");

    when(asrDao.queryWithNoReplay(anyString())).thenReturn(List.of(entity));

    DescribeTaskStatusResponse response = new DescribeTaskStatusResponse();
    TaskStatus taskStatus = new TaskStatus();
    taskStatus.setStatusStr(AsrQueryRecTaskResponse.STATUS_SUCCESS);
    taskStatus.setErrorMsg("");
    taskStatus.setResult("transcribed text");
    taskStatus.setAudioDuration(60.0F);
    response.setData(taskStatus);

    when(client.DescribeTaskStatus(any())).thenReturn(response);

    // When
    provider.updateRecTaskStatus();

    // Then
    verify(asrDao, times(1)).save(any());
    verify(producer, times(1)).send(any(Message.class));
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void updateRecTaskStatus_nullData() {
    // Given
    AsrRecTaskEntity entity = new AsrRecTaskEntity();
    entity.setTaskId("task123");
    entity.setProviderTaskId("123456");

    when(asrDao.queryWithNoReplay(anyString())).thenReturn(List.of(entity));

    DescribeTaskStatusResponse response = new DescribeTaskStatusResponse();
    response.setData(null);

    when(client.DescribeTaskStatus(any())).thenReturn(response);

    // When
    provider.updateRecTaskStatus();

    // Then
    verify(asrDao, times(0)).save(any());
    verify(producer, times(0)).send(any(Message.class));
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void updateRecTaskStatus_exception() {
    // Given
    AsrRecTaskEntity entity = new AsrRecTaskEntity();
    entity.setTaskId("task123");
    entity.setProviderTaskId("123456");

    when(asrDao.queryWithNoReplay(anyString())).thenReturn(List.of(entity));

    when(client.DescribeTaskStatus(any())).thenThrow(new TencentCloudSDKException("Test exception"));

    // When
    provider.updateRecTaskStatus();

    // Then
    verify(asrDao, times(0)).save(any());
    verify(producer, times(0)).send(any(Message.class));
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void updateRecTaskStatus_emptyList() {
    // Given
    when(asrDao.queryWithNoReplay(anyString())).thenReturn(Collections.emptyList());

    // When
    assertDoesNotThrow(provider::updateRecTaskStatus);

    // Then
    verify(client, times(0)).DescribeTaskStatus(any());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void updateRecTaskStatus_daoException() {
    // Given
    when(asrDao.queryWithNoReplay(anyString())).thenThrow(new RuntimeException("Test exception"));

    // When
    assertDoesNotThrow(provider::updateRecTaskStatus);

    // Then
    verify(client, times(0)).DescribeTaskStatus(any());
  }

  @Test
  void queryRecTask_withDetails() {
    // Given
    AsrRecTaskEntity entity = new AsrRecTaskEntity();
    entity.setTaskId("task123");
    entity.setStatus(AsrQueryRecTaskResponse.STATUS_DOING);
    entity.setText("processing text");
    entity.setMessage("processing message");
    entity.setAudioDuration(30000L);
    entity.setCreateTime(new Date());
    entity.setUpdateTime(new Date());

    when(asrDao.queryByTaskId("task123")).thenReturn(entity);

    // When
    EgressApiResponse<AsrQueryRecTaskResponse> result = provider.queryRecTask("task123");

    // Then
    assertEquals(200, result.getCode());
    assertNotNull(result.getData());
    assertEquals("task123", result.getData().getTaskId());
    assertEquals(AsrQueryRecTaskResponse.STATUS_DOING, result.getData().getStatus());
    assertEquals("processing text", result.getData().getText());
    assertEquals("processing message", result.getData().getMessage());
    assertEquals(30000L, result.getData().getAudioDuration());
  }

  @Test
  @SneakyThrows(TencentCloudSDKException.class)
  void createRecTask_withNullData() {
    // Given
    AsrCreateRecTaskRequest request = new AsrCreateRecTaskRequest();
    request.setUrl("https://example.com/audio.mp3");

    CreateRecTaskResponse response = new CreateRecTaskResponse();
    response.setData(null);

    when(client.CreateRecTask(any())).thenReturn(response);

    // When
    EgressApiResponse<AsrCreateRecTaskResponse> result = provider.createRecTask(request);

    // Then
    assertEquals(500, result.getCode());
    // 不检查 result.getData() 是否为 null，因为实际实现可能返回空对象而不是 null
  }
}