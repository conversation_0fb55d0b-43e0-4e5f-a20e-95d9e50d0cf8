package com.sharecrm.egress.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.RequestPath;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ShortUrlRateLimiterFilterTest {

  private ShortUrlRateLimiterFilter filter;

  @Mock
  private ReactiveStringRedisTemplate redisTemplate;

  @Mock
  private ReactiveValueOperations<String, String> valueOps;

  @Mock
  private ServerWebExchange exchange;

  @Mock
  private WebFilterChain chain;

  @Mock
  private ServerHttpRequest request;

  @Mock
  private ServerHttpResponse response;

  @Mock
  private HttpHeaders headers;

  @Mock
  private RequestPath requestPath;

  private ShortUrlProperties properties;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

    properties = new ShortUrlProperties();
    properties.setRateLimiterEnabled(true);
    properties.setRateLimiterCount(10);
    properties.setRateLimiterDuration(Duration.ofMinutes(1));

    filter = new ShortUrlRateLimiterFilter(redisTemplate, properties);

    when(exchange.getRequest()).thenReturn(request);
    when(exchange.getResponse()).thenReturn(response);
    when(request.getHeaders()).thenReturn(headers);
    when(request.getPath()).thenReturn(requestPath);
    when(chain.filter(any())).thenReturn(Mono.empty());
    when(redisTemplate.opsForValue()).thenReturn(valueOps);
  }

  @Test
  void shouldAllowRequestWhenNotShortUrlPath() {
    // Given
    when(requestPath.value()).thenReturn("/api/other-path");

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(exchange);
    verifyNoInteractions(valueOps);
  }

  @Test
  void shouldAllowRequestWhenRateLimiterDisabled() {
    // Given
    properties.setRateLimiterEnabled(false);
    when(requestPath.value()).thenReturn("/public-short-urls/abc123");

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(exchange);
    verifyNoInteractions(valueOps);
  }

  @Test
  void shouldAllowRequestWhenNoRealIp() {
    // Given
    when(requestPath.value()).thenReturn("/public-short-urls/abc123");
    when(headers.getFirst("X-Forwarded-For")).thenReturn(null);
    when(headers.getFirst("x-real-ip")).thenReturn(null);

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(exchange);
    verifyNoInteractions(valueOps);
  }

  @Test
  void shouldAllowRequestWhenUnderRateLimit() {
    // Given
    when(requestPath.value()).thenReturn("/public-short-urls/abc123");
    when(headers.getFirst("X-Forwarded-For")).thenReturn("***********");
    when(headers.getFirst("User-Agent")).thenReturn("test-agent");
    when(valueOps.increment(anyString(), eq(1L))).thenReturn(Mono.just(5L));
    when(redisTemplate.expire(anyString(), any(Duration.class))).thenReturn(Mono.just(true));

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(exchange);
    verify(valueOps).increment(anyString(), eq(1L));
  }

  @Test
  void shouldBlockRequestWhenOverRateLimit() {
    // Given
    when(requestPath.value()).thenReturn("/public-short-urls/abc123");
    when(headers.getFirst("X-Forwarded-For")).thenReturn("***********");
    when(headers.getFirst("User-Agent")).thenReturn("test-agent");
    when(valueOps.increment(anyString(), eq(1L))).thenReturn(Mono.just(11L));
    when(response.setComplete()).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(response).setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
    verify(response).setComplete();
    verify(chain, never()).filter(exchange);
  }

  @Test
  void shouldSetExpiryOnFirstRequest() {
    // Given
    when(requestPath.value()).thenReturn("/public-short-urls/abc123");
    when(headers.getFirst("X-Forwarded-For")).thenReturn("***********");
    when(headers.getFirst("User-Agent")).thenReturn("test-agent");
    when(valueOps.increment(anyString(), eq(1L))).thenReturn(Mono.just(1L));
    when(redisTemplate.expire(anyString(), any(Duration.class))).thenReturn(Mono.just(true));

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(redisTemplate).expire(anyString(), eq(properties.getRateLimiterDuration()));
    verify(chain).filter(exchange);
  }

  @Test
  void shouldAllowRequestOnRedisError() {
    // Given
    when(requestPath.value()).thenReturn("/public-short-urls/abc123");
    when(headers.getFirst("X-Forwarded-For")).thenReturn("***********");
    when(headers.getFirst("User-Agent")).thenReturn("test-agent");
    when(valueOps.increment(anyString(), eq(1L))).thenReturn(Mono.error(new RuntimeException("Redis error")));

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(exchange);
  }
}
