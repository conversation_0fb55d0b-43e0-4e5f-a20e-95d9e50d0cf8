package com.sharecrm.egress.config;

import com.sharecrm.egress.utils.WebUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class QuotaLimiterFilterTest {

  @Mock
  private ReactiveStringRedisTemplate redisTemplate;

  @Mock
  private QuotaLimiterProperties properties;

  @Mock
  private ServerWebExchange exchange;

  @Mock
  private WebFilterChain chain;

  @Mock
  private ServerHttpRequest request;

  @Mock
  private ServerHttpResponse response;

  @Mock
  private ReactiveHashOperations<String, Object, Object> hashOperations;

  private QuotaLimiterFilter filter;

  @BeforeEach
  void setUp() {
    filter = new QuotaLimiterFilter(redisTemplate, properties);
  }

  @Test
  void testFilterWhenRedisTemplateIsNullShouldPassThrough() {
    // Given
    QuotaLimiterFilter filterWithNullRedis = new QuotaLimiterFilter(null, properties);
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    // When & Then
    StepVerifier.create(filterWithNullRedis.filter(exchange, chain))
      .verifyComplete();

    verify(chain).filter(exchange);
  }

  @Test
  void testFilterWhenPropertiesDisabledShouldPassThrough() {
    // Given
    when(properties.isEnabled()).thenReturn(false);
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    // When & Then
    StepVerifier.create(filter.filter(exchange, chain))
      .verifyComplete();

    verify(chain).filter(exchange);
  }

  @Test
  void testFilterWhenEiIsEmptyShouldPassThrough() {
    // Given
    when(properties.isEnabled()).thenReturn(true);
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(0L);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(chain).filter(exchange);
    }
  }

  @Test
  void testFilterWhenEiIsIgnoredShouldPassThrough() {
    // Given
    long ei = 12345L;
    when(properties.isEnabled()).thenReturn(true);
    when(properties.getIgnoreEis()).thenReturn(Set.of(ei));
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(chain).filter(exchange);
    }
  }

  @Test
  void testFilterWhenPathNotMatchedShouldPassThrough() {
    // Given
    long ei = 12345L;
    String path = "/unknown/path";

    when(properties.isEnabled()).thenReturn(true);
    when(properties.getIgnoreEis()).thenReturn(Set.of());
    when(properties.getPathCatalogs()).thenReturn(Map.of());
    when(exchange.getRequest()).thenReturn(request);
    when(request.getPath()).thenReturn(mock(org.springframework.http.server.RequestPath.class));
    when(request.getPath().value()).thenReturn(path);
    when(request.getMethod()).thenReturn(HttpMethod.GET);
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(chain).filter(exchange);
    }
  }

  @Test
  void testFilterWhenQuotaUnlimitedShouldAllow() {
    // Given
    long ei = 12345L;
    String path = "/api/sms/send";
    String catalog = "SMS_SEND";

    setupMocksForQuotaTest(ei, path, catalog);
    when(properties.getTenantQuotas()).thenReturn(Map.of());
    when(properties.getGlobalQuotas()).thenReturn(Map.of(catalog, -1L));
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(chain).filter(exchange);
    }
  }

  @Test
  void testFilterWhenQuotaIsZeroShouldReject() {
    // Given
    long ei = 12345L;
    String path = "/api/sms/send";
    String catalog = "SMS_SEND";

    setupMocksForQuotaTest(ei, path, catalog);
    when(properties.getTenantQuotas()).thenReturn(Map.of());
    when(properties.getGlobalQuotas()).thenReturn(Map.of(catalog, 0L));
    when(exchange.getResponse()).thenReturn(response);
    when(response.setComplete()).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(response).setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
      verify(response).setComplete();
      verify(chain, never()).filter(exchange);
    }
  }

  @Test
  void testFilterWhenWithinQuotaShouldAllow() {
    // Given
    long ei = 12345L;
    String path = "/api/sms/send";
    String catalog = "SMS_SEND";
    long quota = 100L;
    long currentCount = 50L;

    setupMocksForQuotaTest(ei, path, catalog);
    when(properties.getTenantQuotas()).thenReturn(Map.of());
    when(properties.getGlobalQuotas()).thenReturn(Map.of(catalog, quota));
    when(redisTemplate.opsForHash()).thenReturn(hashOperations);
    when(hashOperations.increment(anyString(), anyString(), eq(1L))).thenReturn(Mono.just(currentCount));
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(chain).filter(exchange);
    }
  }

  @Test
  void testFilterWhenExceedQuotaShouldReject() {
    // Given
    long ei = 12345L;
    String path = "/api/sms/send";
    String catalog = "SMS_SEND";
    long quota = 100L;
    long currentCount = 101L;

    setupMocksForQuotaTest(ei, path, catalog);
    when(properties.getTenantQuotas()).thenReturn(Map.of());
    when(properties.getGlobalQuotas()).thenReturn(Map.of(catalog, quota));
    when(redisTemplate.opsForHash()).thenReturn(hashOperations);
    when(hashOperations.increment(anyString(), anyString(), eq(1L))).thenReturn(Mono.just(currentCount));
    when(exchange.getResponse()).thenReturn(response);
    when(response.setComplete()).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(response).setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
      verify(response).setComplete();
      verify(chain, never()).filter(exchange);
    }
  }

  @Test
  void testFilterWhenFirstRequestShouldSetExpiration() {
    // Given
    long ei = 12345L;
    String path = "/api/sms/send";
    String catalog = "SMS_SEND";
    long quota = 100L;

    setupMocksForQuotaTest(ei, path, catalog);
    when(properties.getTenantQuotas()).thenReturn(Map.of());
    when(properties.getGlobalQuotas()).thenReturn(Map.of(catalog, quota));
    when(redisTemplate.opsForHash()).thenReturn(hashOperations);
    when(hashOperations.increment(anyString(), anyString(), eq(1L))).thenReturn(Mono.just(1L));
    when(redisTemplate.expireAt(anyString(), any(Instant.class))).thenReturn(Mono.just(true));
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(redisTemplate).expireAt(anyString(), any(Instant.class));
      verify(chain).filter(exchange);
    }
  }

  @Test
  void testFilterWhenRedisErrorShouldAllow() {
    // Given
    long ei = 12345L;
    String path = "/api/sms/send";
    String catalog = "SMS_SEND";
    long quota = 100L;

    setupMocksForQuotaTest(ei, path, catalog);
    when(properties.getTenantQuotas()).thenReturn(Map.of());
    when(properties.getGlobalQuotas()).thenReturn(Map.of(catalog, quota));
    when(redisTemplate.opsForHash()).thenReturn(hashOperations);
    when(hashOperations.increment(anyString(), anyString(), eq(1L)))
      .thenReturn(Mono.error(new RuntimeException("Redis connection failed")));
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(chain).filter(exchange);
    }
  }

  @Test
  void testFilterWithTenantSpecificQuotaShouldUseTenantQuota() {
    // Given
    long ei = 12345L;
    String path = "/api/sms/send";
    String catalog = "SMS_SEND";
    long tenantQuota = 200L;
    long globalQuota = 100L;

    setupMocksForQuotaTest(ei, path, catalog);
    Map<Long, Map<String, Long>> tenantQuotas = new HashMap<>();
    tenantQuotas.put(ei, Map.of(catalog, tenantQuota));
    when(properties.getTenantQuotas()).thenReturn(tenantQuotas);
    when(properties.getGlobalQuotas()).thenReturn(Map.of(catalog, globalQuota));
    when(redisTemplate.opsForHash()).thenReturn(hashOperations);
    when(hashOperations.increment(anyString(), anyString(), eq(1L))).thenReturn(Mono.just(150L));
    when(chain.filter(exchange)).thenReturn(Mono.empty());

    try (MockedStatic<WebUtils> webUtilsMock = mockStatic(WebUtils.class)) {
      webUtilsMock.when(() -> WebUtils.getEiNumFromHeader(exchange)).thenReturn(ei);

      // When & Then
      StepVerifier.create(filter.filter(exchange, chain))
        .verifyComplete();

      verify(chain).filter(exchange);
    }
  }

  @Test
  void testServiceCatalogWhenPathAndMethodMatchShouldReturnCatalog() {
    // Given
    String path = "/api/sms/send";
    String method = "POST";
    String expectedCatalog = "SMS_SEND";

    QuotaLimiterProperties.CatalogPair catalogPair = new QuotaLimiterProperties.CatalogPair();
    catalogPair.setPath("/api/sms/send");
    catalogPair.setMethod("POST");

    Map<String, QuotaLimiterProperties.CatalogPair> pathCatalogs = Map.of(expectedCatalog, catalogPair);
    when(properties.getPathCatalogs()).thenReturn(pathCatalogs);

    // When
    String result = invokeServiceCatalog(path, method);

    // Then
    assertEquals(expectedCatalog, result);
  }

  @Test
  void testServiceCatalogWhenNoMatchShouldReturnNull() {
    // Given
    String path = "/api/unknown";
    String method = "GET";

    when(properties.getPathCatalogs()).thenReturn(Map.of());

    // When
    String result = invokeServiceCatalog(path, method);

    // Then
    assertNull(result);
  }

  @Test
  void testServiceCatalogWithPartialPathMatchShouldMatch() {
    // Given
    String path = "/egress-api-service/api/sms/send/batch";
    String method = "POST";
    String expectedCatalog = "SMS_SEND";

    QuotaLimiterProperties.CatalogPair catalogPair = new QuotaLimiterProperties.CatalogPair();
    catalogPair.setPath("/api/sms/send");
    catalogPair.setMethod("POST");

    Map<String, QuotaLimiterProperties.CatalogPair> pathCatalogs = Map.of(expectedCatalog, catalogPair);
    when(properties.getPathCatalogs()).thenReturn(pathCatalogs);

    // When
    String result = invokeServiceCatalog(path, method);

    // Then
    assertEquals(expectedCatalog, result);
  }

  private void setupMocksForQuotaTest(long ei, String path, String catalog) {
    when(properties.isEnabled()).thenReturn(true);
    when(properties.getIgnoreEis()).thenReturn(Set.of());

    QuotaLimiterProperties.CatalogPair catalogPair = new QuotaLimiterProperties.CatalogPair();
    catalogPair.setPath(path);
    catalogPair.setMethod("POST");
    when(properties.getPathCatalogs()).thenReturn(Map.of(catalog, catalogPair));

    when(exchange.getRequest()).thenReturn(request);
    when(request.getPath()).thenReturn(mock(org.springframework.http.server.RequestPath.class));
    when(request.getPath().value()).thenReturn(path);
    when(request.getMethod()).thenReturn(HttpMethod.POST);
  }

  private String invokeServiceCatalog(String path, String method) {
    try {
      java.lang.reflect.Method serviceCatalogMethod = QuotaLimiterFilter.class
        .getDeclaredMethod("serviceCatalog", String.class, String.class);
      serviceCatalogMethod.setAccessible(true);
      return (String) serviceCatalogMethod.invoke(filter, path, method);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}