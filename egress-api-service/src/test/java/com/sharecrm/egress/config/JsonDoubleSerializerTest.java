package com.sharecrm.egress.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;

import static org.mockito.Mockito.verify;

class JsonDoubleSerializerTest {

    private JsonDoubleSerializer serializer;

    @Mock
    private JsonGenerator jsonGenerator;

    @Mock
    private SerializerProvider serializerProvider;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        serializer = new JsonDoubleSerializer();
    }

    @Test
    void shouldSerializeNullAsNull() throws IOException {
        // When
        serializer.serialize(null, jsonGenerator, serializerProvider);

        // Then
        verify(jsonGenerator).writeNull();
    }

    @Test
    void shouldSerializeDoubleWithSixDecimalPlaces() throws IOException {
        // Given
        Double value = 123.456789;

        // When
        serializer.serialize(value, jsonGenerator, serializerProvider);

        // Then
        verify(jsonGenerator).writeNumber("123.456789");
    }

    @Test
    void shouldSerializeDoubleWithoutTrailingZeros() throws IOException {
        // Given
        Double value = 123.0;

        // When
        serializer.serialize(value, jsonGenerator, serializerProvider);

        // Then
        verify(jsonGenerator).writeNumber("123");
    }

    @Test
    void shouldSerializeVerySmallDouble() throws IOException {
        // Given
        Double value = 0.000001;

        // When
        serializer.serialize(value, jsonGenerator, serializerProvider);

        // Then
        verify(jsonGenerator).writeNumber("0.000001");
    }

    @Test
    void shouldSerializeVeryLargeDouble() throws IOException {
        // Given
        Double value = **********.123456;

        // When
        serializer.serialize(value, jsonGenerator, serializerProvider);

        // Then
        verify(jsonGenerator).writeNumber("**********.123456");
    }
}
