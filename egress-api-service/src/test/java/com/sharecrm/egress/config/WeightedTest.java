package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class WeightedTest {

    // 创建一个实现 Weighted 接口的测试类
    static class TestWeighted implements Weighted {
        private final int weight;

        TestWeighted(int weight) {
            this.weight = weight;
        }

        @Override
        public int getWeight() {
            return weight;
        }
    }

    @Test
    void shouldReturnCorrectWeight() {
        // Given
        int expectedWeight = 10;
        Weighted weighted = new TestWeighted(expectedWeight);

        // When
        int actualWeight = weighted.getWeight();

        // Then
        assertEquals(expectedWeight, actualWeight);
    }

    @Test
    void shouldReturnZeroWeight() {
        // Given
        int expectedWeight = 0;
        Weighted weighted = new TestWeighted(expectedWeight);

        // When
        int actualWeight = weighted.getWeight();

        // Then
        assertEquals(expectedWeight, actualWeight);
    }

    @Test
    void shouldReturnNegativeWeight() {
        // Given
        int expectedWeight = -5;
        Weighted weighted = new TestWeighted(expectedWeight);

        // When
        int actualWeight = weighted.getWeight();

        // Then
        assertEquals(expectedWeight, actualWeight);
    }
}
