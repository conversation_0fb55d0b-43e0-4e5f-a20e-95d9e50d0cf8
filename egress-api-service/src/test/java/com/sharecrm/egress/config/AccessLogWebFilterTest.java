package com.sharecrm.egress.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.RequestPath;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import java.net.URI;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.InetSocketAddress;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AccessLogWebFilterTest {

  private AccessLogWebFilter filter;

  @Mock
  private ServerWebExchange exchange;

  @Mock
  private WebFilterChain chain;

  @Mock
  private ServerHttpRequest request;

  @Mock
  private ServerHttpResponse response;

  @Mock
  private HttpHeaders requestHeaders;

  @Mock
  private HttpHeaders responseHeaders;

  @Mock
  private RequestPath requestPath;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    filter = new AccessLogWebFilter();

    when(exchange.getRequest()).thenReturn(request);
    when(exchange.getResponse()).thenReturn(response);
    when(request.getHeaders()).thenReturn(requestHeaders);
    when(response.getHeaders()).thenReturn(responseHeaders);
    when(request.getPath()).thenReturn(requestPath);
    when(requestPath.value()).thenReturn("/api/test");
    when(request.getURI()).thenReturn(java.net.URI.create("http://localhost:8080/api/test"));
    when(chain.filter(any())).thenReturn(Mono.empty());
  }

  @Test
  void shouldLogAccessInformation() {
    // Given
    when(request.getMethod()).thenReturn(HttpMethod.GET);
    when(requestPath.value()).thenReturn("/api/test");
    when(request.getRemoteAddress()).thenReturn(new InetSocketAddress("127.0.0.1", 8080));
    when(response.getStatusCode()).thenReturn(HttpStatus.OK);
    when(requestHeaders.getContentLength()).thenReturn(100L);
    when(responseHeaders.getContentLength()).thenReturn(200L);

    // Headers
    when(requestHeaders.getFirst("X-fs-ei")).thenReturn("test-ei");
    when(requestHeaders.getFirst("X-fs-Enterprise-Account")).thenReturn("test-ea");
    when(requestHeaders.getFirst("X-fs-Employee-Id")).thenReturn("test-user");
    when(requestHeaders.getFirst("x-peer-name")).thenReturn("test-peer");
    when(requestHeaders.getFirst("X-Forwarded-For")).thenReturn("***********");
    when(requestHeaders.getFirst("x-real-ip")).thenReturn("***********");
    when(requestHeaders.getFirst("X-fs-Trace-Id")).thenReturn("test-trace-id");

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(any(ServerWebExchange.class));
  }

  @Test
  void shouldHandleNullValues() {
    // Given
    when(request.getMethod()).thenReturn(HttpMethod.GET);
    // Path is already mocked in setUp
    when(request.getRemoteAddress()).thenReturn(null);
    when(response.getStatusCode()).thenReturn(null);
    when(requestHeaders.getContentLength()).thenReturn(-1L);
    when(responseHeaders.getContentLength()).thenReturn(-1L);

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(any(ServerWebExchange.class));
  }

  @Test
  void shouldAbbreviatePathIfTooLong() {
    // Given
    StringBuilder longPath = new StringBuilder("/api/");
    for (int i = 0; i < 200; i++) {
      longPath.append("test");
    }

    when(request.getMethod()).thenReturn(HttpMethod.GET);
    when(requestPath.value()).thenReturn(longPath.toString());
    when(request.getRemoteAddress()).thenReturn(new InetSocketAddress("127.0.0.1", 8080));
    when(response.getStatusCode()).thenReturn(HttpStatus.OK);

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();

    verify(chain).filter(any(ServerWebExchange.class));
  }
}
