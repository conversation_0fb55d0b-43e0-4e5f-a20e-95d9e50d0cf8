package com.sharecrm.egress.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sharecrm.egress.entity.PhoneAreaLangResponse;
import org.junit.jupiter.api.Test;
import org.springframework.core.ResolvableType;
import org.springframework.core.task.TaskExecutor;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ExchangeStrategies;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

class HttpInterfaceConfigTest {

  TaskExecutor executor = mock(TaskExecutor.class);
  HttpInterfaceConfig config = new HttpInterfaceConfig(executor);

  @Test
  void jackson2JsonCodecs() {
    ExchangeStrategies strategies = config.jackson2JsonCodecs(new ObjectMapper());
    boolean match = strategies.messageWriters().stream()
      .anyMatch(e -> e.canWrite(ResolvableType.forClass(PhoneAreaLangResponse.class), MediaType.APPLICATION_JSON));
    assertTrue(match);
  }
}