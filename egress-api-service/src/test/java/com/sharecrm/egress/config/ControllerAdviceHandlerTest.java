package com.sharecrm.egress.config;

import com.sharecrm.egress.exception.SmsArgException;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.support.WebExchangeBindException;
import org.springframework.web.reactive.resource.NoResourceFoundException;
import org.springframework.web.server.MethodNotAllowedException;
import org.springframework.web.server.MissingRequestValueException;
import org.springframework.web.server.NotAcceptableStatusException;
import org.springframework.web.server.ServerWebInputException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ControllerAdviceHandlerTest {

  private ControllerAdviceHandler handler;

  @BeforeEach
  void setUp() {
    handler = new ControllerAdviceHandler();
  }

  @Test
  void shouldHandleSmsArgException() {
    // Given
    SmsArgException exception = new SmsArgException("Invalid SMS format");

    // When
    Mono<ResponseEntity<EgressApiResponse<SmsSendResponse>>> result = handler.smsArgException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
          EgressApiResponse<SmsSendResponse> body = response.getBody();
          assertNotNull(body);
          assertEquals(400, body.getCode());
          assertEquals("Invalid SMS format", body.getMessage());
          assertFalse(body.getData().isSuccess());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleNotAcceptableStatusException() {
    // Given
    NotAcceptableStatusException exception = new NotAcceptableStatusException("Not acceptable");

    // When
    Mono<ResponseEntity<EgressApiResponse<String>>> result = handler.notAcceptableStatusException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.NOT_ACCEPTABLE, response.getStatusCode());
          EgressApiResponse<String> body = response.getBody();
          assertNotNull(body);
          assertEquals(406, body.getCode());
          assertEquals("Not Acceptable", body.getMessage());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleWebExchangeBindException() {
    // Given
    WebExchangeBindException exception = mock(WebExchangeBindException.class);
    FieldError fieldError = new FieldError("object", "field", "error message");
    when(exception.getMessage()).thenReturn("Validation failed");
    when(exception.getFieldError()).thenReturn(fieldError);

    // When
    Mono<ResponseEntity<EgressApiResponse<FieldError>>> result = handler.methodArgumentNotValidException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
          EgressApiResponse<FieldError> body = response.getBody();
          assertNotNull(body);
          assertEquals(400, body.getCode());
          assertEquals("Validation failed", body.getMessage());
          assertEquals(fieldError, body.getData());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleServerWebInputException() {
    // Given
    ServerWebInputException exception = mock(ServerWebInputException.class);
    when(exception.getMessage()).thenReturn("Invalid input");
    when(exception.getReason()).thenReturn("Bad format");

    // When
    Mono<ResponseEntity<EgressApiResponse<String>>> result = handler.serverWebInputException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
          EgressApiResponse<String> body = response.getBody();
          assertNotNull(body);
          assertEquals(400, body.getCode());
          assertEquals("Invalid input", body.getMessage());
          assertEquals("Bad format", body.getData());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleNoResourceFoundExceptionForShortUrl() {
    // Given
    NoResourceFoundException exception = mock(NoResourceFoundException.class);
    when(exception.getMessage()).thenReturn("No static resource public-short-urls/abc123");

    // When
    Mono<ResponseEntity<EgressApiResponse<String>>> result = handler.noResourceFoundException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
          assertNull(response.getBody());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleNoResourceFoundExceptionForOtherResources() {
    // Given
    NoResourceFoundException exception = mock(NoResourceFoundException.class);
    when(exception.getMessage()).thenReturn("No static resource /api/test");

    // When
    Mono<ResponseEntity<EgressApiResponse<String>>> result = handler.noResourceFoundException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
          assertNull(response.getBody());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleMissingRequestValueException() {
    // Given
    MissingRequestValueException exception = mock(MissingRequestValueException.class);
    when(exception.getMessage()).thenReturn("Missing parameter");
    when(exception.getReason()).thenReturn("Required parameter missing");

    // When
    Mono<ResponseEntity<EgressApiResponse<String>>> result = handler.missingRequestValueException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
          EgressApiResponse<String> body = response.getBody();
          assertNotNull(body);
          assertEquals(400, body.getCode());
          assertEquals("Missing parameter", body.getMessage());
          assertEquals("Required parameter missing", body.getData());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleMethodNotAllowedException() {
    // Given
    MethodNotAllowedException exception = mock(MethodNotAllowedException.class);
    when(exception.getMessage()).thenReturn("Method not allowed");
    when(exception.getReason()).thenReturn("POST not supported");

    // When
    Mono<ResponseEntity<EgressApiResponse<String>>> result = handler.methodNotAllowedException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
          EgressApiResponse<String> body = response.getBody();
          assertNotNull(body);
          assertEquals(400, body.getCode());
          assertEquals("Method not allowed", body.getMessage());
          assertEquals("POST not supported", body.getData());
        })
        .verifyComplete();
  }

  @Test
  void shouldHandleGenericException() {
    // Given
    Exception exception = new RuntimeException("Unexpected error");

    // When
    Mono<ResponseEntity<EgressApiResponse<String>>> result = handler.handleException(exception);

    // Then
    StepVerifier.create(result)
        .assertNext(response -> {
          assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
          EgressApiResponse<String> body = response.getBody();
          assertNotNull(body);
          assertEquals(500, body.getCode());
          assertEquals("Internal Server Error", body.getMessage());
        })
        .verifyComplete();
  }
}
