package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class FluxTraceTest {

    @Test
    void shouldCreateEmptyFluxTrace() {
        // Given
        FluxTrace trace = new FluxTrace();

        // Then
        assertNull(trace.getUid());
        assertNull(trace.getEmployeeId());
        assertNull(trace.getEi());
        assertNull(trace.getEa());
        assertNull(trace.getCaller());
        assertNull(trace.getTraceId());
    }

    @Test
    void shouldSetAndGetProperties() {
        // Given
        FluxTrace trace = new FluxTrace();

        // When
        trace.setUid("obj0509.1000");
        trace.setEmployeeId("1000");
        trace.setEi("test-ei");
        trace.setEa("test-ea");
        trace.setCaller("test-caller");
        trace.setTraceId("test-trace-id");

        // Then
        assertEquals("obj0509.1000", trace.getUid());
        assertEquals("1000", trace.getEmployeeId());
        assertEquals("test-ei", trace.getEi());
        assertEquals("test-ea", trace.getEa());
        assertEquals("test-caller", trace.getCaller());
        assertEquals("test-trace-id", trace.getTraceId());
    }

    @Test
    void shouldImplementEqualsAndHashCode() {
        // Given
        FluxTrace trace1 = new FluxTrace();
        trace1.setUid("obj0509.1000");
        trace1.setEmployeeId("1000");
        trace1.setEi("test-ei");
        trace1.setEa("test-ea");
        trace1.setCaller("test-caller");
        trace1.setTraceId("test-trace-id");

        FluxTrace trace2 = new FluxTrace();
        trace2.setUid("obj0509.1000");
        trace2.setEmployeeId("1000");
        trace2.setEi("test-ei");
        trace2.setEa("test-ea");
        trace2.setCaller("test-caller");
        trace2.setTraceId("test-trace-id");

        // Then
        assertEquals(trace1, trace2);
        assertEquals(trace1.hashCode(), trace2.hashCode());
    }

    @Test
    void shouldImplementToString() {
        // Given
        FluxTrace trace = new FluxTrace();
        trace.setUid("obj0509.1000");
        trace.setEmployeeId("1000");
        trace.setEi("test-ei");
        trace.setEa("test-ea");
        trace.setCaller("test-caller");
        trace.setTraceId("test-trace-id");

        // When
        String toString = trace.toString();

        // Then
        // Verify that toString contains all the fields
        assertTrue(toString.contains("uid=obj0509.1000"));
        assertTrue(toString.contains("employeeId=1000"));
        assertTrue(toString.contains("ei=test-ei"));
        assertTrue(toString.contains("ea=test-ea"));
        assertTrue(toString.contains("caller=test-caller"));
        assertTrue(toString.contains("traceId=test-trace-id"));
    }
}
