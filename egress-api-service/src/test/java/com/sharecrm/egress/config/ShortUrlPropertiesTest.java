package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = ShortUrlProperties.class)
@TestPropertySource(properties = {
    "sharecrm.api.short.url.proxy-egress-url=http://test-proxy-url",
    "sharecrm.api.short.url.double-write-enabled=true",
    "sharecrm.api.short.url.double-read-enabled=true",
    "sharecrm.api.short.url.domain=https://test.cn",
    "sharecrm.api.short.url.default-expire-day=90",
    "sharecrm.api.short.url.default-delete-day=90",
    "sharecrm.api.short.url.code-length=8",
    "sharecrm.api.short.url.max-retry=10",
    "sharecrm.api.short.url.expire-rule.test-url=30"
})
class ShortUrlPropertiesTest {

  @Autowired
  private ShortUrlProperties properties;

  @Test
  void shouldLoadShortUrlProperties() {
    // Then
    assertEquals("http://test-proxy-url", properties.getProxyEgressUrl());
    assertTrue(properties.isDoubleWriteEnabled());
    assertTrue(properties.isDoubleReadEnabled());
    assertEquals("https://test.cn", properties.getDomain());
    assertEquals(90, properties.getDefaultExpireDay());
    assertEquals(90, properties.getDefaultDeleteDay());
    assertEquals(8, properties.getCodeLength());
    assertEquals(10, properties.getMaxRetry());
    assertEquals(30, properties.getExpireRule().get("test-url"));
  }

  @Test
  void shouldHaveDefaultValues() {
    // Create a new instance to test defaults
    ShortUrlProperties defaultProperties = new ShortUrlProperties();
    
    assertNull(defaultProperties.getProxyEgressUrl());
    assertFalse(defaultProperties.isDoubleWriteEnabled());
    assertFalse(defaultProperties.isDoubleReadEnabled());
    assertEquals("https://fs80.cn", defaultProperties.getDomain());
    assertEquals(180, defaultProperties.getDefaultExpireDay());
    assertEquals(180, defaultProperties.getDefaultDeleteDay());
    assertEquals(6, defaultProperties.getCodeLength());
    assertEquals(20, defaultProperties.getMaxRetry());
    assertTrue(defaultProperties.getExpireRule().isEmpty());
  }
}
