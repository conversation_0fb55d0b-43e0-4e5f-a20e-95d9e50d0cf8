package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = ShortUrlProperties.class)
@TestPropertySource(properties = {
    "sharecrm.api.short.url.domain=https://test.cn"
})
class ShortUrlPropertiesTest {

  @Autowired
  private ShortUrlProperties properties;

  @Test
  void shouldLoadShortUrlProperties() {
    // Then - test that properties object is created with default values
    assertNotNull(properties);
    assertEquals("https://fs80.cn", properties.getDomain()); // default value
    assertFalse(properties.isDoubleWriteEnabled());
    assertFalse(properties.isDoubleReadEnabled());
    assertEquals(180, properties.getDefaultExpireDay());
    assertEquals(180, properties.getDefaultDeleteDay());
    assertEquals(6, properties.getCodeLength());
    assertEquals(20, properties.getMaxRetry());
    assertNotNull(properties.getExpireRule());
  }

  @Test
  void shouldHaveDefaultValues() {
    // Create a new instance to test defaults
    ShortUrlProperties defaultProperties = new ShortUrlProperties();

    assertNull(defaultProperties.getProxyEgressUrl());
    assertFalse(defaultProperties.isDoubleWriteEnabled());
    assertFalse(defaultProperties.isDoubleReadEnabled());
    assertEquals("https://fs80.cn", defaultProperties.getDomain());
    assertEquals(180, defaultProperties.getDefaultExpireDay());
    assertEquals(180, defaultProperties.getDefaultDeleteDay());
    assertEquals(6, defaultProperties.getCodeLength());
    assertEquals(20, defaultProperties.getMaxRetry());
    assertTrue(defaultProperties.getExpireRule().isEmpty());
  }
}
