package com.sharecrm.egress.config;

import com.sharecrm.egress.entity.AsrCreateRecTaskRequest;
import com.sharecrm.egress.entity.AsrCreateRecTaskResponse;
import com.sharecrm.egress.entity.AsrQueryRecTaskResponse;
import com.sharecrm.egress.entity.BusinessCardRequest;
import com.sharecrm.egress.entity.BusinessCardResponse;
import com.sharecrm.egress.entity.TencentSpeechResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.AsrProvider;
import com.sharecrm.egress.service.OcrProvider;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@TestConfiguration
public class TestBeanConfig {

  @Bean
  OcrProvider mockOcrProvider() {
    return new OcrProvider() {

      @Override
      public String id() {
        return "mock";
      }

      @Override
      public EgressApiResponse<BusinessCardResponse> businessCard(BusinessCardRequest req) {
        return EgressApiResponse.ok(new BusinessCardResponse());
      }
    };
  }

  @Bean
  AsrProvider mockAsrProvider() {
    return new AsrProvider() {
      @Override
      public EgressApiResponse<TencentSpeechResponse> speechRecognition(byte[] dataBytes) {
        return EgressApiResponse.ok(new TencentSpeechResponse());
      }

      @Override
      public EgressApiResponse<AsrCreateRecTaskResponse> createRecTask(AsrCreateRecTaskRequest request) {
        return EgressApiResponse.badRequest();
      }

      @Override
      public EgressApiResponse<AsrQueryRecTaskResponse> queryRecTask(String taskId) {
        return EgressApiResponse.badRequest();
      }
    };
  }
}
