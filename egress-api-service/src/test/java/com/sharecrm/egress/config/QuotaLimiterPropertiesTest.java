package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = QuotaLimiterProperties.class)
@TestPropertySource(properties = {
    "sharecrm.api.quota.enabled=true"
})
class QuotaLimiterPropertiesTest {

  @Autowired
  private QuotaLimiterProperties properties;

  @Test
  void shouldLoadQuotaLimiterProperties() {
    // Then - test that properties object is created and collections are initialized
    assertNotNull(properties);
    assertNotNull(properties.getPathCatalogs());
    assertNotNull(properties.getGlobalQuotas());
    assertNotNull(properties.getTenantQuotas());
    assertNotNull(properties.getIgnoreEis());
  }

  @Test
  void shouldHaveDefaultValues() {
    // Create a new instance to test defaults
    QuotaLimiterProperties defaultProperties = new QuotaLimiterProperties();

    assertFalse(defaultProperties.isEnabled());
    assertTrue(defaultProperties.getPathCatalogs().isEmpty());
    assertTrue(defaultProperties.getGlobalQuotas().isEmpty());
    assertTrue(defaultProperties.getTenantQuotas().isEmpty());
    assertTrue(defaultProperties.getIgnoreEis().isEmpty());
  }

  @Test
  void shouldTestCatalogPair() {
    // Test CatalogPair class
    QuotaLimiterProperties.CatalogPair catalogPair = new QuotaLimiterProperties.CatalogPair();
    catalogPair.setMethod("GET");
    catalogPair.setPath("/test/**");

    assertEquals("GET", catalogPair.getMethod());
    assertEquals("/test/**", catalogPair.getPath());
  }
}
