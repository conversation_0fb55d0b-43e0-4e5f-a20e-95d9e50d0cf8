package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = QuotaLimiterProperties.class)
@TestPropertySource(properties = {
    "sharecrm.api.quota.enabled=true",
    "sharecrm.api.quota.path-catalogs.sms.method=POST",
    "sharecrm.api.quota.path-catalogs.sms.path=/api/sms/**",
    "sharecrm.api.quota.global-quotas.sms=1000",
    "sharecrm.api.quota.tenant-quotas.12345.sms=2000",
    "sharecrm.api.quota.ignore-eis=12345,67890"
})
class QuotaLimiterPropertiesTest {

  @Autowired
  private QuotaLimiterProperties properties;

  @Test
  void shouldLoadQuotaLimiterProperties() {
    // Then
    assertTrue(properties.isEnabled());
    
    // Test path catalogs
    Map<String, QuotaLimiterProperties.CatalogPair> pathCatalogs = properties.getPathCatalogs();
    assertNotNull(pathCatalogs.get("sms"));
    assertEquals("POST", pathCatalogs.get("sms").getMethod());
    assertEquals("/api/sms/**", pathCatalogs.get("sms").getPath());
    
    // Test global quotas
    Map<String, Long> globalQuotas = properties.getGlobalQuotas();
    assertEquals(1000L, globalQuotas.get("sms"));
    
    // Test tenant quotas
    Map<Long, Map<String, Long>> tenantQuotas = properties.getTenantQuotas();
    assertNotNull(tenantQuotas.get(12345L));
    assertEquals(2000L, tenantQuotas.get(12345L).get("sms"));
    
    // Test ignore EIs
    Set<Long> ignoreEis = properties.getIgnoreEis();
    assertTrue(ignoreEis.contains(12345L));
    assertTrue(ignoreEis.contains(67890L));
  }

  @Test
  void shouldHaveDefaultValues() {
    // Create a new instance to test defaults
    QuotaLimiterProperties defaultProperties = new QuotaLimiterProperties();
    
    assertFalse(defaultProperties.isEnabled());
    assertTrue(defaultProperties.getPathCatalogs().isEmpty());
    assertTrue(defaultProperties.getGlobalQuotas().isEmpty());
    assertTrue(defaultProperties.getTenantQuotas().isEmpty());
    assertTrue(defaultProperties.getIgnoreEis().isEmpty());
  }

  @Test
  void shouldTestCatalogPair() {
    // Test CatalogPair class
    QuotaLimiterProperties.CatalogPair catalogPair = new QuotaLimiterProperties.CatalogPair();
    catalogPair.setMethod("GET");
    catalogPair.setPath("/test/**");
    
    assertEquals("GET", catalogPair.getMethod());
    assertEquals("/test/**", catalogPair.getPath());
  }
}
