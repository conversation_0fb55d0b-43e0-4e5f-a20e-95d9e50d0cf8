package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.http.server.reactive.MockServerHttpResponse;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = TraceWebFilter.class)
class TraceWebFilterTest {

  @Autowired
  private TraceWebFilter filter;

  @MockBean
  private WebFilterChain chain;

  @Test
  void shouldProcessTraceIdFromHeader() {
    // Given
    MockServerHttpRequest request = MockServerHttpRequest.get("/test")
        .header("X-fs-Trace-Id", "test-trace-id")
        .build();
    MockServerHttpResponse response = new MockServerHttpResponse();
    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    when(chain.filter(any())).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
  }

  @Test
  void shouldProcessTraceIdFromQueryParam() {
    // Given
    MockServerHttpRequest request = MockServerHttpRequest.get("/test")
        .queryParam("traceId", "test-trace-id")
        .build();
    MockServerHttpResponse response = new MockServerHttpResponse();
    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    when(chain.filter(any())).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
  }

  @Test
  void shouldGenerateTraceIdWhenNotProvided() {
    // Given
    MockServerHttpRequest request = MockServerHttpRequest.get("/test")
        .build();
    MockServerHttpResponse response = new MockServerHttpResponse();
    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    when(chain.filter(any())).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
  }

  @Test
  void shouldProcessTraceColorFromHeader() {
    // Given
    MockServerHttpRequest request = MockServerHttpRequest.get("/test")
        .header("X-fs-Trace-Color", "blue")
        .build();
    MockServerHttpResponse response = new MockServerHttpResponse();
    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    when(chain.filter(any())).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
  }

  @Test
  void shouldProcessTraceColorFromQueryParam() {
    // Given
    MockServerHttpRequest request = MockServerHttpRequest.get("/test")
        .queryParam("_color", "green")
        .build();
    MockServerHttpResponse response = new MockServerHttpResponse();
    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    when(chain.filter(any())).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
  }

  @Test
  void shouldProcessUserIdFromHeader() {
    // Given
    MockServerHttpRequest request = MockServerHttpRequest.get("/test")
        .header("X-fs-User-Info", "E.test-user-id")
        .build();
    MockServerHttpResponse response = new MockServerHttpResponse();
    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    when(chain.filter(any())).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
  }

  @Test
  void shouldProcessOtherHeaders() {
    // Given
    MockServerHttpRequest request = MockServerHttpRequest.get("/test")
        .header("X-fs-Enterprise-Id", "test-ei")
        .header("X-fs-Enterprise-Account", "test-ea")
        .header("x-peer-name", "test-peer")
        .build();
    MockServerHttpResponse response = new MockServerHttpResponse();
    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    when(chain.filter(any())).thenReturn(Mono.empty());

    // When
    Mono<Void> result = filter.filter(exchange, chain);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
  }
}
