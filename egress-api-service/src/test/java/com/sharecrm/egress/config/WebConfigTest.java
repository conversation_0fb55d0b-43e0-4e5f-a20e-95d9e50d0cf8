package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.codec.CodecProperties;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.util.unit.DataSize;

import static org.mockito.Mockito.*;

class WebConfigTest {

  @Test
  void shouldConfigureMaxInMemorySize() {
    // Given
    CodecProperties codecProperties = mock(CodecProperties.class);
    when(codecProperties.getMaxInMemorySize()).thenReturn(DataSize.ofMegabytes(10));
    
    ServerCodecConfigurer configurer = mock(ServerCodecConfigurer.class);
    ServerCodecConfigurer.ServerDefaultCodecs defaultCodecs = mock(ServerCodecConfigurer.ServerDefaultCodecs.class);
    when(configurer.defaultCodecs()).thenReturn(defaultCodecs);
    
    WebConfig webConfig = new WebConfig(codecProperties);
    
    // When
    webConfig.configureHttpMessageCodecs(configurer);
    
    // Then
    verify(configurer).defaultCodecs();
    verify(defaultCodecs).maxInMemorySize(10485760); // 10MB in bytes
  }
}
