package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = OcrProperties.class)
@EnableConfigurationProperties(OcrProperties.class)
@TestPropertySource(properties = {
    "sharecrm.api.ocr.parallelism=64",
    "sharecrm.api.ocr.tencent.enabled=true",
    "sharecrm.api.ocr.tencent.secret-id=test-secret-id",
    "sharecrm.api.ocr.tencent.secret-key=test-secret-key",
    "sharecrm.api.ocr.tencent.region=ap-beijing",
    "sharecrm.api.ocr.huawei.enabled=true",
    "sharecrm.api.ocr.huawei.secret-id=test-ak",
    "sharecrm.api.ocr.huawei.secret-key=test-sk",
    "sharecrm.api.ocr.huawei.region=cn-north-4",
    "sharecrm.api.ocr.huawei.project-id=test-project-id"
})
class OcrPropertiesTest {

  @Autowired
  private OcrProperties ocrProperties;

  @Test
  void shouldBindBasicProperties() {
    assertEquals(64, ocrProperties.getParallelism());
  }

  @Test
  void shouldBindTencentProperties() {
    OcrProperties.TencentConfig tencentConfig = ocrProperties.getTencent();
    assertNotNull(tencentConfig);
    assertTrue(tencentConfig.isEnabled());
    assertEquals("test-secret-id", tencentConfig.getSecretId());
    assertEquals("test-secret-key", tencentConfig.getSecretKey());
    assertEquals("ap-beijing", tencentConfig.getRegion());
  }

  @Test
  void shouldBindHuaweiProperties() {
    OcrProperties.HuaweiConfig huaweiConfig = ocrProperties.getHuawei();
    assertNotNull(huaweiConfig);
    assertTrue(huaweiConfig.isEnabled());
    assertEquals("test-ak", huaweiConfig.getSecretId());
    assertEquals("test-sk", huaweiConfig.getSecretKey());
    assertEquals("cn-north-4", huaweiConfig.getRegion());
    assertEquals("test-project-id", huaweiConfig.getProjectId());
  }

  @Test
  void shouldHaveDefaultValues() {
    // Verify default values
    assertEquals(100, ocrProperties.getTencent().getOrder());
    assertEquals(200, ocrProperties.getHuawei().getOrder());
    assertNotNull(ocrProperties.getHuawei().getEndpoints());
    assertFalse(ocrProperties.getHuawei().getEndpoints().isEmpty());
  }
}