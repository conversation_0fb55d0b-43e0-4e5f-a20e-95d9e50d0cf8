package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = MapProperties.class)
@TestPropertySource(properties = {
    "sharecrm.api.map.cache-timeout=PT10M",
    "sharecrm.api.map.long-cache-timeout=PT12H",
    "sharecrm.api.map.empty-timeout=PT5S",
    "sharecrm.api.map.cache-maximum-size=5000",
    "sharecrm.api.map.max-cache-radius=50"
})
class MapPropertiesTest {

  @Autowired
  private MapProperties properties;

  @Test
  void shouldLoadMapProperties() {
    // Then
    assertEquals(Duration.ofMinutes(10), properties.getCacheTimeout());
    assertEquals(Duration.ofHours(12), properties.getLongCacheTimeout());
    assertEquals(Duration.ofSeconds(5), properties.getEmptyTimeout());
    assertEquals(5000, properties.getCacheMaximumSize());
    assertEquals(50, properties.getMaxCacheRadius());
  }

  @Test
  void shouldHaveDefaultValues() {
    // Create a new instance to test defaults
    MapProperties defaultProperties = new MapProperties();

    assertEquals(Duration.ofMinutes(15), defaultProperties.getCacheTimeout());
    assertEquals(Duration.ofHours(24), defaultProperties.getLongCacheTimeout());
    assertEquals(Duration.ofSeconds(10), defaultProperties.getEmptyTimeout());
    assertEquals(10000, defaultProperties.getCacheMaximumSize());
    assertEquals(100, defaultProperties.getMaxCacheRadius());

    // Test that maps are initialized as empty
    assertTrue(defaultProperties.getAmap().isEmpty());
    assertTrue(defaultProperties.getBaidu().isEmpty());
    assertTrue(defaultProperties.getTencent().isEmpty());
    assertTrue(defaultProperties.getGoogle().isEmpty());
    assertTrue(defaultProperties.getHuawei().isEmpty());
  }

  @Test
  void shouldHaveMaxmindAndElasticsearchConfigs() {
    // Test that Maxmind and Elasticsearch configs are initialized
    assertNotNull(properties.getMaxmind());
    assertNotNull(properties.getElasticsearch());

    // Test default values
    assertEquals("maxmind", properties.getMaxmind().getId());
    assertTrue(properties.getMaxmind().isEnabled());
  }
}
