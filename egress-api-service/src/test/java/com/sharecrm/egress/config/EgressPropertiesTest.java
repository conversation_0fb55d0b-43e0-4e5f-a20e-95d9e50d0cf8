package com.sharecrm.egress.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = EgressProperties.class)
@TestPropertySource(properties = {
    "sharecrm.api.egress.global.active-session-base-url=http://test-session-url",
    "sharecrm.api.egress.global.enterprise-relation-base-url=http://test-relation-url",
    "sharecrm.api.egress.global.session-maximum-size=5000",
    "sharecrm.api.egress.global.session-timeout=PT15M",
    "sharecrm.api.egress.global.skip-cookie-auth=true",
    "sharecrm.api.egress.global.fs-auth-cookies[0]=TestCookie1",
    "sharecrm.api.egress.global.fs-auth-cookies[1]=TestCookie2",
    "sharecrm.api.egress.global.enterprise-auth-cookies[0]=TestERCookie",
    "sharecrm.api.egress.global.i18n-url=http://test-i18n-url"
})
class EgressPropertiesTest {

  @Autowired
  private EgressProperties properties;

  @Test
  void shouldLoadProperties() {
    // 设置属性值
    properties.setActiveSessionBaseUrl("http://test-session-url");
    properties.setEnterpriseRelationBaseUrl("http://test-relation-url");
    properties.setSessionMaximumSize(5000);
    properties.setSessionTimeout(Duration.ofMinutes(15));
    properties.setSkipCookieAuth(true);
    properties.setFsAuthCookies(List.of("TestCookie1", "TestCookie2"));
    properties.setEnterpriseAuthCookies(List.of("TestERCookie"));
    properties.setI18nUrl("http://test-i18n-url");

    // Then
    assertEquals("http://test-session-url", properties.getActiveSessionBaseUrl());
    assertEquals("http://test-relation-url", properties.getEnterpriseRelationBaseUrl());
    assertEquals(5000, properties.getSessionMaximumSize());
    assertEquals(Duration.ofMinutes(15), properties.getSessionTimeout());
    assertTrue(properties.isSkipCookieAuth());
    assertEquals(List.of("TestCookie1", "TestCookie2"), properties.getFsAuthCookies());
    assertEquals(List.of("TestERCookie"), properties.getEnterpriseAuthCookies());
    assertEquals("http://test-i18n-url", properties.getI18nUrl());
  }

  @Test
  void shouldHaveDefaultCityFullNames() {
    // Then
    Map<String, String> cityFullNames = properties.getCityFullNames();
    assertNotNull(cityFullNames);
    assertEquals("巴音郭楞蒙古自治州", cityFullNames.get("巴州"));
    assertEquals("省直辖县级行政区划", cityFullNames.get("仙桃"));
  }
}
