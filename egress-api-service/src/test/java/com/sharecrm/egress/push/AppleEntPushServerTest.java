package com.sharecrm.egress.push;

import com.sharecrm.egress.config.NotifyProperties;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date: 2024/12/10
 * @since 1.0.0
 */
public class AppleEntPushServerTest {

  private final AppleEntPushServer server = new AppleEntPushServer(config());


  @NotNull
  private NotifyProperties config() {
    NotifyProperties properties = new NotifyProperties();
    NotifyProperties.ApplePushConfig config = new NotifyProperties.ApplePushConfig();
    config.setTeamId("test");
    config.setKeyId("test");
    config.setTopic("test");
    properties.setAppleEnt(config);
    return properties;
  }


  @Test
  void pushServerSources() {
    server.pushServerSources();
  }

  @Test
  void adapter() {
    server.adapter();
  }

}