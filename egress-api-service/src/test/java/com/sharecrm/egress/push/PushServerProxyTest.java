package com.sharecrm.egress.push;

import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.ThirdPushException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.ObjectProvider;

import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Collections;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PushServerProxyTest {

  @Mock
  private ObjectProvider<PushServer> serverObjectProvider;

  @Mock
  private PushServer pushServer1;

  @Mock
  private PushServer pushServer2;

  private PushServerProxy pushServerProxy;

  @BeforeEach
  void setUp() {
    pushServerProxy = new PushServerProxy(serverObjectProvider);
  }

  // 创建 PushMessageDTO 的辅助方法
  private PushMessageDTO createPushMessageDTO() throws Exception {
    Constructor<PushMessageDTO> constructor = PushMessageDTO.class.getDeclaredConstructor();
    constructor.setAccessible(true);
    return constructor.newInstance();
  }

  @Test
  void testPushWithValidServerSource() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource("server1");

    // 配置 mock 行为
    when(pushServer1.pushServerSources()).thenReturn(Arrays.asList("server1", "server3"));
    when(pushServer2.pushServerSources()).thenReturn(Collections.singletonList("server2"));
    when(serverObjectProvider.stream()).thenReturn(Stream.of(pushServer1, pushServer2));

    // 执行测试
    pushServerProxy.push(messageDTO);

    // 验证结果
    verify(pushServer1).push(messageDTO);
    verify(pushServer2, never()).push(any());
  }

  @Test
  void testPushWithInvalidServerSource() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource("invalidServer");

    // 配置 mock 行为
    when(pushServer1.pushServerSources()).thenReturn(Arrays.asList("server1", "server3"));
    when(pushServer2.pushServerSources()).thenReturn(Collections.singletonList("server2"));
    when(serverObjectProvider.stream()).thenReturn(Stream.of(pushServer1, pushServer2));

    // 执行测试并验证结果
    assertThrows(ThirdPushException.class, () -> pushServerProxy.push(messageDTO));

    // 验证没有调用任何 push 方法
    verify(pushServer1, never()).push(any());
    verify(pushServer2, never()).push(any());
  }

  @Test
  void testPushWithNullServerSource() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource(null);

    // 执行测试并验证结果
    assertThrows(ThirdPushException.class, () -> pushServerProxy.push(messageDTO));

    // 验证没有调用任何 push 方法
    verify(pushServer1, never()).push(any());
    verify(pushServer2, never()).push(any());
  }

  @Test
  void testPushWithEmptyServerSource() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource("");

    // 执行测试并验证结果
    assertThrows(ThirdPushException.class, () -> pushServerProxy.push(messageDTO));

    // 验证没有调用任何 push 方法
    verify(pushServer1, never()).push(any());
    verify(pushServer2, never()).push(any());
  }

  @Test
  void testPushWithWhitespaceServerSource() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource("  ");

    // 执行测试并验证结果
    assertThrows(ThirdPushException.class, () -> pushServerProxy.push(messageDTO));

    // 验证没有调用任何 push 方法
    verify(pushServer1, never()).push(any());
    verify(pushServer2, never()).push(any());
  }

  @Test
  void testPushWithTrimmedServerSource() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource("  server1  ");

    // 配置 mock 行为
    when(pushServer1.pushServerSources()).thenReturn(Arrays.asList("server1", "server3"));
    when(pushServer2.pushServerSources()).thenReturn(Collections.singletonList("server2"));
    when(serverObjectProvider.stream()).thenReturn(Stream.of(pushServer1, pushServer2));

    // 执行测试
    pushServerProxy.push(messageDTO);

    // 验证结果
    verify(pushServer1).push(messageDTO);
    verify(pushServer2, never()).push(any());
  }

  @Test
  void testPushWithMultipleMatchingServers() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource("common");

    // 配置 mock 行为 - 两个服务器都支持相同的源
    when(pushServer1.pushServerSources()).thenReturn(Arrays.asList("server1", "common"));
    when(pushServer2.pushServerSources()).thenReturn(Arrays.asList("server2", "common"));
    when(serverObjectProvider.stream()).thenReturn(Stream.of(pushServer1, pushServer2));

    // 执行测试
    pushServerProxy.push(messageDTO);

    // 验证结果 - 应该只使用第一个匹配的服务器
    verify(pushServer1).push(messageDTO);
    verify(pushServer2, never()).push(any());
  }

  @Test
  void testPushWithNoAvailableServers() throws Exception {
    // 准备测试数据
    PushMessageDTO messageDTO = createPushMessageDTO();
    messageDTO.setPushServerSource("server1");

    // 配置 mock 行为 - 没有可用的服务器
    when(serverObjectProvider.stream()).thenReturn(Stream.empty());

    // 执行测试并验证结果
    assertThrows(ThirdPushException.class, () -> pushServerProxy.push(messageDTO));
  }
}
