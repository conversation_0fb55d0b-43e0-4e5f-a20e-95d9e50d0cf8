package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

class FcmPushAdapterTest {

  private final FcmPushAdapter adapter = new FcmPushAdapter(mock(OkHttpSupport.class), new NotifyProperties(), mock(StringRedisTemplate.class));

  @Test
  void pushToServer() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);

    assertNotNull(adapter.buildMessage(dto));
    assertNotNull(adapter.buildAndroidPayload(dto));
    assertNotNull(adapter.buildNotificationMessage(dto));

  }
}