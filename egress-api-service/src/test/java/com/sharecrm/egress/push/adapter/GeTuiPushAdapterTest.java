package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.gexin.rp.sdk.http.IGtPush;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class GeTuiPushAdapterTest {

  private final NotifyProperties properties = mock(NotifyProperties.class);
  private final IGtPush sender = mock(IGtPush.class);
  private final GeTuiPushAdapter adapter = new GeTuiPushAdapter(properties, () -> sender);

  @BeforeEach
  void setup() {
    when(properties.getGetui()).thenReturn(new NotifyProperties.GeTuiPushConfig());
  }

  @Test
  void pushToServer() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setSummary("has summary");
    message.setNotification(notification);
    assertDoesNotThrow(() -> adapter.pushToServer(PushMessageDTO.Convert.from(message)));
  }
}