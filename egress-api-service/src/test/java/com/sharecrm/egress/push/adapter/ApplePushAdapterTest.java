package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class ApplePushAdapterTest {


  private final ApplePushAdapter adapter = new ApplePushAdapter(mockConfig());

  private NotifyProperties.ApplePushConfig mockConfig() {
    NotifyProperties.ApplePushConfig config = new NotifyProperties.ApplePushConfig();
    config.setTeamId("testTeamId");
    config.setKeyId("testKeyId");
    config.setTopic("testTopic");
    return config;
  }

  @Test
  void init() {
    assertNotNull(adapter);
  }

  @Test
  void apnsPushNotificationRequest() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    PNSToken pnsToken = new PNSToken();
    pnsToken.setToken("test");
    message.setPnsToken(pnsToken);
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setOriginalMessage("test message");
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    assertNotNull(adapter.apnsPushNotificationRequest(dto));
  }

}