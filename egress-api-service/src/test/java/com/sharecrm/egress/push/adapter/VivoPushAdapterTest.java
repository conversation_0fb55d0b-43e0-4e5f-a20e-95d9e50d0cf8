package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

class VivoPushAdapterTest {
  private final OkHttpSupport client = mock(OkHttpSupport.class);
  private final VivoPushAdapter adapter = new VivoPushAdapter(client, new NotifyProperties());

  @Test
  void messageToPayload() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);

    VivoPushAdapter.Payload payload = adapter.messageToPayload(dto);
    assertNotNull(payload);
    assertNotNull(payload.getContent());
    assertNotNull(payload.getSkipContent());
    //当前只支持这一种类型，如果支持类型了，一定要根据文档适配
    assertEquals("IM", payload.getCategory());
  }
}