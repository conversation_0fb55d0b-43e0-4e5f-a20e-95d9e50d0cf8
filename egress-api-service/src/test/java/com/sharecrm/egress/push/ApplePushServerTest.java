package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * <AUTHOR>
 * @date: 2024/12/10
 * @since 1.0.0
 */
public class ApplePushServerTest {

  private final ApplePushServer server = new AppleProdPushServer(config());

  
  @NotNull
  private NotifyProperties config(){
    NotifyProperties notifyProperties = new NotifyProperties();
    NotifyProperties.ApplePushConfig applePushConfig = new NotifyProperties.ApplePushConfig();
    applePushConfig.setKeyId("test KeyId");
    applePushConfig.setTopic("test Topic");
    applePushConfig.setTeamId("test TeamId");
    notifyProperties.setAppleProd(applePushConfig);
    return notifyProperties;
  }

  @Test
  void send() {
    ThirdPartPushCommonMessage thirdMessage = new ThirdPartPushCommonMessage();
    thirdMessage.setNotification(new ThirdPartNotification());
    thirdMessage.setPnsToken(new PNSToken());
    PushMessageDTO message = PushMessageDTO.Convert.from(thirdMessage);
    assertThrows(Throwable.class, () -> server.send(message));
  }


}