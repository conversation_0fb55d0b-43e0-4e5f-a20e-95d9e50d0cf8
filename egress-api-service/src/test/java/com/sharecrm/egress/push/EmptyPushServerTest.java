package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.entity.PushMessageDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

class EmptyPushServerTest {

  private final EmptyPushServer server = new EmptyPushServer();


  @NotNull
  private PushMessageDTO config() {
    ThirdPartPushCommonMessage thirdPartPushCommonMessage = new ThirdPartPushCommonMessage();
    thirdPartPushCommonMessage.setPnsToken(new PNSToken());
    thirdPartPushCommonMessage.setNotification(new ThirdPartNotification());
    return PushMessageDTO.Convert.from(thirdPartPushCommonMessage);
  }

  @Test
  void send() {
    assertDoesNotThrow(() -> server.send(config()));
  }

  @Test
  void push() {
    assertDoesNotThrow(() -> server.push(config()));
  }

  @Test
  void pushServerSources() {
    assertDoesNotThrow(server::pushServerSources);
  }

}