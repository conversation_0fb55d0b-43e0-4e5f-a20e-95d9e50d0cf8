package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.Platform;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.dao.NotifyDao;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.entity.PushRecord;
import com.sharecrm.egress.exception.ThirdPushClientException;
import com.sharecrm.egress.exception.ThirdPushException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;

import java.lang.reflect.Field;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PushServerTest {

  private static class TestPushServer extends PushServer {
    private boolean sendCalled = false;
    private Throwable throwOnSend = null;

    @Override
    public List<String> pushServerSources() {
      return List.of("test-source");
    }

        @Override
        protected void send(PushMessageDTO pushMessageDTO) throws Throwable {
          sendCalled = true;
          if (throwOnSend != null) {
            throw throwOnSend;
          }
        }

        public void setThrowOnSend(Throwable throwOnSend) {
          this.throwOnSend = throwOnSend;
        }

        public boolean isSendCalled() {
          return sendCalled;
        }
      }

      @Mock
      private NotifyDao notifyDao;

      @Mock
      private PNSTokenService tokenService;

      @Mock
      private TaskExecutor taskExecutor;

      private TestPushServer pushServer;

      @BeforeEach
      void setUp() throws Exception {
        pushServer = new TestPushServer();

        // Use reflection to set private fields
        setPrivateField(pushServer, "notifyDao", notifyDao);
        setPrivateField(pushServer, "tokenService", tokenService);
        setPrivateField(pushServer, "executor", taskExecutor);
      }

      private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = PushServer.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
      }

      @Test
      void shouldCallSendWhenPushingValidMessage() {
        // Given
        PushMessageDTO message = createValidMessage();

        // When
        pushServer.push(message);

        // Then
        assertTrue(pushServer.isSendCalled());
        // ID should be set
        assertNotNull(message.getId());
      }

      @Test
      void shouldValidateMessageTimeExpiration() {
        // Given - message that is older than expiration time
        PushMessageDTO message = createValidMessage();
        message.setProduceTime(System.currentTimeMillis() - (31 * 60 * 1000)); // 31 minutes old

        // When/Then
        Exception exception = assertThrows(ThirdPushException.class, () -> pushServer.messageValidate(message));

        // Verify exception and that send was not called
        assertEquals("TIME_EXPIRED", exception.getMessage());
        assertFalse(pushServer.isSendCalled());
      }

    @Test
    void shouldSaveRecordWhenExceptionOccurs() {
      // Given
      PushMessageDTO message = createValidMessage();
      pushServer.setThrowOnSend(new ThirdPushException("Test error"));

      // When
      pushServer.push(message);

      // Then
      ArgumentCaptor<PushRecord> recordCaptor = ArgumentCaptor.forClass(PushRecord.class);
      verify(notifyDao).save(recordCaptor.capture());

      PushRecord capturedRecord = recordCaptor.getValue();
      assertEquals(message.getId(), capturedRecord.getObjectId());
      assertEquals(message.getToken(), capturedRecord.getToken());
      assertEquals("2", capturedRecord.getStatus());
      assertEquals("Test error", capturedRecord.getResultInfo());
    }

    @Test
    void shouldNotSaveRecordWhenClientExceptionOccurs() {
      // Given
      PushMessageDTO message = createValidMessage();
      pushServer.setThrowOnSend(new ThirdPushClientException("Client error"));

      // When
      pushServer.push(message);

      // Then - no record should be saved for client exceptions
      verify(notifyDao, never()).save(any(PushRecord.class));
    }

    private PushMessageDTO createValidMessage() {
      // Create PushMessageDTO using the static factory method Convert.from()
      ThirdPartPushCommonMessage thirdMessage = new ThirdPartPushCommonMessage();

      // Set up the PNSToken
      PNSToken token = new PNSToken();
      token.setToken("test-token");
      token.setEnterpriseAccount("test-ea");
      token.setOsVersion("14.5");
      token.setClientVersion("1.0.0");
      thirdMessage.setPnsToken(token);

      // Set up the notification
      ThirdPartNotification notification = new ThirdPartNotification();
      notification.setEmployeeId(123);
      notification.setTitle("Test Title");
      notification.setSummary("Test Summary");
      thirdMessage.setNotification(notification);

      // Set time
      thirdMessage.setCreateTime(System.currentTimeMillis());

      // Create the PushMessageDTO
      PushMessageDTO dto = PushMessageDTO.Convert.from(thirdMessage);

      // Additional setup not covered by the factory method
      dto.setId("test-id");
      dto.setPushContent("Test Content");
      dto.setPushServerSource("test-source");
      dto.setPushedTime(System.currentTimeMillis() + 100);

      return dto;
    }
}