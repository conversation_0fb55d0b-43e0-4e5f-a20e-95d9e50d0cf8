package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.utils.JwtTestConfig;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class HarmonyPushAdapterTest {

  private final OkHttpSupport client = mock(OkHttpSupport.class);
  private final HarmonyPushAdapter adapter = new HarmonyPushAdapter(mockConf(), client);

  private NotifyProperties.HarmonyPushConfig mockConf() {
    NotifyProperties.HarmonyPushConfig config = new NotifyProperties.HarmonyPushConfig();
    config.setJwt(JwtTestConfig.properties());
    return config;
  }

  @Test
  void pushToServer() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    assertThrows(MessageInvalidException.class, () -> adapter.pushToServer(PushMessageDTO.Convert.from(message)));
  }


  @Test
  void sendByNotification() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setSummary("this summary");
    notification.setData(Map.of("bty", "CRMTODO"));

    PNSToken token = new PNSToken();
    token.setToken("test");
    message.setPnsToken(token);

    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    HuaWeiPushResult rs = new HuaWeiPushResult();
    rs.setCode("80000000");
    when(client.parseObject(any(), any())).thenReturn(rs);
    HuaWeiPushResult result = adapter.pushToServer(dto);
    assertTrue(result.isSuccess());
  }

}