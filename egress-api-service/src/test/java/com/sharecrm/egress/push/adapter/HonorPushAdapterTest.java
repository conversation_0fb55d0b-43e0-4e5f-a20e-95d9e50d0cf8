package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.sharecrm.egress.push.adapter.honor.HonorPushResult;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class HonorPushAdapterTest {

  private final OkHttpSupport client = mock(OkHttpSupport.class);
  private final HonorPushAdapter adapter = new HonorPushAdapter(mockConf(), client);

  private NotifyProperties.HonorPushConfig mockConf() {
    NotifyProperties.HonorPushConfig config = new NotifyProperties.HonorPushConfig();
    config.setClientId("test");
    config.setClientSecret("test");
    config.setTokenTimeout(Duration.ofSeconds(10));
    return config;
  }

  @Test
  void pushToServer() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    assertThrows(MessageInvalidException.class, () -> adapter.pushToServer(PushMessageDTO.Convert.from(message)));
  }

  @Test
  void sendByNotification() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setSummary("this summary");

    PNSToken token = new PNSToken();
    token.setToken("test");
    message.setPnsToken(token);

    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    HonorPushResult rs = new HonorPushResult();
    rs.setCode(200);
    when(client.parseObject(any(), any())).thenReturn(rs);
    HonorPushResult result = adapter.pushToServer(dto);
    assertEquals(200, result.getCode());
  }


}