package com.sharecrm.egress.push;


import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.push.adapter.FcmPushAdapter;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

/**
 * <AUTHOR>
 * @date: 2024/12/10
 * @since 1.0.0
 */
public class FcmPushServerTest {


  private final FcmPushServer server = new FcmPushServer(mock(FcmPushAdapter.class));

  @NotNull
  private PushMessageDTO config() {
    ThirdPartPushCommonMessage thirdPartPushCommonMessage = new ThirdPartPushCommonMessage();
    thirdPartPushCommonMessage.setPnsToken(new PNSToken());
    thirdPartPushCommonMessage.setNotification(new ThirdPartNotification());
    return PushMessageDTO.Convert.from(thirdPartPushCommonMessage);
  }

  @Test
  void pushServerSources() {
    assertDoesNotThrow(() -> server.pushServerSources());
  }

  @Test
  void send() {
    assertThrows(Throwable.class, () -> server.send(config()));
  }
}