package com.sharecrm.egress.push;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.mock;

class PNSTokenServiceTest {

  private final PNSTokenService service = new PNSTokenService(mockConfig(), mock(OkHttpSupport.class));

  @NotNull
  private static NotifyProperties mockConfig() {
    NotifyProperties properties = new NotifyProperties();
    properties.setTokenUrl("http://test-mock");
    return properties;
  }

  @Test
  void removeAppleTokenFromOMS() {
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS("ea", "token", true));
  }

  @Test
  void removeAndroidTokenFromOMS() {
    assertDoesNotThrow(() -> service.removeAndroidTokenFromOMS("ea", "token"));
  }
}