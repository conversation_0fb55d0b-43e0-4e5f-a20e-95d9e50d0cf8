package com.sharecrm.egress.push;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.mock;

/**
 * <AUTHOR>
 * @date: 2024/12/10
 * @since 1.0.0
 */
public class HuaWeiEntPushServerTest {


  private final HuaWeiEntPushServer server = new HuaWeiEntPushServer(properties(), mock(OkHttpSupport.class));


  @NotNull
  private NotifyProperties properties() {
    return new NotifyProperties();
  }

  @Test
  void adapter() {
    assertDoesNotThrow(server::adapter);
  }

  @Test
  void pushServerSources() {
    assertDoesNotThrow(server::pushServerSources);
  }
}