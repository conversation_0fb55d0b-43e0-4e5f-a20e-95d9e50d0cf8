package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.config.NotifyProperties;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;

class NotifyPushConsumerListenerTest {

  NotifyPushConsumerListener listener = new NotifyPushConsumerListener(mock(PushServerProxy.class), mock(NotifyProperties.class));

  @Test
  void consumeMessage() {
    // 加个用例提示好好处理异常，不要随便抛出去
    MessageExt msg = new MessageExt();
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    message.setNotification(new ThirdPartNotification());
    msg.setBody(message.toProto());
    assertDoesNotThrow(() -> listener.consumeMessage(msg));
  }

  @Test
  void fallback() {
    // 加个用例提示谨慎改状态
    assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, listener.fallback(new MessageExt(), null, null));
  }
}