package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

class AppleProdPushServerTest {

  AppleProdPushServer server = new AppleProdPushServer(config());

  @NotNull
  private NotifyProperties config() {
    NotifyProperties properties = new NotifyProperties();
    NotifyProperties.ApplePushConfig config = new NotifyProperties.ApplePushConfig();
    config.setTeamId("test");
    config.setKeyId("test");
    config.setTopic("test");
    properties.setAppleProd(config);
    return properties;
  }

  @Test
  @SneakyThrows
  void adapter() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    PNSToken token = new PNSToken();
    token.setToken("test");
    message.setPnsToken(token);
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setOriginalMessage("has original message");
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    assertDoesNotThrow(() -> server.adapter().pushToServer(dto));
  }
}