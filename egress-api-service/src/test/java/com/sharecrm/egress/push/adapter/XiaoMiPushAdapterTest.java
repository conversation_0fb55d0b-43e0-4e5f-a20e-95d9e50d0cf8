package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import com.xiaomi.xmpush.server.Message;
import com.xiaomi.xmpush.server.Result;
import com.xiaomi.xmpush.server.Sender;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

class XiaoMiPushAdapterTest {

  private final XiaoMiPushAdapter adapter = new XiaoMiPushAdapter(mockSender(), mockConfig());

  private NotifyProperties mockConfig() {
    NotifyProperties properties = new NotifyProperties();
    NotifyProperties.XiaoMiPushConfig xiaomi = new NotifyProperties.XiaoMiPushConfig();
    xiaomi.setPackageNames(List.of("test"));
    xiaomi.setAppId("test");
    xiaomi.setAppKey("test");
    xiaomi.setAppSecret("test");
    properties.setXiaomi(xiaomi);
    return properties;
  }

  private Supplier<Sender> mockSender() {
    return () -> new Sender("key", "secret") {
      @Override
      public Result send(Message message, String registrationId, int retries) {
        return new Result.Builder().build();
      }
    };
  }

  @Test
  void pushToServer() throws Throwable {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    PNSToken pnsToken = new PNSToken();
    pnsToken.setToken("test");
    message.setPnsToken(pnsToken);
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setOriginalMessage("test message");
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    assertThrows(MessageInvalidException.class, () -> adapter.pushToServer(dto));

    dto.setSummary("test summary");
    assertNotNull(adapter.pushToServer(dto));

  }
}