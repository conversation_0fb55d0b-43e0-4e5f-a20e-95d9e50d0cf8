package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.gexin.rp.sdk.base.impl.PushResult;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.push.adapter.GeTuiPushAdapter;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

class GeTuiPushServerTest {

  private final GeTuiPushServer server = new GeTuiPushServer(mock(GeTuiPushAdapter.class));

  @Test
  void resultHandler() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    PNSToken token = new PNSToken();
    token.setToken("test");
    message.setPnsToken(token);
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setOriginalMessage("has original message");
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    PushResult pushResult = new PushResult();
    pushResult.setResponse(Map.of("result", "ok"));
    assertThrows(Exception.class, () -> server.resultHandler(pushResult, dto));
  }
}