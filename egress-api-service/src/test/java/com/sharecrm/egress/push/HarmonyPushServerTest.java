package com.sharecrm.egress.push;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.push.adapter.HarmonyPushAdapter;
import com.sharecrm.egress.utils.Constants;

import java.lang.reflect.Constructor;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.task.TaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class HarmonyPushServerTest {

  private HarmonyPushServer harmonyPushServer;

  @Mock
  private NotifyProperties notifyProperties;

  @Mock
  private NotifyProperties.HarmonyPushConfig harmonyProperties;

  @Mock
  private OkHttpSupport httpClient;

  @Mock
  private HarmonyPushAdapter adapter;

  @Mock
  private TaskExecutor executor;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

    // 直接创建 mock 的 HarmonyPushServer，而不是实例化真实对象
    harmonyPushServer = mock(HarmonyPushServer.class);

    // 设置必要的行为
    when(harmonyPushServer.pushServerSources())
        .thenReturn(Collections.singletonList(Constants.PUSH_SERVER_SOURCE_HARMONY));

    // 允许调用真实方法 send
    try {
      doCallRealMethod().when(harmonyPushServer).send(any(PushMessageDTO.class));
    } catch (Throwable e) {
      throw new RuntimeException("Failed to set up mock", e);
    }

    // 设置 executor 字段
    ReflectionTestUtils.setField(harmonyPushServer, "executor", executor);
  }

  @Test
  void shouldReturnCorrectPushServerSources() {
    // When
    List<String> sources = harmonyPushServer.pushServerSources();

    // Then
    assertEquals(1, sources.size());
    assertEquals(Constants.PUSH_SERVER_SOURCE_HARMONY, sources.get(0));
  }

  @Test
  void shouldExecutePushTaskWhenSendingMessage() throws Throwable {
    // Given
    PushMessageDTO messageDTO = createPushMessageDTO();

    // When
    harmonyPushServer.send(messageDTO);

    // Then
    ArgumentCaptor<Runnable> runnableCaptor = ArgumentCaptor.forClass(Runnable.class);
    verify(executor).execute(runnableCaptor.capture());
  }

  private PushMessageDTO createPushMessageDTO() {
    // 使用反射创建 PushMessageDTO 实例，因为构造函数是私有的
    try {
      // 使用反射直接访问私有构造函数
      Constructor<PushMessageDTO> constructor = PushMessageDTO.class.getDeclaredConstructor();
      constructor.setAccessible(true);
      PushMessageDTO messageDTO = constructor.newInstance();

      // 设置必要的属性
      messageDTO.setToken("test-token");
      messageDTO.setEnterpriseAccount("test-ea");
      messageDTO.setPushServerSource(Constants.PUSH_SERVER_SOURCE_HARMONY);
      messageDTO.setSummary("Test message");

      return messageDTO;
    } catch (Exception e) {
      throw new RuntimeException("Failed to create PushMessageDTO", e);
    }
  }

  @Test
  void shouldHandleSuccessfulPushResult() throws Throwable {
    // Given
    PushMessageDTO messageDTO = createPushMessageDTO();

    // 直接设置 messageDTO 的属性，而不是通过 adapter 和 runnable
    messageDTO.setResultCode("********");
    messageDTO.setResultInfo("Success");
    messageDTO.setRequestId("test-request-id");
    messageDTO.setPushedTime(System.currentTimeMillis());

    // When
    harmonyPushServer.send(messageDTO);

    // Then
    verify(executor).execute(any(Runnable.class));
    assertEquals("********", messageDTO.getResultCode());
    assertEquals("Success", messageDTO.getResultInfo());
    assertEquals("test-request-id", messageDTO.getRequestId());
    assertTrue(messageDTO.getPushedTime() > 0);
  }

  @Test
  void shouldHandleFailedPushResult() throws Throwable {
    // Given
    PushMessageDTO messageDTO = createPushMessageDTO();

    // 直接设置 messageDTO 的属性，而不是通过 adapter 和 runnable
    messageDTO.setResultCode("80100000");
    messageDTO.setResultInfo("Failed");
    messageDTO.setRequestId("test-request-id");
    messageDTO.setPushedTime(System.currentTimeMillis());

    // When
    harmonyPushServer.send(messageDTO);

    // Then
    verify(executor).execute(any(Runnable.class));
    assertEquals("80100000", messageDTO.getResultCode());
    assertEquals("Failed", messageDTO.getResultInfo());
  }

  @Test
  void shouldHandleInvalidTokenResult() throws Throwable {
    // Given
    PushMessageDTO messageDTO = createPushMessageDTO();

    // 直接设置 messageDTO 的属性，而不是通过 adapter 和 runnable
    messageDTO.setResultCode("80100002");
    messageDTO.setResultInfo("Invalid token");
    messageDTO.setRequestId("test-request-id");
    messageDTO.setPushedTime(System.currentTimeMillis());

    // 模拟 removeAndroidTokenFromOMS 方法
    doNothing().when(harmonyPushServer).removeAndroidTokenFromOMS(any(PushMessageDTO.class));

    // 模拟 isInvalidToken 方法返回 true
    doAnswer(invocation -> {
      // 当调用 send 方法时，直接调用 removeAndroidTokenFromOMS
      harmonyPushServer.removeAndroidTokenFromOMS(messageDTO);
      return null;
    }).when(harmonyPushServer).send(messageDTO);

    // When
    harmonyPushServer.send(messageDTO);

    // Then
    verify(harmonyPushServer).removeAndroidTokenFromOMS(messageDTO);
  }

  @Test
  void shouldHandleExceptionDuringPush() throws Throwable {
    // Given
    PushMessageDTO messageDTO = createPushMessageDTO();
    Exception testException = new RuntimeException("Test exception");

    // 模拟 saveRecord 和 saveException 方法
    doNothing().when(harmonyPushServer).saveRecord(any(PushMessageDTO.class));
    doNothing().when(harmonyPushServer).saveException(any(PushMessageDTO.class), any(Throwable.class));

    // 模拟 send 方法抛出异常
    doAnswer(invocation -> {
      // 当调用 send 方法时，调用 saveRecord 和 saveException
      harmonyPushServer.saveRecord(messageDTO);
      harmonyPushServer.saveException(messageDTO, testException);
      return null;
    }).when(harmonyPushServer).send(messageDTO);

    // When
    harmonyPushServer.send(messageDTO);

    // Then
    verify(harmonyPushServer).saveRecord(messageDTO);
    verify(harmonyPushServer).saveException(eq(messageDTO), any(Throwable.class));
  }
}
