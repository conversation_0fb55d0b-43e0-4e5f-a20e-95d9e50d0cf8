package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.oppo.push.server.Sender;
import com.sharecrm.egress.entity.PushMessageDTO;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.mock;

class OppoPushAdapterTest {

  private final Sender sender = mock(Sender.class);
  private final OppoPushAdapter adapter = new OppoPushAdapter(() -> sender);

  @Test
  void pushToServer() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setSummary("has summary");
    message.setNotification(notification);
    assertDoesNotThrow(() -> adapter.pushToServer(PushMessageDTO.Convert.from(message)));
  }
}