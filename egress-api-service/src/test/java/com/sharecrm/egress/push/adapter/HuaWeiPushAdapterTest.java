package com.sharecrm.egress.push.adapter;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.exception.MessageInvalidException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

class HuaWeiPushAdapterTest {

  private final HuaWeiPushAdapter adapter = new HuaWeiPushAdapter(new NotifyProperties.HuaWeiPushConfig(), mock(OkHttpSupport.class));

  @Test
  void pushToServer() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    assertThrows(MessageInvalidException.class, () -> adapter.pushToServer(PushMessageDTO.Convert.from(message)));
  }

  @Test
  void sendByTransmissionRequest() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    String body = adapter.sendByTransmissionRequest(dto);
    assertNotNull(body);
    assertNotNull(dto.getPushContent());
  }

  @Test
  void sendByNotification() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);
    String result = adapter.sendByNotificationRequest(dto);
    assertNotNull(result);
    assertNotNull(dto.getPushContent());
  }
}