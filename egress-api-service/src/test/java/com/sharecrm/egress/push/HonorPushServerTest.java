package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.push.adapter.honor.HonorPushResult;
import com.sharecrm.egress.push.adapter.honor.HonorPushResultData;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class HonorPushServerTest {

  OkHttpSupport client = mock(OkHttpSupport.class);
  HonorPushServer server = new HonorPushServer(mockHonorConfig(), client);

  private NotifyProperties mockHonorConfig() {
    NotifyProperties.HonorPushConfig config = new NotifyProperties.HonorPushConfig();
    config.setClientId("test");
    config.setClientSecret("test");
    config.setTokenTimeout(Duration.ofSeconds(10));
    NotifyProperties properties = new NotifyProperties();
    properties.setHonor(config);
    return properties;
  }

  @Test
  void send() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    PNSToken token = new PNSToken();
    token.setToken("test");
    message.setPnsToken(token);
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    notification.setOriginalMessage("has original message");
    notification.setSummary("has summary");
    message.setNotification(notification);
    PushMessageDTO dto = PushMessageDTO.Convert.from(message);

    HonorPushResult rs = new HonorPushResult();
    rs.setCode(200);
    HonorPushResultData data = new HonorPushResultData();
    data.setRequestId("test");
    data.setSendResult(true);
    rs.setData(data);
    when(client.parseObject(any(), any())).thenReturn(rs);

    assertThrows(Exception.class, () -> server.pushTask(dto));
  }

}