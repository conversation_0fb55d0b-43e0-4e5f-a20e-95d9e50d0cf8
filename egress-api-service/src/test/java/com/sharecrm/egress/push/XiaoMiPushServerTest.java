package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.config.NotifyProperties;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.push.adapter.XiaoMiPushAdapter;
import com.xiaomi.xmpush.server.Message;
import com.xiaomi.xmpush.server.Result;
import com.xiaomi.xmpush.server.Sender;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * <AUTHOR>
 * @date: 2024/11/28
 * @since 1.0.0
 */
class XiaoMiPushServerTest {


  private final XiaoMiPushAdapter adapter = new XiaoMiPushAdapter(mockSender(), config());

  private Supplier<Sender> mockSender() {
    return () -> new Sender("key", "secret") {
      @Override
      public Result send(Message message, String registrationId, int retries) {
        return new Result.Builder().build();
      }
    };
  }


  @NotNull
  private NotifyProperties config() {
    NotifyProperties properties = new NotifyProperties();
    NotifyProperties.ApplePushConfig config = new NotifyProperties.ApplePushConfig();
    config.setTeamId("test");
    config.setKeyId("test");
    config.setTopic("test");
    properties.setAppleProd(config);
    return properties;
  }

  @Test
  @SneakyThrows
  void pushTask() {
    ThirdPartPushCommonMessage message = new ThirdPartPushCommonMessage();
    message.setPnsToken(new PNSToken());
    ThirdPartNotification notification = new ThirdPartNotification();
    String title = "test title";
    notification.setTitle(title);
    message.setNotification(notification);
    PushMessageDTO pushMessageDTO = PushMessageDTO.Convert.from(message);
    assertThrows(Throwable.class, () -> adapter.pushToServer(pushMessageDTO));
  }
}