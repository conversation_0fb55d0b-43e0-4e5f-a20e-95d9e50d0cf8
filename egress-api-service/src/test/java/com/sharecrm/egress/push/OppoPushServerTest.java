package com.sharecrm.egress.push;

import com.facishare.oms.api.model.PNSToken;
import com.facishare.oms.api.model.ThirdPartNotification;
import com.facishare.oms.common.model.ThirdPartPushCommonMessage;
import com.sharecrm.egress.entity.PushMessageDTO;
import com.sharecrm.egress.push.adapter.OppoPushAdapter;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

/**
 * <AUTHOR>
 * @date: 2024/12/10
 * @since 1.0.0
 */
class OppoPushServerTest {


  private final OppoPushServer server = new OppoPushServer(mock(OppoPushAdapter.class));


  @Test
  void send() {
    ThirdPartPushCommonMessage thirdMessage = new ThirdPartPushCommonMessage();
    thirdMessage.setNotification(new ThirdPartNotification());
    thirdMessage.setPnsToken(new PNSToken());
    PushMessageDTO message = PushMessageDTO.Convert.from(thirdMessage);
    assertThrows(Throwable.class, () -> server.send(message));
  }

  @Test
  void pushServerSources() {
    assertDoesNotThrow(server::pushServerSources);
  }
}