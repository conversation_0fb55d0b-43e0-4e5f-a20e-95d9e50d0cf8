package com.sharecrm.egress.entity;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class LocationPointTest {
  // test jackson serialization and deserialization
  @Test
  void testJacksonSerializationAndDeserialization() throws Exception {
    LocationPoint locationPoint = new LocationPoint(37.7749, -122.419456789);
    ObjectMapper objectMapper = new ObjectMapper();
    String locationPointJson = objectMapper.writeValueAsString(locationPoint);
    LocationPoint deserializedLocationPoint = objectMapper.readValue(locationPointJson, LocationPoint.class);
    LocationPoint expectedLocationPoint = new LocationPoint(37.7749, -122.419457);
    assertEquals(expectedLocationPoint, deserializedLocationPoint);
  }

}
