package com.sharecrm.egress.entity;

import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.sharecrm.egress.entity.BusinessCardKey.firstNotBlankAlias;
import static org.junit.jupiter.api.Assertions.assertEquals;

class BusinessCardKeyTest {


  @Test
  void testFirstNotBlankAlias() {
    String name = "李先生";
    String nameEn = "Bruce Lee";
    String nameAny = "Oh";
    assertEquals(name, firstNotBlankAlias(Map.of("姓名", name), BusinessCardKey.NAME));
    assertEquals(nameEn, firstNotBlankAlias(Map.of("姓名", "", "英文姓名", nameEn), BusinessCardKey.NAME));
    assertEquals(nameAny, firstNotBlankAlias(Map.of("姓名", "", "俄语姓名", nameAny), BusinessCardKey.NAME));
    //优先中文姓名
    assertEquals(name, firstNotBlankAlias(Map.of("姓名", name, "俄语姓名", nameAny), BusinessCardKey.NAME));
  }
}