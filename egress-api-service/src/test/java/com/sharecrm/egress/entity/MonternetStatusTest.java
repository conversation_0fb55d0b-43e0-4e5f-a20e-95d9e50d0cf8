package com.sharecrm.egress.entity;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MonternetStatus 测试用例
 */
@DisplayName("梦网状态码转换测试")
class MonternetStatusTest {

  @Test
  @DisplayName("测试梦网状态码转换 - 成功状态")
  void testGetOrDefault_SuccessStatus() {
    // 测试成功状态码
    assertEquals("发送成功", MonternetStatus.getOrDefault("0", "未知"));

    // 测试失败状态码
    assertEquals("参数为空。信息、电话号码等有空指针,登陆失败",
      MonternetStatus.getOrDefault("-1", "未知"));
    assertEquals(" 用户余额不足",
      MonternetStatus.getOrDefault("-10003", "未知"));
    assertEquals("服务器内部错误",
      MonternetStatus.getOrDefault("-999", "未知"));
  }

  @Test
  @DisplayName("测试梦网状态码转换 - 不存在的状态码")
  void testGetOrDefault_NonExistentStatus() {
    // 测试不存在的状态码，应该返回默认值
    assertEquals("未知状态", MonternetStatus.getOrDefault("999", "未知状态"));
    assertEquals("默认消息", MonternetStatus.getOrDefault("-9999", "默认消息"));
    assertEquals("", MonternetStatus.getOrDefault("abc", ""));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 成功状态")
  void testGetReplayMessage_SuccessStatus() {
    // 测试成功状态
    String result = MonternetStatus.getReplayMessage("DELIVRD");
    assertTrue(result.contains("成功"));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 关机状态")
  void testGetReplayMessage_PowerOffStatus() {
    // 测试关机相关状态码
    String result1 = MonternetStatus.getReplayMessage("UNDELIV");
    assertTrue(result1.contains("关机"));

    String result2 = MonternetStatus.getReplayMessage("REJECTD");
    assertTrue(result2.contains("关机"));

    String result3 = MonternetStatus.getReplayMessage("EXPIRED");
    assertTrue(result3.contains("关机"));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 空号状态")
  void testGetReplayMessage_EmptyNumberStatus() {
    // 测试空号相关状态码
    String result1 = MonternetStatus.getReplayMessage("MN:0001");
    assertTrue(result1.contains("空号"));

    String result2 = MonternetStatus.getReplayMessage("R:00012");
    assertTrue(result2.contains("空号"));

    String result3 = MonternetStatus.getReplayMessage("ID:0013");
    assertTrue(result3.contains("空号"));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 停机状态")
  void testGetReplayMessage_ShutdownStatus() {
    // 测试停机相关状态码
    String result1 = MonternetStatus.getReplayMessage("MK:0005");
    assertTrue(result1.contains("停机"));

    String result2 = MonternetStatus.getReplayMessage("R:00013");
    assertTrue(result2.contains("停机"));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 黑名单状态")
  void testGetReplayMessage_BlacklistStatus() {
    // 测试黑名单相关状态码
    String result1 = MonternetStatus.getReplayMessage("M2:0045");
    assertTrue(result1.contains("黑名单"));

    String result2 = MonternetStatus.getReplayMessage("DB:0141");
    assertTrue(result2.contains("黑名单"));

    String result3 = MonternetStatus.getReplayMessage("MA:0036");
    assertTrue(result3.contains("黑名单"));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 关键字拦截状态")
  void testGetReplayMessage_KeywordBlockedStatus() {
    // 测试关键字拦截相关状态码
    String result1 = MonternetStatus.getReplayMessage("ID:0076");
    assertTrue(result1.contains("关键字拦截"));

    String result2 = MonternetStatus.getReplayMessage("ID:1241");
    assertTrue(result2.contains("关键字拦截"));

    String result3 = MonternetStatus.getReplayMessage("GB:0013");
    assertTrue(result3.contains("关键字拦截"));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 超流速状态")
  void testGetReplayMessage_OverSpeedStatus() {
    // 测试超流速相关状态码
    String result1 = MonternetStatus.getReplayMessage("IA:0054");
    assertTrue(result1.contains("超流速"));

    String result2 = MonternetStatus.getReplayMessage("R:00061");
    assertTrue(result2.contains("超流速"));

    String result3 = MonternetStatus.getReplayMessage("IB:0008");
    assertTrue(result3.contains("超流速"));
  }

  @ParameterizedTest
  @CsvSource({
    "DELIVRD, 成功",
    "UNDELIV, 关机",
    "MN:0001, 空号",
    "MK:0005, 停机",
    "M2:0045, 黑名单",
    "ID:0076, 关键字拦截",
    "IA:0054, 超流速",
    "R:00219, 不在服务区",
    "MK:0017, 收件箱",
    "UNKNOWN, 状态未知"
  })
  @DisplayName("测试运营商状态码转换 - 参数化测试")
  void testGetReplayMessage_ParameterizedTest(String code, String expectedKeyword) {
    String result = MonternetStatus.getReplayMessage(code);
    assertTrue(result.contains(expectedKeyword),
      String.format("状态码 [%s] 转换结果 [%s] 应包含关键词 [%s]", code, result, expectedKeyword));
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 不存在的状态码")
  void testGetReplayMessage_NonExistentCode() {
    // 测试不存在的状态码
    String result1 = MonternetStatus.getReplayMessage("UNKNOWN_CODE");
    assertNotNull(result1);

    String result2 = MonternetStatus.getReplayMessage("XYZ:9999");
    assertNotNull(result2);
  }

  @Test
  @DisplayName("测试运营商状态码转换 - 特殊字符")
  void testGetReplayMessage_SpecialCharacters() {
    // 测试包含特殊字符的状态码
    String result1 = MonternetStatus.getReplayMessage("R:00001");
    assertNotNull(result1);

    String result2 = MonternetStatus.getReplayMessage("M2:0043");
    assertNotNull(result2);

    String result3 = MonternetStatus.getReplayMessage("MK:0036");
    assertNotNull(result3);
  }

  @Test
  @DisplayName("测试梦网状态码转换 - 边界值测试")
  void testGetOrDefault_BoundaryValues() {
    // 测试边界值
    assertEquals("发送成功", MonternetStatus.getOrDefault("0", "默认"));
    assertEquals("参数为空。信息、电话号码等有空指针,登陆失败",
      MonternetStatus.getOrDefault("-1", "默认"));

    // 测试非常大的数字
    assertEquals("默认", MonternetStatus.getOrDefault("999999", "默认"));
    assertEquals("默认", MonternetStatus.getOrDefault("-999999", "默认"));
  }

  @Test
  @DisplayName("测试大小写敏感性")
  void testCaseSensitivity() {
    // 测试状态码是否大小写敏感
    String upperCase = MonternetStatus.getReplayMessage("DELIVRD");
    String lowerCase = MonternetStatus.getReplayMessage("delivrd");

    // 验证大小写是否影响结果（根据实际实现调整断言）
    assertNotEquals(upperCase, lowerCase);
  }

  @Test
  @DisplayName("测试常见错误码覆盖率")
  void testCommonErrorCodesCoverage() {
    // 测试常见的梦网错误码
    String[] monternetCodes = {"-1", "0", "-12", "-14", "-200", "-999",
      "-10001", "-10003", "-10011", "-10029",
      "-10030", "-10031", "-10057", "-10056", "-1002"};

    for (String code : monternetCodes) {
      String result = MonternetStatus.getOrDefault(code, "未知");
      assertNotEquals("未知", result, "梦网状态码 " + code + " 应该有对应的消息");
    }
    
    // 测试常见的运营商错误码
    String[] operatorCodes = {"DELIVRD", "UNDELIV", "REJECTD", "EXPIRED",
      "MN:0001", "R:00012", "MK:0005", "M2:0045",
      "ID:0076", "IA:0054"};

    for (String code : operatorCodes) {
      String result = MonternetStatus.getReplayMessage(code);
      assertFalse(result.isEmpty(), "运营商状态码 " + code + " 应该有对应的消息");
    }
  }
} 