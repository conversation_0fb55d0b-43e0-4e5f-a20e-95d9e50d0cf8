package com.sharecrm.egress.gateway;

import com.sharecrm.egress.config.MapProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import static org.mockito.ArgumentMatchers.any;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class TencentSigGatewayFilterFactoryTest {

  private TencentSigGatewayFilterFactory factory;
  private MapProperties mapProperties;
  private GatewayFilterChain filterChain;

  @BeforeEach
  void setUp() {
    mapProperties = new MapProperties();

    // Configure Tencent properties
    Map<String, MapProperties.TencentConfig> tencentConfigMap = new HashMap<>();

    MapProperties.TencentConfig config1 = new MapProperties.TencentConfig();
    config1.setId("test-tencent-1");
    config1.setKey("test-key-1");
    config1.setSig("test-sig-1");
    config1.setEnabled(true);
    config1.setSupports(Arrays.asList("gateway-rest"));

    tencentConfigMap.put("test1", config1);

    mapProperties.setTencent(tencentConfigMap);

    factory = new TencentSigGatewayFilterFactory(mapProperties);

    // Mock filter chain
    filterChain = Mockito.mock(GatewayFilterChain.class);
    when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());
  }

  @Test
  void testAppendQueryKeyAndSign() {
    URI uri = URI.create("https://apis.map.qq.com/ws/geocoder/v1/?address=北京市朝阳区阜通东大街6号");

    GatewayFilter filter = factory.apply(new Object());

    MockServerHttpRequest request = MockServerHttpRequest
        .get(uri.toString())
        .build();

    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    filter.filter(exchange, filterChain);

    // Just verify that the filter chain was called
    verify(filterChain, times(1)).filter(any(ServerWebExchange.class));
  }

  @Test
  void testInitQueryWithExistingQuery() {
    URI uri = URI.create("https://apis.map.qq.com/ws/geocoder/v1/?address=北京市朝阳区阜通东大街6号");
    StringBuilder result = factory.initQuery(uri);

    assertEquals("address=北京市朝阳区阜通东大街6号&", result.toString());
  }

  @Test
  void testInitQueryWithEmptyQuery() {
    URI uri = URI.create("https://apis.map.qq.com/ws/geocoder/v1/");
    StringBuilder result = factory.initQuery(uri);

    assertEquals("", result.toString());
  }
}