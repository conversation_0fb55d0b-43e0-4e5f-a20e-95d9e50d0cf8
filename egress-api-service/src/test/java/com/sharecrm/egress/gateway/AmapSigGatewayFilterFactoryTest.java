package com.sharecrm.egress.gateway;

import com.sharecrm.egress.config.MapProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AmapSigGatewayFilterFactoryTest {

  private AmapSigGatewayFilterFactory factory;
  private MapProperties mapProperties;
  private GatewayFilterChain filterChain;

  @BeforeEach
  void setUp() {
    mapProperties = new MapProperties();

    // Configure AMap properties
    Map<String, MapProperties.AMapConfig> amapConfigMap = new HashMap<>();

    MapProperties.AMapConfig config1 = new MapProperties.AMapConfig();
    config1.setId("test-amap-1");
    config1.setKey("test-key-1");
    config1.setSig("test-sig-1");
    config1.setEnabled(true);
    config1.setSupports(Arrays.asList("gateway-rest"));

    amapConfigMap.put("test1", config1);

    mapProperties.setAmap(amapConfigMap);

    factory = new AmapSigGatewayFilterFactory(mapProperties);

    // Mock filter chain
    filterChain = Mockito.mock(GatewayFilterChain.class);
    when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());
  }

  @Test
  void testAppendQueryKeyAndSign() {
    URI uri = URI.create("https://restapi.amap.com/v3/geocode/geo?address=北京市朝阳区阜通东大街6号");

    GatewayFilter filter = factory.apply(new Object());

    MockServerHttpRequest request = MockServerHttpRequest
        .get(uri.toString())
        .build();

    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    filter.filter(exchange, filterChain);

    // Just verify that the filter chain was called
    verify(filterChain, times(1)).filter(any(ServerWebExchange.class));
  }

  @Test
  void testInitQueryWithExistingQuery() {
    URI uri = URI.create("https://restapi.amap.com/v3/geocode/geo?address=北京市朝阳区阜通东大街6号");
    StringBuilder result = factory.initQuery(uri);

    assertEquals("address=北京市朝阳区阜通东大街6号&", result.toString());
  }

  @Test
  void testInitQueryWithEmptyQuery() {
    URI uri = URI.create("https://restapi.amap.com/v3/geocode/geo");
    StringBuilder result = factory.initQuery(uri);

    assertEquals("", result.toString());
  }

  @Test
  void testSelect() {
    MapProperties.AMapConfig config = factory.select();
    assertNotNull(config);
    assertEquals("test-key-1", config.getKey());
  }
}