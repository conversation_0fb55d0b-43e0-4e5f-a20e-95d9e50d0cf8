package com.sharecrm.egress.gateway;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.web.reactive.WebFluxProperties;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class StripBasePathGatewayFilterFactoryTest {

  private StripBasePathGatewayFilterFactory factory;
  private WebFluxProperties webFluxProperties;
  private GatewayFilterChain filterChain;

  @BeforeEach
  void setUp() {
    webFluxProperties = new WebFluxProperties();
    factory = new StripBasePathGatewayFilterFactory(webFluxProperties);

    // Mock filter chain
    filterChain = Mockito.mock(GatewayFilterChain.class);
    when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());
  }

  @Test
  void testStripBasePath() {
    // Set base path
    webFluxProperties.setBasePath("/api");

    GatewayFilter filter = factory.apply(new Object());

    MockServerHttpRequest request = MockServerHttpRequest
        .get("/api/v1/users")
        .build();

    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    filter.filter(exchange, filterChain);

    // Just verify that the filter chain was called
    verify(filterChain, times(1)).filter(any(ServerWebExchange.class));
  }

  @Test
  void testNoBasePathConfigured() {
    // Set empty base path
    webFluxProperties.setBasePath("");

    GatewayFilter filter = factory.apply(new Object());

    MockServerHttpRequest request = MockServerHttpRequest
        .get("/v1/users")
        .build();

    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    filter.filter(exchange, filterChain);

    // Verify the filter chain was called with the original exchange
    verify(filterChain, times(1)).filter(exchange);
  }

  @Test
  void testRootBasePathConfigured() {
    // Set root base path
    webFluxProperties.setBasePath("/");

    GatewayFilter filter = factory.apply(new Object());

    MockServerHttpRequest request = MockServerHttpRequest
        .get("/v1/users")
        .build();

    MockServerWebExchange exchange = MockServerWebExchange.from(request);

    filter.filter(exchange, filterChain);

    // Verify the filter chain was called with the original exchange
    verify(filterChain, times(1)).filter(exchange);
  }
}