package com.sharecrm.egress.gateway;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.gateway.config.HttpClientProperties;
import org.springframework.cloud.gateway.filter.NettyRoutingFilter;
import org.springframework.cloud.gateway.filter.headers.HttpHeadersFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.web.server.ServerWebExchange;
import reactor.netty.http.client.HttpClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProxyNettyRoutingFilterTest {

  @Mock
  private HttpClient mockHttpClient;

  @Mock
  private ObjectProvider<List<HttpHeadersFilter>> headersFiltersProvider;

  @Mock
  private HttpClientProperties httpClientProperties;

  @Mock
  private Route mockRoute;

  @Mock
  private ServerWebExchange mockExchange;

  private ProxyNettyRoutingFilter filter;

  @BeforeEach
  void setUp() {
    filter = new ProxyNettyRoutingFilter(mockHttpClient, headersFiltersProvider, httpClientProperties);
  }

  @Test
  void testGetOrder() {
    // When
    int order = filter.getOrder();

    // Then
    assertEquals(NettyRoutingFilter.ORDER - 1, order);
    assertTrue(order < NettyRoutingFilter.ORDER);
  }

  @Test
  void testConstructorCreatesFilterInstance() {
    // Then
    assertNotNull(filter);
    assertTrue(filter instanceof NettyRoutingFilter);
    assertTrue(filter instanceof ProxyNettyRoutingFilter);
  }

  @Test
  void testFilterHasHigherPriorityThanNettyRoutingFilter() {
    // Given
    NettyRoutingFilter baseFilter = new NettyRoutingFilter(mockHttpClient, headersFiltersProvider, httpClientProperties);

    // When
    int proxyFilterOrder = filter.getOrder();
    int baseFilterOrder = baseFilter.getOrder();

    // Then
    assertTrue(proxyFilterOrder < baseFilterOrder, 
        "ProxyNettyRoutingFilter should have higher priority (lower order number) than NettyRoutingFilter");
  }

  @Test
  void testGetHttpClientMethodExists() {
    // Given
    Map<String, Object> metadata = new HashMap<>();
    when(mockRoute.getMetadata()).thenReturn(metadata);

    // When & Then - 测试方法可以被调用而不抛出异常
    assertDoesNotThrow(() -> {
      filter.getHttpClient(mockRoute, mockExchange);
    });
  }

  @Test
  void testRouteMetadataHandling() {
    // Given - 测试各种metadata场景
    Map<String, Object> metadata1 = new HashMap<>();
    Map<String, Object> metadata2 = new HashMap<>();
    metadata2.put("http-proxy", null);
    Map<String, Object> metadata3 = new HashMap<>();
    metadata3.put("http-proxy", "");
    Map<String, Object> metadata4 = new HashMap<>();
    metadata4.put("http-proxy", "http://proxy.example.com:8080");

    when(mockRoute.getMetadata())
        .thenReturn(metadata1)
        .thenReturn(metadata2)
        .thenReturn(metadata3)
        .thenReturn(metadata4);

    // When & Then - 验证所有场景都不会抛出异常
    assertDoesNotThrow(() -> filter.getHttpClient(mockRoute, mockExchange));
    assertDoesNotThrow(() -> filter.getHttpClient(mockRoute, mockExchange));
    assertDoesNotThrow(() -> filter.getHttpClient(mockRoute, mockExchange));
    assertDoesNotThrow(() -> filter.getHttpClient(mockRoute, mockExchange));
  }

  @Test
  void testFilterInheritsFromNettyRoutingFilter() {
    // Then
    assertTrue(filter instanceof NettyRoutingFilter, 
        "ProxyNettyRoutingFilter should extend NettyRoutingFilter");
  }
} 