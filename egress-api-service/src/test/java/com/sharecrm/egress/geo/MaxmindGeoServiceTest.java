package com.sharecrm.egress.geo;

import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date: 2024/12/6
 * @since 1.0.0
 */
public class MaxmindGeoServiceTest {

  private final MaxmindGeoService maxmindGeoService = mock(MaxmindGeoService.class);

  @Test
  void supports() {
    List<String> supportedLanguages = Arrays.asList("***********");
    when(maxmindGeoService.supports()).thenReturn(supportedLanguages);
    Mono<List<String>> monoResult = Mono.just(maxmindGeoService.supports());
    StepVerifier.create(monoResult)
      .expectNextMatches(list -> list != null && !list.isEmpty())
      .verifyComplete();
  }

  @Test
  void queryReverseGeoAddress() {
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLongitude(23.11);
    request.setLatitude(121.11);
    when(maxmindGeoService.queryReverseGeoAddress(any())).thenReturn(Mono.just(new GeoAddress()));
    StepVerifier.create(maxmindGeoService.queryReverseGeoAddress(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }


  @Test
  void queryGeoAddress() {
    when(maxmindGeoService.queryGeoAddress(any())).thenReturn(Mono.just(new GeoAddress()));
    StepVerifier.create(maxmindGeoService.queryGeoAddress(new GeoEncodeRequest()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryLocationPoint() {
    String address = "北京市朝阳区东南大门";
    String city = "北京";
    when(maxmindGeoService.queryLocationPoint(any(String.class), any(String.class))).thenReturn(Mono.just(new LocationPoint()));
    StepVerifier.create(maxmindGeoService.queryLocationPoint(address, city))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryDistance() {
    List<LocationPoint> points = Arrays.asList();
    when(maxmindGeoService.queryDistance(any(), any())).thenReturn(Mono.just(Arrays.asList(new PointDistance())));
    StepVerifier.create(maxmindGeoService.queryDistance(points, points))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryDriving() {
    DrivingRouteRequest request = DrivingRouteRequest.builder()
      .from(new LocationPoint())
      .to(new LocationPoint())
      .waypoints(Arrays.asList(new LocationPoint(), new LocationPoint()))
      .language("zh-CN")
      .build();
    when(maxmindGeoService.queryDriving(any())).thenReturn(Mono.just(new PointDistance()));
    StepVerifier.create(maxmindGeoService.queryDriving(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryPoiAround() {
    PoiSearchRequest request = new PoiSearchRequest();
    request.setLongitude(23.11);
    request.setLatitude(121.11);
    when(maxmindGeoService.queryPoiAround(any())).thenReturn(Mono.just(new PoiResponse()));
    StepVerifier.create(maxmindGeoService.queryPoiAround(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }


  @Test
  void queryIpLocation() {
    String ip = "************";
    String language = "CN";

    // 创建模拟返回值
    IpLocation ipLocation = new IpLocation();
    ipLocation.setIp(ip);
    ipLocation.setLanguage(language);

    when(maxmindGeoService.queryIpLocation(any(String.class), any(String.class))).thenReturn(Mono.just(ipLocation));
    StepVerifier.create(maxmindGeoService.queryIpLocation(ip, language))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

}