package com.sharecrm.egress.geo;

import com.sharecrm.egress.config.FluxTrace;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoAddressEsCache;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.IpLocationRequest;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.PoiResponse;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.ApplicationContext;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_DISTANCE;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_DRIVING;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_GEO_ADDRESS;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_IP_LOCATION;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_LOCATION_POINT;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_POI_AROUND;
import static com.sharecrm.egress.geo.GeoAdapter.SUPPORT_REVERSE_GEO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class GeoServiceTest {

  private final ApplicationContext applicationContext = mock(ApplicationContext.class);
  private final GeoCache geoCache = mock(GeoCache.class);
  private final GeoEsCacheService geoEsCacheService = mock(GeoEsCacheService.class);

  private final GeoAdapter geoAdapter = mock(GeoAdapter.class);

  private final GeoService service = new GeoService(applicationContext, geoCache, geoEsCacheService);

  private final List<String> supports = List.of(SUPPORT_DISTANCE, SUPPORT_DRIVING, SUPPORT_LOCATION_POINT, SUPPORT_GEO_ADDRESS, SUPPORT_REVERSE_GEO, SUPPORT_POI_AROUND, SUPPORT_IP_LOCATION);


  @BeforeEach
  void setup() {
    when(applicationContext.getBeansOfType(GeoAdapter.class)).thenReturn(Map.of("amap01", geoAdapter));
  }

  @Test
  void queryReverseGeoAddress() {

    when(geoAdapter.supports()).thenReturn(supports);
    when(geoAdapter.provider()).thenReturn(new MapProperties.AMapConfig());
    GeoAddress address = new GeoAddress();
    address.setCity("北京市");
    address.setPois(List.of(new MapPoi()));
    when(geoAdapter.queryReverseGeoAddress(any())).thenReturn(Mono.just(address));
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    StepVerifier.create(service.queryReverseGeoAddress(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryReverseGeoAddressByCache() {
    when(geoCache.getReverseGeoAddress(any())).thenReturn(null);
    GeoAddressEsCache cache = new GeoAddressEsCache();
    cache.setUpdateTime(System.currentTimeMillis());
    when(geoEsCacheService.queryReverseGeoAddress(any(), any(FluxTrace.class), any())).thenReturn(cache);
    GeoAddress address = new GeoAddress();
    address.setCity("北京市");
    address.setPois(List.of(new MapPoi()));
    when(geoAdapter.queryReverseGeoAddress(any())).thenReturn(Mono.just(address));

    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    StepVerifier.create(service.queryReverseGeoAddressByCache(request, new FluxTrace(), "amap"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }


  @Test
  void queryGeoAddress() {
    when(geoAdapter.supports()).thenReturn(supports);
    when(geoAdapter.provider()).thenReturn(new MapProperties.AMapConfig());
    GeoAddress address = new GeoAddress();
    address.setCity("北京市");
    when(geoAdapter.queryGeoAddress(any())).thenReturn(Mono.just(address));
    GeoEncodeRequest request = new GeoEncodeRequest();
    request.setAddress("北京市");
    request.setProvider("amap");
    StepVerifier.create(service.queryGeoAddress(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }


  @Test
  void queryPoiAround() {
    when(geoAdapter.supports()).thenReturn(supports);
    when(geoAdapter.provider()).thenReturn(new MapProperties.GoogleConfig());

    PoiResponse data = new PoiResponse();
    data.setCount(1);
    MapPoi poi = new MapPoi();
    poi.setLocation(new LocationPoint(39.977054, 116.331934));
    data.setResults(List.of(poi));
    when(geoAdapter.queryPoiAround(any())).thenReturn(Mono.just(data));

    PoiSearchRequest request = new PoiSearchRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    request.setRadius(1000);
    request.setTypes(List.of("餐馆"));
    request.setLanguage("zh");
    request.setPageSize(10);
    request.setPageNum(1);
    StepVerifier.create(service.queryPoiAround(request, "google"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryIpLocation() {
    when(geoAdapter.supports()).thenReturn(supports);
    when(geoAdapter.provider()).thenReturn(new MapProperties.MaxmindConfig());
    when(geoAdapter.queryIpLocation(any(), any())).thenReturn(Mono.just(new IpLocation()));
    IpLocationRequest request = new IpLocationRequest();
    request.setIp("*********");
    StepVerifier.create(service.queryIpLocation(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryDriving() {
    when(geoAdapter.supports()).thenReturn(supports);
    when(geoAdapter.provider()).thenReturn(new MapProperties.AMapConfig());
    when(geoAdapter.queryDriving(any())).thenReturn(Mono.just(new PointDistance()));
    DrivingRouteRequest request = DrivingRouteRequest.builder()
      .from(new LocationPoint(117.190091, 39.071510))
      .to(new LocationPoint(118.190091, 40.071510))
      .waypoints(List.of(new LocationPoint(116.422462, 39.829647)))
      .build();
    StepVerifier.create(service.queryDriving(request, true, "amap"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryDistance() {
    when(geoAdapter.supports()).thenReturn(supports);
    when(geoAdapter.provider()).thenReturn(new MapProperties.AMapConfig());
    when(geoAdapter.queryDistance(any(), any())).thenReturn(Mono.just(List.of(new PointDistance())));
    List<LocationPoint> origins = List.of(new LocationPoint(117.190091, 39.071510));
    List<LocationPoint> destination = List.of(new LocationPoint(118.190091, 40.071510));
    StepVerifier.create(service.queryDistance(origins, destination, true))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryLocationPoint() {
    when(geoAdapter.supports()).thenReturn(supports);
    when(geoAdapter.provider()).thenReturn(new MapProperties.AMapConfig());
    when(geoAdapter.queryLocationPoint(any(), any())).thenReturn(Mono.just(new LocationPoint()));
    StepVerifier.create(service.queryLocationPoint("海淀区甲63号", "北京市", true, "amap"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }


}