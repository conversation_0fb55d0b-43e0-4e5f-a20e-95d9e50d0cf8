package com.sharecrm.egress.geo;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import com.sharecrm.egress.config.FluxTrace;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.GeoAddress;
import com.sharecrm.egress.entity.GeoAddressEsCache;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.entity.IpLocationEsCache;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.MapPoi;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.Duration;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class GeoEsCacheServiceTest {

  /**
   * 主要是为了不启用ES，client为null时不能报错
   */
  private final GeoEsCacheService serviceNullClient = new GeoEsCacheService(null, new MapProperties());

  private final ElasticsearchClient client = mock(ElasticsearchClient.class);
  private final MapProperties properties = mock(MapProperties.class);
  private final GeoEsCacheService service = new GeoEsCacheService(client, properties);

  @BeforeEach
  void setup() {
    when(properties.getElasticsearch()).thenReturn(mockEsConfig());
  }

  @Test
  void queryIpLocation() throws IOException {
    assertNull(serviceNullClient.queryIpLocation("***********", "en", "amap"));
    SearchResponse<IpLocationEsCache> response = mock(SearchResponse.class);
    IpLocationEsCache cache = new IpLocationEsCache();
    cache.setCountry("China");
    when(response.hits()).thenReturn(HitsMetadata.of(h -> h.hits(Hit.of(ht -> ht
      .index("index")
      .id("id")
      .source(cache)
    ))));
    when(client.search(any(SearchRequest.class), eq(IpLocationEsCache.class))).thenReturn(response);
    IpLocation rs = service.queryIpLocation("***********", "en", "amap");
    assertNotNull(rs);
    assertNotNull(rs.getCountry());
  }

  @NotNull
  private MapProperties.ElasticsearchConfig mockEsConfig() {
    MapProperties.ElasticsearchConfig config = new MapProperties.ElasticsearchConfig();
    config.setIpCacheReadEnabled(true);
    config.setIpCacheWriteEnabled(true);
    config.setIpCacheTimeout(Duration.ofDays(7));
    config.setCacheTimeout(Duration.ofDays(10));
    return config;
  }

  @Test
  void cacheIpLocation() {
    assertDoesNotThrow(() -> serviceNullClient.cacheIpLocation(new IpLocation()));
    assertDoesNotThrow(() -> service.cacheIpLocation(new IpLocation()));
  }

  @Test
  void queryReverseGeoAddress() throws IOException {
    assertNull(serviceNullClient.queryReverseGeoAddress(new ReverseGeoRequest(), new FluxTrace(), "amap"));
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    request.setLanguage("zh-CN");
    SearchResponse<GeoAddressEsCache> response = mock(SearchResponse.class);
    GeoAddressEsCache cache = new GeoAddressEsCache();
    cache.setGeocode("39.977054,116.331934");
    when(response.hits()).thenReturn(HitsMetadata.of(h -> h.hits(Hit.of(ht -> ht
      .index("index")
      .id("id")
      .sort(List.of(FieldValue.of(11.0)))
      .source(cache)
    ))));
    when(client.search(any(SearchRequest.class), eq(GeoAddressEsCache.class))).thenReturn(response);
    GeoAddressEsCache rs = service.queryReverseGeoAddress(request, new FluxTrace(), "amap");
    //这儿需要完善
    assertNull(rs);
  }

  @Test
  void cacheReverseGeoAddress() {
    assertDoesNotThrow(() -> serviceNullClient.cacheReverseGeoAddress(new GeoAddress()));
    assertDoesNotThrow(() -> {
      GeoAddress address = new GeoAddress();
      address.setCity("北京市");
      address.setPois(List.of(new MapPoi()));
      serviceNullClient.cacheReverseGeoAddress(address);
    });
  }

  @Test
  void clearIpLocationExpireData() {
    assertDoesNotThrow(serviceNullClient::clearIpLocationExpireData);
    assertDoesNotThrow(service::clearIpLocationExpireData);
  }

  @Test
  void clearGeoAddressExpireData() {
    assertDoesNotThrow(serviceNullClient::clearGeoAddressExpireData);
    assertDoesNotThrow(service::clearGeoAddressExpireData);
  }

  @Test
  void formatDocumentAddress() {
    MapPoi poi = new MapPoi();
    poi.setAddress("和平街35号");
    GeoAddress address = new GeoAddress();
    address.setProvince("北京市");
    address.setCity("北京市");
    String rs = serviceNullClient.formatDocumentAddress(poi, address);
    assertEquals("北京市和平街35号", rs);

  }

  @Test
  void cacheAddressList() {
    GeoAddress address = new GeoAddress();
    assertTrue(serviceNullClient.cacheAddressList(address).isEmpty());
    MapPoi poi = new MapPoi();
    poi.setAddress("和平街35号");
    poi.setLocation(LocationPoint.parseLatitudeLongitude("116.397428,39.90923"));
    address.setPois(List.of(poi));
    assertNotNull(serviceNullClient.cacheAddressList(address).getFirst().getAddress());
  }

}
