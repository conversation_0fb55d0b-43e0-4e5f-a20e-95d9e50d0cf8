package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.TencentMapApi;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.*;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class TencentGeoServiceTest {


  private final List<LocationPoint> origins = List.of(new LocationPoint(117.190091, 39.071510), new LocationPoint(116.422462, 39.829647));
  private final List<LocationPoint> destinations = List.of(new LocationPoint(116.389160, 40.007632), new LocationPoint(116.423289, 39.831261));


  private final TencentMapApi tencentApi = mock(TencentMapApi.class);
  private final MapProperties.TencentConfig config = mock(MapProperties.TencentConfig.class);

  private final TencentGeoService service = new TencentGeoService(tencentApi, config);


  @Test
  void queryReverseGeoAddress() {
    when(tencentApi.reverseGeoCode(any(), any(), any())).thenReturn(Mono.just(tencentAddressResult()));
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    StepVerifier.create(service.queryReverseGeoAddress(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();

  }

  private TencentAddressResult tencentAddressResult() {
    TencentAddressResult result = new TencentAddressResult();
    result.setStatus(0);
    TencentAddressDetail detail = new TencentAddressDetail();
    TencentGeoAddress address = new TencentGeoAddress();
    address.setAddressComponent(detail);
    result.setResult(address);
    return result;
  }

  @Test
  void queryGeoAddress() {
    when(tencentApi.geoCode(any(), any(), any())).thenReturn(Mono.just(mockTencentGeoResult()));
    StepVerifier.create(service.queryGeoAddress(new GeoEncodeRequest()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private TencentGeoResult mockTencentGeoResult() {
    TencentGeoResult rs = new TencentGeoResult();
    rs.setStatus(0);
    TencentGeoLocation location = new TencentGeoLocation();
    location.setDetail(new TencentAddressDetail());
    location.setLocation(new LocationPoint(39.977054, 116.331934));
    rs.setResult(location);
    return rs;
  }

  @Test
  void queryLocationPoint() {
    when(tencentApi.geoCode(any(), any(), any())).thenReturn(Mono.just(mockTencentGeoResult()));
    StepVerifier.create(service.queryLocationPoint("海淀区甲63号", "北京市"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryDistance() {
    when(tencentApi.distance(any(), any())).thenReturn(Mono.just(mockDistanceResult()));
    StepVerifier.create(service.queryDistance(origins, destinations))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private TencentDistanceResult mockDistanceResult() {
    TencentDistanceResult result = new TencentDistanceResult();
    result.setStatus(0);
    TencentDistanceResultData data = new TencentDistanceResultData();
    TencentDistanceRowData rowData = new TencentDistanceRowData();
    rowData.setElements(List.of(new PointDistance()));
    data.setRows(List.of(rowData));
    result.setResult(data);
    return result;
  }

  @Test
  void queryDriving() {
    when(tencentApi.driving(any(), any(), any())).thenReturn(Mono.just(mockDrivingResult()));
    DrivingRouteRequest request = DrivingRouteRequest.builder()
      .from(new LocationPoint(117.190091, 39.071510))
      .to(new LocationPoint(118.190091, 40.071510))
      .waypoints(List.of(new LocationPoint(116.422462, 39.829647)))
      .build();
    StepVerifier.create(service.queryDriving(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private TencentDrivingResult mockDrivingResult() {
    TencentDrivingResult result = new TencentDrivingResult();
    result.setStatus(0);
    TencentRoute route = new TencentRoute();
    PointDistance e = new PointDistance();
    e.setDuration(10);
    e.setDistance(1000);
    route.setRoutes(List.of(e));
    result.setResult(route);
    return result;
  }

  @Test
  void queryPoiAround() {
    when(tencentApi.poiAround(any(), any(), any(), any(Integer.class), any(Integer.class), any())).thenReturn(Mono.just(mockPoiAroundResult()));
    PoiSearchRequest request = new PoiSearchRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    request.setRadius(1000);
    request.setTypes(List.of("餐馆"));
    request.setLanguage("zh");
    request.setPageSize(10);
    request.setPageNum(1);
    StepVerifier.create(service.queryPoiAround(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private TencentPoiResult mockPoiAroundResult() {
    TencentPoiResult result = new TencentPoiResult();
    result.setStatus(0);
    TencentPoi poi = new TencentPoi();
    poi.setLocation(new LocationPoint(39.977054, 116.331934));
    result.setData(List.of(poi));
    result.setCount(1);
    return result;
  }

  @Test
  void queryIpLocation() {
    when(tencentApi.ipLocation(any(), any())).thenReturn(Mono.just(mockIpResult()));
    StepVerifier.create(service.queryIpLocation("*********", "zh-CN"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private TencentIpResult mockIpResult() {
    TencentIpResult result = new TencentIpResult();
    result.setStatus(0);
    TencentIpContent content = new TencentIpContent();
    content.setIp("*********");
    content.setLocation(new LocationPoint(39.977054, 116.331934));
    content.setAddressDetail(new TencentAddressDetail());
    result.setContent(content);
    return result;
  }


}