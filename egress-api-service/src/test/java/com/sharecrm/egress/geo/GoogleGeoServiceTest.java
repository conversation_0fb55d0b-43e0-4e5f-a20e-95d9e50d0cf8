package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.GoogleApi;
import com.sharecrm.egress.api.GoogleRouteApi;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.GoogleGeoLocation;
import com.sharecrm.egress.entity.GoogleGeoResult;
import com.sharecrm.egress.entity.GoogleGeometry;
import com.sharecrm.egress.entity.GooglePoiLocation;
import com.sharecrm.egress.entity.GooglePoiResult;
import com.sharecrm.egress.entity.GoogleRoute;
import com.sharecrm.egress.entity.GoogleRouteResult;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class GoogleGeoServiceTest {

  private final GoogleApi googleApi = mock(GoogleApi.class);
  private final GoogleRouteApi googleRouteApi = mock(GoogleRouteApi.class);
  private final MapProperties.GoogleConfig config = mock(MapProperties.GoogleConfig.class);

  private final GoogleGeoService service = new GoogleGeoService(googleApi, googleRouteApi, config);


  @Test
  void queryReverseGeoAddress() {
    when(googleApi.reverseGeoCode(any(), any())).thenReturn(Mono.just(mockAddressResult()));
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    StepVerifier.create(service.queryReverseGeoAddress(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();

  }

  private GoogleGeoResult mockAddressResult() {
    GoogleGeoResult result = new GoogleGeoResult();
    result.setStatus("OK");
    GoogleGeoLocation location = new GoogleGeoLocation();
    result.setResults(List.of(location));
    return result;
  }


  @Test
  void queryGeoAddress() {
    when(googleApi.geoCode(any(), any(), any())).thenReturn(Mono.just(mockGoogleResult()));
    StepVerifier.create(service.queryGeoAddress(new GeoEncodeRequest()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private GoogleGeoResult mockGoogleResult() {
    GoogleGeoResult result = new GoogleGeoResult();
    GoogleGeoLocation location = new GoogleGeoLocation();
    GoogleGeometry geometry = new GoogleGeometry();
    geometry.setLocation(LocationPoint.parseLatitudeLongitude("39.984154,116.307490"));
    location.setGeometry(geometry);
    result.setResults(List.of(location));
    result.setStatus("OK");
    return result;
  }


  @Test
  void queryDriving() {
    when(googleRouteApi.driving(any(), any())).thenReturn(Mono.just(mockDrivingResult()));
    DrivingRouteRequest request = DrivingRouteRequest.builder()
      .from(new LocationPoint(117.190091, 39.071510))
      .to(new LocationPoint(118.190091, 40.071510))
      .waypoints(List.of(new LocationPoint(116.422462, 39.829647)))
      .build();
    StepVerifier.create(service.queryDriving(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private GoogleRouteResult mockDrivingResult() {
    GoogleRouteResult result = new GoogleRouteResult();
    GoogleRoute route = new GoogleRoute();
    route.setDuration("10s");
    route.setDistance(100);
    result.setRoutes(List.of(route));
    return result;
  }

  @Test
  void queryPoiAround() {
    when(googleApi.poiAround(any(), any(Integer.class), any(), any(), any(), any())).thenReturn(Mono.just(mockPoiAroundResult()));
    PoiSearchRequest request = new PoiSearchRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    request.setRadius(1000);
    request.setTypes(List.of("餐馆"));
    request.setLanguage("zh");
    request.setPageSize(10);
    request.setPageNum(1);
    StepVerifier.create(service.queryPoiAround(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private GooglePoiResult mockPoiAroundResult() {
    GooglePoiResult result = new GooglePoiResult();
    result.setStatus("OK");
    GooglePoiLocation location = new GooglePoiLocation();
    location.setGeometry(new GoogleGeometry());
    result.setResults(List.of(location));
    return result;
  }

}