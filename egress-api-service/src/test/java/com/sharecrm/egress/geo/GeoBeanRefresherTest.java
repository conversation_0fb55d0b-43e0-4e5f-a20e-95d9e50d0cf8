package com.sharecrm.egress.geo;

import com.sharecrm.egress.config.MapProperties;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.reactive.function.client.ExchangeStrategies;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.mock;

class GeoBeanRefresherTest {

  GeoBeanRefresher refresher = new GeoBeanRefresher(new MapProperties(), mock(ConfigurableApplicationContext.class), mock(ExchangeStrategies.class));

  @Test
  void handleRefreshedEvent() {
    assertDoesNotThrow(() -> refresher.handleRefreshedEvent(new RefreshScopeRefreshedEvent()));
  }
}