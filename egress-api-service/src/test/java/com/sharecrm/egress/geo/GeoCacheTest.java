package com.sharecrm.egress.geo;

import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiSearchRequest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class GeoCacheTest {

  /**
   * 不是为了检验cache是否对，主要是为了保证MapProperties不要丢失默认值，防止配置错误
   */
  private final GeoCache cache = new GeoCache(new MapProperties());

  @Test
  void getLocationPoint() {
    LocationPoint point = new LocationPoint();
    cache.putLocationPoint("test", point);
    assertEquals(point, cache.getLocationPoint("test"));
  }

  @Test
  void getDistanceReturnsNullForNonExistentKey() {
    assertNull(cache.getDistance("nonExistentKey"));
  }

  @Test
  void getGeoAddressReturnsNullForNonExistentKey() {
    assertNull(cache.getGeoAddress("nonExistentKey"));
  }

  @Test
  void getIpLocationReturnsNullForNonExistentKey() {
    assertNull(cache.getIpLocation("nonExistentKey", "en"));
  }

  @Test
  void getReverseGeoAddressReturnsNullForNonExistentKey() {
    assertNull(cache.getReverseGeoAddress("nonExistentKey"));
  }

  @Test
  void getPoiResponseReturnsNullForNonExistentKey() {
    assertNull(cache.getPoiResponse(new PoiSearchRequest()));
  }


}