package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.AMapApi;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.AMapAddressResult;
import com.sharecrm.egress.entity.AMapCoordinateResult;
import com.sharecrm.egress.entity.AMapDistanceResult;
import com.sharecrm.egress.entity.AMapDrivingResult;
import com.sharecrm.egress.entity.AMapGeoAddress;
import com.sharecrm.egress.entity.AMapGeoLocation;
import com.sharecrm.egress.entity.AMapGeoResult;
import com.sharecrm.egress.entity.AMapIpResult;
import com.sharecrm.egress.entity.AMapPoiResult;
import com.sharecrm.egress.entity.AMapReverseGeoCode;
import com.sharecrm.egress.entity.AMapRoute;
import com.sharecrm.egress.entity.AmapPoi;
import com.sharecrm.egress.entity.CoordinateConvertRequest;
import com.sharecrm.egress.entity.DrivingRouteRequest;
import com.sharecrm.egress.entity.GeoEncodeRequest;
import com.sharecrm.egress.entity.LocationPoint;
import com.sharecrm.egress.entity.PoiSearchRequest;
import com.sharecrm.egress.entity.PointDistance;
import com.sharecrm.egress.entity.ReverseGeoRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class AMapGeoServiceTest {

  private final List<LocationPoint> origins = List.of(new LocationPoint(117.190091, 39.071510), new LocationPoint(116.422462, 39.829647));
  private final List<LocationPoint> destinations = List.of(new LocationPoint(116.389160, 40.007632), new LocationPoint(116.423289, 39.831261));


  private final AMapApi amapApi = mock(AMapApi.class);

  private AMapGeoService service;

  @BeforeEach
  void setup() {
    service = new AMapGeoService(amapApi, new MapProperties.AMapConfig());
  }

  @Test
  void queryReverseGeoAddress() {
    when(amapApi.reverseGeoCode(any(), any(), any(), any())).thenReturn(Mono.just(mockAmapReverseAddress()));
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    StepVerifier.create(service.queryReverseGeoAddress(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private AMapAddressResult mockAmapReverseAddress() {
    AMapAddressResult result = new AMapAddressResult();
    result.setStatus(1);
    result.setInfoCode(10000);
    AMapReverseGeoCode geo = new AMapReverseGeoCode();
    geo.setComponent(new AMapGeoAddress());
    result.setResult(geo);
    return result;
  }

  @Test
  void queryGeoAddress() {
    when(amapApi.geoCode(any(), any(), any(), any())).thenReturn(Mono.just(mockGeoResult()));
    StepVerifier.create(service.queryGeoAddress(new GeoEncodeRequest()))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private AMapGeoResult mockGeoResult() {
    AMapGeoResult result = new AMapGeoResult();
    result.setStatus(1);
    result.setInfoCode(10000);
    result.setCount(1);
    AMapGeoLocation location = new AMapGeoLocation();
    location.setLocation("39.977054, 116.331934");
    result.setGeoCodes(new AMapGeoLocation[]{location});
    return result;
  }

  @Test
  void queryLocationPoint() {
    when(amapApi.geoCode(any(), any(), any(), any())).thenReturn(Mono.just(mockGeoResult()));
    StepVerifier.create(service.queryLocationPoint("海淀区甲63号", "北京市"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryDistance() {
    when(amapApi.distance(any(), any())).thenReturn(Mono.just(mockDistanceResult()));
    StepVerifier.create(service.queryDistance(origins, destinations))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private AMapDistanceResult mockDistanceResult() {
    AMapDistanceResult result = new AMapDistanceResult();
    result.setStatus(1);
    result.setInfoCode(10000);
    result.setCount(1);
    result.setResults(List.of(new PointDistance()));
    return result;
  }

  @Test
  void queryDriving() {
    when(amapApi.driving(any(), any(), any())).thenReturn(Mono.just(mockDrivingResult()));
    DrivingRouteRequest request = DrivingRouteRequest.builder()
      .from(new LocationPoint(117.190091, 39.071510))
      .to(new LocationPoint(118.190091, 40.071510))
      .waypoints(List.of(new LocationPoint(116.422462, 39.829647)))
      .build();
    StepVerifier.create(service.queryDriving(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private AMapDrivingResult mockDrivingResult() {
    AMapDrivingResult result = new AMapDrivingResult();
    result.setStatus(1);
    result.setInfoCode(10000);
    result.setCount(1);
    AMapRoute route = new AMapRoute();
    route.setPaths(List.of(new PointDistance()));
    result.setRoute(route);
    return result;
  }

  @Test
  void queryPoiAround() {
    when(amapApi.poiAround(any(), any(Integer.class), any(), any(), any(Integer.class), any(Integer.class), any())).thenReturn(Mono.just(mockPoiAroundResult()));
    PoiSearchRequest request = new PoiSearchRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    request.setRadius(1000);
    request.setTypes(List.of("餐馆"));
    request.setLanguage("zh");
    request.setPageSize(10);
    request.setPageNum(1);
    StepVerifier.create(service.queryPoiAround(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private AMapPoiResult mockPoiAroundResult() {
    AMapPoiResult result = new AMapPoiResult();
    result.setStatus(1);
    result.setInfoCode(10000);
    result.setCount(1);
    AmapPoi poi = new AmapPoi();
    poi.setLocation("39.977054, 116.331934");
    result.setPois(List.of(poi));
    return result;
  }

  @Test
  void queryIpLocation() {
    when(amapApi.ipLocation(any(), any())).thenReturn(Mono.just(mockIpResult()));
    StepVerifier.create(service.queryIpLocation("*********", "zh-CN"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void coordinateConvert() {
    when(amapApi.coordinate(any(), any())).thenReturn(Mono.just(mockCoordinateConvertResult()));
    CoordinateConvertRequest request = new CoordinateConvertRequest();
    request.setLongitude(116.331934);
    request.setLatitude(39.977054);
    StepVerifier.create(service.coordinateConvert(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private AMapCoordinateResult mockCoordinateConvertResult() {
    AMapCoordinateResult result = new AMapCoordinateResult();
    result.setStatus(1);
    result.setInfoCode(10000);
    result.setLocations("39.977054, 116.331934");
    return result;
  }

  private AMapIpResult mockIpResult() {
    AMapIpResult result = new AMapIpResult();
    result.setStatus(1);
    result.setInfoCode(10000);
    result.setCity("北京市");
    return result;
  }

  @Test
  void supports() {
    assertNotNull(service.supports());
  }

  @Test
  void provider() {
    //默认值可别乱改，改了要慎重
    assertEquals("amap", service.provider().getType());
  }
}