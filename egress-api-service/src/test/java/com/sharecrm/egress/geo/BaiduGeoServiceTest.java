package com.sharecrm.egress.geo;

import com.sharecrm.egress.api.BaiduApi;
import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.*;
import com.sharecrm.egress.exception.EgressAppException;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class BaiduGeoServiceTest {

  private final List<LocationPoint> origins = List.of(new LocationPoint(117.190091, 39.071510), new LocationPoint(116.422462, 39.829647));
  private final List<LocationPoint> destinations = List.of(new LocationPoint(116.389160, 40.007632), new LocationPoint(116.423289, 39.831261));


  private final BaiduApi baiduApi = mock(BaiduApi.class);
  private final MapProperties.BaiduConfig config = mock(MapProperties.BaiduConfig.class);

  private final BaiduGeoService service = new BaiduGeoService(baiduApi, config);


  @Test
  void queryReverseGeoAddress() {
    when(baiduApi.reverseGeoCode(any(), any(), any())).thenReturn(Mono.just(mockAddressResult()));
    ReverseGeoRequest request = new ReverseGeoRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    StepVerifier.create(service.queryReverseGeoAddress(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();

  }

  private BaiduAddressResult mockAddressResult() {
    BaiduAddressResult result = new BaiduAddressResult();
    result.setStatus(0);
    BaiduAddressDetail detail = new BaiduAddressDetail();
    BaiduAddressContent address = new BaiduAddressContent();
    address.setAddressComponent(detail);
    result.setResult(address);
    return result;
  }

  @Test
  void queryGeoAddress() {
    StepVerifier.create(service.queryGeoAddress(new GeoEncodeRequest()))
      .expectError(EgressAppException.class)
      .verify();
  }

  private BaiduGeoResult mockGeoResult() {
    BaiduGeoResult rs = new BaiduGeoResult();
    rs.setStatus(0);
    BaiduGeoLocation result = new BaiduGeoLocation();
    result.setLocation(new LocationPoint(39.977054, 116.331934));
    rs.setResult(result);
    return rs;
  }

  @Test
  void queryLocationPoint() {
    when(baiduApi.geoCode(any(), any())).thenReturn(Mono.just(mockGeoResult()));
    StepVerifier.create(service.queryLocationPoint("海淀区甲63号", "北京市"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  @Test
  void queryDistance() {
    when(baiduApi.distance(any(), any())).thenReturn(Mono.just(mockDistanceResult()));
    StepVerifier.create(service.queryDistance(origins, destinations))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private BaiduDistanceResult mockDistanceResult() {
    BaiduDistanceResult result = new BaiduDistanceResult();
    result.setStatus(0);
    BaiduDistance distance = new BaiduDistance();
    distance.setDistance(new TextAndValue());
    distance.setDuration(new TextAndValue());
    result.setResult(List.of(distance));
    return result;
  }

  @Test
  void queryDriving() {
    when(baiduApi.driving(any(), any(), any())).thenReturn(Mono.just(mockDrivingResult()));
    DrivingRouteRequest request = DrivingRouteRequest.builder()
      .from(new LocationPoint(117.190091, 39.071510))
      .to(new LocationPoint(118.190091, 40.071510))
      .waypoints(List.of(new LocationPoint(116.422462, 39.829647)))
      .build();
    StepVerifier.create(service.queryDriving(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private BaiduDrivingResult mockDrivingResult() {
    BaiduDrivingResult result = new BaiduDrivingResult();
    result.setStatus(0);
    BaiduRoute route = new BaiduRoute();
    route.setRoutes(List.of(new PointDistance()));
    route.setTotal(1);
    route.setDuration(10);
    result.setResult(route);
    return result;
  }

  @Test
  void queryPoiAround() {
    when(baiduApi.poiAround(any(), any(Integer.class), any(), any(), any(Integer.class), any(Integer.class), any())).thenReturn(Mono.just(mockPoiAroundResult()));
    PoiSearchRequest request = new PoiSearchRequest();
    request.setLatitude(39.977054);
    request.setLongitude(116.331934);
    request.setRadius(1000);
    request.setTypes(List.of("餐馆"));
    request.setLanguage("zh");
    request.setPageSize(10);
    request.setPageNum(1);
    StepVerifier.create(service.queryPoiAround(request))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private BaiduPoiResult mockPoiAroundResult() {
    BaiduPoiResult result = new BaiduPoiResult();
    result.setStatus(0);
    result.setTotal(1);
    BaiduPoi poi = new BaiduPoi();
    poi.setLocation(new LocationPoint(39.977054, 116.331934));
    result.setResults(List.of(poi));
    return result;
  }

  @Test
  void queryIpLocation() {
    when(baiduApi.ipLocation(any(), any())).thenReturn(Mono.just(mockIpResult()));
    StepVerifier.create(service.queryIpLocation("*********", "zh-CN"))
      .expectNextMatches(Objects::nonNull)
      .verifyComplete();
  }

  private BaiduIpResult mockIpResult() {
    BaiduIpResult result = new BaiduIpResult();
    result.setStatus(0);
    BaiduIpContent content = new BaiduIpContent();
    BaiduAddressDetail detail = new BaiduAddressDetail();
    detail.setCity("北京市");
    content.setAddressDetail(detail);
    result.setContent(content);
    return result;
  }

}