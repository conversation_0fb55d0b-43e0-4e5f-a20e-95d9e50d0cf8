package com.sharecrm.egress;

import com.sharecrm.egress.sms.SmsUtils;

/**
 * 手机号脱敏功能演示
 */
public class MobileNumberMaskingDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 手机号脱敏功能演示 ===\n");
        
        // 测试用例1: 正常手机号
        testCase("正常手机号", "您的手机号13812345678已绑定成功");
        
        // 测试用例2: 多个手机号
        testCase("多个手机号", "联系人：13912345678，备用：15987654321");
        
        // 测试用例3: 订单号（不应该被替换）
        testCase("订单号（修复前会误判）", "订单号：202313345678901，金额：1234.56");
        
        // 测试用例4: 混合内容
        testCase("混合内容", "账号：1234567890123456789，手机：13812345678");
        
        // 测试用例5: 紧挨着的数字
        testCase("紧挨着的数字", "编号12313812345678901234");
        
        // 测试用例6: 独立手机号
        testCase("独立手机号", "手机号码 13812345678 已验证");
        
        // 测试用例7: 手机号在开头
        testCase("手机号在开头", "13812345678是您的联系方式");
        
        // 测试用例8: 手机号在结尾
        testCase("手机号在结尾", "您的联系方式是13812345678");
    }
    
    private static void testCase(String description, String input) {
        String output = SmsUtils.replaceContext(input);
        System.out.println(description + ":");
        System.out.println("  输入: " + input);
        System.out.println("  输出: " + output);
        System.out.println("  结果: " + (input.equals(output) ? "未替换" : "已替换"));
        System.out.println();
    }
}
