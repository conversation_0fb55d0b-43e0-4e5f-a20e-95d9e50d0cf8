sonar.sourceEncoding=UTF-8
sonar.java.source=21
sonar.qualitygate.wait=true
sonar.qualitygate.timeout=600
sonar.log.level=INFO
sonar.scm.provider=git
sonar.host.url=https://sonarqube.firstshare.cn
sonar.login=sqa_ee5c8f970c75fa609b6d50bb3325ee44d874f1c7
sonar.projectName=egress-api-service
sonar.links.scm=https://git.firstshare.cn/BizCommon/egress-api-service.git
sonar.links.ci=https://git.firstshare.cn/BizCommon/egress-api-service/-/pipelines
sonar.links.homepage=https://git.firstshare.cn/BizCommon/egress-api-service
sonar.links.issue=https://git.firstshare.cn/BizCommon/egress-api-service/-/issues
sonar.findbugs.allowuncompiledcode=true
maven.test.failure.ignore=true
sonar.core.codeCoveragePlugin=jacoco
sonar.coverage.exclusions=*.md,*.http,egress-api-sdk,**/entity/**,**/config/**,**/egress/io/**,**/egress/api/**,**/egress/task/**
