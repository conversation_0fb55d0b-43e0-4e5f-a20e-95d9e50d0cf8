<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke.cloud</groupId>
    <artifactId>fxiaoke-spring-cloud-parent</artifactId>
    <version>3.0.0-SNAPSHOT</version>
  </parent>
  
  <groupId>com.sharecrm.api</groupId>
  <artifactId>egress-api</artifactId>
  <name>egress-api</name>
  <version>3.0.0</version>
  <packaging>pom</packaging>
  <description>访问外部第三方服务</description>
  
  <modules>
    <!-- 使用这个SDK的大部分都是非Spring Boot项目且各个组件的版本都比较低，注意兼容 -->
    <module>egress-api-sdk</module>
    <module>egress-api-service</module>
  </modules>

</project>
