---
description: ALWAYS use when writing or updating Markdown files to ensure consistent formatting and readability. This rule enforces standardized Markdown practices across all documentation.
globs: **/*.{md,mdx}
---
# Markdown文档标准

## 要求
- 所有基础及扩展语法均遵循官方@Markdown指南。
- 保持清晰的文档结构和可读性。
- 必要时包含适当的元数据。
- 在适当的地方使用Mermaid图表进行可视化文档说明。
- 始终使用YAML前置内容来添加元数据，但要保持元数据简洁。

## Markdown参考
所有基础和扩展Markdown语法，请参考：
- @基础语法指南
- @扩展语法指南

## 格式规则
- 使用ATX风格标题，井号后留空格：`# 标题`
- 保持正确的标题层级结构（不要跳过层级）
- 标题最大深度为4级
- 在标题前后各添加一行空行
- 将XML标签内的内容缩进2个空格
- 在与父级缩进相同的位置，单独一行闭合XML标签
- 使用带表情符号的块引用表示不同类型的提示：

<example>
  > 🚨 **警告**：此处为关键信息。

  > 💡 **提示**：有用的建议。

  > ℹ️ **注意**：更多背景信息。
</example>

## 代码块
- 使用三个反引号并指定语言
- 正确缩进代码块
- 在代码块前后各添加一行空行
- 对于简短引用，使用行内代码

<example>
```typescript
function example(): void {
  console.log('Hello, Universe!');
}
```

在行文中引用`example()`函数。
</example>

## 表格
- 使用对齐指示符
- 包含表头分隔线
- 保持表格简洁易读
- 在表格前后各添加空行

<example>
| 名称    | 类型    | 描述     |
|:--------|:-------:|---------------:|
| id      | 数字    | 主键    |
| 名称    | 字符串  | 用户姓名    |
</example>

## 特殊元素

### 提示
使用带表情符号的块引用表示不同类型的提示：

<example>
> 🚨 **警告**：此处为关键信息。

> 💡 **提示**：有用的建议。

> ℹ️ **注意**：更多背景信息。
</example>

### Mermaid图表
使用Mermaid图表来可视化以下内容：
- 架构流程
- 流程顺序
- 决策树
- 状态机
- 组件关系
- AI代理规则流程

### 何时使用Mermaid图表
- 简单和复杂的工作流程都需要可视化
- 系统架构需要进行解释
- 流程存在多个分支
- 状态转换需要清晰呈现
- AI决策树需要绘制

### 图表最佳实践
1. 使用`---`语法添加清晰的标题
2. 使用描述性的节点标签
3. 为复杂流程添加注释
4. 使用子图对相关组件进行分组
5. 使用一致的方向（自上而下/从左到右/自下而上）
6. 保持图表聚焦且具体

<example>
```mermaid
---
title: 示例工作流程
---
graph TD
    A[开始] --> B{决策}
    B -->|是| C[流程1]
    B -->|否| D[流程2]
    C --> E[结束]
    D --> E
```
</example>

<example type="invalid">
```mermaid
graph TD
A-->B
B-->C
```

无标题，标签不清晰，无上下文
</example>