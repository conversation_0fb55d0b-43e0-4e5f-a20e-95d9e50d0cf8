---
description: Use ALWAYS when asked to CREATE A RULE or UPDATE A RULE or taught a lesson from the user that should be retained as a new rule for Cursor
globs: .cursor/rules/*.mdc
alwaysApply: false
---

# Cursor Rules Format
## 核心结构

```mdc
---
description: ACTION when TRIGGER to OUTCOME
globs: *.mdc
---

## 文件组织

### 位置
- 路径：`.cursor/rules/`
- 扩展名：`.mdc`

### 命名约定
PREFIX-name.mdc，其中PREFIX为：
- 0XX：核心标准
- 1XX：工具配置
- 3XX：单元测试标准
- 4XX：Markdown标准
- 8XX：工作流程
- 9XX：模板
- 1XXX：语言规则
- 2XXX：框架规则
- 1XXXX：开发指南
- 2XXXX：代码审查
- _name.mdc：私有规则

### 通配符模式示例
不同规则类型的常见通配符模式：
- 核心标准：.cursor/rules/*.mdc
- 语言规则：src/**/*.{java}
- 测试标准：**/*.Test.{java}
- 文档：docs/**/*.md
- 配置文件：src/**/*.{properties}
- 构建产物：target/**

## 必填字段

### 前置元数据
- description：采用“操作触发结果”格式
- globs：“文件和文件夹的通配符模式”

### 正文
- <version>X.Y.Z</version>
- context：使用条件
- requirements：可操作事项
- examples：包括有效和无效示例

## 格式指南
- 主要使用简洁的 Markdown 格式
- XML 标签限于：
  - <example>
  - <danger>
  - <required>
  - <rules>
  - <rule>
  - <critical>
  - <version>
- XML 标签或嵌套 XML 标签内的内容始终缩进 2 个空格
- 规则尽可能简短
- 如果使用 Mermaid 语法能比描述复杂规则更简短或更清晰，则使用该语法
- 在适当的地方使用表情符号，以传达有助于 AI 代理理解规则的含义
- 示例尽可能简短，以清晰展示正面或反面示例

## AI 优化技巧
1. 在描述中使用精确、确定的“操作触发结果”格式
2. 提供规则在实际应用中的简洁正面和反面示例
3. 优化 AI 上下文窗口的效率
4. 删除任何非必要或冗余的信息
5. 使用不带引号的标准通配符模式（例如，*.java, src/**/*.java）

## AI 上下文效率
1. 保持前置元数据描述在 120 个字符以内，同时确保 AI 代理能清晰理解规则选择意图
2. 示例仅保留必要的模式
3. 使用分层结构以便快速解析
4. 去除各部分的冗余信息
5. 以最少的令牌保持高信息密度
6. 专注于机器可执行的指令，而非人为解释

<critical>
  - 绝不要包含冗长的解释或冗余的上下文，以免增加 AI 令牌开销
  - 文件要尽可能简短且切中要点，但绝不能以牺牲规则对 AI 代理的影响力和实用性为代价
</critical>

