---
description: USE ALWAYS when writing or modifying code to ensure adherence to DRY and SOLID principles
globs: src/**/*.{java,properties,xml,md}
---

# DRY and SOLID Principles Standards

<version>1.0.0</version>

## Context
- Applied when creating or modifying any code files
- Ensures consistent application of software design principles
- Promotes maintainable, scalable, and testable code

## Requirements

### DRY (Don't Repeat Yourself)
- Extract repeated logic into reusable functions or components
- Create shared utility functions for common operations
- Use TypeScript types/interfaces to share common data structures
- Implement shared hooks for common React patterns
- Create shared constants for repeated values

### Single Responsibility Principle
- Each component/class/function should do exactly one thing
- Split large components into smaller, focused components
- Extract complex logic into separate utility functions
- Separate data fetching from presentation logic
- Move complex state management to dedicated hooks

### Open/Closed Principle
- Design components to be extended without modification
- Use props for component customization
- Implement strategy patterns for varying behaviors
- Use composition over inheritance
- Create abstract interfaces for plugins/extensions

### Liskov Substitution Principle
- Ensure derived components can replace base components
- Maintain consistent prop contracts
- Keep component behavior predictable
- Document any breaking changes in inheritance
- Use TypeScript to enforce type constraints

### Interface Segregation
- Create focused, minimal prop interfaces
- Split large prop interfaces into smaller ones
- Use composition to combine interfaces
- Avoid props that components don't use
- Create specialized hooks for specific features

### Dependency Inversion
- Depend on abstractions, not concrete implementations
- Use dependency injection via props
- Create interface-based service layers
- Use context for shared dependencies
- Implement feature flags via abstractions
