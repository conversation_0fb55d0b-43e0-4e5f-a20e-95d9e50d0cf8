---
description: Maven pom 规则
globs: **/pom.xml
alwaysApply: true
---

# Maven pom 规则

- 使用我们自定义的 parent 代替 spring-boot-starter-parent。

```xml
<parent>
  <groupId>com.fxiaoke.cloud</groupId>
  <artifactId>fxiaoke-spring-cloud-parent</artifactId>
  <version>3.0.0-SNAPSHOT</version>
 <relativePath/>
</parent>

```

- 不需要指定 spring-boot.version，使用我们的pom中已经定义好的版本

- dependencyManagement 不需要 import spring-boot-dependencies。

this is bad

```xml
    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot 依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
```

- maven多模块项目，只在需要运行@SpringBootApplication的模块中引入 spring-boot-maven-plugin