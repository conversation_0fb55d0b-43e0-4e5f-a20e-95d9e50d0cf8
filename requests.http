###
GET http://localhost:8080/api/v2/ip/location?ip=*******

###
GET http://**************:13020/egress-api-service/api/v2/ip/location/database?ip=*************

###
GET http://localhost:8080/api/v2/ip/locations?ips=***********&ips=***********&ips=***********&ips=*******&ips=*******

###
GET http://localhost:8080/api/v2/mobile/query?mobile=13552346839

###
GET http://localhost:8080/api/v2/geocode/geo-address?address=广西桂林&cache=true

###
GET http://localhost:8080/api/v2/reverse-geocode/decode?latLng=39.990464%2C116.481488&cache=false

###
GET http://localhost:8080/api/v2/public-short-urls/5dv9tj

###
GET http://localhost:8080/api/v2/public-short-urls/4813

###
POST http://localhost:8080/api/v2/private-short-urls
content-type: application/json
Accept: application/json

{
  "url": "https://www.bing.com/search?q=success"
}

###
POST http://localhost:8080/api/v2/private-short-urls/original
content-type: application/json
Accept: application/json

{
  "code": "4813"
}


###
POST http://localhost:8080/api/v2/private-short-urls/batch
content-type: application/json
Accept: application/json

{
  "urls": [
    "https://www.bing.com/search?q=success&form=ANNTH1&refig=********************************",
    "https://www.baa.com"
  ]
}

###
GET http://localhost:8080/api/v2/private-short-urls/ocv7g0


###
POST http://localhost/egress-api-service/api/v2/sms/histories
content-type: application/json
Accept: application/json

{
  "pageNum":1,
  "pageSize":1000,
  "phone":"13716368080"
}


###
POST http://localhost:8080/api/v2/sms/internal
content-type: application/json
Accept: application/json

{
  "content": "fs-sms test message",
  "bizName": "qixin",
  "phones": [
    "13552346839"
  ]
}

###
POST http://localhost:8080/api/v2/sms/tts
content-type: application/json
Accept: application/json

{
  "templateId": 499,
  "bizName": "just",
  "phones": [
    "13552346839"
  ]
}

###
POST http://localhost:8080/api/v2/dirty-words/has
content-type: application/json
Accept: application/json

{
  "group": "sms",
  "text": "test words"
}

###
POST http://localhost:8080/api/v2/dirty-words/parse
content-type: application/json
Accept: application/json

{
  "group": "sms",
  "text": "你的大法违法吗检查一下吧"
}


###
POST http://localhost/egress-api-service/api/v2/emails
content-type: application/json
Accept: application/json

{
  "to": [
    "<EMAIL>"
  ],
  "subject": "邮件中文Test",
  "content": "邮件详情，你写点啥吧，<h1>换行大标题</h1>"
}


###
POST http://localhost:8080/translates
content-type: application/json
Accept: application/json

{
  "texts": [
    "这是我发的邮件,请查收",
    "如果有问题给我打电话"
  ],
  "target": "en"
}

###
POST https://apitest.i.sinotrans.com/tech-notify/template-sms/v3/templateSend
content-type:application/json
Accept:application/json
keyId:bb72c7c4-23c3-47da-bc40-9e24a7ebdac3
appCode:55276

{
  "systemCode": "55276",
  "systemName": "客户经营管理平台",
  "smsType": 0,
  "acceptorType": 0,
  "templateId": 1978913,
  "templateName": "中国外运CRM测试环境国内短信模板",
  "acceptNo": "13552346839",
  "country": "+86",
  "templateParamList": [
    "this, is a sms， from sino test curl"
  ]
}